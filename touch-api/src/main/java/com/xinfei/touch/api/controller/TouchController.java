package com.xinfei.touch.api.controller;

import com.xinfei.touch.api.dto.TouchRequest;
import com.xinfei.touch.api.dto.TouchResponse;
import com.xinfei.touch.application.service.TouchApplicationService;
import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 触达控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/touch")
@RequiredArgsConstructor
@Validated
public class TouchController {
    
    private final TouchApplicationService touchApplicationService;
    
    /**
     * 统一触达入口（支持T0实时触达和离线触达）
     */
    @PostMapping("/send")
    public ApiResponse<TouchResponse> send(@RequestBody @Valid TouchRequest request) {
        log.info("收到触达请求: requestId={}, touchType={}, channel={}, userId={}",
                request.getRequestId(), request.getTouchType(), request.getChannel(), request.getUserId());
        
        try {
            // 转换API DTO到应用层DTO
            com.xinfei.touch.application.dto.TouchRequest appRequest = convertToAppRequest(request);
            com.xinfei.touch.application.dto.TouchResponse appResponse = touchApplicationService.processTouch(appRequest);
            TouchResponse response = convertToApiResponse(appResponse);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("处理触达请求失败: requestId={}", request.getRequestId(), e);
            return ApiResponse.error("TOUCH_FAILED", "触达处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量触达入口（支持T0实时触达和离线触达）
     */
    @PostMapping("/batch-send")
    public ApiResponse<List<TouchResponse>> batchSend(@RequestBody @Valid List<TouchRequest> requests) {
        log.info("收到批量触达请求: size={}", requests.size());
        
        try {
            // 转换API DTO到应用层DTO
            List<com.xinfei.touch.application.dto.TouchRequest> appRequests = requests.stream()
                    .map(this::convertToAppRequest)
                    .collect(Collectors.toList());
            List<com.xinfei.touch.application.dto.TouchResponse> appResponses = touchApplicationService.processBatchTouch(appRequests);
            List<TouchResponse> responses = appResponses.stream()
                    .map(this::convertToApiResponse)
                    .collect(Collectors.toList());
            return ApiResponse.success(responses);
        } catch (Exception e) {
            log.error("处理批量触达请求失败", e);
            return ApiResponse.error("BATCH_TOUCH_FAILED", "批量触达处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询触达状态
     */
    @GetMapping("/status/{requestId}")
    public ApiResponse<TouchStatus> getStatus(@PathVariable String requestId) {
        log.info("查询触达状态: requestId={}", requestId);
        
        try {
            TouchStatus status = touchApplicationService.getTouchStatus(requestId);
            return ApiResponse.success(status);
        } catch (Exception e) {
            log.error("查询触达状态失败: requestId={}", requestId, e);
            return ApiResponse.error("QUERY_FAILED", "查询状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询触达记录
     */
    @GetMapping("/records")
    public ApiResponse<PageResult<TouchRecordDto>> getRecords(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long strategyId,
            @RequestParam(required = false) TouchChannel channel,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize) {
        
        log.info("查询触达记录: userId={}, strategyId={}, channel={}, page={}/{}", 
                userId, strategyId, channel, pageNum, pageSize);
        
        try {
            com.xinfei.touch.application.dto.PageResult<com.xinfei.touch.application.dto.TouchRecordDto> appResult =
                    touchApplicationService.getTouchRecords(userId, strategyId, channel, pageNum, pageSize);
            PageResult<TouchRecordDto> records = convertToApiPageResult(appResult);
            return ApiResponse.success(records);
        } catch (Exception e) {
            log.error("查询触达记录失败", e);
            return ApiResponse.error("QUERY_FAILED", "查询记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("OK");
    }

    /**
     * 转换API请求到应用层请求
     */
    private com.xinfei.touch.application.dto.TouchRequest convertToAppRequest(TouchRequest apiRequest) {
        com.xinfei.touch.application.dto.TouchRequest appRequest = new com.xinfei.touch.application.dto.TouchRequest();
        appRequest.setRequestId(apiRequest.getRequestId());
        appRequest.setTouchType(apiRequest.getTouchType());
        appRequest.setChannel(apiRequest.getChannel());
        appRequest.setStrategyId(apiRequest.getStrategyId());
        appRequest.setUserId(apiRequest.getUserId());
        appRequest.setBizEventType(apiRequest.getBizEventType());
        appRequest.setTemplateParams(apiRequest.getTemplateParams());
        appRequest.setTimestamp(apiRequest.getTimestamp());
        appRequest.setExtParams(apiRequest.getExtParams());

        if (apiRequest.getTouchConfig() != null) {
            com.xinfei.touch.application.dto.TouchConfigDto appConfig = new com.xinfei.touch.application.dto.TouchConfigDto();
            appConfig.setTemplateId(apiRequest.getTouchConfig().getTemplateId());
            appConfig.setTemplateContent(apiRequest.getTouchConfig().getTemplateContent());
            appConfig.setPriority(apiRequest.getTouchConfig().getPriority());
            appConfig.setTimeout(apiRequest.getTouchConfig().getTimeout());
            appConfig.setRetryTimes(apiRequest.getTouchConfig().getRetryTimes());
            appConfig.setAsync(apiRequest.getTouchConfig().getAsync());
            appConfig.setCallbackUrl(apiRequest.getTouchConfig().getCallbackUrl());
            appConfig.setExtConfig(apiRequest.getTouchConfig().getExtConfig());
            appRequest.setTouchConfig(appConfig);
        }

        return appRequest;
    }

    /**
     * 转换应用层响应到API响应
     */
    private TouchResponse convertToApiResponse(com.xinfei.touch.application.dto.TouchResponse appResponse) {
        TouchResponse apiResponse = new TouchResponse();
        apiResponse.setRequestId(appResponse.getRequestId());
        apiResponse.setStatus(appResponse.getStatus());
        apiResponse.setBatchNo(appResponse.getBatchNo());
        apiResponse.setErrorCode(appResponse.getErrorCode());
        apiResponse.setErrorMessage(appResponse.getErrorMessage());
        apiResponse.setTimestamp(appResponse.getTimestamp());
        apiResponse.setData(appResponse.getData());
        return apiResponse;
    }

    /**
     * 转换应用层分页结果到API分页结果
     */
    private PageResult<TouchRecordDto> convertToApiPageResult(
            com.xinfei.touch.application.dto.PageResult<com.xinfei.touch.application.dto.TouchRecordDto> appResult) {

        List<TouchRecordDto> apiRecords = null;
        if (appResult.getList() != null) {
            apiRecords = appResult.getList().stream()
                    .map(this::convertToApiRecord)
                    .collect(Collectors.toList());
        }

        return PageResult.of(appResult.getPageNum(), appResult.getPageSize(), appResult.getTotal(), apiRecords);
    }

    /**
     * 转换应用层记录到API记录
     */
    private TouchRecordDto convertToApiRecord(com.xinfei.touch.application.dto.TouchRecordDto appRecord) {
        TouchRecordDto apiRecord = new TouchRecordDto();
        apiRecord.setId(appRecord.getId());
        apiRecord.setRequestId(appRecord.getRequestId());
        apiRecord.setBatchNo(appRecord.getBatchNo());
        apiRecord.setTouchType(appRecord.getTouchType());
        apiRecord.setChannel(appRecord.getChannel());
        apiRecord.setStrategyId(appRecord.getStrategyId());
        apiRecord.setUserId(appRecord.getUserId());
        apiRecord.setBizEventType(appRecord.getBizEventType());
        apiRecord.setTemplateParams(appRecord.getTemplateParams());
        apiRecord.setStatus(appRecord.getStatus());
        apiRecord.setErrorCode(appRecord.getErrorCode());
        apiRecord.setErrorMessage(appRecord.getErrorMessage());
        apiRecord.setSendTime(appRecord.getSendTime());
        apiRecord.setCallbackTime(appRecord.getCallbackTime());
        apiRecord.setCreatedTime(appRecord.getCreatedTime());
        apiRecord.setUpdatedTime(appRecord.getUpdatedTime());
        return apiRecord;
    }
}
