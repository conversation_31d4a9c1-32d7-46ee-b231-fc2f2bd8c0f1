package com.xinfei.touch.api.controller;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchStatus;
import com.xinfei.touch.domain.model.TouchType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 触达记录DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TouchRecordDto {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 请求唯一标识
     */
    private String requestId;
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 触达类型
     */
    private TouchType touchType;
    
    /**
     * 触达渠道
     */
    private TouchChannel channel;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 业务事件类型
     */
    private String bizEventType;
    
    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;
    
    /**
     * 状态
     */
    private TouchStatus status;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 回执时间
     */
    private LocalDateTime callbackTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}
