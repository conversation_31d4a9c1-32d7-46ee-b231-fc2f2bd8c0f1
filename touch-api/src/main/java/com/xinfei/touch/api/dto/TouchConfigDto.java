package com.xinfei.touch.api.dto;

import lombok.Data;

import java.util.Map;

/**
 * 触达配置DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TouchConfigDto {
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 模板内容
     */
    private String templateContent;
    
    /**
     * 优先级：数字越小优先级越高
     */
    private Integer priority;
    
    /**
     * 超时时间（毫秒）
     */
    private Integer timeout;
    
    /**
     * 重试次数
     */
    private Integer retryTimes;
    
    /**
     * 是否异步处理
     */
    private Boolean async;
    
    /**
     * 回调地址
     */
    private String callbackUrl;
    
    /**
     * 扩展配置
     */
    private Map<String, Object> extConfig;
}
