package com.xinfei.touch.domain.model;

import lombok.Getter;

/**
 * 频控类型枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum FlowControlType {
    
    /**
     * 事件级流控
     */
    EVENT(1, "事件级流控", "对特定业务事件进行流控"),
    
    /**
     * 触达级流控
     */
    TOUCH(2, "触达级流控", "对触达请求进行流控"),
    
    /**
     * 分布式流控
     */
    DISTRIBUTED(3, "分布式流控", "分布式环境下的流控"),
    
    /**
     * 批量流控
     */
    BATCH(4, "批量流控", "对批量触达进行流控");
    
    /**
     * 类型编码
     */
    private final Integer code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;
    
    FlowControlType(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据编码获取类型
     */
    public static FlowControlType getByCode(Integer code) {
        for (FlowControlType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的频控类型编码: " + code);
    }
}
