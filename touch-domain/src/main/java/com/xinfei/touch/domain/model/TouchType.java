package com.xinfei.touch.domain.model;

import lombok.Getter;

/**
 * 触达类型枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum TouchType {
    
    /**
     * 实时普通触达
     */
    REALTIME_NORMAL(1, "实时普通触达", "T0普通触达"),
    
    /**
     * 实时引擎触达
     */
    REALTIME_ENGINE(2, "实时引擎触达", "T0引擎触达"),
    
    /**
     * 离线普通触达
     */
    OFFLINE_NORMAL(3, "离线普通触达", "离线普通触达"),
    
    /**
     * 离线引擎触达
     */
    OFFLINE_ENGINE(4, "离线引擎触达", "离线引擎触达");
    
    /**
     * 类型编码
     */
    private final Integer code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;
    
    TouchType(Integer code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 根据编码获取类型
     */
    public static TouchType getByCode(Integer code) {
        for (TouchType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的触达类型编码: " + code);
    }

    /**
     * 根据编码获取类型（兼容方法）
     */
    public static TouchType fromCode(Integer code) {
        return getByCode(code);
    }
    
    /**
     * 是否为实时触达
     */
    public boolean isRealtime() {
        return this == REALTIME_NORMAL || this == REALTIME_ENGINE;
    }
    
    /**
     * 是否为离线触达
     */
    public boolean isOffline() {
        return this == OFFLINE_NORMAL || this == OFFLINE_ENGINE;
    }
    
    /**
     * 是否为引擎触达
     */
    public boolean isEngine() {
        return this == REALTIME_ENGINE || this == OFFLINE_ENGINE;
    }
    
    /**
     * 是否为普通触达
     */
    public boolean isNormal() {
        return this == REALTIME_NORMAL || this == OFFLINE_NORMAL;
    }
}
