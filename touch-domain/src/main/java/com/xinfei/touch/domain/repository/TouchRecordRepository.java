package com.xinfei.touch.domain.repository;

import com.xinfei.touch.domain.model.TouchRecord;
import com.xinfei.touch.domain.model.TouchStatus;

import java.time.LocalDateTime;

/**
 * 触达记录仓储接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TouchRecordRepository {
    
    /**
     * 保存触达记录
     * 
     * @param touchRecord 触达记录
     * @return 保存结果
     */
    boolean save(TouchRecord touchRecord);
    
    /**
     * 根据请求ID查询触达记录
     * 
     * @param requestId 请求ID
     * @return 触达记录
     */
    TouchRecord findByRequestId(String requestId);
    
    /**
     * 根据批次号查询触达记录
     * 
     * @param batchNo 批次号
     * @return 触达记录
     */
    TouchRecord findByBatchNo(String batchNo);
    
    /**
     * 根据批次号更新状态
     * 
     * @param batchNo 批次号
     * @param status 状态
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @param callbackTime 回执时间
     * @return 更新记录数
     */
    int updateStatusByBatchNo(String batchNo, TouchStatus status, String errorCode, 
                             String errorMessage, LocalDateTime callbackTime);
}
