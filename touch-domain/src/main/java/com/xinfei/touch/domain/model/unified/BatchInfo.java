package com.xinfei.touch.domain.model.unified;

import lombok.Data;

/**
 * 批量处理信息模型
 * 用于批量触达场景的处理信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class BatchInfo {
    
    /**
     * 批次号
     * 对应现有的batchNo字段
     */
    private String batchNo;
    
    /**
     * 批次大小（每批处理的用户数量）
     */
    private Integer batchSize;
    
    /**
     * 总用户数
     */
    private Integer totalCount;
    
    /**
     * 当前批次序号（从1开始）
     */
    private Integer currentBatch;
    
    /**
     * 总批次数
     */
    private Integer totalBatch;
    
    /**
     * 明细表序号（用于分表存储）
     * 对应现有的detailTableNo字段，格式：yyyyMM
     */
    private String detailTableNo;
    
    /**
     * 批次开始时间
     */
    private Long batchStartTime;
    
    /**
     * 批次结束时间
     */
    private Long batchEndTime;
    
    /**
     * 批次状态
     */
    private BatchStatus status;
    
    /**
     * 批次处理进度（0-100）
     */
    private Integer progress;
    
    /**
     * 批次状态枚举
     */
    public enum BatchStatus {
        PENDING("PENDING", "待处理"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "处理失败"),
        CANCELLED("CANCELLED", "已取消");
        
        private final String code;
        private final String name;
        
        BatchStatus(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
    }
    
    /**
     * 创建批量信息
     */
    public static BatchInfo create(String batchNo, Integer totalCount, Integer batchSize) {
        BatchInfo batchInfo = new BatchInfo();
        batchInfo.setBatchNo(batchNo);
        batchInfo.setTotalCount(totalCount);
        batchInfo.setBatchSize(batchSize);
        batchInfo.setCurrentBatch(1);
        batchInfo.setTotalBatch(calculateTotalBatch(totalCount, batchSize));
        batchInfo.setStatus(BatchStatus.PENDING);
        batchInfo.setProgress(0);
        batchInfo.setBatchStartTime(System.currentTimeMillis());
        
        // 设置明细表序号为当前年月
        java.time.LocalDate now = java.time.LocalDate.now();
        batchInfo.setDetailTableNo(now.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMM")));
        
        return batchInfo;
    }
    
    /**
     * 计算总批次数
     */
    private static Integer calculateTotalBatch(Integer totalCount, Integer batchSize) {
        if (totalCount == null || totalCount <= 0 || batchSize == null || batchSize <= 0) {
            return 1;
        }
        return (int) Math.ceil((double) totalCount / batchSize);
    }
    
    /**
     * 开始处理
     */
    public void startProcessing() {
        this.status = BatchStatus.PROCESSING;
        this.batchStartTime = System.currentTimeMillis();
    }
    
    /**
     * 完成处理
     */
    public void completeProcessing() {
        this.status = BatchStatus.COMPLETED;
        this.batchEndTime = System.currentTimeMillis();
        this.progress = 100;
    }
    
    /**
     * 处理失败
     */
    public void failProcessing() {
        this.status = BatchStatus.FAILED;
        this.batchEndTime = System.currentTimeMillis();
    }
    
    /**
     * 取消处理
     */
    public void cancelProcessing() {
        this.status = BatchStatus.CANCELLED;
        this.batchEndTime = System.currentTimeMillis();
    }
    
    /**
     * 更新进度
     */
    public void updateProgress(Integer processedCount) {
        if (totalCount != null && totalCount > 0 && processedCount != null) {
            this.progress = Math.min(100, (processedCount * 100) / totalCount);
        }
    }
    
    /**
     * 移动到下一批次
     */
    public boolean moveToNextBatch() {
        if (hasNextBatch()) {
            this.currentBatch++;
            return true;
        }
        return false;
    }
    
    /**
     * 判断是否有下一批次
     */
    public boolean hasNextBatch() {
        return currentBatch != null && totalBatch != null && currentBatch < totalBatch;
    }
    
    /**
     * 判断是否为最后一批次
     */
    public boolean isLastBatch() {
        return currentBatch != null && totalBatch != null && currentBatch.equals(totalBatch);
    }
    
    /**
     * 获取当前批次的开始索引（从0开始）
     */
    public Integer getCurrentBatchStartIndex() {
        if (currentBatch == null || batchSize == null) {
            return 0;
        }
        return (currentBatch - 1) * batchSize;
    }
    
    /**
     * 获取当前批次的结束索引（不包含）
     */
    public Integer getCurrentBatchEndIndex() {
        if (currentBatch == null || batchSize == null || totalCount == null) {
            return 0;
        }
        int endIndex = currentBatch * batchSize;
        return Math.min(endIndex, totalCount);
    }
    
    /**
     * 获取当前批次的实际大小
     */
    public Integer getCurrentBatchActualSize() {
        return getCurrentBatchEndIndex() - getCurrentBatchStartIndex();
    }
    
    /**
     * 获取处理耗时（毫秒）
     */
    public Long getProcessingDuration() {
        if (batchStartTime == null) {
            return null;
        }
        Long endTime = batchEndTime != null ? batchEndTime : System.currentTimeMillis();
        return endTime - batchStartTime;
    }
    
    /**
     * 判断是否正在处理
     */
    public boolean isProcessing() {
        return BatchStatus.PROCESSING.equals(status);
    }
    
    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return BatchStatus.COMPLETED.equals(status);
    }
    
    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return BatchStatus.FAILED.equals(status);
    }
    
    /**
     * 验证批量信息
     */
    public void validate() {
        if (batchNo == null || batchNo.trim().isEmpty()) {
            throw new IllegalArgumentException("batchNo不能为空");
        }
        if (totalCount == null || totalCount <= 0) {
            throw new IllegalArgumentException("totalCount必须大于0");
        }
        if (batchSize == null || batchSize <= 0) {
            throw new IllegalArgumentException("batchSize必须大于0");
        }
        if (currentBatch == null || currentBatch <= 0) {
            throw new IllegalArgumentException("currentBatch必须大于0");
        }
        if (totalBatch == null || totalBatch <= 0) {
            throw new IllegalArgumentException("totalBatch必须大于0");
        }
        if (currentBatch > totalBatch) {
            throw new IllegalArgumentException("currentBatch不能大于totalBatch");
        }
    }
}
