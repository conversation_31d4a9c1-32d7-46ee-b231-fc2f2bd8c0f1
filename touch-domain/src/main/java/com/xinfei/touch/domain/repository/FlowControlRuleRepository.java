package com.xinfei.touch.domain.repository;

import com.xinfei.touch.domain.model.FlowControlRule;
import com.xinfei.touch.domain.model.TouchChannel;

import java.util.List;

/**
 * 频控规则仓储接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FlowControlRuleRepository {
    
    /**
     * 根据ID查询规则
     * 
     * @param ruleId 规则ID
     * @return 频控规则
     */
    FlowControlRule findById(Long ruleId);
    
    /**
     * 查询事件级流控规则
     * 
     * @param eventType 事件类型
     * @return 规则列表
     */
    List<FlowControlRule> findEventFlowControlRules(String eventType);
    
    /**
     * 查询触达级流控规则
     * 
     * @param channel 触达渠道
     * @param strategyId 策略ID
     * @param bizEventType 业务事件类型
     * @return 规则列表
     */
    List<FlowControlRule> findTouchFlowControlRules(TouchChannel channel, Long strategyId, String bizEventType);
    
    /**
     * 查询分布式流控规则
     * 
     * @param channel 触达渠道
     * @param strategyId 策略ID
     * @return 规则列表
     */
    List<FlowControlRule> findDistributedFlowControlRules(TouchChannel channel, Long strategyId);
    
    /**
     * 查询批量流控规则
     * 
     * @param channel 触达渠道
     * @param strategyId 策略ID
     * @return 规则列表
     */
    List<FlowControlRule> findBatchFlowControlRules(TouchChannel channel, Long strategyId);
    
    /**
     * 查询所有启用的规则
     * 
     * @return 规则列表
     */
    List<FlowControlRule> findAllEnabledRules();
    
    /**
     * 保存规则
     * 
     * @param rule 频控规则
     * @return 保存后的规则
     */
    FlowControlRule save(FlowControlRule rule);
    
    /**
     * 删除规则
     * 
     * @param ruleId 规则ID
     */
    void deleteById(Long ruleId);
    
    /**
     * 批量保存规则
     * 
     * @param rules 规则列表
     * @return 保存后的规则列表
     */
    List<FlowControlRule> saveAll(List<FlowControlRule> rules);
}
