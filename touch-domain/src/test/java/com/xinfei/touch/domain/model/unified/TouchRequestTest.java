package com.xinfei.touch.domain.model.unified;

import com.xinfei.touch.domain.model.TouchChannel;
import com.xinfei.touch.domain.model.TouchType;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TouchRequest测试类
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
class TouchRequestTest {
    
    @Test
    void testCreateT0NormalTouchRequest() {
        // 创建T0普通触达请求
        TouchRequest request = new TouchRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_NORMAL);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        request.setStrategyId(1L);
        request.setChannel(TouchChannel.SMS);
        request.setBizEventType("Login");
        
        // 设置用户信息
        TouchUserInfo userInfo = new TouchUserInfo();
        userInfo.setUserId(12345L);
        userInfo.setMobile("***********");
        userInfo.setApp("test-app");
        request.setUserInfo(userInfo);
        
        // 设置触达配置
        TouchConfig touchConfig = TouchConfig.createDefault();
        request.setTouchConfig(touchConfig);
        
        // 设置流控配置
        FlowControlConfig flowControlConfig = FlowControlConfig.createT0Config();
        request.setFlowControlConfig(flowControlConfig);
        
        // 验证请求
        assertDoesNotThrow(() -> request.validate());
        
        // 验证判断方法
        assertTrue(request.isT0Touch());
        assertFalse(request.isOfflineTouch());
        assertTrue(request.isNormalTouch());
        assertFalse(request.isEngineTouch());
        assertTrue(request.isSingleMode());
        assertFalse(request.isBatchMode());
        assertEquals(1, request.getUserCount());
    }
    
    @Test
    void testCreateT0EngineTouchRequest() {
        // 创建T0引擎触达请求
        TouchRequest request = new TouchRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.REALTIME_ENGINE);
        request.setTouchMode(TouchMode.SINGLE);
        request.setTimestamp(System.currentTimeMillis());
        request.setStrategyId(2L);
        request.setChannel(TouchChannel.PUSH);
        request.setBizEventType("Purchase");
        request.setEngineGroupName("test-engine-group");
        
        // 设置用户信息
        TouchUserInfo userInfo = new TouchUserInfo();
        userInfo.setUserId(67890L);
        userInfo.setMobile("13900000000");
        userInfo.setApp("test-app");
        request.setUserInfo(userInfo);
        
        // 设置引擎详细信息
        Map<String, Object> engineDetail = new HashMap<>();
        engineDetail.put("algorithm", "collaborative-filtering");
        engineDetail.put("confidence", 0.85);
        request.setEngineDetail(engineDetail);
        
        // 验证请求
        assertDoesNotThrow(() -> request.validate());
        
        // 验证判断方法
        assertTrue(request.isT0Touch());
        assertFalse(request.isOfflineTouch());
        assertFalse(request.isNormalTouch());
        assertTrue(request.isEngineTouch());
    }
    
    @Test
    void testCreateOfflineBatchTouchRequest() {
        // 创建离线批量触达请求
        TouchRequest request = new TouchRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setTouchType(TouchType.OFFLINE_NORMAL);
        request.setTouchMode(TouchMode.BATCH);
        request.setTimestamp(System.currentTimeMillis());
        request.setStrategyId(3L);
        request.setChannel(TouchChannel.VOICE);
        
        // 设置用户列表
        TouchUserInfo user1 = new TouchUserInfo();
        user1.setUserId(11111L);
        user1.setMobile("13700000001");
        user1.setApp("test-app");
        
        TouchUserInfo user2 = new TouchUserInfo();
        user2.setUserId(22222L);
        user2.setMobile("13700000002");
        user2.setApp("test-app");
        
        request.setUserList(Arrays.asList(user1, user2));
        
        // 设置批量信息
        BatchInfo batchInfo = BatchInfo.create("BATCH_TEST_001", 2, 2);
        request.setBatchInfo(batchInfo);
        
        // 验证请求
        assertDoesNotThrow(() -> request.validate());
        
        // 验证判断方法
        assertFalse(request.isT0Touch());
        assertTrue(request.isOfflineTouch());
        assertTrue(request.isNormalTouch());
        assertFalse(request.isEngineTouch());
        assertFalse(request.isSingleMode());
        assertTrue(request.isBatchMode());
        assertEquals(2, request.getUserCount());
    }
    
    @Test
    void testValidationFailures() {
        TouchRequest request = new TouchRequest();
        
        // 测试requestId为空的情况
        assertThrows(IllegalArgumentException.class, () -> request.validate());
        
        request.setRequestId("test-request-id");
        // 测试touchType为空的情况
        assertThrows(IllegalArgumentException.class, () -> request.validate());
        
        request.setTouchType(TouchType.REALTIME_NORMAL);
        // 测试touchMode为空的情况
        assertThrows(IllegalArgumentException.class, () -> request.validate());
        
        request.setTouchMode(TouchMode.SINGLE);
        // 测试strategyId为空的情况
        assertThrows(IllegalArgumentException.class, () -> request.validate());
        
        request.setStrategyId(1L);
        // 测试channel为空的情况
        assertThrows(IllegalArgumentException.class, () -> request.validate());
        
        request.setChannel(TouchChannel.SMS);
        // 测试单用户模式下userInfo为空的情况
        assertThrows(IllegalArgumentException.class, () -> request.validate());
        
        // 设置用户信息
        TouchUserInfo userInfo = new TouchUserInfo();
        userInfo.setUserId(12345L);
        userInfo.setMobile("***********");
        userInfo.setApp("test-app");
        request.setUserInfo(userInfo);
        
        // 测试T0触达没有bizEventType的情况
        assertThrows(IllegalArgumentException.class, () -> request.validate());
        
        request.setBizEventType("TestEvent");
        // 现在应该验证通过
        assertDoesNotThrow(() -> request.validate());
    }
    
    @Test
    void testTouchUserInfoValidation() {
        TouchUserInfo userInfo = new TouchUserInfo();
        
        // 测试userId为空的情况
        assertThrows(IllegalArgumentException.class, () -> userInfo.validate());
        
        userInfo.setUserId(12345L);
        // 测试mobile为空的情况
        assertThrows(IllegalArgumentException.class, () -> userInfo.validate());
        
        userInfo.setMobile("***********");
        // 测试app为空的情况
        assertThrows(IllegalArgumentException.class, () -> userInfo.validate());
        
        userInfo.setApp("test-app");
        // 现在应该验证通过
        assertDoesNotThrow(() -> userInfo.validate());
        
        // 测试工具方法
        userInfo.setAbNum("A001");
        assertTrue(userInfo.hasAbNum());
        
        userInfo.setCrowdId(100L);
        assertTrue(userInfo.belongsToCrowd(100L));
        assertFalse(userInfo.belongsToCrowd(200L));
        
        userInfo.setCreditUserId(99999L);
        assertEquals(99999L, userInfo.getEffectiveCreditUserId());
        
        // 测试标签和扩展信息
        userInfo.setUserLabel("vip", true);
        userInfo.setUserExt("source", "mobile");
        
        assertEquals(true, userInfo.getUserLabel("vip"));
        assertEquals("mobile", userInfo.getUserExt("source"));
    }
    
    @Test
    void testTouchConfigMethods() {
        TouchConfig config = TouchConfig.createDefault();
        
        assertTrue(config.isMarketing());
        assertFalse(config.isNotify());
        
        TouchConfig notifyConfig = TouchConfig.createNotifyConfig();
        assertFalse(notifyConfig.isMarketing());
        assertTrue(notifyConfig.isNotify());
        
        // 测试配置合并
        TouchConfig customConfig = new TouchConfig();
        customConfig.setTimeout(60000);
        customConfig.setChannelConfig("priority", "high");
        
        config.merge(customConfig);
        assertEquals(60000, config.getTimeout());
        assertEquals("high", config.getChannelConfig("priority"));
    }
    
    @Test
    void testBatchInfoLifecycle() {
        BatchInfo batchInfo = BatchInfo.create("TEST_BATCH", 100, 20);
        
        assertEquals("TEST_BATCH", batchInfo.getBatchNo());
        assertEquals(100, batchInfo.getTotalCount());
        assertEquals(20, batchInfo.getBatchSize());
        assertEquals(5, batchInfo.getTotalBatch()); // 100/20 = 5
        assertEquals(1, batchInfo.getCurrentBatch());
        
        // 测试批次状态变化
        batchInfo.startProcessing();
        assertTrue(batchInfo.isProcessing());
        
        batchInfo.updateProgress(50);
        assertEquals(50, batchInfo.getProgress());
        
        batchInfo.completeProcessing();
        assertTrue(batchInfo.isCompleted());
        assertEquals(100, batchInfo.getProgress());
        
        // 测试批次移动
        BatchInfo movingBatch = BatchInfo.create("MOVING_BATCH", 100, 20);
        assertTrue(movingBatch.hasNextBatch());
        assertTrue(movingBatch.moveToNextBatch());
        assertEquals(2, movingBatch.getCurrentBatch());
        
        // 移动到最后一批
        while (movingBatch.hasNextBatch()) {
            movingBatch.moveToNextBatch();
        }
        assertTrue(movingBatch.isLastBatch());
        assertFalse(movingBatch.moveToNextBatch());
    }
}
