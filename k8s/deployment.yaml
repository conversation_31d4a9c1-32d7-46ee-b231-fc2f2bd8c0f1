apiVersion: apps/v1
kind: Deployment
metadata:
  name: touch-service
  namespace: touch
  labels:
    app: touch-service
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: touch-service
  template:
    metadata:
      labels:
        app: touch-service
        version: v1.0.0
    spec:
      containers:
      - name: touch-service
        image: touch-service:1.0.0
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: APOLLO_META
          value: "http://apollo-config-service:8080"
        - name: JAVA_OPTS
          value: "-Xms512m -Xmx1024m -XX:+UseG1GC"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /touch-service/actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /touch-service/actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}
      restartPolicy: Always

---
apiVersion: v1
kind: Service
metadata:
  name: touch-service
  namespace: touch
  labels:
    app: touch-service
spec:
  selector:
    app: touch-service
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: touch-service-config
  namespace: touch
data:
  application-prod.yml: |
    spring:
      datasource:
        druid:
          url: ***********************************************************************************************************************************************************
          username: ${DB_USERNAME:root}
          password: ${DB_PASSWORD:password}
      redis:
        host: redis-service
        port: 6379
        password: ${REDIS_PASSWORD:}
    
    rocketmq:
      name-server: rocketmq-nameserver:9876
    
    apollo:
      meta: http://apollo-config-service:8080
    
    logging:
      level:
        com.xinfei.touch: INFO
        root: WARN
