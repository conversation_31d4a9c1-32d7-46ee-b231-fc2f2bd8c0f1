package com.xftech.cdp.api.dto.resp.auth;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 14:49:33
 */
@Setter
@Getter
public class LoginUser {


    /**
     * 2,
     */
    @JsonProperty("id")
    @JSONField(name = "id")
    private Long id;

    /**
     * "储德盛",
     */
    @JsonProperty("name")
    @JSONField(name = "name")
    private String name;

    /**
     * "17302185530",
     */
    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    /**
     * "<EMAIL>",
     */
    @JsonProperty("email")
    @JSONField(name = "email")
    private String email;

    /**
     * 0,
     */
    @JsonProperty("is_deleted")
    @JSONField(name = "is_deleted")
    private String isDeleted;

    /**
     * "2022-03-04 11:15:58",
     */
    @JsonProperty("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "created_time")
    private LocalDateTime createdTime;

    /**
     * "2022-03-07 14:36:57",
     */
    @JsonProperty("updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "updated_time")
    private LocalDateTime updatedTime;

    /**
     * "",
     */
    @JsonProperty("open_id")
    @JSONField(name = "open_id")
    private String openId;

    /**
     * "feishu",
     */
    @JsonProperty("login_type")
    @JSONField(name = "login_type")
    private String loginType;

    /**
     * 1,
     */
    @JsonProperty("is_mobile_login")
    @JSONField(name = "is_mobile_login")
    private Integer isMobileLogin;

    /**
     * "8",
     */
    @JsonProperty("group")
    @JSONField(name = "group")
    private String group;

    /**
     * "hzy",
     */
    @JsonProperty("third_type")
    @JSONField(name = "third_type")
    private String thirdType;

    /**
     * "1101",
     */
    @JsonProperty("third_id")
    @JSONField(name = "third_id")
    private String thirdId;

    /**
     * 0
     */
    @JsonProperty("parent_id")
    @JSONField(name = "parent_id")
    private Long parentId;


    /**
     * 是否超级管理员
     */
    private Boolean ifAdmin = false;

}
