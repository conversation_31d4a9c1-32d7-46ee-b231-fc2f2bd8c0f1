/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ CrowdRefreshInfo, v 0.1 2024/7/24 17:17 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrowdRefreshInfoReq extends PageRequestDto {

    @ApiModelProperty(value = "人群包id", required = true)
    private Long id;
}