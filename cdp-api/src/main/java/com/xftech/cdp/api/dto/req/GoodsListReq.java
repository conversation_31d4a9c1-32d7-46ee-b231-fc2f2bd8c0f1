/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version $ GoodsListReq, v 0.1 2024/5/6 16:23 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsListReq extends PageRequestDto {

    @ApiModelProperty(value = "商品id")
    private Long goodsId;

}