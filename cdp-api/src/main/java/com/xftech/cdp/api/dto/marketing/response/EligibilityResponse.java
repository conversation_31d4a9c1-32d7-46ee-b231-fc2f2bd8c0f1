package com.xftech.cdp.api.dto.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/11
 * @description Eligibility
 */
@NoArgsConstructor
@Data
@ApiModel("查询用户活动资格 响应体")
public class EligibilityResponse {

    @ApiModelProperty(value = "用户活动资格: 1=未参加活动;2=已参与&待使用优惠券;3=已参与&已使用优惠券;4=已参与&优惠券已失效;5=用户不可参加活动;6=活动已结束")
    private Integer status;

    @ApiModelProperty(value = "优惠券ID, status=2/3/4时返回")
    private String couponId;

    @ApiModelProperty(value = "优惠券名称, status=2/3/4时返回")
    private String couponName;

    @ApiModelProperty(value = "优惠券活动名称")
    private String couponActivityName;

    @ApiModelProperty(value = "优惠券有效期倒计时-时间戳, status=2时返回")
    private Long expirationCountdown;

    @ApiModelProperty(value = "优惠券膨胀进度, status=2时返回")
    private Integer expansionStatus;

    public EligibilityResponse(Integer status) {
        this.status = status;
    }

}
