package com.xftech.cdp.api.dto.resp.flowctrl;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */

@Data
public class FlowCtrlListResp {

    @ApiModelProperty("规则编号")
    public Long id;

    @ApiModelProperty(value = "规则名称")
    private String name;

    @ApiModelProperty(value = "规则类型 1-策略 2-渠道 3-多策略共享")
    private Integer type;

    @ApiModelProperty(value = "渠道/策略详情,多个用\",\"分隔")
    private String effectiveContent;

    @ApiModelProperty("规则状态 0-初始化 1-生效中 2-已关闭")
    private Integer status;

    @ApiModelProperty("更新人")
    private String updatedOp;

    @ApiModelProperty("创建人")
    private String createdOp;

    @ApiModelProperty("业务线")
    private String bizType;

    @ApiModelProperty("生效渠道")
    private String effectiveChannel;

    @ApiModelProperty("策略类型 1 离线策略 2事件策略")
    private Integer strategyType;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updatedTime;

}
