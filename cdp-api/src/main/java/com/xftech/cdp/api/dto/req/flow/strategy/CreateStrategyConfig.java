/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.flow.strategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.api.dto.req.DispatchConfig;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ StragegyConfig, v 0.1 2023/12/13 15:48 yye.xu Exp $
 */

@Data
public class CreateStrategyConfig {

    // 节点信息
    private Node node;
    private Node nextNode;

    @ApiModelProperty(value = "状态0-初始 4-暂停", required = true)
    private Integer status;

    @ApiModelProperty(value = "策略名称", required = true)
    private String name;

    @ApiModelProperty(value = "发送频次")
    private StrategyCreateReq.SendFrequency sendFrequency;

    @ApiModelProperty(value = "策略描述")
    private String detailDescription;

    @ApiModelProperty(value = "首个策略有：人群包id,多个人群\";\"分隔", required = true)
    @NotBlank(message = "人群包id不能为空")
    private String crowdPackIds;

    @ApiModelProperty(value = "abTest 0-否 1-是", required = true)
    private Integer abTest;

    @ApiModelProperty(value = "分组方式，0：uid倒数两位 1：随机数")
    private Integer abType;

    @ApiModelProperty(value = "随机数场景key值")
    private String bizKey;

    @ApiModelProperty(value = "0-单次, 1-例行, 2-事件, 3-周期, 9-引擎", required = true)
    private Integer sendRuler;

    @ApiModelProperty(value = "人群分组")
    private List<StrategyCreateReq.StrategyGroup> strategyGroups;

    @ApiModelProperty(value = "营销条件配置")
    private List<InstantStrategyCreateReq.MarketCondition> marketCondition;

    @ApiModelProperty(value = "流控规则限制")
    private FlowCtrlLimit flowCtrlLimit;

    @ApiModelProperty(value = "排除项类型")
    private List<Integer> excludeType;

    // 派发数据配置
    @ApiModelProperty(value = "派发数据配置")
    private DispatchConfig dispatchConfig;


    @JsonIgnore
    @JSONField(serialize = false)
    public Pair<Boolean, String> isValid() {
        if (node == null || StringUtils.isEmpty(node.getNodeId())) {
            return Pair.of(false, "节点编号有问题");
        }
        if (StringUtils.isEmpty(name)){
            return Pair.of(false, "策略名称有问题");
        }
        return Pair.of(true, null);
    }
}