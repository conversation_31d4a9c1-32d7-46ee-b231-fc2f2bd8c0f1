package com.xftech.cdp.api;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlCreateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlDetailReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlEffectiveContentReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlListReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlOperateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlUpdateReq;
import com.xftech.cdp.api.dto.resp.flowctrl.EffectiveContentListResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlDetailResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlListResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 流量控制相关接口
 * @<NAME_EMAIL>
 */

@Api(tags = "流量控制配置")
@RequestMapping("/flowCtrl")
public interface FlowCtrlApi {
    @ApiOperation("创建流控规则")
    @PostMapping("/rule/create")
    Response<Boolean> create(@Validated @RequestBody FlowCtrlCreateReq flowCtrlCreateReq);

    @ApiOperation("更新流控规则")
    @PostMapping("/rule/update")
    Response<Boolean> update(@Validated @RequestBody FlowCtrlUpdateReq flowCtrlUpdateReq);

    @ApiOperation("流控规则列表")
    @PostMapping("/rule/list")
    Response<PageResultResponse<FlowCtrlListResp>> list(@Validated @RequestBody FlowCtrlListReq flowCtrlListReq);

    @ApiOperation("流控规则详情")
    @PostMapping("/rule/detail")
    Response<FlowCtrlDetailResp> detail(@Validated @RequestBody FlowCtrlDetailReq flowCtrlDetailReq);

    @ApiOperation("流控规则操作")
    @PostMapping("/rule/operate")
    Response<Boolean> operate(@Validated @RequestBody FlowCtrlOperateReq flowCtrlOperateReq);

    @ApiOperation("获取策略/渠道列表")
    @PostMapping("/get-effective-content")
    Response<List<EffectiveContentListResp>> getEffectiveContent(@Validated @RequestBody FlowCtrlEffectiveContentReq flowCtrlEffectiveContentReq);
}
