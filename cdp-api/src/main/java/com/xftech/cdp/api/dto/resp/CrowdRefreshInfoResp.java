/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ CrowdRefreshInfoResp, v 0.1 2024/7/24 17:19 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrowdRefreshInfoResp implements Serializable {
    private static final long serialVersionUID = -3285062731655257406L;

    @ApiModelProperty("人群包id")
    public Long id;

    @ApiModelProperty(value = "刷新时间")
    public String time;

    @ApiModelProperty(value = "刷新人数")
    public Integer num;

}