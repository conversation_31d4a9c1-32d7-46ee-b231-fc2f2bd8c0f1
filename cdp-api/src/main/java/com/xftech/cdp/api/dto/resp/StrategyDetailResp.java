package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class StrategyDetailResp extends BaseReq {

    @ApiModelProperty(value = "策略名称", required = true)
    private String name;

    @ApiModelProperty(value = "策略描述")
    private String detailDescription;

    @ApiModelProperty(value = "策略分组")
    private Integer strategyGroupType;

    @ApiModelProperty(value = "人群包id,多个人群\";\"分隔", required = true)
    private String crowdPackIds;

    @ApiModelProperty(value = "用户是否转换 0-否 1-是")
    private Integer userConvert;

    @ApiModelProperty(value = "abTest 0-否 1-是", required = true)
    private Integer abTest;

    @ApiModelProperty(value = "分组方式，0：uid倒数两位 1：随机数")
    private Integer abType;

    @ApiModelProperty(value = "随机数")
    private String randomItem;

    @ApiModelProperty(value = "发送规则 0-单次 1-例行", required = true)
    private Integer sendRuler;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略开始时间")
    private LocalDateTime validityBegin;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "策略结束时间")
    private LocalDateTime validityEnd;

    @ApiModelProperty(value = "发送频次")
    private SendFrequency sendFrequency;

    @ApiModelProperty(value = "人群分组")
    private List<StrategyGroup> strategyGroups;

    @ApiModelProperty(value = "限制天数")
    private Integer limitDays;

    @ApiModelProperty(value = "限制次数")
    private Integer limitTimes;

    @ApiModelProperty(value = "营销条件配置")
    List<InstantStrategyDetailResp.MarketCondition> marketCondition;
    @ApiModelProperty(value = "排除项类型")
    private List<Integer> excludeType;

    private String engineCode;

    private Integer type;

    // 画布id
    private String flowNo;
    private String dispatchConfig;
    @ApiModelProperty(value = "状态：-1-未发布 0-已发布 1-执行中 4-暂停 5-已结束")
    private Integer status;
    private List<Integer> marketChannels;

    // 触达类型: MKT-营销,  NOTIFY-通知
    @ApiModelProperty(value = "触达类型: MKT-营销,  NOTIFY-通知")
    private String dispatchType;
    @Data
    public static class StrategyGroup {

        @JsonFormat(shape = JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "组id")
        private String groupId;

        @ApiModelProperty(value = "组名")
        private String name;

        @ApiModelProperty(value = "是否执行")
        private Integer isExecutable;

        @ApiModelProperty(value = "分组配置")
        private GroupConfig groupConfig;

        @ApiModelProperty(value = "扩展信息")
        private String extInfo;

        @ApiModelProperty(value = "触达渠道")
        private List<StrategyMarketChannel> strategyMarketChannels;
    }

    @Data
    public static class StrategyMarketChannel {
        @ApiModelProperty(value = "渠道id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private String channelId;

        @ApiModelProperty(value = "营销渠道，0: 不营销，1:短信，2:电销，3:优惠券")
        private Integer marketChannel;

        @ApiModelProperty(value = "模板id")
        private String templateId;

        @ApiModelProperty(value = "模板关联app")
        private String app;

        @ApiModelProperty(value = "模板内容")
        private String template;

        @JsonFormat(pattern = TimeFormat.TIME_SEC)
        @ApiModelProperty(value = "发送时间")
        private LocalTime sendTime;
        
        @ApiModelProperty(value = "扩展字段存储目前存储电销策略ID")
        private String extInfo;

    }

    @Data
    public static class GroupConfig {

        private Integer digits;

        private Integer positionStart;

        private Integer positionEnd;

        private Object crowdLabelOption;

        // 是否选中，灰度选中
        @ApiModelProperty(value = "频率类型，0：非选中进入流量组，1：选中进入流量组")
        private Integer selected;
    }

    @Data
    public static class SendFrequency {

        @ApiModelProperty(value = "频率类型, 0:每天, 1:每周, 2:每月, 3-每日循环周期")
        private Integer type;

        @ApiModelProperty(value = "每周/每月第几天/周期")
        private List<Integer> value;

        @JsonFormat(pattern = TimeFormat.TIME_SEC)
        @ApiModelProperty(value = "发送时间")
        private LocalTime sendTime;
    }
}
