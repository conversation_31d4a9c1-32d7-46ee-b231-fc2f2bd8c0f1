package com.xftech.cdp.api.dto.resp.auth;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 15:56:01
 */
@Setter
@Getter
public class GetTokenResp {


    @JsonProperty("user")
    @JSONField(name = "user")
    private LoginUser user;

    @JsonProperty("api")
    @JSONField(name = "api")
    private Map<String, Object> api;

    @JsonProperty("menu")
    @JSONField(name = "menu")
    private List<Menu> menu;

    @JsonProperty("role_tag")
    @JSONField(name = "role_tag")
    private List<String> roleTag;

    @JsonProperty("role_id")
    @JSONField(name = "role_id")
    private List<String> roleId;

    @JsonProperty("role_name")
    @J<PERSON>NField(name = "role_name")
    private List<String> roleName;

    @JsonProperty("token")
    @JSONField(name = "token")
    private String token;

    @JsonProperty("api_all")
    @JSONField(name = "api_all")
    private List<String> apiAll;

}
