/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @version $ PolicyListResp, v 0.1 2024/1/12 15:46 ****.**** Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicyListResp {
    @ApiModelProperty(value = "策略ID")
    private Integer id;
    @ApiModelProperty(value = "电销策略名称")
    private String policyName;
    @ApiModelProperty(value = "策略类型(tag:标签策略,case:案件策略)")
    private String type;
    @ApiModelProperty(value = "策略状态(1:启用,0:禁用)")
    private Integer status;
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    @ApiModelProperty(value = "更新时间")
    private String updatedTime;
    @ApiModelProperty(value = "优先级")
    private Integer priority;
}