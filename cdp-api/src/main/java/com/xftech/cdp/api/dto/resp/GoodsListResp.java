/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ GoodsListResp, v 0.1 2024/5/6 16:27 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsListResp implements Serializable {

    @ApiModelProperty(value = "商品ID")
    private Long goodsId;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "品牌")
    private String brandName;

    @ApiModelProperty(value = "权益类型")
    private String goodsType;

    @ApiModelProperty(value = "官方价")
    private Long goodsPriceOfficial;

    @ApiModelProperty(value = "售价")
    private Long goodsPriceSell;

    @ApiModelProperty(value = "供应商")
    private String supplier;

}