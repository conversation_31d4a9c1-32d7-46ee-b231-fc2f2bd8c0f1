package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/14 18:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CouponListReq extends PageRequestDto {

    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 券类型 1:借款免息券 2:还款立减金，3:限时提额券，4.拉卡拉聚合支付 ,5:x天免息券
     */
    private List<Integer> couponType;
    /**
     * 券模版id
     */
    private Long couponId;
    /**
     * 券模版名称
     */
    private String couponName;

    /**
     * 类型： 1：金融券（默认）， 2:消费券
     */
    private Integer type;
}
