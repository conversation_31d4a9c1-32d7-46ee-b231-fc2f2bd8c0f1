/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ NameTypeResp, v 0.1 2023/10/13 11:38 wancheng.qu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NameTypeResp implements Serializable {
    @ApiModelProperty(value = "名单类型ID")
    private Integer nameTypeId;
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    @ApiModelProperty(value = "归属业务(first_loan:首贷,compound_loan:复贷)")
    private String businessType;




}