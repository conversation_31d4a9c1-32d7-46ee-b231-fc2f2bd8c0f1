/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @version $ UpdatePolicyPriorityReq, v 0.1 2024/7/24 15:45 benlin.wang Exp $
 */

@Setter
@Getter
public class UpdatePolicyPriorityReq extends BaseReq {
    @NotNull
    @ApiModelProperty(value = "策略id", required = true)
    private Long id;

    @NotNull
    @ApiModelProperty(value = "优先级", required = true)
    private Integer priority;



    public UpdatePolicyPriorityReq() {
        traceId = UUID.randomUUID().toString();
        ts = System.currentTimeMillis() / 1000;
        ua = "xyf-cdp";
    }

    private String traceId;
    private Long ts;
    private String ua;
}
