/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @version $ StrategyReportDailyDetailResp, v 0.1 2023/12/6 14:39 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StrategyReportDailyDetailResp implements Serializable {
    private static final long serialVersionUID = -3071500185018549893L;

    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "策略渠道执行情况")
    private String strategyInfo;
}