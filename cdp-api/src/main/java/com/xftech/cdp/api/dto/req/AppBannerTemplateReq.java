/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerTemplateReq, v 0.1 2024/4/18 19:27 benlin.wang Exp $
 */

@Data
public class AppBannerTemplateReq implements Serializable {
    private static final long serialVersionUID = -7144667479238744283L;

    @ApiModelProperty(value = "来源：麻雀cdp", required = true)
    @NotNull(message = "来源不能为空")
    private String utm_source;

    @ApiModelProperty(value = "业务类型：1运营-新客2运营-老客3运营-变现 100产品-新客101产品-老客102产品-变现103产品-通用")
    private Integer biz_type;

    @ApiModelProperty(value = "弹窗id")
    private Integer id;

    @ApiModelProperty(value = "状态：1生效中2已过期3未开启4编辑中")
    private Integer status;

    @ApiModelProperty(value = "生效app，多个app以逗号分隔")
    private String app;

    @ApiModelProperty(value = "弹窗名称")
    private String resource_name;

    @ApiModelProperty(value = "展示类型：101额度弹窗-底部，102优惠券弹窗，103额度弹窗-剧中，104好评引导弹窗，105提额专区弹窗-底部，106消息通知弹窗，107纯图片弹窗")
    private Integer bind_logic;

    @ApiModelProperty(value = "页码")
    private Integer page;

    @ApiModelProperty(value = "每页数量")
    private Integer page_size;

//    @ApiModelProperty(value = "业务归属")
//    private String business;
//
//    @ApiModelProperty(value = "弹窗id")
//    private String id;
//
//    @ApiModelProperty(value = "弹框状态")
//    private String status;
//
//    @ApiModelProperty(value = "展示app")
//    private String app;
//
//    @ApiModelProperty(value = "弹框名称")
//    private String name;
//
//    @ApiModelProperty(value = "样式类型")
//    private String style;
}