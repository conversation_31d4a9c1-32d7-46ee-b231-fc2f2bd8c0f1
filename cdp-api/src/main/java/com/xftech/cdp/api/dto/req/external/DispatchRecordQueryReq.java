package com.xftech.cdp.api.dto.req.external;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @<NAME_EMAIL>
 * @date 2023-07-21 13:58
 */
@Data
public class DispatchRecordQueryReq {
    /**
     * 用户ID
     */
    private Long appUserId;
    /**
     * 策略ID
     */
    private Long strategyId;
    /**
     * 天数
     */
    private Integer days;
    /**
     * 营销渠道，逗号分隔
     */
    private String marketChannel;

    public List<Integer> getMarketChannel() {
        if (StringUtils.isBlank(marketChannel)) {
            return Collections.emptyList();
        }
        return Stream.of(marketChannel.split(",")).map(Integer::valueOf).collect(Collectors.toList());
    }
}
