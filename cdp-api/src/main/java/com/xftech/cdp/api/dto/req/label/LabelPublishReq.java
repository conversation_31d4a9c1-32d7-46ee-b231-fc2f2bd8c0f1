/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.label;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ LabelPublishReq, v 0.1 2024/6/20 16:13 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelPublishReq implements Serializable {
    private static final long serialVersionUID = 1445441189270618709L;

    @ApiModelProperty(value = "是否新增配置")
    @NotNull
    private Integer ifAdd;

    @ApiModelProperty(value = "修改配置时的lId")
    private Long labelId;

    @ApiModelProperty(value = "标签code")
    private String labelCode;

    @ApiModelProperty(value = "标签名称")
    private String labelName;

    @ApiModelProperty(value = "一级分类code")
    private Integer firstLevel;

    @ApiModelProperty(value = "二级分类code")
    private Integer secondLevel;

    @ApiModelProperty(value = "交互类型code")
    private Integer labelExchangeCode;

    @ApiModelProperty(value = "枚举交互code")
    private Map<String, String> enumResult;

    @ApiModelProperty(value = "业务线")
    private List<String> businessTypes;
}