package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */

@Setter
@Getter
public class StrategyListResp extends BaseResp {

    @ApiModelProperty("策略编号")
    public Long id;

    @ApiModelProperty(value = "策略类型 0-默认其它，1-引擎规则")
    private Integer type = 0;

    @Size(max = 64, message = "引擎code不能超过64个字符")
    @ApiModelProperty(value = "引擎code")
    private String engineCode;

    @ApiModelProperty(value = "策略名称")
    private String name;

    @ApiModelProperty(value = "人群包id,多个人群\";\"分隔")
    private String crowdPackId;

    @ApiModelProperty(value = "当日执行人数")
    private String execCount;

    @ApiModelProperty(value = "触达渠道，1:短信，2:电销，3:优惠券,4:新电销")
    private List<String> marketChannel;

    @ApiModelProperty(value = "发送规则 0-单次 1-例行 2-事件")
    private Integer sendRuler;

    @ApiModelProperty("状态，0: 初始化、1: 执行中、2: 执行成功、3: 执行失败、4: 暂停中、5: 已结束")
    private Integer status;

    @ApiModelProperty("更新人")
    private String updateOp;


    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "策略分组")
    private Integer strategyGroupType;

    @ApiModelProperty(value = "策略触达成功率")
    private String marketSuccRate;

    @ApiModelProperty(value = "策略分组信息")
    private List<StrategyGroupInfo> groupInfoList;

    @Data
    public static class StrategyGroupInfo {

        @ApiModelProperty(value = "策略分组id")
        private String groupId;

        @ApiModelProperty(value = "策略分组名称")
        private String groupName;
    }
}
