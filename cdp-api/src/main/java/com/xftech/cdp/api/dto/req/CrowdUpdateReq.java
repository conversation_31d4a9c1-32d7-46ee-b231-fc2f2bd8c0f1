package com.xftech.cdp.api.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Setter
@Getter
public class CrowdUpdateReq extends BaseReq {

    @ApiModelProperty(value = "id", required = true)
    private Integer id;

    @Size(max = 20)
    @NotEmpty
    @ApiModelProperty(value = "人群包名称", required = true)
    private String crowdName;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "筛选方式,0:文件上传,1:标签圈选", required = true)
    private Integer filterMethod;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "刷新类型，0:手动刷新，1:例行刷新", required = true)
    private Integer refreshType;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "生效周期，开始时间，yyyy-MM-dd HH:mm:ss", required = true)
    private LocalDateTime validityBegin;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "生效周期，结束时间，yyyy-MM-dd HH:mm:ss", required = true)
    private LocalDateTime validityEnd;

    @JsonFormat(pattern = TimeFormat.TIME)
    @ApiModelProperty(value = "刷新时间，HH:mm:ss", required = true)
    private LocalTime refreshTime;

    @ApiModelProperty(value = "h5自定义字段")
    private String h5Option;

    /***** 标签配置 *****/

    @ApiModelProperty(value = "人群圈选列表")
    private List<CrowdCreateReq.CrowdLabelPrimary> crowdLabelPrimaries;

    @ApiModelProperty(value = "人群分组")
    private Integer groupType;
//
//    @Data
//    public static class CrowdLabelPrimary {
//
//        @Min( 0 )
//        @Max( 1 )
//        @ApiModelProperty(value = "人群圈选，0:圈选标签， 1: 排除标签", required = true)
//        private Integer labelGroupType;
//
//        @ApiModelProperty(value = "一级标签", required = true)
//        private Integer primaryLabel;
//
//        @ApiModelProperty(value = "与上一个一级标签的关系，0:或，1:且", required = true)
//        private Integer primaryLabelRelation;
//
//        @ApiModelProperty(value = "执行顺序号", required = true)
//        private Integer execIndex;
//
//        @ApiModelProperty(value = "标签选择")
//        private List<CrowdLabel> crowdLabels;
//    }
//
//    @Data
//    public static class CrowdLabel {
//        @ApiModelProperty(value = "标签id", required = true)
//        private Long labelId;
//
//        @ApiModelProperty(value = "标签值", required = true)
//        private String labelValue;
//
//        @ApiModelProperty(value = "可选配置类型, 0:固定单选,1固定多选:,2:输入项,3:时间范围,4:数值范围")
//        private Integer labelOptionType;
//
//        @ApiModelProperty(value = "执行顺序号", required = true)
//        private Integer execIndex;
//
//        @ApiModelProperty(value = "与上一个一级标签的关系，0:或，1:且", required = true)
//        private Integer labelRelation;
//    }

}
