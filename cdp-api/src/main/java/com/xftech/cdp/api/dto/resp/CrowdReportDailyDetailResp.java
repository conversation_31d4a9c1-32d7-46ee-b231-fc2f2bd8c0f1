/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @version $ CrowdReportDailyDetailResp, v 0.1 2023/12/6 13:56 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrowdReportDailyDetailResp implements Serializable {
    private static final long serialVersionUID = -4271699322139859519L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "统计日期")
    private Date date;

    @ApiModelProperty(value = "人群包id")
    private Long crowdId;

    @ApiModelProperty(value = "人群名称")
    private String crowdName;

    @ApiModelProperty(value = "执行开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime execStartTime;

    @ApiModelProperty(value = "执行结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime execEndTime;

    @ApiModelProperty(value = "执行耗时(min)")
    private Long betweenTime;

    @ApiModelProperty(value = "执行结果")
    private String execStatus;

    @ApiModelProperty(value = "备注")
    private String failReason;

    @ApiModelProperty(value = "人群包今日人数")
    private Integer crowdTodaySum;

    @ApiModelProperty(value = "人群包昨日人数")
    private Integer crowdYesterdaySum;

    @ApiModelProperty(value = "环比增长率")
    private String growthRate;

}