package com.xftech.cdp.api.dto.req.flowctrl;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @<NAME_EMAIL>
 */

@Data
public class FlowCtrlOperateReq{
    @NotNull(message = "规则编号不能为空")
    @ApiModelProperty(value = "规则编号", required = true)
    private Long ruleId;

    @NotNull(message = "操作类型不能为空")
    @ApiModelProperty(value = "操作类型 1-开启,2-关闭,3-删除")
    private Integer runType;
}
