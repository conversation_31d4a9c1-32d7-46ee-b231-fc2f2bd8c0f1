package com.xftech.cdp.api.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.PageRequestDto;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StrategyListReq extends PageRequestDto {

    @Size(max = 20)
    @ApiModelProperty(value = "策略名称")
    private String name;

    @ApiModelProperty(value = "策略id")
    private String strategyId;

    @ApiModelProperty(value = "更新人")
    private String updatedOp;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "触达渠道,数组对象:[1,2]")
    private List<Integer> marketChannel;

    @ApiModelProperty(value = "发送规则 0-单次,1-例行, 2-事件, 3-策略引擎 4-周期策略 5-离线引擎")
    private Integer sendRuler;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "更新开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "更新结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "业务线 new-cust-新客  old-cust-老客")
    @NotBlank(message = "业务线不能为空")
    private String businessType;

    @ApiModelProperty(value = "策略分组")
    private Integer strategyGroupType;

    @ApiModelProperty(value = "非请求参数, 请不要传参")
    private Integer type;
}
