/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.api.dto.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ ActivityCommonResponse, v 0.1 2025/1/4 17:07 xu.fan Exp $
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel("活动基础响应对象")
public class ActivityBaseResponse {

    @ApiModelProperty(value = "业务执行结果Code")
    private String bizCode;

    @ApiModelProperty(value = "业务执行结果message")
    private String bizMessage;
}
