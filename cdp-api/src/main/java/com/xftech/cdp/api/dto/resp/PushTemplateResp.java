/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ PushTemplateResp, v 0.1 2024/1/18 19:29 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PushTemplateResp implements Serializable {
    private static final long serialVersionUID = -6579121183015731468L;

    @ApiModelProperty(value = "模板id")
    private String templateId;

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "模板类型")
    private String tplType;

    @ApiModelProperty(value = "模板业务类型")
    private String bizType;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(value = "创建人")
    private String createdDept;
}