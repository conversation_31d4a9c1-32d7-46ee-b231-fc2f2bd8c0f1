/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.log;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ OperateLogResp, v 0.1 2024/5/28 11:31 lingang.han Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperateLogResp {
    @ApiModelProperty(value = "pk")
    private Long id;

    @ApiModelProperty(value = "操作内容")
    private String requestParam;

    @ApiModelProperty(value = "操作结果")
    private String response;

    @ApiModelProperty(value = "操作类型")
    private String type;

    @ApiModelProperty(value = "操作模块")
    private String model;

    @ApiModelProperty(value = "操作对象id")
    private Long objId;

    @ApiModelProperty(value = "操作描述")
    private String description;

    @ApiModelProperty(value = "操作人")
    private String userIdentify;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime createdTime;

}