package com.xftech.cdp.api.dto.resp.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TemplateParamListResp {
    @ApiModelProperty(value = "参数id", required = true)
    private Long id;

    @ApiModelProperty(value = "参数名称", required = true)
    private String name;

    @ApiModelProperty(value = "参数备注")
    private String description;

    @ApiModelProperty(value = "key值", required = true)
    private String paramKey;

    @ApiModelProperty(value = "需求方", required = true)
    private String demandSide;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "上线时间", required = true)
    private LocalDateTime createTime;
}
