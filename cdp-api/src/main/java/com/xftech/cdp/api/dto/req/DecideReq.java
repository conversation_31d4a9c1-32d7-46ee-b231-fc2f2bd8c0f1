/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ AppBannerReq, v 0.1 2024/4/18 17:43 benlin.wang Exp $
 */

@Data
public class DecideReq implements Serializable {
    private static final long serialVersionUID = -206992880178563531L;

    @ApiModelProperty(value = "调用来源")
    private CallingSourceEnum callingSource;

    @ApiModelProperty(value = "用户Id")
    private Long userId;

    @ApiModelProperty(value = "用户手机号")
    private String mobile;

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "innerApp")
    private String innerApp;

    @ApiModelProperty(value = "businessType")
    private String businessType;

    @ApiModelProperty(value = "策略id列表")
    private List<Long> strategyIdList;
}