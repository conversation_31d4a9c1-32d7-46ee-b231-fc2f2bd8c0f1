package com.xftech.cdp.api.dto.req.dict;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class EventMetaDataReq {
    @ApiModelProperty(value = "业务线")
    @NotNull(message = "业务线不能为空")
    private String businessType;

    @ApiModelProperty(value = "策略类型 2-T0策略 3-离线策略")
    @NotNull(message = "策略类型不能为空")
    private Integer strategyType;

    @ApiModelProperty(value = "策略模型：1:常规策略(单策略,根节点画布策略) 2:非根节点画布策略")
    private Integer strategyModel;
}
