<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="reportDailyTaskMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyTaskDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="refresh_time" jdbcType="TIMESTAMP" property="refreshTime" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="d_flag" jdbcType="INTEGER" property="dFlag" />
    <result column="created_op" jdbcType="VARCHAR" property="createdOp" />
    <result column="updated_op" jdbcType="VARCHAR" property="updatedOp" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `date`, `type`, `refresh_time`, `created_time`, `updated_time`, `d_flag`, `created_op`, 
    `updated_op`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from report_daily_task
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyTaskDo">
    insert into report_daily_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="refreshTime != null">
        `refresh_time`,
      </if>
      <if test="createdTime != null">
        `created_time`,
      </if>
      <if test="updatedTime != null">
        `updated_time`,
      </if>
      <if test="dFlag != null">
        `d_flag`,
      </if>
      <if test="createdOp != null">
        `created_op`,
      </if>
      <if test="updatedOp != null">
        `updated_op`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="refreshTime != null">
        #{refreshTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        #{updatedOp,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyTaskDo">
    update report_daily_task
    <set>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="refreshTime != null">
        `refresh_time` = #{refreshTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        `created_op` = #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        `updated_op` = #{updatedOp,jdbcType=VARCHAR},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="existReportDailyTaskRecord" resultType="integer">
    select count(*)
    from report_daily_task
    where type = #{type}
      and date(date) = current_date()
  </select>

  <select id="selectTodayByType" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from report_daily_task
    where `type` = #{type,jdbcType=INTEGER}
    and date(date) = current_date()
  </select>

  <update id="updateByDateAndType" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyTaskDo">
    update report_daily_task
    <set>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="refreshTime != null">
        `refresh_time` = #{refreshTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        `created_op` = #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        `updated_op` = #{updatedOp,jdbcType=VARCHAR},
      </if>
    </set>
    where type = #{type}
    and date(date) = current_date()
  </update>

</mapper>