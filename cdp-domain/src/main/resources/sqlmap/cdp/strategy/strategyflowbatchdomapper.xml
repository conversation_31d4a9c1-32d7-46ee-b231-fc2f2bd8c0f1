<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyFlowBatchDoMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="date_value" jdbcType="INTEGER" property="dateValue" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, flow_no, batch_no, date_value, d_flag, created_time, updated_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from strategy_flow_batch
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByDateValue" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_flow_batch
    where flow_no = #{flowNo} and date_value = #{dateValue} and d_flag = 0
    order by id desc limit 1;
  </select>

  <select id="selectByFlowNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_flow_batch
    where flow_no = #{flowNo} and d_flag = 0
  </select>

  <select id="selectByBatchNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_flow_batch
    where batch_no = #{batchNo} and d_flag = 0
    order by id desc limit 1;
  </select>

  <insert id="insert" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo">
    insert into strategy_flow_batch (id, flow_no, batch_no, 
      date_value, d_flag, created_time, 
      updated_time)
    values (#{id,jdbcType=BIGINT}, #{flowNo,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{dateValue,jdbcType=INTEGER}, #{dFlag,jdbcType=SMALLINT}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo">
    insert into strategy_flow_batch
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowNo != null">
        flow_no,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="dateValue != null">
        date_value,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="flowNo != null">
        #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="dateValue != null">
        #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_flow_batch
    <set>
      <if test="flowNo != null">
        flow_no = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="dateValue != null">
        date_value = #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_flow_batch
    set flow_no = #{flowNo,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      date_value = #{dateValue,jdbcType=INTEGER},
      d_flag = #{dFlag,jdbcType=SMALLINT},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>