<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyMarketEventMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="eventName" column="event_name" jdbcType="VARCHAR"/>
        <result property="timeType" column="time_type" jdbcType="SMALLINT"/>
        <result property="timeValue" column="time_value" jdbcType="INTEGER"/>
        <result property="timeUnit" column="time_unit" jdbcType="SMALLINT"/>
        <result property="dispatchMinUserNum" column="dispatch_min_user_num" jdbcType="INTEGER"/>
        <result property="dispatchMaxUserNum" column="dispatch_max_user_num" jdbcType="INTEGER"/>
        <result property="dFlag" column="d_flag" jdbcType="SMALLINT"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="createdOp" column="created_op" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="updatedOp" column="updated_op" jdbcType="VARCHAR"/>
        <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,strategy_id,event_name,
        time_type,time_value,time_unit,dispatch_min_user_num,dispatch_max_user_num,
        d_flag,created_time,created_op,
        updated_time,updated_op
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_event
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="getByEventName" resultMap="BaseResultMap">
        select sme.*, s.business_type as biz_type
        from strategy_market_event sme
        inner join strategy s on sme.strategy_id = s.id
        where sme.event_name = #{bizEventType}
        and sme.d_flag = 0
        and s.status = 1
    </select>

    <select id="getByStrategyId" resultMap="BaseResultMap">
        select *
        from strategy_market_event
        where strategy_id = #{strategyId}
        and d_flag = 0
    </select>

    <select id="selectByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_event
        where strategy_id = #{strategyId} and d_flag = 0
    </select>

    <select id="listStrategyMarketEvent" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_market_event
        where d_flag = 0
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo"
            useGeneratedKeys="true">
        insert into strategy_market_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="eventName != null">event_name,</if>
            <if test="timeType != null">time_type,</if>
            <if test="timeValue != null">time_value,</if>
            <if test="timeUnit != null">time_unit,</if>
            <if test="dispatchMinUserNum != null">dispatch_min_user_num,</if>
            <if test="dispatchMaxUserNum != null">dispatch_max_user_num,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="eventName != null">#{eventName,jdbcType=VARCHAR},</if>
            <if test="timeType != null">#{timeType,jdbcType=SMALLINT},</if>
            <if test="timeValue != null">#{timeValue,jdbcType=INTEGER},</if>
            <if test="timeUnit != null">#{timeUnit,jdbcType=SMALLINT},</if>
            <if test="dispatchMinUserNum != null">#{dispatchMinUserNum,jdbcType=INTEGER},</if>
            <if test="dispatchMaxUserNum != null">#{dispatchMaxUserNum,jdbcType=INTEGER},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo">
        update strategy_market_event
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="eventName != null">
                event_name = #{eventName,jdbcType=VARCHAR},
            </if>
            <if test="timeType != null">
                time_type = #{timeType,jdbcType=SMALLINT},
            </if>
            <if test="timeValue != null">
                time_value = #{timeValue,jdbcType=INTEGER},
            </if>
            <if test="timeUnit != null">
                time_unit = #{timeUnit,jdbcType=SMALLINT},
            </if>
            <if test="dispatchMinUserNum != null">
                dispatch_min_user_num = #{dispatchMinUserNum,jdbcType=INTEGER},
            </if>
            <if test="dispatchMaxUserNum != null">
                dispatch_max_user_num = #{dispatchMaxUserNum,jdbcType=INTEGER},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update
            strategy_market_event
        set d_flag =1
        where id = #{id,jdbcType=BIGINT}
    </delete>

</mapper>
