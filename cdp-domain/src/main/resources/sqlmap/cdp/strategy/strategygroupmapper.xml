<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyGroup">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo">
        <id column="id" property="id"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="name" property="name"/>
        <result column="group_config" property="groupConfig"/>
        <result column="is_executable" property="isExecutable"/>
        <result column="ext_info" property="extInfo"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, strategy_id, name, group_config, is_executable, ext_info, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_group
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="selectBlankGroup" resultMap="BaseResultMap">
        select sg.*
        from strategy_group sg
                 inner join strategy_market_channel smc on sg.id = smc.strategy_group_id
        where sg.strategy_id = #{strategyId}
          and smc.market_channel = 0
          and smc.d_flag = 0
    </select>

    <select id="selectListByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_group
        where strategy_id = #{strategyId} and d_flag = 0
        order by name
    </select>

    <select id="selectAllByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_group
        where strategy_id = #{strategyId}
        order by name
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update strategy_group
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByStrategyId">
        update strategy_group
        set d_flag = 1
        where strategy_id = #{strategyId}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo"
            useGeneratedKeys="true">
        insert into strategy_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="name != null">name,</if>
            <if test="groupConfig != null">group_config,</if>
            <if test="isExecutable != null">is_executable,</if>
            <if test="extInfo != null">ext_info,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="groupConfig != null">#{groupConfig,jdbcType=OTHER},</if>
            <if test="isExecutable != null">#{isExecutable,jdbcType=SMALLINT},</if>
            <if test="extInfo != null">#{extInfo,jdbcType=VARCHAR},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo">
        update strategy_group
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="groupConfig != null">
                group_config = #{groupConfig,jdbcType=OTHER},
            </if>
            <if test="isExecutable != null">
                is_executable = #{isExecutable,jdbcType=SMALLINT},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
