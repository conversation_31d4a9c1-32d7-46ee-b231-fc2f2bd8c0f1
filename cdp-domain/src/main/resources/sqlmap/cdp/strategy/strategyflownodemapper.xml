<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyFlowNodeDoMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="flow_no" jdbcType="VARCHAR" property="flowNo" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="level_num" jdbcType="INTEGER" property="levelNum" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, node_id, flow_no, strategy_id, level_num, parent_id, d_flag, created_time, updated_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from strategy_flow_node
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectByFlowNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from strategy_flow_node
    where flow_no = #{flowNo} and d_flag = 0
    order by level_num
  </select>

  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into strategy_flow_node
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="nodeId != null">
        node_id,
      </if>
      <if test="flowNo != null">
        flow_no,
      </if>
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="levelNum != null">
        level_num,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="nodeId != null">
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="flowNo != null">
        #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="levelNum != null">
        #{levelNum,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_flow_node
    <set>
      <if test="nodeId != null">
        node_id = #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="flowNo != null">
        flow_no = #{flowNo,jdbcType=VARCHAR},
      </if>
      <if test="strategyId != null">
        strategy_id = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="levelNum != null">
        level_num = #{levelNum,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update strategy_flow_node
    set node_id = #{nodeId,jdbcType=VARCHAR},
      flow_no = #{flowNo,jdbcType=VARCHAR},
      strategy_id = #{strategyId,jdbcType=BIGINT},
      level_num = #{levelNum,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=VARCHAR},
      d_flag = #{dFlag,jdbcType=SMALLINT},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="deleteByFlowNo">
    update strategy_flow_node set d_flag = 1
    where flow_no = #{flowNo}
  </update>

</mapper>