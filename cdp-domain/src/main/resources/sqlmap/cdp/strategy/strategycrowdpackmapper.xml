<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyCrowdPack">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyCrowdPackDo">
        <id column="id" property="id"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="crowd_pack_id" property="crowdPackId"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, strategy_id, crowd_pack_id, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_crowd_pack
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="selectByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_crowd_pack
        where strategy_id = #{strategyId}
        and d_flag = 0
    </select>

    <select id="selectCrowdPackIdByStrategyId" resultMap="BaseResultMap">
        select
        strategy_id,crowd_pack_id
        from strategy_crowd_pack
        where d_flag = 0 and strategy_id in
        <foreach collection="strategyIdList" item="strategyId" open="(" separator="," close=")">
            #{strategyId}
        </foreach>
    </select>

    <select id="selectCrowdPackIdsByStrategyId" resultType="java.lang.Long">
        select distinct crowd_pack_id
        from strategy_crowd_pack
        where strategy_id = #{strategyId}
          and d_flag = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update strategy_crowd_pack
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByStrategyId">
        update strategy_crowd_pack
        set d_flag = 1
        where strategy_id = #{strategyId}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyCrowdPackDo"
            useGeneratedKeys="true">
        insert into strategy_crowd_pack
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="crowdPackId != null">crowd_pack_id,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="crowdPackId != null">#{crowdPackId,jdbcType=BIGINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyCrowdPackDo">
        update strategy_crowd_pack
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="crowdPackId != null">
                crowd_pack_id = #{crowdPackId,jdbcType=BIGINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCrowdPackId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_crowd_pack
        where crowd_pack_id = #{crowdPackId}
        and d_flag = 0
    </select>

    <select id="selectByCrowdPackIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_crowd_pack
        where d_flag = 0
        and crowd_pack_id in
        <foreach collection="crowdIds" item="crowdId" open="(" separator="," close=")">
            #{crowdId}
        </foreach>
    </select>

</mapper>
