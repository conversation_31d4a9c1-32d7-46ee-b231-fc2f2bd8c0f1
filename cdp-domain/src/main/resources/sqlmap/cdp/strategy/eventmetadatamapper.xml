<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="eventMetadataMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.EventMetaDataDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="eventName" column="event_name" jdbcType="VARCHAR"/>
        <result property="eventDesc" column="event_desc" jdbcType="VARCHAR"/>
        <result property="operateType" column="operate_type" jdbcType="INTEGER"/>
        <result property="fillType" column="fill_type" jdbcType="VARCHAR"/>
        <result property="labelValueType" column="label_value_type" jdbcType="VARCHAR"/>
        <result property="eventType" column="event_type" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result column="d_flag" property="dFlag"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,event_name,event_desc,operate_type,fill_type,label_value_type,event_type,parent_id,created_time,updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from event_meta_data
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="selectByBusinessType" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from event_meta_data
        where d_flag = 0 and find_in_set(#{businessType}, business_type)
    </select>

    <select id="selectCacheInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from event_meta_data
        where d_flag = 0
    </select>

    <select id="selectByEventName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from event_meta_data
        where d_flag = 0 and event_name = #{eventName} limit 1
    </select>

</mapper>
