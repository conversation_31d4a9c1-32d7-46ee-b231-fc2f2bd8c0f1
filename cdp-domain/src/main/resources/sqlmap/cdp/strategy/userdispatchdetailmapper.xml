<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="userDispatchDetailMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="strategyChannelId" column="strategy_channel_id" jdbcType="BIGINT"/>
        <result property="marketChannel" column="market_channel" jdbcType="SMALLINT"/>
        <result property="strategyExecId" column="strategy_exec_id" jdbcType="VARCHAR"/>
        <result property="batchNum" column="batch_num" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="SMALLINT"/>
        <result property="crowdPackId" column="crowd_pack_id" jdbcType="BIGINT"/>
        <result property="execLogId" column="exec_log_id" jdbcType="BIGINT"/>
        <result property="dispatchTime" column="dispatch_time" jdbcType="TIMESTAMP"/>
        <result property="usedStatus" column="used_status" jdbcType="SMALLINT"/>
        <result property="messageId" column="message_id" jdbcType="VARCHAR"/>
        <result property="triggerDatetime" column="trigger_datetime" jdbcType="TIMESTAMP"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="templateId" column="template_id"/>
        <result property="strategyGroupId" column="strategy_group_id" jdbcType="BIGINT"/>
        <result property="strategyGroupName" column="strategy_group_name" jdbcType="VARCHAR"/>
        <result column="ext_detail" jdbcType="VARCHAR" property="extDetail" />
        <result column="dispatch_type" jdbcType="VARCHAR" property="dispatchType" />
        <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    </resultMap>

    <resultMap id="UserDispatchDetailIndexMapper" type="com.xftech.cdp.domain.strategy.model.dto.UserDispatchDetailDto">
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="count" column="count" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="UserDispatchGroupNum" type="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchGroupNum">
        <result property="strategyChannelId" column="strategy_channel_id" jdbcType="BIGINT"/>
        <result property="userNum" column="user_num" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,strategy_id,strategy_channel_id,crowd_pack_id,exec_log_id,
        market_channel,strategy_exec_id,batch_num,
        user_id,mobile,status,
        dispatch_time, used_status, message_id,trigger_datetime,created_time,updated_time,group_name,template_id,
        strategy_group_id,strategy_group_name,ext_detail,dispatch_type,biz_type
    </sql>

    <sql id="IndexCountCondition">
        AND status = 1
        <choose>
            <when test="flowCtrlType == 1">
                AND strategy_id = #{strategyId}
            </when>
            <otherwise>
                AND market_channel = #{channel}
            </otherwise>
        </choose>
        AND user_id IN
        <foreach collection="userIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </sql>

    <select id="selectPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            AND status = 1
            AND user_id IN
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </where>
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select user_id, strategy_id, market_channel, dispatch_time
        from ${tableName}
        <where>
            AND status = 1
            AND user_id IN
            <foreach collection="userIds" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </where>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            and id = #{id,jdbcType=BIGINT}
        </where>
    </select>

    <select id="selectListByBatchNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            and batch_num = #{batchNum}
            and market_channel = #{marketChannel}
        </where>
    </select>

    <!--  具体策略触达指标统计  -->
    <select id="getIndexByStrategy" resultMap="UserDispatchDetailIndexMapper">
        SELECT user_id, count(1) AS count
        FROM ${tableName}
        <where>
            AND strategy_id in
            <foreach collection="strategyIdList" item="strategyId" index="index" open="(" separator="," close=")">
                #{strategyId}
            </foreach>
            AND dispatch_time BETWEEN #{startDate} AND #{endDate}
            AND (dispatch_type IS NULL OR  dispatch_type = 'MKT')
            AND user_id IN
            <foreach collection="userIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="statusList !=null and statusList.size()>0">
                and status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="noFlcChannelList !=null and noFlcChannelList.size()>0">
                AND market_channel not in
                <foreach collection="noFlcChannelList" item="noFlcChannel" open="(" close=")" separator=",">
                    #{noFlcChannel}
                </foreach>
            </if>
        </where>
        GROUP BY user_id
    </select>

    <!--  具体策略 具体渠道 触达指标统计 new -->
    <select id="getIndexByStrategyAndChannel" resultMap="UserDispatchDetailIndexMapper">
        SELECT user_id, count(1) AS count
        FROM ${tableName}
        <where>
            AND strategy_id in
            <foreach collection="strategyIdList" item="strategyId" index="index" open="(" separator="," close=")">
                #{strategyId}
            </foreach>
            AND dispatch_time BETWEEN #{startDate} AND #{endDate}
            AND (dispatch_type IS NULL OR  dispatch_type = 'MKT')
            AND user_id IN
            <foreach collection="userIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="statusList !=null and statusList.size()>0">
                and status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="channel !=null">
                AND market_channel = #{channel}
            </if>
        </where>
        GROUP BY user_id
    </select>
    <!--  全部策略触达指标统计  -->
    <select id="getIndexForAllStrategy" resultMap="UserDispatchDetailIndexMapper">
        SELECT user_id, count(1) AS count
        FROM ${tableName}
        <where>
            AND dispatch_time BETWEEN #{startDate} AND #{endDate}
            AND (dispatch_type IS NULL OR  dispatch_type = 'MKT')
            AND user_id IN
            <foreach collection="userIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="statusList !=null and statusList.size()>0">
                AND status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="noFlcChannelList !=null and noFlcChannelList.size()>0">
                AND market_channel not in
                <foreach collection="noFlcChannelList" item="noFlcChannel" open="(" close=")" separator=",">
                    #{noFlcChannel}
                </foreach>
            </if>
        </where>
        GROUP BY user_id
    </select>

    <!--  具体渠道触达指标统计  -->
    <select id="getIndexByChannel" resultMap="UserDispatchDetailIndexMapper">
        SELECT user_id, count(1) AS count
        FROM ${tableName}
        <where>
            AND market_channel = #{channel}
            AND dispatch_time BETWEEN #{startDate} AND #{endDate}
            AND (dispatch_type IS NULL OR  dispatch_type = 'MKT')
            AND user_id IN
            <foreach collection="userIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="statusList !=null and statusList.size()>0">
                AND status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="noFlcChannelList !=null and noFlcChannelList.size()>0">
                AND market_channel not in
                <foreach collection="noFlcChannelList" item="noFlcChannel" open="(" close=")" separator=",">
                    #{noFlcChannel}
                </foreach>
            </if>
        </where>
        GROUP BY user_id
    </select>

    <select id="getIndexByOneChannel" resultMap="UserDispatchDetailIndexMapper">
        SELECT user_id, count(1) AS count
        FROM ${tableName}
        <where>
            AND market_channel = #{channel}
            AND dispatch_time BETWEEN #{startDate} AND #{endDate}
            AND (dispatch_type IS NULL OR  dispatch_type = 'MKT')
            AND user_id IN
            <foreach collection="userIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="statusList !=null and statusList.size()>0">
                AND status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
        </where>
        GROUP BY user_id
    </select>

    <!--  全部渠道触达指标统计  -->
    <select id="getIndexForAllChannel" resultMap="UserDispatchDetailIndexMapper">
        SELECT user_id, count(1) AS count
        FROM ${tableName}
        <where>
            AND dispatch_time BETWEEN #{startDate} AND #{endDate}
            AND (dispatch_type IS NULL OR  dispatch_type = 'MKT')
            AND user_id IN
            <foreach collection="userIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="statusList !=null and statusList.size()>0">
                AND status in
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="noFlcChannelList !=null and noFlcChannelList.size()>0">
                AND market_channel not in
                <foreach collection="noFlcChannelList" item="noFlcChannel" open="(" close=")" separator=",">
                    #{noFlcChannel}
                </foreach>
            </if>
        </where>
        GROUP BY user_id
    </select>

    <select id="countArriveStatus" resultType="integer">
        SELECT count(*)
        FROM ${tableName}
        where batch_num = #{batchNum}
          and status = #{status}
    </select>

    <select id="countUsedCount" resultType="integer">
        SELECT count(*)
        FROM ${tableName}
        where batch_num = #{batchNum}
          and used_status = #{usedStatus}
    </select>

    <select id="countArriveByStatusAndExecLogId" resultType="integer">
        SELECT count(*)
        FROM ${tableName}
        where exec_log_id = #{execLogId}
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="countUsedCountByExecLogId" resultType="integer">
        SELECT count(*)
        FROM ${tableName}
        where exec_log_id = #{execLogId}
          and used_status = #{usedStatus}
    </select>

    <select id="countByStrategyIdAndDispatchTimeOrStatus" resultType="java.lang.Integer">
        SELECT count(distinct user_id) resultCount
        FROM ${tableName}
        <where>
            and strategy_id = #{strategyId}
            and dispatch_time between #{startTime} and #{endTime}
            <if test="status != null">
                and `status` = #{status}
            </if>
        </where>
    </select>

    <select id="selectTodayDispatchStrategy" resultMap="BaseResultMap">
        SELECT strategy_id
        FROM ${tableName}
        where dispatch_time between #{startTime} and #{endTime}
        group by strategy_id
    </select>

    <select id="countDispatchUserNum" resultType="integer">
        SELECT count(distinct user_id)
        FROM ${tableName}
        where strategy_id = #{strategyId}
          and created_time between #{startTime} and #{endTime}
    </select>

    <select id="selectFailDispatchForUpdate" resultMap="BaseResultMap">
        SELECT id FROM ${tableName} where market_channel in (1,5) and status= -1 and created_time &lt;= #{overTime} limit 10
    </select>

    <!-- 查询单个用户触达次数 -->
    <select id="getIndexFromLocal" resultType="integer">
        SELECT count(1) AS count
        FROM ${tableName}
        <where>
            AND status = 1
            AND user_id = #{userId}
            AND strategy_id = #{strategyId}
            AND dispatch_time BETWEEN #{startDate} AND #{endDate}
            AND market_channel in
            <foreach collection="marketChannelList" item="marketChannel" index="index" open="(" separator="," close=")">
                #{marketChannel}
            </foreach>
            limit 1
        </where>
    </select>

    <!-- 查询单个用户指定策略指定时间内最大的触达时间 -->
    <select id="getMaxDispatchTime" resultType="java.time.LocalDateTime">
        SELECT created_time
        AS time
        FROM ${tableName}
        <where>
            user_id = #{appUserId}
            AND created_time &gt;= #{startDate}
            AND created_time &lt; #{endDate}
            <if test="strategyIdList !=null and strategyIdList.size()>0">
                AND strategy_id in
                <foreach collection="strategyIdList" item="strategyId" index="index" open="(" separator="," close=")">
                    #{strategyId}
                </foreach>
            </if>
            order by id desc
            limit 1
        </where>
    </select>


    <update id="dispatchFailUserUpdate" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo">
        update ${tableName} set status = 0 where id = #{id}
    </update>

    <update id="batchUpdateDispatchFail">
        update ${tableName} set status = 0, updated_time = now() where  status != 1 and id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateDispatchSucceedFromInit">
        update ${tableName} set status = 1, updated_time = now() where  status = -1 and id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateDispatchFailFromInit">
        update ${tableName} set status = 0, updated_time = now() where  status = -1 and id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateBybatchNumAndMobile"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo">
        update ${tableName}
        set
        <if test="status != null">status = #{status},</if>
        <if test="usedStatus != null">used_status = #{usedStatus},</if>
        dispatch_time = #{dispatchTime}
        where batch_num = #{batchNum} and mobile = #{mobile}
        <if test="usedStatus != null and usedStatus == -1">
            and status != 1
        </if>
    </update>

    <update id="updateByBatchNumAndUserNo"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo">
        update ${tableName}
        set
        <if test="status != null">status = #{status},</if>
        <if test="usedStatus != null">used_status = #{usedStatus},</if>
        dispatch_time = #{dispatchTime}
        where batch_num = #{batchNum} and user_id = #{userId}
        and status != 1
    </update>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo"
            useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyChannelId != null">strategy_channel_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="strategyExecId != null">strategy_exec_id,</if>
            <if test="execLogId != null">exec_log_id,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="crowdPackId != null">crowd_pack_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="mobile != null">mobile,</if>
            <if test="status != null">status,</if>
            <if test="dispatchTime != null">dispatch_time,</if>
            <if test="usedStatus != null">used_status,</if>
            <if test="messageId != null">message_id,</if>
            <if test="triggerDatetime != null">trigger_datetime,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="groupName != null">group_name,</if>
            <if test="bizEventType != null">biz_event_type,</if>
            <if test="templateId != null">template_id,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="strategyGroupName != null">strategy_group_name,</if>
            <if test="extDetail != null">ext_detail,</if>
            <if test="dispatchType != null">dispatch_type,</if>
            <if test="bizType != null">biz_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="strategyChannelId != null">#{strategyChannelId,jdbcType=BIGINT},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=SMALLINT},</if>
            <if test="strategyExecId != null">#{strategyExecId,jdbcType=VARCHAR},</if>
            <if test="execLogId != null">#{execLogId,jdbcType=BIGINT},</if>
            <if test="batchNum != null">#{batchNum,jdbcType=VARCHAR},</if>
            <if test="crowdPackId != null">#{crowdPackId,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=SMALLINT},</if>
            <if test="dispatchTime != null">#{dispatchTime,jdbcType=TIMESTAMP},</if>
            <if test="usedStatus != null">#{usedStatus,jdbcType=SMALLINT},</if>
            <if test="messageId != null">#{messageId,jdbcType=VARCHAR},</if>
            <if test="triggerDatetime != null">#{triggerDatetime,jdbcType=TIMESTAMP},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="groupName != null">#{groupName,jdbcType=VARCHAR},</if>
            <if test="bizEventType != null">#{bizEventType,jdbcType=VARCHAR},</if>
            <if test="templateId != null">#{templateId,jdbcType=VARCHAR},</if>
            <if test="strategyGroupId != null">#{strategyGroupId,jdbcType=BIGINT},</if>
            <if test="strategyGroupName != null">#{strategyGroupName,jdbcType=VARCHAR},</if>
            <if test="extDetail != null">#{extDetail,jdbcType=VARCHAR},</if>
            <if test="dispatchType != null">#{dispatchType,jdbcType=VARCHAR},</if>
            <if test="bizType != null">#{bizType,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo">
        update ${tableName}
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="strategyChannelId != null">
                strategy_channel_id = #{strategyChannelId,jdbcType=BIGINT},
            </if>
            <if test="marketChannel != null">
                market_channel = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="strategyExecId != null">
                strategy_exec_id = #{strategyExecId,jdbcType=VARCHAR},
            </if>
            <if test="execLogId != null">
                exec_log_id = #{execLogId,jdbcType=VARCHAR},
            </if>
            <if test="batchNum != null">
                batch_num = #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="crowdPackId != null">
                crowd_pack_id = #{crowdPackId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="dispatchTime != null">
                dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="usedStatus != null">
                used_status = #{usedStatus,jdbcType=SMALLINT},
            </if>
            <if test="messageId != null">
                message_id = #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="triggerDatetime != null">
                trigger_datetime = #{triggerDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="strategyGroupId != null">
                strategy_group_id = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupName != null">
                strategy_group_name = #{strategyGroupName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByBatchAndUserIdAndChannel"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo">
        update ${tableName}
        <set>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="dispatchTime != null">
                dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where batch_num = #{batchNum} and user_id = #{userId} and market_channel = #{marketChannel}
    </update>

    <select id="countStrategyGroupDispatchUserNum" resultType="integer">
        select count(distinct user_id) as user_num
        from ${tableName}
        where strategy_id = #{strategyId}
        <if test="strategyChannelIds !=null and strategyChannelIds.size()>0">
        and strategy_channel_id in
            <foreach collection="strategyChannelIds" item="strategyChannelId" open="(" close=")" separator=",">
                #{strategyChannelId}
            </foreach>
        </if>
          and trigger_datetime &gt;= #{startDate}
          and trigger_datetime &lt; #{endDate}
    </select>

    <!-- TODO 优化SQL 查询策略组的触达用户数 -->
    <select id="countByDetail" resultType="integer">
        select count(distinct user_id) as user_num
        from ${tableName}
        where strategy_id = #{strategyDoId}
          and strategy_group_id = #{strategyGroupId}
        <if test="groupId !=null">
          and group_name = #{groupId}
        </if>
          and market_channel =  #{marketChannel}
          and template_id = #{templateId}
        <if test="statusList !=null and statusList.size()>0">
            and status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
          and trigger_datetime &gt;= #{startDate}
          and trigger_datetime &lt; #{endDate}
    </select>

    <select id="selectByBatchNoAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
             batch_num = #{batchNum}
            and user_id = #{appUserId}
        </where>
    </select>

</mapper>
