<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="operateLog">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.OperateLogDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="model" jdbcType="INTEGER" property="model" />
    <result column="obj_id" jdbcType="BIGINT" property="objId" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="user_identify" jdbcType="VARCHAR" property="userIdentify" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="request_param" jdbcType="LONGVARCHAR" property="requestParam" />
    <result column="response" jdbcType="LONGVARCHAR" property="response" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `url`, `type`, `model`,  `request_param`, `response`, `obj_id`, `description`, `user_identify`, `d_flag`,
    `created_time`, `updated_time`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from operate_log
    where `id` = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from operate_log
    where d_flag = 0
    <if test="id != null">
      AND obj_id = #{id,jdbcType=BIGINT}
    </if>
    <if test="model != null">
      AND model = #{model,jdbcType=INTEGER}
    </if>
    order by id desc
  </select>

  <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.OperateLogDo">
    insert into operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="url != null">
        `url`,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="model != null">
        `model`,
      </if>
      <if test="objId != null">
        `obj_id`,
      </if>
      <if test="description != null">
        `description`,
      </if>
      <if test="userIdentify != null">
        `user_identify`,
      </if>
      <if test="dFlag != null">
        `d_flag`,
      </if>
      <if test="createdTime != null">
        `created_time`,
      </if>
      <if test="updatedTime != null">
        `updated_time`,
      </if>
      <if test="requestParam != null">
        `request_param`,
      </if>
      <if test="response != null">
        `response`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        #{model,jdbcType=INTEGER},
      </if>
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="userIdentify != null">
        #{userIdentify,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestParam != null">
        #{requestParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="response != null">
        #{response,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.OperateLogDo">
    update operate_log
    <set>
      <if test="url != null">
        `url` = #{url,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        `model` = #{model,jdbcType=INTEGER},
      </if>
      <if test="objId != null">
        `obj_id` = #{objId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        `description` = #{description,jdbcType=VARCHAR},
      </if>
      <if test="userIdentify != null">
        `user_identify` = #{userIdentify,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="requestParam != null">
        `request_param` = #{requestParam,jdbcType=LONGVARCHAR},
      </if>
      <if test="response != null">
        `response` = #{response,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>