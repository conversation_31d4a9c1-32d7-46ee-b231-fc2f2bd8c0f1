<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="dispatchUserDelayDoMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="market_channel" jdbcType="SMALLINT" property="marketChannel" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="date_value" jdbcType="INTEGER" property="dateValue" />
    <result column="dispatch_time" jdbcType="TIMESTAMP" property="dispatchTime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="d_flag" jdbcType="SMALLINT" property="dFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, strategy_id, user_id, market_channel, group_name, ext_info, date_value, dispatch_time,
    status, d_flag, create_time, updated_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from dispatch_user_delay
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectTodoList" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from dispatch_user_delay
    where status=#{status} and date_value = #{dateValue} and dispatch_time &lt; #{at} and id &gt; #{startIndex} and d_flag = 0
    order by id
    limit #{pageSize}
  </select>

  <select id="selectNotTelTodoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispatch_user_delay
    where status=#{status}  and date_value = #{dateValue} and dispatch_time &lt; #{at}
    <if test="telTypes !=null and telTypes.size()>0">
      and market_channel not in
      <foreach collection="telTypes" item="type" open="(" close=")" separator=",">
        #{type}
      </foreach>
    </if>
    and d_flag = 0
    limit #{pageSize}
  </select>

  <select id="selectTelTodoList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispatch_user_delay
    where status=#{status} and date_value = #{dateValue} and dispatch_time &lt; #{at}
    <if test="telTypes !=null and telTypes.size()>0">
      and market_channel in
      <foreach collection="telTypes" item="type" open="(" close=")" separator=",">
        #{type}
      </foreach>
    </if>
    and d_flag = 0
    limit #{pageSize}
  </select>

  <select id="selectTodoListByType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from dispatch_user_delay
    where status=#{status}  and date_value = #{dateValue} and dispatch_time &lt; #{at}
    and market_channel = #{type}
    and d_flag = 0
    limit #{pageSize}
  </select>

  <update id="updateFinishedStatus">
    update dispatch_user_delay set status = #{status}
                               where id = #{id}
  </update>

  <insert id="insert" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into dispatch_user_delay (id, strategy_id, user_id,
    market_channel, group_name, ext_info,
    date_value, dispatch_time, status,
    d_flag, create_time, updated_time
    )
    values (#{id,jdbcType=BIGINT}, #{strategyId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},
    #{marketChannel,jdbcType=SMALLINT}, #{groupName,jdbcType=VARCHAR}, #{extInfo,jdbcType=VARCHAR},
    #{dateValue,jdbcType=INTEGER}, #{dispatchTime,jdbcType=TIMESTAMP}, #{status,jdbcType=SMALLINT},
    #{dFlag,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into dispatch_user_delay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="marketChannel != null">
        market_channel,
      </if>
      <if test="groupName != null">
        group_name,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="dateValue != null">
        date_value,
      </if>
      <if test="dispatchTime != null">
        dispatch_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="marketChannel != null">
        #{marketChannel,jdbcType=SMALLINT},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="dateValue != null">
        #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="dispatchTime != null">
        #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dispatch_user_delay
    <set>
      <if test="strategyId != null">
        strategy_id = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="marketChannel != null">
        market_channel = #{marketChannel,jdbcType=SMALLINT},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="dateValue != null">
        date_value = #{dateValue,jdbcType=INTEGER},
      </if>
      <if test="dispatchTime != null">
        dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=SMALLINT},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=SMALLINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update dispatch_user_delay
    set strategy_id = #{strategyId,jdbcType=BIGINT},
    user_id = #{userId,jdbcType=BIGINT},
    market_channel = #{marketChannel,jdbcType=SMALLINT},
    group_name = #{groupName,jdbcType=VARCHAR},
    ext_info = #{extInfo,jdbcType=VARCHAR},
    date_value = #{dateValue,jdbcType=INTEGER},
    dispatch_time = #{dispatchTime,jdbcType=TIMESTAMP},
    status = #{status,jdbcType=SMALLINT},
    d_flag = #{dFlag,jdbcType=SMALLINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>