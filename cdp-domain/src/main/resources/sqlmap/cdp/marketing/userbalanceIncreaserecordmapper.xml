<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="userbalanceIncreaserecordmapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo">
    <id property="id" column="id" jdbcType="BIGINT"/>
    <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    <result property="appUserId" column="app_user_id" jdbcType="BIGINT"/>
    <result property="amount" column="amount" jdbcType="DECIMAL"/>
    <result property="status" column="status" jdbcType="INTEGER"/>
    <result property="reason" column="reason" jdbcType="VARCHAR"/>
    <result property="dFlag" column="d_flag" jdbcType="TINYINT"/>
    <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, app_user_id, amount, status, reason, d_flag, order_number
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_balance_increase_record
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="selectByOderAndUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user_balance_increase_record
    where app_user_id = #{appUserId,jdbcType=BIGINT}
    <if test="orderNumber != null">
      and order_number = #{orderNumber,jdbcType=VARCHAR}
    </if>
    and status = 0 and d_flag = 0
  </select>

  <insert id="insert" parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo">
    insert into user_balance_increase_record (id, create_time, update_time, 
      app_user_id, amount, status, 
      reason, d_flag, order_number
      )
    values (#{id,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{appUserId,jdbcType=BIGINT}, #{amount,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{dFlag,jdbcType=TINYINT}, #{orderNumber,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo" useGeneratedKeys="true">
    insert into user_balance_increase_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="appUserId != null">
        app_user_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="dFlag != null">
        d_flag,
      </if>
      <if test="orderNumber != null">
        order_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appUserId != null">
        #{appUserId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="orderNumber != null">
        #{orderNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo">
    update user_balance_increase_record
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appUserId != null">
        app_user_id = #{appUserId,jdbcType=BIGINT},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="dFlag != null">
        d_flag = #{dFlag,jdbcType=TINYINT},
      </if>
      <if test="orderNumber != null">
        order_number = #{orderNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo">
    update user_balance_increase_record
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      app_user_id = #{appUserId,jdbcType=BIGINT},
      amount = #{amount,jdbcType=DECIMAL},
      status = #{status,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      d_flag = #{dFlag,jdbcType=TINYINT},
      order_number = #{orderNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>