<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdExecSnapshot">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecSnapshotDo">
        <id column="id" property="id" />
        <result column="crowd_id" property="crowdId" />
        <result column="crowd_exec_log_id" property="crowdExecLogId" />
        <result column="snapshot_detail" property="snapshotDetail" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="d_flag" property="dFlag" />
        <result column="created_op" property="createdOp" />
        <result column="updated_op" property="updatedOp" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, crowd_id, crowd_exec_log_id, snapshot_detail, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectSnapshotByCrowdId" resultType="string">
        select snapshot_detail from crowd_exec_snapshot where crowd_id = #{crowdId} order by id desc limit 1
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecSnapshotDo" useGeneratedKeys="true">
        insert into crowd_exec_snapshot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="crowdExecLogId != null">
                crowd_exec_log_id,
            </if>
            <if test="snapshotDetail != null">
                snapshot_detail,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdExecLogId != null">
                #{crowdExecLogId,jdbcType=BIGINT},
            </if>
            <if test="snapshotDetail != null">
                #{snapshotDetail},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
            now(),
            now(),
        </trim>
    </insert>

</mapper>
