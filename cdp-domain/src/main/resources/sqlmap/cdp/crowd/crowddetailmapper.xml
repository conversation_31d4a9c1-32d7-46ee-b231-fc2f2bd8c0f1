<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdDetail">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo">
        <id column="id" property="id"/>
        <result column="crowd_id" property="crowdId"/>
        <result column="user_id" property="userId"/>
        <result column="app" property="app"/>
        <result column="inner_app" property="innerApp"/>
        <result column="crowd_exec_log_id" property="crowdExecLogId"/>
        <result column="ab_num" property="abNum"/>
        <result column="app_user_id_last2" property="appUserIdLast2"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="register_time" property="registerTime"/>
        <result column="d_flag" property="dFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , crowd_id, user_id, app, inner_app, crowd_exec_log_id,ab_num,app_user_id_last2, register_time, created_time, updated_time, d_flag
    </sql>

    <select id="selectMinId" resultType="com.xftech.cdp.domain.crowd.model.dispatch.MinMaxId">
        select min(Id) minId
        from crowd_detail where crowd_id in (select id from crowd_pack where filter_method = 1)
    </select>

    <select id="selectSubMinId" resultType="com.xftech.cdp.domain.crowd.model.dispatch.MinMaxId">
        select min(Id) minId from ${tableName}
    </select>

    <select id="selectByIdPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_detail where id > #{id} and d_flag = 0
        <if test="crowdExecLogId != null">
            AND crowd_exec_log_id = #{crowdExecLogId}
        </if>
        <if test="crowdExecLogId == null">
            AND crowd_id = #{crowdId}
        </if>
        order by id limit #{pageSize}
    </select>

    <select id="selectBatchByCrowdMaxExecLogId" resultMap="BaseResultMap">
        select id,
               crowd_id,
               user_id,
               app,
               inner_app,
               ab_num,
               app_user_id_last2,
               register_time
        from ${tableName}
        where id &gt; #{crowdDetailId}
          and crowd_exec_log_id = #{crowdMaxExecLogId}
          and d_flag = 0
        limit #{pageSize}
    </select>

    <select id="hasUserRecord" resultMap="BaseResultMap">
        select id,
               crowd_id,
               user_id,
               app,
               inner_app,
               ab_num,
               app_user_id_last2,
               register_time
        from ${tableName}
        where user_id = #{userId}
          and crowd_exec_log_id = #{crowdExecLogId}
          and d_flag = 0
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo" useGeneratedKeys="true">
        insert into crowd_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="app != null">
                app,
            </if>
            <if test="innerApp != null">
                inner_app,
            </if>
            <if test="crowdExecLogId != null">
                crowd_exec_log_id,
            </if>
            <if test="abNum != null">
                ab_num,
            </if>
            <if test="appUserIdLast2 != null">
                app_user_id_last2,
            </if>
            <if test="registerTime != null">
                register_time,
            </if>
            <if test="dFlag != null">
                d_flag,
            </if>
            created_time,
            updated_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="app != null">
                #{app,jdbcType=VARCHAR},
            </if>
            <if test="innerApp != null">
                #{innerApp,jdbcType=VARCHAR},
            </if>
            <if test="crowdExecLogId != null">
                #{crowdExecLogId,jdbcType=BIGINT},
            </if>
            <if test="abNum != null">
                #{abNum,jdbcType=VARCHAR},
            </if>
            <if test="appUserIdLast2 != null">
                #{appUserIdLast2,jdbcType=INTEGER},
            </if>
            <if test="registerTime != null">
                #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                #{dFlag,jdbcType=INTEGER},
            </if>
            now(),
            now(),
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo">
        update crowd_detail
        <set>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="app != null">
                app = #{app,jdbcType=VARCHAR},
            </if>
            <if test="innerApp != null">
                inner_app = #{innerApp,jdbcType=VARCHAR},
            </if>
            <if test="crowdExecLogId != null">
                crowd_exec_log_id = #{crowdExecLogId,jdbcType=BIGINT},
            </if>
            <if test="abNum != null">
                ab_num = #{abNum,jdbcType=VARCHAR},
            </if>
            <if test="appUserIdLast2 != null">
                app_user_id_last2 = #{appUserIdLast2,jdbcType=INTEGER},
            </if>
            <if test="registerTime != null">
                register_time = #{registerTime,jdbcType=TIMESTAMP},
            </if>
            updated_time = now()
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="clearData">
        delete from crowd_detail
        where created_time &lt;= #{sinceTime} and crowd_id in
        <foreach collection="crowdIds" item="it" open="(" close=")" separator=",">
            #{it}
        </foreach>
    </delete>

    <delete id="clearDataByExecLogId">
        delete from crowd_detail
        where id between #{minId} and #{maxId} and crowd_id in (select id from crowd_pack where filter_method = 1)
    </delete>

    <delete id="clearSubData">
        delete from ${tableName} where id between #{minId} and #{maxId}
    </delete>

    <delete id="deleteByCrowdId">
        update crowd_detail
        set d_flag = 1
        where crowd_id = #{crowdId}
    </delete>

    <delete id="deleteById">
        update crowd_detail
        set d_flag = 1
        where id = #{id}
    </delete>

    <update id="updateFlagByCrowdIdAndExecLogId">
        update crowd_detail
        set d_flag = #{dFlag}
        where crowd_id = #{crowdId}
          and crowd_exec_log_id = #{crowdExecLogId}
          and d_flag = #{oriDflag}
    </update>
</mapper>
