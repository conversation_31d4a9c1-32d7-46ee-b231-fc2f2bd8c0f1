<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="flowCtrlInterceptionLogMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlInterceptionLogDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="flowCtrlId" column="flow_ctrl_id" jdbcType="BIGINT"/>
        <result property="strategyId" column="strategy_id" jdbcType="BIGINT"/>
        <result property="strategyChannelId" column="strategy_channel_id" jdbcType="BIGINT"/>
        <result property="interceptionTime" column="interception_time" jdbcType="TIMESTAMP"/>
        <result property="messageId" column="message_id" jdbcType="VARCHAR"/>
        <result property="triggerDatetime" column="trigger_datetime" jdbcType="TIMESTAMP"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result column="market_channel" jdbcType="SMALLINT" property="marketChannel" />
    </resultMap>

    <resultMap id="FlowCtrlGroupNum" type="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlGroupNum">
        <result property="strategyChannelId" column="strategy_channel_id" jdbcType="BIGINT"/>
        <result property="userNum" column="user_num" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,flow_ctrl_id,
        strategy_id,strategy_channel_id,interception_time,message_id,trigger_datetime,
        created_time,updated_time,market_channel
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from flow_ctrl_interception_log
        where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlInterceptionLogDo"
            useGeneratedKeys="true">
        insert into flow_ctrl_interception_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="flowCtrlId != null">flow_ctrl_id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyChannelId != null">strategy_channel_id,</if>
            <if test="interceptionTime != null">interception_time,</if>
            <if test="messageId != null">message_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="triggerDatetime != null">trigger_datetime,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="bizEventType != null">biz_event_type,</if>
            <if test="newFlag != null">new_flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="flowCtrlId != null">#{flowCtrlId,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="strategyChannelId != null">#{strategyChannelId,jdbcType=BIGINT},</if>
            <if test="interceptionTime != null">#{interceptionTime,jdbcType=TIMESTAMP},</if>
            <if test="messageId != null">#{messageId,jdbcType=VARCHAR},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=SMALLINT},</if>
            <if test="triggerDatetime != null">#{triggerDatetime,jdbcType=TIMESTAMP},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="bizEventType != null">#{bizEventType,jdbcType=VARCHAR},</if>
            <if test="newFlag != null">#{newFlag,jdbcType=SMALLINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlInterceptionLogDo">
        update flow_ctrl_interception_log
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="flowCtrlId != null">
                flow_ctrl_id = #{flowCtrlId,jdbcType=BIGINT},
            </if>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="strategyChannelId != null">
                strategy_channel_id = #{strategyChannelId,jdbcType=BIGINT},
            </if>
            <if test="interceptionTime != null">
                interception_time = #{interceptionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="messageId != null">
                message_id = #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="triggerDatetime != null">
                trigger_datetime = #{triggerDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 策略分组流控数据统计 -->
    <select id="countStrategyGroupFlowCtrl" resultType="integer">
        select count(distinct user_id) as user_num
        from flow_ctrl_interception_log
        where strategy_id = #{strategyId}
        <if test="strategyChannelIds !=null and strategyChannelIds.size()>0">
            and strategy_channel_id in
            <foreach collection="strategyChannelIds" item="strategyChannelId" open="(" close=")" separator=",">
                #{strategyChannelId}
            </foreach>
        </if>
          and trigger_datetime &gt;= #{startDate}
          and trigger_datetime &lt; #{endDate}
    </select>

</mapper>
