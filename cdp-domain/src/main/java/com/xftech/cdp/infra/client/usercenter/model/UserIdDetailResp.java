package com.xftech.cdp.infra.client.usercenter.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/6 18:14
 */
@Data
public class UserIdDetailResp {


   /* @JsonProperty("id")
    @JSONField(name = "id")
    private Long appUserId;
    @JsonProperty("app")
    @JSONField(name = "app")
    private String app;*/
/*
    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    private String innerApp;*/

    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;

    /*@JsonProperty("mobile_protyle")
    @JSONField(name = "mobile_protyle")
    private String mobileProtyle;*/


}
