package com.xftech.cdp.infra.client.ads.model.resp;

import lombok.Data;

import java.util.Objects;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/27 10:41
 */
@Data
public class BaseAdsResponse<T> {
    /**
     * 必须
     * 请求流水号，同请求参数
     */
    private String requestId;
    /**
     * 必须
     * 接口名称
     */
    private String apiName;
    /**
     * 必须
     * 时间戳
     */
    private Long timestamp;
    /**
     * 必须
     * 200	success
     * 100	failed
     * 1001	ua校验不通过
     * 1002	请求参数ua为空
     * 1003	签名验证不通过
     * 1004	请求参数异常
     * 1005	请求参数为空
     * 500	系统异常
     * 5001	空指针异常
     * 5002	格式转换错误
     * -1	系统繁忙，请稍后再试....
     */
    private Integer code;
    /**
     * 必须
     * 响应描述
     */
    private String msg;
    /**
     * 响应实体
     */
    private T payload;

    public boolean isSuccess() {
        return Objects.equals(code, 200);
    }
}
