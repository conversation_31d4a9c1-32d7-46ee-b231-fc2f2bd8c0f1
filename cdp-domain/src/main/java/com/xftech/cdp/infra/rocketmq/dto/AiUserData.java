/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ AiUserData, v 0.1 2024/8/21 11:43 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiUserData implements Serializable {
    private static final long serialVersionUID = 161001458819691372L;

    private Long userNo;

    private String app;

    /**
     * 参数
     */
    private Map<String, Object> params;

    public AiUserData(Long userNo, String app) {
        this.userNo = userNo;
        this.app = app;
    }

    public AiUserData(CrowdDetailDo crowdDetailDo) {
        this.userNo = crowdDetailDo.getUserId();
        this.app = crowdDetailDo.getApp();
    }
}