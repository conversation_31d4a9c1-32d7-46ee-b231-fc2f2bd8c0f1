package com.xftech.cdp.infra.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/10/25 13:34
 */
public class SysConstants {
    /**
     * 本系统UA
     */
    public static final String CURRENT_SYS_UA = "xyf-cdp";


    @Getter
    @AllArgsConstructor
    public enum SignerEnum {
        /**
         * 默认
         */
        TEST("test", "123456"),
        /**
         * 电销
         */
        TELE_MARKETING("xyf-telemarketing-system", "540B244B52E06862"),

        // xyf-app-manage
        XYF_APP_MANAGE("xyf-app-manage", "xyf-app-manage"),
        // xyf-app-api
        XYF_APP_API("xyf-app-api","xyf-app-api"),
        //  psenginecore
        PSENGINECORE("psenginecore","psenginecore"),

        PRDOCCORE("prdocccore", "prdocccore"),

        NONE("none", "none"),
        
        ;

        private final String ua;

        private final String key;
    }
}
