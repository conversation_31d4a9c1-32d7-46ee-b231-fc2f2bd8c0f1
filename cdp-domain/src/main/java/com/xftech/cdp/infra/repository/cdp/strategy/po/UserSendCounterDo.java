package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.util.Date;

/**
 * Database Table Remarks:
 *   用户营销推送计数
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table user_send_counter
 */
public class UserSendCounterDo {
    /**
     * Database Column Remarks:
     *   自增ID主键
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   APP用户ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.app_user_id
     *
     * @mbg.generated
     */
    private Long appUserId;

    /**
     * Database Column Remarks:
     *   手机号
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.mobile
     *
     * @mbg.generated
     */
    private String mobile;

    /**
     * Database Column Remarks:
     *   策略ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.strategy_id
     *
     * @mbg.generated
     */
    private Long strategyId;

    /**
     * Database Column Remarks:
     *   营销渠道，0: 不营销，1:短信，2:电销，3:优惠券
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.market_channel
     *
     * @mbg.generated
     */
    private Short marketChannel;

    /**
     * Database Column Remarks:
     *   yyyyMMdd
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.date_value
     *
     * @mbg.generated
     */
    private Integer dateValue;

    /**
     * Database Column Remarks:
     *   总推送数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.sum_value
     *
     * @mbg.generated
     */
    private Integer sumValue;

    /**
     * Database Column Remarks:
     *   回执失败数量
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.failed_value
     *
     * @mbg.generated
     */
    private Integer failedValue;

    /**
     * Database Column Remarks:
     *   1-删除 0-没有
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.d_flag
     *
     * @mbg.generated
     */
    private Short dFlag;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_send_counter.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.id
     *
     * @return the value of user_send_counter.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.id
     *
     * @param id the value for user_send_counter.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.app_user_id
     *
     * @return the value of user_send_counter.app_user_id
     *
     * @mbg.generated
     */
    public Long getAppUserId() {
        return appUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.app_user_id
     *
     * @param appUserId the value for user_send_counter.app_user_id
     *
     * @mbg.generated
     */
    public void setAppUserId(Long appUserId) {
        this.appUserId = appUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.mobile
     *
     * @return the value of user_send_counter.mobile
     *
     * @mbg.generated
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.mobile
     *
     * @param mobile the value for user_send_counter.mobile
     *
     * @mbg.generated
     */
    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.strategy_id
     *
     * @return the value of user_send_counter.strategy_id
     *
     * @mbg.generated
     */
    public Long getStrategyId() {
        return strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.strategy_id
     *
     * @param strategyId the value for user_send_counter.strategy_id
     *
     * @mbg.generated
     */
    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.market_channel
     *
     * @return the value of user_send_counter.market_channel
     *
     * @mbg.generated
     */
    public Short getMarketChannel() {
        return marketChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.market_channel
     *
     * @param marketChannel the value for user_send_counter.market_channel
     *
     * @mbg.generated
     */
    public void setMarketChannel(Short marketChannel) {
        this.marketChannel = marketChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.date_value
     *
     * @return the value of user_send_counter.date_value
     *
     * @mbg.generated
     */
    public Integer getDateValue() {
        return dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.date_value
     *
     * @param dateValue the value for user_send_counter.date_value
     *
     * @mbg.generated
     */
    public void setDateValue(Integer dateValue) {
        this.dateValue = dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.sum_value
     *
     * @return the value of user_send_counter.sum_value
     *
     * @mbg.generated
     */
    public Integer getSumValue() {
        return sumValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.sum_value
     *
     * @param sumValue the value for user_send_counter.sum_value
     *
     * @mbg.generated
     */
    public void setSumValue(Integer sumValue) {
        this.sumValue = sumValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.failed_value
     *
     * @return the value of user_send_counter.failed_value
     *
     * @mbg.generated
     */
    public Integer getFailedValue() {
        return failedValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.failed_value
     *
     * @param failedValue the value for user_send_counter.failed_value
     *
     * @mbg.generated
     */
    public void setFailedValue(Integer failedValue) {
        this.failedValue = failedValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.d_flag
     *
     * @return the value of user_send_counter.d_flag
     *
     * @mbg.generated
     */
    public Short getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.d_flag
     *
     * @param dFlag the value for user_send_counter.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Short dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.created_time
     *
     * @return the value of user_send_counter.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.created_time
     *
     * @param createdTime the value for user_send_counter.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_send_counter.updated_time
     *
     * @return the value of user_send_counter.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_send_counter.updated_time
     *
     * @param updatedTime the value for user_send_counter.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }
}