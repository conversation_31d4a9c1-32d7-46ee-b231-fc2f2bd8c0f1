package com.xftech.cdp.infra.client.ads.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/4 11:39
 */
@Data
@AllArgsConstructor
public class StrategyIndexReq {
    /**
     * 用户id（一个或者一批逗号隔开）
     */
    private List<Long> appUserId;
    /**
     * 策略id（一个或者多个逗号隔开）
     */
    private List<String> strategyId;

    /**
     * 渠道id
     */
    private List<Integer> marketChannel;

    /**
     * 开始日期（yyyy-MM-dd），包括开始日期这一天
     */
    private LocalDate startTime;
    /**
     * 结束日期（yyyy-MM-dd)，包括结束日期这一天
     */
    private LocalDate endTime;
}
