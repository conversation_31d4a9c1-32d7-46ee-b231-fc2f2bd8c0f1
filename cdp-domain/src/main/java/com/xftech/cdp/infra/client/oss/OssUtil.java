package com.xftech.cdp.infra.client.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.xftech.cdp.infra.client.oss.config.OssConfig;
import com.xftech.cdp.infra.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Date;

/**
 * <AUTHOR> wanghu
 * @since : 2021/10/29 11:05:39
 */
@Component
public class OssUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(OssUtil.class);
    private static final Long EXPIRE_TIME = 90 * 24 * 60 * 60L;
    @Autowired
    private OssConfig ossConfig;
    private OSS ossClient;

    /**
     * 获取oss
     *
     * @return
     */
    public OSS getOSSClient() {
        return new OSSClientBuilder().build(ossConfig.getEndpoint(), ossConfig.getAccessKeyId(), ossConfig.getAccessKeySecret());
    }

    /**
     * 上传文件流
     */
    public void upload(String key, InputStream is) {
        try {
            ossClient = getOSSClient();
            LOGGER.info("oss准备上传到 {}", key);
            long startTime = System.currentTimeMillis();
            if (!ossClient.doesBucketExist(ossConfig.getBucketName())) {
                LOGGER.info("创建 bucketName");
                ossClient.createBucket(ossConfig.getBucketName());
            }
            ObjectMetadata objectMeta = new ObjectMetadata();
            PutObjectResult putObjectResult = ossClient.putObject(ossConfig.getBucketName(), key, is, objectMeta);
            long endTime = System.currentTimeMillis();
            LOGGER.info("上传 tag = {}, path = {}, 共花费时间约：{} ms", putObjectResult.getETag(), key, endTime - startTime);
        } catch (Exception e) {
            LOGGER.error("oss准备上传到 path = {}, 异常", key, e);
            throw new BizException("oss上传文件异常" + e.getMessage());
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            } catch (IOException e) {
                LOGGER.error("OssUtil.upload close stream error");
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取文件访问路径
     */
    public String getOssUrl(String key) {
        String accessUrl = "";
        try {
            ossClient = getOSSClient();
            long expireEndTime = System.currentTimeMillis() + EXPIRE_TIME * 1000;
            Date expiration = new Date(expireEndTime);
            if (StringUtils.isNotBlank(key)) {
                URL url = ossClient.generatePresignedUrl(ossConfig.getBucketName(), key, expiration);
                accessUrl = url.toString();
                LOGGER.info("====>>>> url = {}", accessUrl);
            }
        } catch (Exception e) {
            LOGGER.error("==>>>>>>   OSS getOssUrl 异常 e = ", e);
        } finally {
            try {
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            } catch (Exception e) {
                LOGGER.error("OssUtil.getOssUrl close stream error");
                e.printStackTrace();
            }
        }
        return accessUrl;
    }

    /**
     * 文件下载
     */
    public void downloadFile(HttpServletResponse response, String fileName, String key) {
        BufferedInputStream is = null;
        BufferedOutputStream os = null;
        try {
            ossClient = getOSSClient();
            OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), key);
            is = new BufferedInputStream(ossObject.getObjectContent());
            response.setHeader("content-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            os = new BufferedOutputStream(response.getOutputStream());
            byte[] b = new byte[1024];
            int len = -1;
            while ((len = is.read(b)) != -1) {
                os.write(b, 0, len);
            }
        } catch (Exception e) {
            LOGGER.error("OssUtil.downloadFile error");
            e.printStackTrace();
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (os != null) {
                    os.close();
                }
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            } catch (IOException e) {
                LOGGER.error("OssUtil.downloadFile close stream error");
                e.printStackTrace();
            }
        }
    }

    public InputStream getInputStream(String key) {
        ossClient = getOSSClient();
        return ossClient.getObject(ossConfig.getBucketName(), key).getObjectContent();
    }
}
