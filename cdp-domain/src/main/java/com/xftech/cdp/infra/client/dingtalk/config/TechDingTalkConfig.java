/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.infra.client.dingtalk.config;

import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ TechDingTalkConfig, v 0.1 2025/3/10 14:35 xu.fan Exp $
 */
@Data
public class TechDingTalkConfig {

    // 系统实时异常告警
    public static final String TECH_DINGTALK_ROBOT_URL = "https://oapi.dingtalk.com/robot/send?access_token=72bfa4a4f03850fcd9a8265ca67a1f7b5d01612ea28d9b22a57f955378efaef5";

    public static final String TECH_DINGTALK_ROBOT_SECRET = "SECc0b87068d6a813cd3d3704ca956f9ddc7410baaf885448c28a2a93888586d941";

    public static final List<String> TECH_DINGTALK_APP_IDS = Arrays.asList("18217660543", "17854202078", "18305169793");
}
