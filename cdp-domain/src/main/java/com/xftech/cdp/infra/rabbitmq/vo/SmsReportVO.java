package com.xftech.cdp.infra.rabbitmq.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 短信回执VO
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
public class SmsReportVO {
    /**
     * type类型 固定值text-sms
     */
    private String type;
    /**
     * 回执报告
     */
    private Report report;

    @Data
    public static class Report {
        /**
         * 回执唯一标识
         */
        @JSONField(name = "agent_id")
        private String agentId;
        /**
         * 手机号
         */
        private String mobile;

        private Long userNo;
        /**
         * 运营商名称
         */
        private String agent;
        /**
         * 运营商回执code
         */
        @JSONField(name = "delivery_code")
        private String deliveryCode;
        /**
         * 运营商回执信息
         */
        @JSONField(name = "delivery_msg")
        private String deliveryMsg;
        /**
         * 运营商回执时间
         */
        @JSONField(name = "delivery_time")
        private LocalDateTime deliveryTime;
        /**
         * 最终状态，failed 错误，delivered 到达
         */
        @JSONField(name = "final_status")
        private String finalStatus;
        /**
         * 模版id
         */
        @JSONField(name = "template_id")
        private String templateId;
        /**
         * 业务方标识
         */
        private String ua;
        /**
         * 批次号
         */
        @JSONField(name = "batch_num")
        private String batchNum;
    }
}
