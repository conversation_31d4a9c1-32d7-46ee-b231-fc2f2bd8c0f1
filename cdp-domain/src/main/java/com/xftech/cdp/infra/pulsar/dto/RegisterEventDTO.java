package com.xftech.cdp.infra.pulsar.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @<NAME_EMAIL>
 * @date 2023-07-26 15:46
 */
@Data
public class RegisterEventDTO {

    private String mobile;

    @JsonProperty("mobile_protyle")
    @JSONField(name = "mobile_protyle")
    private String mobileProtyle;

    private String type;

    @JsonProperty("source_type")
    @JSONField(name = "source_type")
    private String sourceType;

    private String app;

    @JsonProperty("inner_app")
    @JSONField(name = "inner_app")
    private String innerApp;

    private String os;

    private String channel;

    @JsonProperty("utm_source")
    @JSONField(name = "utm_source")
    private String utmSource;

    @JsonProperty("version_code")
    @JSONField(name = "version_code")
    private String versionCode;

    @JsonProperty("created_time")
    @JSONField(name = "created_time")
    private String createdTime;

    @JsonProperty("created_at")
    @JSONField(name = "created_at")
    private int createdAt;

    private int version;

    @JsonProperty("app_user_id")
    @JSONField(name = "app_user_id")
    private Long appUserId;

    @JsonProperty("credit_user_utm_source")
    @JSONField(name = "credit_user_utm_source")
    private String creditUserUtmSource;

    @JsonProperty("current_utm_source")
    @JSONField(name = "current_utm_source")
    private String currentUtmSource;
}
