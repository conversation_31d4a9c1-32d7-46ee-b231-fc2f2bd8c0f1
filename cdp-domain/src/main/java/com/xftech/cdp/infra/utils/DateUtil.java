package com.xftech.cdp.infra.utils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

@Slf4j
public class DateUtil {
    public static final String NOMAL_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private final static ZoneId zoneId = ZoneId.of("GMT+8");

    public static long getMills(LocalDateTime localDateTime) {
        return localDateTime
                .toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static Date addDays(Date dt, int days) {
        return DateUtils.addDays(dt, days);
    }

    public static int dayOfInt(Date dt) {
        return Integer.valueOf(DateFormatUtils.format(dt, "yyyyMMdd"));
    }

    public static LocalDateTime convert(Date dt) {
        if(dt == null){
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(dt.getTime()), zoneId);
    }

    public static Date convert(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    public static Date convert(String normalFormat) {
        try {
            return DateUtils.parseDate(normalFormat, NOMAL_DATE_FORMAT);
        } catch (Exception ex) {
            log.error("", ex);
        }
        return null;
    }

    public static boolean isToday(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        LocalDate today = LocalDate.now();
        LocalDate date = dateTime.toLocalDate();

        return date.isEqual(today);
    }

    /**
     * @return null
     * <AUTHOR>
     * @description 判断传入的时间距离当前时间是否超过hours小时
     */
    public static boolean isTimePassed(LocalDateTime inputTime, int hours) {
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime targetTime = inputTime.plus(hours, ChronoUnit.HOURS);

        return currentTime.isAfter(targetTime);
    }

    public static String convertStr(Date dt) {
        return DateFormatUtils.format(dt, NOMAL_DATE_FORMAT);
    }

    public static String convertByFormat(Date dt, String format) {
        return DateFormatUtils.format(dt, format);
    }

    public static int dayOfWeek(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);// 此处可换为具体某一时间
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK);
        return weekDay;
    }

    public static int dayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);// 此处可换为具体某一时间
        int monthDay = calendar.get(Calendar.DAY_OF_MONTH);
        return monthDay;
    }

    public static Long getDefaultFinishExecTimeMillis() {
        Instant yesterday = Instant.now().minus(1, ChronoUnit.DAYS);
        return yesterday.toEpochMilli();
    }

    // 获取给定日期的前一天的毫秒数
    public static Long getPreviousDayMillis(LocalDateTime dateTime) {
        Instant instant = dateTime.atZone(ZoneId.systemDefault()).toInstant().minus(1, ChronoUnit.DAYS);
        return instant.toEpochMilli();
    }

    /**
     * 获取相对当前日期偏移后的“当天零点”时间字符串
     * @param num 相对于今天的天数偏移（正数未来，负数过去，0表示今天）
     * @return yyyy-MM-dd 00:00:00 格式的时间字符串
     */
    public static String getRelativeZeroTime(int num) {
        LocalDate targetDate = LocalDate.now().plusDays(num);
        LocalDateTime targetDateTime = targetDate.atStartOfDay(); // 设置为00:00:00
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return targetDateTime.format(formatter);
    }

    /**
     * 判断指定时间+间隔分钟 是否大于当前时间
     *
     * @param baseTime        指定时间（LocalDateTime）
     * @param intervalMinutes 间隔时间（单位：分钟）
     * @return true 表示满足间隔, false 表示不满足间隔
     */
    public static boolean hasEnoughTime(LocalDateTime baseTime, int intervalMinutes) {
        LocalDateTime expireTime = baseTime.plusMinutes(intervalMinutes);
        return expireTime.isAfter(LocalDateTime.now());
    }

    /**
     * 将时间字符串转换为 LocalDateTime
     * @param timeStr 时间字符串，格式如 "yyyy-MM-dd HH:mm:ss"
     * @return LocalDateTime 对象
     */
    public static LocalDateTime parseToLocalDateTime(String timeStr) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(timeStr, formatter);
    }

    /**
     * 计算两个时间之间相差的分钟数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 间隔的分钟数（绝对值）
     */
    public static long calculateMinutesBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return Math.abs(Duration.between(startTime, endTime).toMinutes());
    }

    /**
     * 判断 targetDate 是否在 startDate 和 endDate 区间内（包含边界）
     *
     * @param targetDate 要判断的日期
     * @param startDate  区间开始日期
     * @param endDate    区间结束日期
     * @return true：在区间内，false：不在区间内
     */
    public static boolean isDateInRange(Date targetDate, Date startDate, Date endDate) {
        if (targetDate == null || startDate == null || endDate == null) {
            return false;
        }
        return (targetDate.equals(startDate) || targetDate.after(startDate)) && (targetDate.equals(endDate) || targetDate.before(endDate));
    }

}
