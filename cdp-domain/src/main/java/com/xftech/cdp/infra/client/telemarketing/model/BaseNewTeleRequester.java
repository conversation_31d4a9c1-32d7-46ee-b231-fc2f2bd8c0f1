/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

/**
 *
 * <AUTHOR>
 * @version $ BaseNewTeleRequester, v 0.1 2024/1/12 16:14 ****.**** Exp $
 */
@Setter
@Getter
public class BaseNewTeleRequester {
    @JsonProperty("ua")
    @JSONField(name = "ua")
    private String ua = "xyf-cdp";
    
    @JsonProperty("traceId")
    @JSONField(name = "traceId")
    private String traceId;
    
    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;
    
    @JsonProperty("ts")
    @JSONField(name = "ts")
    private Long ts = System.currentTimeMillis() / 1000L;
    
    
    public void format() {
        this.ua = "xyf-cdp";
        this.traceId = UUID.randomUUID().toString();
    }
}