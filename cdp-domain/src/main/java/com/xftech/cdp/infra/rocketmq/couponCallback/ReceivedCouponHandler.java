/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.couponCallback;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.EventEnum;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.ReceivedCouponMessageDTO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
public class ReceivedCouponHandler implements MessageHandler {

    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return "tp_userassetcore_user_cash_coupon_entity_send_notify";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        try {
            ReceivedCouponMessageDTO messageVO = JSONObject.parseObject(message.toString(), ReceivedCouponMessageDTO.class);
            if (StringUtils.isAnyBlank(messageVO.getApp()) || Objects.isNull(messageVO.getUserId()) || Objects.isNull(messageVO.getCouponType())
                    || Objects.isNull(messageVO.getBelongsTo())) {
                log.info("ReceivedCouponHandler 不符合[用户收到优惠券事件]事件条件 message={}", JSONObject.toJSONString(messageVO));
                return false;
            }
            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            bizEventMessageVO.setBizEventType(EventEnum.RECEIVED_COUPON.getEventType());
            mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
            return true;
        } catch (Exception e) {
            log.info("ReceivedCouponHandler execute error", e);
        }
        return false;
    }

    private BizEventMessageVO transform(ReceivedCouponMessageDTO receivedCouponMessageDTO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setApp(receivedCouponMessageDTO.getApp());
        bizEventMessageVO.setInnerApp(receivedCouponMessageDTO.getInnerApp());
        bizEventMessageVO.setAppUserId(receivedCouponMessageDTO.getUserId());
        bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));

        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setCouponType(receivedCouponMessageDTO.getCouponType());
        extrData.setCouponBelongsTo(receivedCouponMessageDTO.getBelongsTo());

        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }

}