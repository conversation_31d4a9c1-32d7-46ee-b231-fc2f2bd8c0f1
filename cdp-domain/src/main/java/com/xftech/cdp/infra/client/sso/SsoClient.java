package com.xftech.cdp.infra.client.sso;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.xftech.cdp.infra.client.sso.config.SsoConfig;
import com.xftech.cdp.infra.client.sso.model.CheckTokenResponse;
import com.xftech.cdp.infra.client.sso.model.GetTokenRequester;
import com.xftech.cdp.infra.client.sso.model.GetTokenResponse;
import com.xftech.cdp.infra.client.sso.model.ImageCodeResponse;
import com.xftech.cdp.infra.client.sso.model.SendCaptchaRequester;
import com.xftech.cdp.infra.client.sso.model.constants.SsoConstants;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.utils.HeaderUtil;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> wanghu
 * @since : 2022/8/2 15:34:11
 */

@Component
public class SsoClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(SsoClient.class);

    @Resource
    private SsoConfig ssoConfig;
    @Resource
    private HttpClientUtil httpClientUtil;

    public ImageCodeResponse getImageCode() {
        String url = ssoConfig.getSsoHost() + "/" + ssoConfig.getGetImageCodeRoute();
        Map<String, String> headers = headers(null);
        try {
            LOGGER.info("getImageCode,Url:{},Headers:{},Empty Params.", url, headers);
            String result = httpClientUtil.postJson(url, new HashMap<>(), headers);
            LOGGER.info("getImageCode resp,result:{}", result);
            return JSON.parseObject(result, ImageCodeResponse.class);
        } catch (Exception e) {
            LOGGER.error("getImageCode error,Url:{},Headers:{},Empty Params.", url, headers, e);
            throw e;
        }
    }

    public GetTokenResponse sendCaptcha(SendCaptchaRequester requester) {
        String url = ssoConfig.getSsoHost() + "/" + ssoConfig.getSendCaptchaRoute();
        Map<String, String> headers = headers(null);
        try {
            LOGGER.info("sendCaptcha,Url:{},Headers:{},Params:{}.", url, headers, requester);
            String result = httpClientUtil.postJson(url, requester, headers);
            LOGGER.info("sendCaptcha resp,result:{}", result);
            return JSON.parseObject(result, GetTokenResponse.class);
        } catch (Exception e) {
            LOGGER.error("sendCaptcha error,Url:{},Headers:{},Empty Params.", url, headers);
            throw e;
        }
    }

    public GetTokenResponse getToken(GetTokenRequester requester) {
        String url = ssoConfig.getSsoHost() + "/" + ssoConfig.getGetTokenRoute();
        Map<String, String> headers = headers(null);
        try {
            LOGGER.info("getToken,Url:{},Headers:{},Params:{}", url, headers, JSON.toJSONString(requester));
            String result = httpClientUtil.postJson(url, requester, headers);
            GetTokenResponse resp = JSON.parseObject(result, GetTokenResponse.class);
            LOGGER.info("getToken resp,result:{}", result);
            return resp;
        } catch (Exception e) {
            LOGGER.error("getToken error,Url:{},Headers:{},Empty Params.", url, headers);
            throw e;
        }
    }

    public CheckTokenResponse checkToken(String token) {
        String url = ssoConfig.getSsoHost() + "/" + ssoConfig.getCheckTokenRoute();
        Map<String, String> headers = headers(HeaderUtil.getToken());
        headers.put(HeaderUtil.Constants.TOKEN, token);
        try {
            LOGGER.info("checkToken,Url:{},Headers:{},Empty Params.", url, headers);
            String result = httpClientUtil.postJson(url, Maps.newHashMap(), headers);
            CheckTokenResponse resp = JSON.parseObject(result, CheckTokenResponse.class);
            LOGGER.info("checkToken resp,result:{}", result);
            return resp;
        } catch (Exception e) {
            LOGGER.error("checkToken error,Url:{},Headers:{},Empty Params.", url, headers);
            throw e;
        }
    }

    private Map<String, String> headers(String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put(SsoConstants.APP_HEADER_NAME, Constants.SSO_APP_NAME);
        headers.put(SsoConstants.TOKEN_HEADER_NAME, token);
        return headers;
    }

}
