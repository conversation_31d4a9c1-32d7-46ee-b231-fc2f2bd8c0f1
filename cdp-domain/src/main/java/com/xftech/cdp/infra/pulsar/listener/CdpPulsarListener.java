//package com.xftech.cdp.infra.pulsar.listener;
//
//import cn.hutool.core.date.LocalDateTimeUtil;
//import com.xftech.base.common.util.LogUtil;
//import com.xftech.base.log.IUdpLogger;
//import com.xftech.cdp.api.dto.base.TimeFormat;
//import com.xftech.cdp.infra.constant.RedisKeyConstants;
//import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
//import com.xftech.cdp.infra.utils.RedisUtils;
//import com.xyf.pulsar.annotation.PulsarListener;
//import org.apache.pulsar.client.api.MessageId;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//import java.util.concurrent.TimeUnit;
//
//@Component
//public class CdpPulsarListener {
//    private static final IUdpLogger logger = LogUtil.getLogger(CdpPulsarListener.class);
//
//    @Autowired
//    private RedisUtils redisUtils;
//    @Autowired
//    private CdpPulsarService registerEventService;
//    @Autowired
//    private CdpPulsarService settleSuccessService;
//    @Autowired
//    private CdpPulsarService repaySuccessService;
//    @Autowired
//    private CdpPulsarService riskSingleAdjCarService;
//    @Autowired
//    private CdpPulsarService riskSingleAdjSeameService;
//    @Autowired
//    private CdpPulsarService riskSingleAdjBusinessService;
//    @Autowired
//    private CdpPulsarService loanFinalFailedService;
//    @Autowired
//    private CdpPulsarService riskActivationService;
//    @Autowired
//    private CdpPulsarService trackingLoanApplyView;
//    @Autowired
//    private CdpPulsarService trackingCardClick;
//    @Autowired
//    private CdpPulsarService riskTempCreditService;
//    @Autowired
//    private CdpPulsarService loanSuccessService;
//
//    @PulsarListener(topic = "${pulsar.cdp.register.topic}", subscriptionName = "${pulsar.cdp.register.subscriptionName}")
//    public void listenerRegister(String eventMessage, MessageId messageId) {
//        logger.info("listenerRegister, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            registerEventService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("注册事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.settleSuccess}", subscriptionName = "${pulsar.cdp.subscriptionName.settleSuccess}")
//    public void listenerSettleSuccess(String eventMessage, MessageId messageId) {
//        logger.info("listenerSettleSuccess, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            settleSuccessService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("结清（借据级）事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.repaySuccess}", subscriptionName = "${pulsar.cdp.subscriptionName.repaySuccess}")
//    public void listenerRepaySuccess(String eventMessage, MessageId messageId) {
//        logger.info("listenerRepaySuccess, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            repaySuccessService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("还款成功事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.riskSingleAdjCar}", subscriptionName = "${pulsar.cdp.subscriptionName.riskSingleAdjCar}")
//    public void listenerRiskSingleAdjCar(String eventMessage, MessageId messageId) {
//        logger.info("listenerRiskSingleAdjCar, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            riskSingleAdjCarService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("单人调额-车辆信息事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.riskSingleAdjSeame}", subscriptionName = "${pulsar.cdp.subscriptionName.riskSingleAdjSeame}")
//    public void listenerRiskSingleAdjSeame(String eventMessage, MessageId messageId) {
//        logger.info("listenerRiskSingleAdjSeame, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            riskSingleAdjSeameService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("单人调额-芝麻分事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.riskSingleAdjBusiness}", subscriptionName = "${pulsar.cdp.subscriptionName.riskSingleAdjBusiness}")
//    public void listenerRiskSingleAdjBusiness(String eventMessage, MessageId messageId) {
//        logger.info("listenerRiskSingleAdjBusiness, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            riskSingleAdjBusinessService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("单人调额-营业执照事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.loanFinalFailed}", subscriptionName = "${pulsar.cdp.subscriptionName.loanFinalFailed}")
//    public void listenerLoanFinalFailed(String eventMessage, MessageId messageId) {
//        logger.info("loanFinalFailed, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            loanFinalFailedService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("loanFinalFailed事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.riskActivation}", subscriptionName = "${pulsar.cdp.subscriptionName.riskActivation}")
//    public void listenerRiskActivation(String eventMessage, MessageId messageId) {
//        logger.info("riskActivation, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            riskActivationService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("riskActivation事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.trackingLoanApplyView}", subscriptionName = "${pulsar.cdp.subscriptionName.trackingLoanApplyView}")
//    public void listenerTrackingLoanApplyView(String eventMessage, MessageId messageId) {
//        logger.info("Tracking_LoanApplyView, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            trackingLoanApplyView.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("Tracking_LoanApplyView事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.trackingCardClick}", subscriptionName = "${pulsar.cdp.subscriptionName.trackingCardClick}")
//    public void listenerTrackingCardClick(String eventMessage, MessageId messageId) {
//        logger.info("Tracking_CardClick, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            trackingCardClick.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("Tracking_CardClick事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.riskTempCredit}", subscriptionName = "${pulsar.cdp.subscriptionName.riskTempCredit}")
//    public void listenerRiskTempCredit(String eventMessage, MessageId messageId) {
//        logger.info("RiskTempCredit, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            riskTempCreditService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("RiskTempCredit事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    @PulsarListener(topic = "${pulsar.cdp.topic.loanSuccess}", subscriptionName = "${pulsar.cdp.subscriptionName.loanSuccess}")
//    public void listenerLoanSuccess(String eventMessage, MessageId messageId) {
//        logger.info("LoanSuccess, messageId={}, eventMessage={}", messageId, eventMessage);
//        try {
//            if (verifyMessageId(messageId.toString())) {
//                logger.warn("重复事件，messageId={}", messageId.toString());
//                return;
//            }
//            loanSuccessService.consumer(messageId.toString(), eventMessage);
//        } catch (Exception e) {
//            logger.warn("LoanSuccess事件消费异常，messageId={}", messageId.toString(), e);
//        }
//    }
//
//    /**
//     * messageId加锁，默认10分钟
//     *
//     * @param messageId 消息ID
//     * @return true-获取锁成功，false-获取锁失败
//     */
//    public boolean verifyMessageId(String messageId) {
//        String key = String.format(RedisKeyConstants.PULSAR_MESSAGE_ID, messageId);
//        String value = LocalDateTimeUtil.format(LocalDateTime.now(), TimeFormat.DATE_TIME);
//        long timeout = RedisUtils.DEFAULT_EXPIRE_SECONDS * 10;
//        return !redisUtils.lock(key, value, timeout, TimeUnit.SECONDS);
//    }
//}
