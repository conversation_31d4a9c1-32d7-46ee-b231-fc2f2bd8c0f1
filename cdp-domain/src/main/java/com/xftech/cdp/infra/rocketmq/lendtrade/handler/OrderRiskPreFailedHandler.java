/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.lendtrade.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.lendtrade.LendTradeOrderFinalStatusVO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

import static com.xftech.cdp.infra.rocketmq.EventEnum.ORDER_RISK_PRE_FAILED;


@Slf4j
@Component
public class OrderRiskPreFailedHandler implements MessageHandler {

    @Resource
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return "tp_lendtrade_order_final_status_failed";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        LendTradeOrderFinalStatusVO messageVO = JSONObject.parseObject(message.toString(), LendTradeOrderFinalStatusVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        if (StringUtils.isNotBlank(bizEventMessageVO.getBizEventType())) {
            mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
            return true;
        } else {
            log.info("OrderRiskPreFailedHandler execute 不符合订单风险预筛失败事件筛选条件, message={}", JSONObject.toJSONString(messageVO));
            return false;
        }
    }

    private BizEventMessageVO transform(LendTradeOrderFinalStatusVO lendTradeOrderFinalStatusVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();

        if (lendTradeOrderFinalStatusVO == null ||  StringUtils.isAnyBlank(lendTradeOrderFinalStatusVO.getFailedCode()) || Objects.isNull(lendTradeOrderFinalStatusVO.getUserNo())
        || StringUtils.isAnyBlank(lendTradeOrderFinalStatusVO.getInnerApp())) {
            return bizEventMessageVO;
        }

        bizEventMessageVO.setAppUserId(lendTradeOrderFinalStatusVO.getUserNo());
        bizEventMessageVO.setApp(lendTradeOrderFinalStatusVO.getApp());
        bizEventMessageVO.setInnerApp(lendTradeOrderFinalStatusVO.getInnerApp());
        bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));

        String failedCode = lendTradeOrderFinalStatusVO.getFailedCode();

        if ((Objects.equals(failedCode, "10001"))) {
            bizEventMessageVO.setBizEventType(ORDER_RISK_PRE_FAILED.getEventType());
        }
        return bizEventMessageVO;
    }
}