package com.xftech.cdp.infra.repository.cdp.marketing.po;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/11
 * @description MarketingActivityInfo
 */
@Data
public class MarketingActivityInfoDo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动开始时间
     */
    private Date validityBegin;

    /**
     * 活动结束时间
     */
    private Date validityEnd;

    /**
     * 活动状态 0:待生效;1:已生效;2:已失效
     */
    private Integer status;

    /**
     * 是否删除 0:未删除;1:已删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

}
