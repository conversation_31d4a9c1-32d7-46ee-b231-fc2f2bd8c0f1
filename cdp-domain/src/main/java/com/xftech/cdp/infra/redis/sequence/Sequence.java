package com.xftech.cdp.infra.redis.sequence;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Component
public class Sequence {


    private static SequenceService staticSnowFlakeSequenceService;
    @Resource
    private SequenceService snowFlake;

    public static long nextId() {
        return staticSnowFlakeSequenceService.getSequenceID(null);
    }

    @PostConstruct
    public void init() {
        staticSnowFlakeSequenceService = snowFlake;
    }

}

