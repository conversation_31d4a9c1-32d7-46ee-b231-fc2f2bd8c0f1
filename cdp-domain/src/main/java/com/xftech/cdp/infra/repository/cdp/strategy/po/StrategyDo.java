package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.repository.Do;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 策略表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@Slf4j
@EqualsAndHashCode(callSuper = false)
public class StrategyDo extends Do {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略名称
     */
    private String name;

   /**用户是否转换 0-否 1-是*/
    private Integer userConvert;

    /**
     * 策略描述
     */
    private String detailDescription;

    /**
     * 是否abTest，0: 否 1: 是
     */
    private Integer abTest;

    /**
     * 分组类型，0：uid倒数两位 1：随机数 2：新随机数
     */
    private Integer abType;

    /**
     * 场景值
     */
    private String bizKey;

    /**
     * 发送规则 0:单次，1:例行
     */
    private Integer sendRuler;

    /**
     * 策略开始时间
     */
    private LocalDateTime validityBegin;

    /**
     * 策略结束时间
     */
    private LocalDateTime validityEnd;

    /**
     * 发送频次
     */
    private String sendFrequency;

    /**
     * 人群包列表 逗号分隔
     */
    private String crowdPackId;

    /**
     * 营销渠道 逗号分隔
     */
    private String marketChannel;
    /**
     * 策略状态， 0: 初始化、1: 执行中、2: 执行成功、3: 暂停中、4: 已结束
     */
    private Integer status;

    /**
     * 更新人
     */
    private String updatedOp;

    private String updatedOpMobile;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


    private String businessType;

    /**
     * 人群分组类型
     */
    private Integer marketCrowdType;

    /**
     * 主键ids
     */
    private List<Long> ids;

    /**
     * 策略分组类型
     */
    private Integer groupType;

    private Long flowCtrlId;

    /**
     * Database Column Remarks:
     *   策略类型 0-默认 1-引擎策略
     *
     * @mbg.generated
     */
    private Integer type;

    /**
     * 营销节点类型
     * 1——消息上报
     * 2——接口调用
     */
    private Integer marketType;

    /**
     * 当marketType为接口调用时的触发场景
     * 取值见CallingSourceEnum.getCode
     */
    private Integer callingSource;

    /**
     * Database Column Remarks:
     *   关联的引擎编号
     */
    private String engineCode;

    private String dispatchConfig;

    private String flowNo;

    private String dispatchType;


    public void verifyStrategy() {
        // 单次刷新
        if (sendRuler == StrategyRulerEnum.ONCE.getCode() && validityBegin.isBefore(LocalDateTime.of(LocalDate.now(), LocalTime.parse("00:00:00")))) {
            throw new CrowdException("策略执行时间错误！");
        }

        // 例行刷新
        if (StrategyRulerEnum.getCycleCodes().contains(sendRuler) && validityEnd.isBefore(LocalDateTime.now()) || validityBegin.isAfter(validityEnd)) {
            throw new CrowdException("策略执行时间错误！");
        }
    }

    public void alarmDingTalk(String dingTalkUrl, List<String> atMobiles) {
        this.alarmDingTalk(dingTalkUrl, atMobiles, null);
    }

    public void alarmDingTalk(String dingTalkUrl, List<String> atMobiles, Exception e) {
        this.alarmDingTalk(dingTalkUrl, atMobiles, null, e);
    }

    public void alarmDingTalk(String dingTalkUrl, List<String> atMobiles, String customMsg, Exception e) {
//        StringBuilder content = new StringBuilder();
//        content.append(String.format("策略编号：%s", getId())).append("\n");
//        content.append(String.format("策略名称：%s", getName())).append("\n");
//        content.append(getMsg(customMsg, e));
//        DingTalkUtil.sendTextToDingTalk(dingTalkUrl, content.toString(), atMobiles, false);
        try {
            DingTalkUtil.dingTalk(dingTalkUrl, getId(), getName(), getMsg(customMsg, e), atMobiles);
        }catch (Exception ex){
            log.error("alarmDingTalk error", ex);
        }
    }

    private String getMsg(String customMsg, Exception e) {
        if (e instanceof StrategyException) {
            StrategyException exception = (StrategyException) e;
            if (exception.getCode() == 500001 || exception.getCode() == 500002 || exception.getCode() == 99999999) {
                return e.getMessage();
            }
        }
        String msg = e == null? "策略执行失败，请尽快排查并操作重试！": e.getMessage();
        return StringUtils.isNotBlank(customMsg) ? customMsg : msg; // 自定义消息优先级最高
    }


    public void executing() {
        this.status = StrategyStatusEnum.EXECUTING.getCode();
    }

    public void execSuccess() {
        this.status = StrategyStatusEnum.SUCCESS.getCode();
    }

    public void execFailure() {
        this.status = StrategyStatusEnum.FAIL.getCode();
    }

    public void execEnded() {
        this.status = StrategyStatusEnum.ENDED.getCode();
    }

    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isEngineStrategy() {
        return Objects.equals(1, type) && StringUtils.isNotEmpty(engineCode);
    }
}
