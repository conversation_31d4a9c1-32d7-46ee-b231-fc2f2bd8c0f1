/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.pulsar.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version $ TrackingCardClickServiceImpl, v 0.1 2023/10/7 17:01 lingang.han Exp $
 */

@Slf4j
@Service("trackingCardClick")
@AllArgsConstructor
public class TrackingCardClickServiceImpl implements CdpPulsarService {

    private StrategyEventDispatchService strategyEventDispatchService;
    private AppConfigService appConfigService;

    @Autowired
    private UserCenterClient userCenterClient;

    @Override
    public Optional<BizEventMessageVO> toBizEventMessageVO(String eventMessage) {
        JSONObject jsonObject = JSON.parseObject(eventMessage);
        String eventId = (String) jsonObject.get("event_id");
        String creditUserId = (String) jsonObject.get("credit_user_id");
        //如果没有event_id,则是一条错误的消息，不做处理
        //如果没有credit_user_id,则是未登录状态的上报信息，不做处理
        if (StringUtils.isBlank(eventId) || StringUtils.isBlank(creditUserId)) {
            log.info("Tracking_CardClick eventMessage is have event_id or credit_user_id, eventMessage:{}", eventMessage);
            return Optional.empty();
        }
        BizEventMessageVO bizEventMessageVO = JSON.parseObject(eventMessage, BizEventMessageVO.class);
        String eventTrackingEventName = appConfigService.getEventTrackingEventName(eventId);
        //从配置中取出event_id对应的event_name
        if (StringUtils.isBlank(eventTrackingEventName)) {
            log.info("eventId can not convert event name,eventId:{}", eventId);
            return Optional.empty();
        }

        //xyf转xyf01
        if (Constants.XYF.equals(bizEventMessageVO.getApp())) {
            UserInfoResp userInfoResp = userCenterClient.getUserByMobile(bizEventMessageVO.getMobile(), Constants.XYF01);
            bizEventMessageVO.setApp(Constants.XYF01);
            bizEventMessageVO.setCreditUserId(userInfoResp.getCreditUserId());
            bizEventMessageVO.setAppUserId(userInfoResp.getCreditUserId());
            bizEventMessageVO.setInnerApp(userInfoResp.getInnerApp());
        }

        bizEventMessageVO.setBizEventType(eventTrackingEventName);
        return Optional.of(bizEventMessageVO);
    }

    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("Tracking_CardClick eventMessage toBizEventMessageVO={}", eventMsg);
            strategyEventDispatchService.prescreen(messageId, eventMsg);
        });
    }
}