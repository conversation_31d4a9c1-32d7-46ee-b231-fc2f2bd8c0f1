package com.xftech.cdp.infra.client.sms.model;

import com.xftech.cdp.domain.strategy.model.enums.StrategyOperateEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 14:48:47
 */
@Getter
@Setter
public class SmsGroupArgs {

    private String type;


    // public SmsGroupArgs(GroupType groupType) {
    //     Objects.requireNonNull(groupType, "Group的类型不能为空.");
    //     this.type = groupType.getType();
    // }
    //
    // public enum GroupType {
    //     SINGLE("single"),
    //     BATCH("batch"),
    //     ALL("all");
    //     private final String type;
    //
    //     GroupType(String type) {
    //         this.type = type;
    //     }
    //
    //     public String getType() {
    //         return type;
    //     }
    //
    //
    // }


}
