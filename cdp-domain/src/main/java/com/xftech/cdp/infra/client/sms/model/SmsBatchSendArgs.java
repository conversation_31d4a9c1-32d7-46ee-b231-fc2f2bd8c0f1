package com.xftech.cdp.infra.client.sms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 16:07:58
 */
@Getter
@Setter
public class SmsBatchSendArgs {

    @JsonProperty("mobiles")
    @JSONField(name = "mobiles")
    private List<String> mobiles;

    @JsonProperty("userNoList")
    @JSONField(name = "userNoList")
    private List<Long> userNoList;

    @JsonProperty("template_id")
    @JSONField(name = "template_id")
    private String templateId;

    @JsonProperty("app")
    @JSONField(name = "app")
    private String app;

    @JsonProperty("inner_app")
    @J<PERSON>NField(name = "inner_app")
    private String innerApp;

    @JsonProperty("batch_num")
    @JSONField(name = "batch_num")
    private String batchNum;

    @JsonProperty("signatureKey")
    @JSONField(name = "signatureKey")
    private String signatureKey;

}
