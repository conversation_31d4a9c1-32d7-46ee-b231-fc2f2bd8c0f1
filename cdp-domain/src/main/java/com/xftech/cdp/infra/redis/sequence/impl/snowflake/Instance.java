package com.xftech.cdp.infra.redis.sequence.impl.snowflake;

import java.io.Serializable;

public class Instance implements Serializable {

    private static final long serialVersionUID = -4447941348186918084L;
    private int workerId;

    private String applicationName;

    private String ip;

    private int port;

    private Long upTime;


    public int getWorkerId() {
        return workerId;
    }

    public void setWorkerId(int workerId) {
        this.workerId = workerId;
    }


    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public Long getUpTime() {
        return upTime;
    }

    public void setUpTime(Long upTime) {
        this.upTime = upTime;
    }

}