package com.xftech.cdp.infra.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * aviator异常
 * <AUTHOR>
 */
@Getter
@Setter
public class AviatorException extends RuntimeException {

    private int code;

    public AviatorException(String message) {
        super(message);
    }

    public AviatorException(int code, String message) {
        super(message);
        this.code = code;
    }

    public AviatorException(String message, Throwable cause) {
        super(message, cause);
    }

    public AviatorException(Throwable cause) {
        super(cause);
    }

}
