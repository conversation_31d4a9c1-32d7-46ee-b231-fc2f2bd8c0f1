package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class DispatchUserDelayDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.strategy_id
     *
     * @mbg.generated
     */
    private Long strategyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.market_channel
     *
     * @mbg.generated
     */
    private Short marketChannel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.group_name
     *
     * @mbg.generated
     */
    private String groupName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.ext_info
     *
     * @mbg.generated
     */
    private String extInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.date_value
     *
     * @mbg.generated
     */
    private Integer dateValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.dispatch_time
     *
     * @mbg.generated
     */
    private Date dispatchTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.status
     *
     * @mbg.generated
     */
    private Short status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.d_flag
     *
     * @mbg.generated
     */
    private Short dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_user_delay.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table dispatch_user_delay
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.id
     *
     * @return the value of dispatch_user_delay.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.id
     *
     * @param id the value for dispatch_user_delay.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.strategy_id
     *
     * @return the value of dispatch_user_delay.strategy_id
     *
     * @mbg.generated
     */
    public Long getStrategyId() {
        return strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.strategy_id
     *
     * @param strategyId the value for dispatch_user_delay.strategy_id
     *
     * @mbg.generated
     */
    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.user_id
     *
     * @return the value of dispatch_user_delay.user_id
     *
     * @mbg.generated
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.user_id
     *
     * @param userId the value for dispatch_user_delay.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.market_channel
     *
     * @return the value of dispatch_user_delay.market_channel
     *
     * @mbg.generated
     */
    public Short getMarketChannel() {
        return marketChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.market_channel
     *
     * @param marketChannel the value for dispatch_user_delay.market_channel
     *
     * @mbg.generated
     */
    public void setMarketChannel(Short marketChannel) {
        this.marketChannel = marketChannel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.group_name
     *
     * @return the value of dispatch_user_delay.group_name
     *
     * @mbg.generated
     */
    public String getGroupName() {
        return groupName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.group_name
     *
     * @param groupName the value for dispatch_user_delay.group_name
     *
     * @mbg.generated
     */
    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.ext_info
     *
     * @return the value of dispatch_user_delay.ext_info
     *
     * @mbg.generated
     */
    public String getExtInfo() {
        return extInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.ext_info
     *
     * @param extInfo the value for dispatch_user_delay.ext_info
     *
     * @mbg.generated
     */
    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo == null ? null : extInfo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.date_value
     *
     * @return the value of dispatch_user_delay.date_value
     *
     * @mbg.generated
     */
    public Integer getDateValue() {
        return dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.date_value
     *
     * @param dateValue the value for dispatch_user_delay.date_value
     *
     * @mbg.generated
     */
    public void setDateValue(Integer dateValue) {
        this.dateValue = dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.dispatch_time
     *
     * @return the value of dispatch_user_delay.dispatch_time
     *
     * @mbg.generated
     */
    public Date getDispatchTime() {
        return dispatchTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.dispatch_time
     *
     * @param dispatchTime the value for dispatch_user_delay.dispatch_time
     *
     * @mbg.generated
     */
    public void setDispatchTime(Date dispatchTime) {
        this.dispatchTime = dispatchTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.status
     *
     * @return the value of dispatch_user_delay.status
     *
     * @mbg.generated
     */
    public Short getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.status
     *
     * @param status the value for dispatch_user_delay.status
     *
     * @mbg.generated
     */
    public void setStatus(Short status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.d_flag
     *
     * @return the value of dispatch_user_delay.d_flag
     *
     * @mbg.generated
     */
    public Short getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.d_flag
     *
     * @param dFlag the value for dispatch_user_delay.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Short dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.create_time
     *
     * @return the value of dispatch_user_delay.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.create_time
     *
     * @param createTime the value for dispatch_user_delay.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_user_delay.updated_time
     *
     * @return the value of dispatch_user_delay.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_user_delay.updated_time
     *
     * @param updatedTime the value for dispatch_user_delay.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dispatch_user_delay
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DispatchUserDelayDo other = (DispatchUserDelayDo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
                && (this.getStrategyId() == null ? other.getStrategyId() == null : this.getStrategyId().equals(other.getStrategyId()))
                && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
                && (this.getMarketChannel() == null ? other.getMarketChannel() == null : this.getMarketChannel().equals(other.getMarketChannel()))
                && (this.getGroupName() == null ? other.getGroupName() == null : this.getGroupName().equals(other.getGroupName()))
                && (this.getExtInfo() == null ? other.getExtInfo() == null : this.getExtInfo().equals(other.getExtInfo()))
                && (this.getDateValue() == null ? other.getDateValue() == null : this.getDateValue().equals(other.getDateValue()))
                && (this.getDispatchTime() == null ? other.getDispatchTime() == null : this.getDispatchTime().equals(other.getDispatchTime()))
                && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
                && (this.getdFlag() == null ? other.getdFlag() == null : this.getdFlag().equals(other.getdFlag()))
                && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
                && (this.getUpdatedTime() == null ? other.getUpdatedTime() == null : this.getUpdatedTime().equals(other.getUpdatedTime()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dispatch_user_delay
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getStrategyId() == null) ? 0 : getStrategyId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getMarketChannel() == null) ? 0 : getMarketChannel().hashCode());
        result = prime * result + ((getGroupName() == null) ? 0 : getGroupName().hashCode());
        result = prime * result + ((getExtInfo() == null) ? 0 : getExtInfo().hashCode());
        result = prime * result + ((getDateValue() == null) ? 0 : getDateValue().hashCode());
        result = prime * result + ((getDispatchTime() == null) ? 0 : getDispatchTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getdFlag() == null) ? 0 : getdFlag().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdatedTime() == null) ? 0 : getUpdatedTime().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dispatch_user_delay
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", strategyId=").append(strategyId);
        sb.append(", userId=").append(userId);
        sb.append(", marketChannel=").append(marketChannel);
        sb.append(", groupName=").append(groupName);
        sb.append(", extInfo=").append(extInfo);
        sb.append(", dateValue=").append(dateValue);
        sb.append(", dispatchTime=").append(dispatchTime);
        sb.append(", status=").append(status);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createTime=").append(createTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}