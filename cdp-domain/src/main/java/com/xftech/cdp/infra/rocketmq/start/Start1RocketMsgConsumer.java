/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.start;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;

import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_biz_report_Start1", topic = "tp_biz_report_Start1", consumeMode = ConsumeMode.CONCURRENTLY)
public class Start1RocketMsgConsumer extends MqConsumerListener<String> {

    @Autowired
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        log.info("Start1RocketMsgConsumer doMessage topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage);
        try {
            handlerSelector.doMessage(topic, messageExt.getTags(), messageExt.getMsgId(), bodyMessage);
        } catch (Exception e) {
            log.error("Start1RocketMsgConsumer doMessage error topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage, e);
        }
    }
}