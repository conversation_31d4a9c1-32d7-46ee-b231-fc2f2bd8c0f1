package com.xftech.cdp.infra.rabbitmq.factory.impl;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.config.SmsMqConfig;
import com.xftech.cdp.infra.rabbitmq.vo.SmsReportVO;
import com.xftech.rabbitmq.UdpMqConsumerManager;
import com.xftech.rabbitmq.build.BindParamBuilder;
import com.xftech.rabbitmq.build.ConnectionBuilder;
import com.xftech.rabbitmq.consumer.TraceBatchConsumer;
import com.xftech.rabbitmq.core.UdpMqConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
public class SmsMqService extends BaseMqService {
    private static final Logger logger = LoggerFactory.getLogger(SmsMqConfig.class);
    public static final String SMS_CONNECTION_01 = "SMS_CONNECTION_01";
    public static final String SMS_CONSUMER_01 = "SMS_CONSUMER_01";

    @Autowired
    private SmsMqConfig smsMqConfig;
    @Autowired
    private MqConsumeService mqConsumeService;

    public void init() throws IOException, TimeoutException {
        // 短信回执-创建连接、消费者
        logger.info("短信回执队列MQ-创建连接、消费者......");
        UdpMqConsumer smsConsumer = this.initConnection(SMS_CONNECTION_01);
        logger.info("短信回执队列MQ-连接创建成功......");
        this.createConsumer(smsConsumer, SMS_CONSUMER_01);
        logger.info("短信回执队列MQ-消费者创建成功......");
    }

    public UdpMqConsumer initConnection(String connectionId) throws IOException, TimeoutException {
        logger.info("queueConfig={}", smsMqConfig);
        ConnectionBuilder builder = new ConnectionBuilder().host(smsMqConfig.getHost())
                .port(smsMqConfig.getPort())
                .username(smsMqConfig.getUsername())
                .password(smsMqConfig.getPassword())
                .vHostName(smsMqConfig.getVirtualHost());
        return  UdpMqConsumerManager.getInstance().createUdpMqConsumer(builder, connectionId);
    }

    public void createConsumer(UdpMqConsumer udpMqConsumer, String channelId) throws IOException, TimeoutException {
        logger.info("queueConfig={}", this);
        BindParamBuilder builder = new BindParamBuilder()
                .exchangeName(smsMqConfig.getSmsReportExchange())
                .queueName(smsMqConfig.getSmsReportQueueName())
                .routingKey(smsMqConfig.getSmsReportRoutingKey())
                .exchangeType(smsMqConfig.getSmsExchangeType());
        Channel channel = udpMqConsumer.bindMqQueue(builder, channelId);
        udpMqConsumer.basicConsume(new TraceBatchConsumer(channel, smsMqConfig.getBatchSize(), smsMqConfig.getTimeout()) {
            @Override
            public void batchHandleDelivery(List<Message> messageList) {
                try {
                    // 业务处理批量消息
                    logger.info("短信批量消费, size={}, list={}", messageList.size(), messageList);
                    List<SmsReportVO> smsReportVOList = messageList.stream().map(message -> {
                        SmsReportVO smsReportVO = null;
                        String body = null;
                        try {
                            body = new String(message.getBody(), "UTF-8");
                            logger.info("消息内容：{}", body);
                            smsReportVO = JSONObject.parseObject(body, SmsReportVO.class);
                        } catch (Exception e) {
                            logger.warn("短信批量消费异常，消息内容：{}", body, e);
                        }
                        return smsReportVO;
                    }).collect(Collectors.toList());
                    // 短信回执消费逻辑
                    mqConsumeService.smsReportProcess(smsReportVOList);
                    // 消息确认
                    long deliveryTag = messageList.get(messageList.size() - 1).getMessageProperties().getDeliveryTag();
                    logger.info("deliveryTag={}, batchSize={}, total={}", deliveryTag, messageList.size());
                    getChannel().basicAck(deliveryTag, true);
                } catch (Exception e) {
                    logger.error("短信批量消费异常", e);
                }
            }
        });
    }
}
