/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.telemarketing.model.resp;

import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ TeleNameConfigResp, v 0.1 2023/10/16 11:23 wancheng.qu Exp $
 */

@Data
public class TeleNameConfigResp extends BaseNewTeleResp<TeleNameConfigResp.ResponseData>{
    @Data
    public static class ResponseData {
        private List<Config> policyType;      //策略类型
        private List<Config> judgeMethod;    //撞库方式
        private List<Config> closeCaseEvent;  //结案事件
        @Data
        public static class Config {
            private String code;
            private String name;

        }



    }



}