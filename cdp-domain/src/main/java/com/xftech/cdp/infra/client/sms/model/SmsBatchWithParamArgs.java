package com.xftech.cdp.infra.client.sms.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Zch
 * @since : 2023-06-08
 */
@Getter
@Setter
public class SmsBatchWithParamArgs {
    private String templateId;

    private String app;

    private String innerApp;

    private String batchNum;

    private List<Sms> smsList;

    private String signatureKey;

    @Data
    public static class Sms {
        private String mobile;

        private Long userNo;
        /**
         * 短信参数
         */
        private Map<String, Object> data;

        public Sms() {
        }

        public Sms(String mobile, Long userNo) {
            this.mobile = mobile;
            this.userNo = userNo;
        }

        public Sms(String mobile, Long userNo, Map<String, Object> data) {
            this.mobile = mobile;
            this.userNo = userNo;
            this.data = data;
        }
    }

}
