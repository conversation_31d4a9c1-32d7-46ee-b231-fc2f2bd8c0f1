package com.xftech.cdp.infra.constant;

/**
 * <AUTHOR>
 * @since 2022/8/3 10:28 AM
 */
public interface RedisKeyConstants {

    String PROJECT = "xyf-cdp:";

    String CROWD_DETAIL_CUR_TABLE_NO = "xyf-cdp:crowd_detail:cur_table_no";

    String CROWD_DETAIL_TABLE = "xyf-cdp:crowd_detail:crowd_detail_%02d";

    String CROWD_PUSH_BATCH = "xyf-cdp:crowd_push_batch:%s";

    String STRATEGY_BLANK_GROUP_EXEC_FLAG = "xyf-cdp:strategy_blank_group:exec_flag:%s";

    String STRATEGY_BLANK_GROUP_FAIL_FLAG = "xyf-cdp:strategy_blank_group:fail_flag:%s";

    String STRATEGY_BLANK_GROUP_SUCC_FLAG = "xyf-cdp:strategy_blank_group:succ_flag:%s";

    String STRATEGY_TODAY_DISPATCH_COUNT = "xyf-cdp:strategy:%s:%s:%s";

    String STRATEGY_DISPATCH_COUNT = "xyf-cdp:strategy_dispatch_count:%s";

    String STRATEGY_DISPATCH_CURRENT_TIME = "xyf-cdp:strategy_dispatch_time:%s:%s";

    String STRATEGY_INSTANT_LABEL_STRATEGY = "xyf-cdp:instant_label:%s:%s:%s";

    String STRATEGY_INSTANT_LABEL_MSG = "xyf-cdp:instant_label:%s";

    String STRATEGY_ENGINE_RATE_LIMIT = "xyf-cdp:engine_rate_limit:%s";

    String STRATEGY_EVENT_META_DATA = "xyf-cdp:event_meta_data";

    String STRATEGY_EVENT_TRIGGER_TIME = "xyf-cdp:strategy_event_trigger_time:%s:%s";

    String STRATEGY_EVENT_TRIGGER_TIME_ALARM = "xyf-cdp:strategy_event_trigger_time_alarm:%s";

    String STRATEGY_EVENT_30MIN_HAS_BUT_NOMATCH = "xyf-cdp:strategy_event_30min_has_but_nomatch:%s:%s";

    String STRATEGY_EVENT_HAS_BUT_NOMATCH_ALARM = "xyf-cdp:strategy_event_has_but_nomatch_alarm:%s";

    String EVENT_CHANNEL_EXEC_LOG = "xyf-cdp:event_channel_exec_log:date:%s:group:%s:channel:%s";

    String ENGINE_EVENT_CHANNEL_EXEC_LOG = "xyf-cdp:templateId_event_channel_exec_log:date:%s:group:%s:groupId:%s:channel:%s:templateId:%s";

    String EVENT_CHANNEL_EXEC_LOG_LOCK = "xyf-cdp:event_channel_exec_log_lock:date:%s:group:%s:channel:%s";

    String ENGINE_EVENT_CHANNEL_EXEC_LOG_LOCK = "xyf-cdp:templateId_event_channel_exec_log:date:%s:group:%s:groupId:%s:channel:%s:templateId:%s";

    String EVENT_CHANNEL_EXEC_LOG_EXEC_COUNT = "xyf-cdp:event_channel_exec_log_exec_count:date:%s:group:%s:channel:%s";

    String EVENT_CHANNEL_EXEC_LOG_SEND_COUNT = "xyf-cdp:event_channel_exec_log_send_count:date:%s:group:%s:channel:%s";

    String EVENT_FILTER_EXEC_LOG = "xyf-cdp:event_filter_exec_log:date:%s:strategy:%s";

    String EVENT_FILTER_EXEC_LOG_LOCK = "xyf-cdp:event_filter_exec_log_Lock:date:%s:strategy:%s";

    String EVENT_FILTER_EXEC_LOG_SEND_COUNT = "xyf-cdp:event_filter_exec_log_send_count:date:%s:strategy:%s";

    String EVENT_FILTER_EXEC_LOG_RECEIVE_COUNT = "xyf-cdp:event_filter_exec_log_receive_count:date:%s:strategy:%s";

    String EVENT_CHANNEL_EXEC_LOG_SUCCESS_COUNT = "xyf-cdp:event_channel_exec_log_success_count:date:%s:strategy:%s:channel:%s:group:%s";

    String EVENT_CHANNEL_EXEC_LOG_FAIL_COUNT = "xyf-cdp:event_channel_exec_log_fail_count:date:%s:strategy:%s:channel:%s:group:%s";

    String EVENT_CHANNEL_EXEC_LOG_USED_COUNT = "xyf-cdp:event_channel_exec_log_used_count:date:%s:strategy:%s:channel:%s:group:%s";

    String SMS_REPORT_MESSAGE = "xyf-cdp:sms_report_message:batch_num:%s:mobile:%s";

    String PUSH_REPORT_MESSAGE = "xyf-cdp:push_report_message:batch_num:%s:user_no:%s";

    String AI_REPORT_MESSAGE = "xyf-cdp:ai_report_message:batch_num:%s:user_no:%s";

    String COUPON_CALLBACK_MESSAGE = "xyf-cdp:coupon_callback_message:batch_num:%s:userNo:%s";

    String CROWD_LATEST_REFRESH_TIME = "xyf-cdp:crowd_latest_refresh_time:%s";
    String CROWD_REFRESH_TIMEOUT_ALARM = "xyf-cdp:crowd_refresh_timeout_alarm:%s";

    String OFFLINE_STRATEGY_CROWD_STATUS_ALARM = "xyf-cdp:offline_strategy_crowd_status_alarm:%s";

    String STRATEGY_END_ALARM = "xyf-cdp:strategy_end_alarm:%s";

    String RANDOM_NUM_ALARM =  "xyf-cdp:random_num_alarm:%s";

    /**
     * %s：messageId
     */
    String BV_HL_MESSAGE_ID = "xyf-cdp:bv_hl_messid:%s";
    String BV_ML_MESSAGE_ID = "xyf-cdp:bv_ml_messid:%s";
    String BV_HL_DL_MESSAGE_ID = "xyf-cdp:bv_hl_dl_messid:%s";
    String BV_ML_DL_MESSAGE_ID = "xyf-cdp:bv_ml_dl_messid:%s";
    String BV_LL_DL_MESSAGE_ID = "xyf-cdp:bv_ll_dl_messid:%s";
    String BV_SMS_MESSAGE_ID = "xyf-cdp:bv_sms_messid:%s";
    String BV_PUSH_MESSAGE_ID = "xyf-cdp:bv_push_messid:%s";
    String BV_LIFERIGHTS_MESSAGE_ID = "xyf-cdp:bv_liferights_messid:%s";

    String BV_AI_MESSAGE_ID = "xyf-cdp:bv_ai_messid:%s";
    String BV_XDAYINTERESTFREE_MESSAGE_ID = "xyf-cdp:bv_xdayinterestfree_messid:%s";
    String BV_TELE_MESSAGE_ID = "xyf-cdp:bv_tele_messid:%s";
    String BV_COUPON_MESSAGE_ID = "xyf-cdp:bv_coupon_messid:%s";
    String BV_NOMARKET_MESSAGE_ID = "xyf-cdp:bv_nomarket_messid:%s";
    String BV_DECISION_MESSAGE_ID = "xyf-cdp:bv_decision_messid:%s";

    String PULSAR_MESSAGE_ID = "xyf-cdp:pulsar_message_id:%s";

    /**
     * StrategyMarketEventConditionDo：%s：strategyId
     */
    String SMEC_LIST = "xyf-cdp:smec_list_strategyId:%s";

    /**
     * StrategyMarketEventDo：%s：bizEventType
     */
    String SME_LIST = "xyf-cdp:sme_list:%s";
    String SME_LIST_STRATEGY_ID = "xyf-cdp:sme_list:s_%d";

    /**
     * StrategyMarketSubEventDo：%s：eventId
     */
    String SMSE_LIST = "xyf-cdp:smse_list:%s";

    String SMSE_LIST_BY_STRATEGY = "xyf-cdp:smse_list:st_%s";

    /**
     * StrategyDo：%s：id
     */
    String STRATEGY_ONE = "xyf-cdp:strategy_one:%s";

    /**
     * StrategyGroupDo：%s：id
     */
    String SG_ONE = "xyf-cdp:sg_one:%s";
    /**
     * StrategyGroupDo：%s：strategyId
     */
    String SG_LIST = "xyf-cdp:sg_list:%s";

    /**
     * StrategyMarketChannelDo：%s：groupId，%s：marketChannel
     */
    String SMC_ONE_GROUPID_CHANNEL = "xyf-cdp:smc_one_groupId_channel:%s_%s";
    /**
     * StrategyMarketChannelDo：%s：strategyGroupId
     */
    String SMC_LIST_GROUPID = "xyf-cdp:smc_list_groupId:%s";
    /**
     * StrategyMarketChannelDo：%s：strategyId
     */
    String SMC_LIST_STRATEGYID = "xyf-cdp:smc_list_strategyId:%s";

    /**
     * FlowCtrlDo：%s：strategyId，%s：marketChannel
     */
    String FC_LIST = "xyf-cdp:fc_list:%s_%s";

    String NEW_FC_LIST = "xyf-cdp:new_fc_list:%s_%s";

    /**
     * UtmSourceInfo：utmSource
     */
    String UTM_SOURCE_INFO = "xyf-cdp:utm_source_info:%s";

    /**
     * singleTemplate：app、templateId
     */
    String SINGLE_TEMPLATE = "xyf-cdp:single_template:%s_%s";

    /**
     * EXCE_TABLE：crowdExceLogId
     */
    String CROWD_EXCE_TABLE = "xyf-cdp:crowd_exec_table:%s";

    /**
     * 刷新元数据的标识
     */
    String REFRESH_META_FLAG = "xyf-cdp:refresh:meta:%s";

    /**
     * 人群包最大执行日志id
     */
    String MAX_CROWD_EXEC_LOG_ID = "xyf-cdp:max_crowd_exec_log_id:%s";

    String MAX_CROWD_EXEC_TIME_ID = "xyf-cdp:max_crowd_exec_time_id:%s";

//    /**
//     * CrowdPackDo：id
//     */
//    String CROWD_PACK = "xyf-cdp:crowd_pack:%s";

    String STRATEGY_DICT_DATA = "xyf-cdp:strategy_dict_data:%s";


    String STAT_REALTIME_STRATEGY_DAY = "cdp_stat_retm_strategy_%s";

    String STRATEGY_CHANNEL_ID_GROUP_NAME = "cdp_strategy_channel_id_%s_group_name";

    /**
     * 触发事件总次数
     */
    String STAT_DECN_SUM = "cdp:stat:decn:sum:%s_%s";
    /**
     * 触发事件总用户数
     */
    String STAT_DECN_USER = "cdp:stat:decn:user:%s_%s";

    /**
     * 事件条件总人次(总次数)
     */
    String STAT_DECN_EVENT_SUM = "cdp:stat:decn:event:sum:%s_%s";

    /**
     * 事件条件过滤用户数（failCode=502）
     */
    String STAT_DECN_USER_EVENT = "cdp:stat:decn:user:event:%s_%s";
    /**
     * 注册时间过滤用户数（failCode=506）
     */
    String STAT_DECN_USER_REGTIME = "cdp:stat:decn:user:regtime:%s_%s";
    /**
     * 离线人群过滤用户数（failCode=503）
     */
    String STAT_DECN_USER_CROWD = "cdp:stat:decn:user:crowd:%s_%s";
    /**
     * 实时标签过滤用户数（failCode=521）
     */
    String STAT_DECN_USER_LABEL = "cdp:stat:decn:user:label:%s_%s";
    /**
     * 实时排除项过滤用户数（failCode=522）
     */
    String STAT_DECN_USER_EXCLUDE = "cdp:stat:decn:user:exclude:%s_%s";
    /**
     * 复筛通过用户数（decisionResult=1）
     */
    String STAT_DECN_USER_PASS = "cdp:stat:decn:user:pass:%s_%s";
    /**
     * 统计流控表分组数据 ${strategyID}_${分组Id}
     */
    String STAT_DECN_USER_FLOW = "cdp:stat:decn:user:flow:%s_%s_%s";

    /**
     * 统计流控表分组数据 ${strategyID}
     */
    String STAT_DECN_FLOW_SUM = "cdp:stat:decn:user:flow:%s_%s";

    /**
     * 统计触达表分组数据 ${strategyID}_${分组Id}
     */
    String STAT_DECN_USER_DISP = "cdp:stat:decn:user:disp:%s_%s_%s";
    /**
     * 统计不营销分组数据 ${strategyID}_${分组Id}
     */
    String STAT_DECN_USER_BLANK = "cdp:stat:decn:user:blank:%s_%s_%s";
    /**
     * 策略渠道
     */
    String STGY_CHNL = "cdp:stgy:chnl:%s";

    /**
     * 进入决策引擎人次
     */
    String STAT_ENGINE_SUM = "cdp:stat:engine:sum:%s_%s";
    /**
     * 进入决策引擎人数
     */
    String STAT_ENGINE_USER = "cdp:stat:engine:user:%s_%s";


    /**
     * 决策结果为营销人次
     */
    String STAT_MARKET_SUM = "cdp:stat:market:sum:%s_%s";
    /**
     * 决策结果为营销人数
     */
    String STAT_MARKET_USER = "cdp:stat:market:user:%s_%s";

    /**
     * 决策结果为不营销人次
     */
    String STAT_UN_MARKET_SUM = "cdp:stat:unmarket:sum:%s_%s";
    /**
     * 决策结果为不营销人数
     */
    String STAT_UN_MARKET_USER = "cdp:stat:unmarket:user:%s_%s";

    /**
     * 排除标签过滤人次(决策结果为营销)
     */
    String STAT_DECN_USER_EXCLUDE_MARKET = "cdp:stat:decn:users:exclude:market:%s_%s";

    /**
     * 排除标签过滤人次(决策结果为不营销)
     */
    String STAT_DECN_USER_EXCLUDE_UN_MARKET = "cdp:stat:decn:users:exclude:unmarket:%s_%s";

    /**
     * 决策引擎调用失败人次
     */
    String STAT_ENGINE_FAIL_SUM = "cdp:stat:engine:sum:fail:%s_%s";
    /**
     * 决策引擎调用失败人数
     */
    String STAT_ENGINE_FAIL_USER = "cdp:stat:engine:user:fail:%s_%s";

    /**
     * 离线引擎策略不进入引擎人数
     */
    String STAT_OFF_ENGINE_NOT_INTO_ENGINE_USER = "cdp:stat:off:engine:not_into_:user:%s_%s";

    String ENGINE_STRATEGY_GROUP_ID_DATA = "xyf-cdp:strategy_group_id_data:%s_%s";
    /**
     * 离线引擎策略标签排除数
     */
    String STAT_OFF_ENGINE_LABEL_EXCLUDE_SUM = "cdp:stat:off:engine:label:exclude:sum:%s_%s";

    /**
     * 离线引擎策略应发用户数
     */
    String STAT_OFF_ENGINE_SEND_SUM = "cdp:stat:off:engine:send:user:%s_%s";

    /**
     * 退款消费锁
     */
    String BALANCE_LOCK_KEY = "cdp:balance:lock:%s_%s";

    /**
     * 人群包分片执行锁
     */
    String CROWD_SLICE_EXEC_LOCK_KEY = "cdp:crowd:slice:exec:lock:%d_%d_%s";

    /**
     * 策略用户幂等性校验
     */
    String STRATEGY_USER_HIS_KEY = "cdp:strategy:user:his:%d_%d_%s_%s";
    /**
     * 策略用户执行统计
     */
    String STRATEGY_EXEC_STATISTIC_MAP_KEY = "cdp:exec:statistic:%d_%s";
}

