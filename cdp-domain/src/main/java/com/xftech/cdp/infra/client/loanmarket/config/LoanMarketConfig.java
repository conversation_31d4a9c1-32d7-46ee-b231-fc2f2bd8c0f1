package com.xftech.cdp.infra.client.loanmarket.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-05-16
 */
@RefreshScope
@Component
@Setter
@Getter
public class LoanMarketConfig {

    @Value("${cdp.loanMarket.host:http://dev-service-api-redxinyong.devxinfei.cn}")
    private String loanMarketHost;

    @Value("${cdp.loanMarket.data.getUtmSourceInfo:data/get-utm-source-info}")
    private String utmSourceInfo;
}
