package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class EventMetaDataDo extends Do {
    private Long id;
    private String eventName;
    private String eventDesc;
    private String operateType;
    private Integer fillType;
    private Integer eventType;
    private String labelValueType;
    private Long parentId;
    private String dictType;

}
