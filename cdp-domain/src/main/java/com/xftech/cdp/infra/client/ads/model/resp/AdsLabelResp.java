package com.xftech.cdp.infra.client.ads.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Data
public class AdsLabelResp {

    private String label;

    private List<Param> params;

    public AdsLabelResp() {
    }

    public AdsLabelResp(String label, List<Param> params) {
        this.label = label;
        this.params = params;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Param {

        private Long appUserId;

        private String mobile;

        private String result;
    }
}
