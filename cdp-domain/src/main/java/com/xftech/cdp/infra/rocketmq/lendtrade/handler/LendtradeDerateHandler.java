/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.lendtrade.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.MessageTopicConstants;
import com.xftech.cdp.infra.rocketmq.dto.LendtradeDeratingRocketMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ LendTradeDeratingHandler, v 0.1 2024/8/16 15:07 lingang.han Exp $
 */

@Slf4j
@Component
public class LendtradeDerateHandler implements MessageHandler {

    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return MessageTopicConstants.LENDTRADE_DERATE;
    }

    @Override
    public boolean execute(String messageId, Object message) {
        LendtradeDeratingRocketMessageVO messageVO = JSONObject.parseObject(message.toString(), LendtradeDeratingRocketMessageVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        bizEventMessageVO.setBizEventType("LendtradeDerate");
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(LendtradeDeratingRocketMessageVO lendtradeDeratingMsg) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setAppUserId(lendtradeDeratingMsg.getUserNo());
        bizEventMessageVO.setApp(lendtradeDeratingMsg.getApp());
        bizEventMessageVO.setInnerApp(lendtradeDeratingMsg.getInnerApp());
        bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(new Date(lendtradeDeratingMsg.getEventTime()), null));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setOriLoanAmt(lendtradeDeratingMsg.getOriLoanAmt());
        extrData.setLoanAmt(lendtradeDeratingMsg.getLoanAmt());
        extrData.setExpirationHour(lendtradeDeratingMsg.getExpirationHour());
        extrData.setFreezeType(lendtradeDeratingMsg.getFreezeType());
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}