package com.xftech.cdp.infra.aviator.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OperateModeEnum {
    STRATEGY(1, "策略模块"),
    CROWD(2, "人群模块"),
    FLOW(3, "画布模块"),
    FLC(4, "流控规则模块"),
    TEMPLATE(5, "模版参数模块"),
    LABEL(6, "标签配置模块"),
    MARKETING(7, "营销活动"),
    ;

    private final int code;
    private final String desc;

    public static OperateModeEnum getInstance(Integer code) {
        for (OperateModeEnum operateModeEnum : OperateModeEnum.values()) {
            if (Objects.equals(operateModeEnum.getCode(), code)) {
                return operateModeEnum;
            }
        }
        return null;
    }
}
