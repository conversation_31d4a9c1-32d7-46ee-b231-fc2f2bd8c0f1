package com.xftech.cdp.infra.rocketmq;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version v1.0 2024/9/24
 * @description EventEnum
 */
@Getter
@AllArgsConstructor
public enum EventEnum {

    EVENT_CREDENTIAL_STUFFING_APPROVED("CredentialStuffingApproved", "授信成功未首贷撞库"),

    EVENT_CREDENTIAL_STUFFING_NOT_APPROVED("CredentialStuffingNotApproved", "从未授信成功用户撞库"),

    DISTRIBUTION_GRANTED_SUCCESS("DistributionGrantedSuccess", "分发授信成功"),

    ACCESS_CONTROL_DIVERSION("AccessControlDiversion", "风控禁申"),

    SURPRISE_RIGHT("SurpriseRight", "飞享会员月月惊喜中奖"),
    EVENT_API_CREDENTIAL_STUFFING("apiCredentialStuffing","API撞库事件上报"),

    SETTLE_SUCCESS("NewSettleSuccess", "(新)结清(借据级)"),

    REPAY_SUCCESS("NewRepaySuccess", "(新)还款成功"),

    PRE_LOAN_SUBMISSION_SUCCESSFUL("PreLoanSubmissionSuccessful","预借款提交成功"),

    AI_CALL("AICall","AI通话"),

    API_ENTRY("APIEntry","API进件"),

    ORDER_RISK_PRE_FAILED("OrderRiskPreFailed","订单风险预筛失败"),

    RECEIVED_COUPON("ReceivedCoupon","用户收到优惠券事件"),

    ORDER_RISK_PASSED("OrderRiskPassed","订单风险通过事件"),

    TELE_INVENTORY("TeleInventory","电销入库"),

    ;

    /**
     * 事件类型
     */
    private final String eventType;

    /**
     * 事件名称
     */
    private final String eventName;

}
