package com.xftech.cdp.infra.repository.cdp.strategy.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportDailyTaskDo implements Serializable {
    private static final long serialVersionUID = -470781453962984565L;

    /**
     * pk
     */
    private Long id;

    /**
     * 统计日期
     */
    private Date date;

    /**
     * 类型：1.策略;2.人群
     */
    private Integer type;

    /**
     * 最近刷新时间
     */
    private Date refreshTime;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 修改时间
     */
    private Date updatedTime;

    /**
     * 逻辑删除标识
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;
}