/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.telemkt;

import com.google.gson.Gson;
import com.xftech.cdp.api.dto.req.external.TeleImportResultReq;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 *  新电销回执消息
 * <AUTHOR>
 * @version $ TeleInventoryRocketMqConsumer, v 0.1 2023/10/20 17:10 wancheng.qu Exp $
 */

@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_marketing_telname_enter_result_tele", topic = "tp_marketing_telname_enter_result",consumeMode = ConsumeMode.CONCURRENTLY)
@Slf4j
public class TeleInventoryRocketMqConsumer extends MqConsumerListener<String> {

    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    public void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("TeleInventoryRocketMqConsumerEnable")) {
            log.info("TeleInventoryRocketMqConsumer doMessage topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, "tele", messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("TeleInventoryRocketMqConsumer doMessage error topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }
}