/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.telemkt;

import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.xinfei.xfframework.common.JsonUtil;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version $ TelePushMqProducer, v 0.1 2024/1/12 18:12 ****.**** Exp $
 */

@Component
@Slf4j
public class TelePushMqProducer {
    @Autowired
    private MqTemplate mqTemplate;
    
    public SendResult asyncSend(TelePushArgs telePushArgs) {
        log.info("TelePushMqProducer telePushArgs:{}", JsonUtil.toJson(telePushArgs));
        try {
            return mqTemplate.syncSend("tp_telemkt_name_push", JsonUtil.toJson(telePushArgs));
        } catch (Exception e) {
            log.error("TelePushMqProducer error", e);
        }
        return null;
    }
}