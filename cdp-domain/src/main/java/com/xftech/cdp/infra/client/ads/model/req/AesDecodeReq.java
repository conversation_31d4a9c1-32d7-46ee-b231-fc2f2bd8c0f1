package com.xftech.cdp.infra.client.ads.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.poi.ss.formula.functions.T;


@Data
@AllArgsConstructor
public class AesDecodeReq {

    /**
     * * 加密手机号
     */
    @JsonProperty("mobile")
    @JSONField(name = "mobile")
    private String mobile;
    /**
     * 加密身份证
     */
    @JsonProperty("id_card_number")
    @JSONField(name = "id_card_number")
    private String idCardNumber;

}
