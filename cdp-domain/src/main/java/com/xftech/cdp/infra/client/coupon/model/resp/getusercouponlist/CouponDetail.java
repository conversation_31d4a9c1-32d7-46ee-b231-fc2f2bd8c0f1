package com.xftech.cdp.infra.client.coupon.model.resp.getusercouponlist;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class CouponDetail {
    // 优惠券id
    private String coupon_id;
    private String user_id;
    private String coupon_template_id;
    // 优惠券名称
    private String coupon_name;
    private String discount_amount;
    private String discount_rate;
    private String product_periods;
    private String discount_periods;
    private String discount_periods_limit;
    private String discount_category;
    private String discount_rule;
    private String coupon_type;
    // 过期时间
    private Date expired_time;
    private Date started_time;
    private String button_name;
    private List<String> desc;
    // 0:可使用 1:已使用 2:无效
    private String status;
    private Date used_time;
    private String discount_type;
    private String sub_discount_type;
    private String discount_periods_type;
    private String product_type;
    private String lower_price;
    private String app;
    private Date created_time;
    private String activity_id;
    /**
     * 优惠券活动名称
     */
    private String activity_name;
    private String admin_name;
    private String name;
    private String mobile;
    private String use_detail;
    private String jump_url;
    private String jump_type;
    private String actual_discount_amount;
    private List<PeriodInfo> period_info;
    private String coupon_style;
    private List<CouponStyleConfig> coupon_styleConfig;

}