package com.xftech.cdp.infra.rabbitmq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum DecisionResultEnum {
    NONE(0, "(null)"),
    /**
     * 预筛
     */
    /**
     * 事件对应的策略不存在
     */
    EVENT_STRATEGY_NOT_EXIST(500, "事件对应的策略不存在"),
    /**
     * 策略不在有效期内
     */
    STRATEGY_EXPIRE(501, "策略不在有效期内"),
    /**
     * 营销节点限制条件验证不通过(事件条件不通过)
     */
    MARKET_SUB_FAIL(502, "营销节点限制条件验证不通过"),
    /**
     * 离线人群包筛选不通过
     */
    OFFLINE_CROWD_FILTER_FAIL(503, "离线人群包筛选不通过"),
    /**
     * 实时人群筛选不通过(注册时间筛选不通过)
     */
    INSTANT_CROWD_FILTER_FAIL(506, "实时人群筛选不通过"),
    /**
     * 策略状态错误
     */
    STRATEGY_STATUS_ERR(504, "策略状态错误"),
    /**
     * 注册时间配置错误
     */
    REGISTER_TIME_CONFIG_ERR(505, "注册时间配置错误"),
    /**
     * 初筛不通过
     */
    START_FILTER_FAIL(510, "初筛不通过"),
    /**
     * 复筛
     */
    /**
     * 复筛不通过
     */
    REPEAT_FILTER_FAIL(520, "复筛不通过"),
    /**
     * 实时标签条件筛选不通过
     */
    INSTANT_LABEL_FILTER_FAIL(521, "实时标签条件筛选不通过"),
    /**
     * 排除标签条件筛选不通过
     */
    EXCLUDE_LABEL_FILTER_FAIL(522, "排除标签条件筛选不通过"),

    /**
     * 人群包-渠道不营销过滤
     */
    /**
     * 首次注册渠道不营销
     */
    MOBILE_UMT_FAIL(601, "首次注册渠道不营销"),
    /**
     * 历史进件渠道不营销
     */
    HISTORY_BORROW_UMT_FAIL(602, "历史进件渠道不营销"),
    /**
     * 最近一次放款成功渠道不营销
     */
    LAST_LOAN_SUCCESS_UMT_FAIL(603, "最近一次放款成功渠道不营销"),


    GROUP_NOT_INTO_ENGINE(1000, "分组不进入决策引擎"),

    // 决策引擎调用失败
    EVENT_ENGINE_FAIL(1001, "决策引擎结果失败"),



    APP_BANNER_NOT_HIT_CROWD_PACK(1005, "app资源位没有命中策略的人群包"),
    APP_BANNER_NOT_HIT_LABELS(1006, "app资源位没有命中策略的实时标签"),
    APP_BANNER_NOT_HIT_AB_GROUP(1007, "app资源位没有命中策略的人群分组"),

    LOAN_OVERLOAD_NOT_HIT_CROWD_PACK(1020, "[贷超]未命中人群包"),
    LOAN_OVERLOAD_NOT_HIT_LABELS(1021, "[贷超]未命中策略的标签"),
    LOAN_OVERLOAD_NOT_HIT_AB_GROUP(1022, "[贷超]未命中策略的人群分组"),
    LOAN_OVERLOAD_GROUP_NOT_EXECUTE(1023, "[贷超]分组不执行"),
    LOAN_OVERLOAD_STRATEGY_BEFORE_EXECUTE(1024, "[贷超]策略未到执行时间"),
    LOAN_OVERLOAD_STRATEGY_AFTER_EXECUTE(1025, "[贷超]策略已过期"),
    LOAN_OVERLOAD_STRATEGY_NOT_ACTIVE(1026, "[贷超]策略不生效"),
    LOAN_OVERLOAD_MARKET_SUB_FAIL(1027, "[贷超]营销节点限制条件验证不通过"),
    LOAN_OVERLOAD_INSTANT_LABEL_FILTER_FAIL(1028, "[贷超]实时标签条件筛选不通过"),
    LOAN_OVERLOAD_EXCLUDE_LABEL_FILTER_FAIL(1029, "[贷超]排除标签条件筛选不通过"),
    

    API_HOLD_NOT_HIT_CROWD_PACK(1050, "[API卡单]未命中人群包"),
    API_HOLD_NOT_HIT_LABELS(1051, "[API卡单]未命中策略的标签"),
    API_HOLD_NOT_HIT_AB_GROUP(1052, "[API卡单]未命中策略的人群分组"),
    API_HOLD_GROUP_NOT_EXECUTE(1053, "[API卡单]分组不执行"),
    API_HOLD_STRATEGY_BEFORE_EXECUTE(1054, "[API卡单]策略未到执行时间"),
    API_HOLD_STRATEGY_AFTER_EXECUTE(1055, "[API卡单]策略已过期"),
    API_HOLD_STRATEGY_NOT_ACTIVE(1056, "[API卡单]策略不生效"),
    API_HOLD_WRONG_STRATEGY_CONFIG(1057, "[API卡单]策略配置异常"),
    API_HOLD_MARKET_SUB_FAIL(1058, "[API卡单]营销节点限制条件验证不通过"),
    API_HOLD_INSTANT_LABEL_FILTER_FAIL(1059, "[API卡单]实时标签条件筛选不通过"),
    API_HOLD_EXCLUDE_LABEL_FILTER_FAIL(1060, "[API卡单]排除标签条件筛选不通过"),

    CROWD_NEW_RANDOM_FAIL(1070, "人群包新随机数验证不通过"),

    SERVER_INNER_ERROR(2222, "服务器内部错误"),

    ;

    private int failCode;

    private String failReason;

    public DecisionResultEnum setFailReason(String failReason) {
        this.failReason = failReason;
        return this;
    }
}
