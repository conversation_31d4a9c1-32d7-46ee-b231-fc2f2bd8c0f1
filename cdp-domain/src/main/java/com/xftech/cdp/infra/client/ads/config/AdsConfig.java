package com.xftech.cdp.infra.client.ads.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/4 11:27
 */
@Getter
@Component
@RefreshScope
public class AdsConfig {

    @Value("${cdp.ads.host}")
    private String host;

    @Value("${cdp.ads.crowdHost}")
    private String crowdHost;

    @Value("${cdp.ads.route.queryMarketStrategyRoute:ads/queryMarketStrategy}")
    private String queryMarketStrategyRoute;

    @Value("${cdp.ads.route.queryMarketChannelRoute:ads/queryMarketChannel}")
    private String queryMarketChannelRoute;

    @Value("${cdp.ads.route.queryMarketStrategyRoute:offline/batchOfflineConfig}")
    private String batchOfflineConfig;
    @Value("${cdp.ads.route.queryMarketChannelRoute:dataservice/getModelParam}")
    private String getModelParam;
    @Value("${cdp.ads.route.aesDecode:aes/decode}")
    private String aesDecode;

    @Value("${cdp.ads.crowd.runCrowdJob:crowd/runCrowdTask}")
    private String runCrowdJob;

    @Value("${cdp.ads.crowd.getCrowdTotal:crowd/queryCrowdJobStatus}")
    private String getCrowdTotal;

    @Value("${cdp.ads.crowd.tabName:ads_user_crowd_detail}")
    private String adsTable;

}
