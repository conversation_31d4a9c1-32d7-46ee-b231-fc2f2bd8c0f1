package com.xftech.cdp.infra.constant;

import com.xftech.cdp.domain.strategy.service.dispatch.offline.impl.StrategyDispatchForAiProntoServiceImpl;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/6 9:40
 */
public interface StrategyDispatchConstants {

    String SMS_SERVICE = "strategyDispatchForSmsService";

    String TELE_SERVICE = "strategyDispatchForTeleService";
    String NEW_TELE_SERVICE = "strategyDispatchForNewTeleService";

    String COUPON_SERVICE = "strategyDispatchForCouponService";

    String PUSH_SERVICE = "strategyDispatchForPushService";

    String AI_PRONTO_SERVICE = "strategyDispatchForAiProntoService";


    String INCREASE_AMOUNT_SERVICE = "strategyDispatchForIncreaseAmountService";

    String X_DAY_INTEREST_FREE_SERVICE = "strategyDispatchForXDayInterestFreeService";

    String LIFE_RIGHTS = "strategyDispatchForLifeRightsService";

    String OFFLINE_ENGINE_SERVICE = "strategyDispatchForOfflineEngineService";

    String API_OPEN_AMOUNT_SERVICE = "strategyAPIOpenAmountService";

    String REMAINING_SUM_RECHARGING = "remainingSumChargingService";

    String NONE_SERVICE = "strategyNoneService";
}
