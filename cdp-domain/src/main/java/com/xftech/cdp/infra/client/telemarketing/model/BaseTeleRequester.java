package com.xftech.cdp.infra.client.telemarketing.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 14:44:39x
 */
@Setter
@Getter
public class BaseTeleRequester<Args> {

    @JsonProperty("ua")
    @JSONField(name = "ua")
    private String ua;

    @JsonProperty("call")
    @JSONField(name = "call")
    private String call;

    @JsonProperty("args")
    @JSONField(name = "args")
    private Args args;

    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;

    @JsonProperty("timestamp")
    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000L;


}
