/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.thread;


import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigChangeListener;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.xftech.cdp.infra.config.AppConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashSet;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version $ DispatchFlcExecutorService, v 0.1 2023/10/11 13:33 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class DispatchFlcExecutor {
    private static int pooloDefaultCoreSize = 8;
    private static int pooloDefaultMaxSize = 20;
    private static String dispatchFlcExecutorCoreSizeKey = "dispatchFlcExecutor.pool.coreSize";
    private static String dispatchFlcExecutorMaxSizeKey = "dispatchFlcExecutor.pool.maxSize";

    private AppConfigService appConfigService;

    public static ExecutorService pool = new ThreadPoolExecutor(pooloDefaultCoreSize, pooloDefaultMaxSize,
            15L, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(16),
            new CustomizableThreadFactory("dispatchFlcExecutor-"),
            new ThreadPoolExecutor.CallerRunsPolicy());

    public static ExecutorService getPool() {
        return pool;
    }


    @PostConstruct
    public void init() throws Exception {
        int coreSize = Integer.parseInt(appConfigService.getPropertyValue(dispatchFlcExecutorCoreSizeKey,
                String.valueOf(pooloDefaultCoreSize)));
        int maxSize = Integer.parseInt(appConfigService.getPropertyValue(dispatchFlcExecutorMaxSizeKey,
                String.valueOf(pooloDefaultMaxSize)));
        setPoolCoreSize(coreSize);
        setPoolMaxSize(maxSize);
        Config config = ConfigService.getAppConfig();
        config.addChangeListener(new ConfigChangeListener() {
                                     @Override
                                     public void onChange(ConfigChangeEvent configChangeEvent) {
                                         for (String key : configChangeEvent.changedKeys()) {
                                             ConfigChange change = configChangeEvent.getChange(key);
                                             if (change != null) {
                                                 if (dispatchFlcExecutorCoreSizeKey.equals(key)) {
                                                     setPoolCoreSize(Integer.parseInt(change.getNewValue()));
                                                 } else if (dispatchFlcExecutorMaxSizeKey.equals(key)) {
                                                     setPoolMaxSize(Integer.parseInt(change.getNewValue()));
                                                 }
                                             }
                                         }
                                     }
                                 }, new HashSet<>(Arrays.asList(dispatchFlcExecutorCoreSizeKey, dispatchFlcExecutorMaxSizeKey))
        );

    }

    public void setPoolCoreSize(int size) {
        if (size <= 0) {
            return;
        }
        if (((ThreadPoolExecutor) pool).getCorePoolSize() != size) {
            log.info("dispatchFlcExecutor, 线程池核心大小重新设置, coreSize = {}", size);
            ((ThreadPoolExecutor) pool).setCorePoolSize(size);
        }
    }

    public void setPoolMaxSize(int size) {
        if (size <= 0) {
            return;
        }
        if (((ThreadPoolExecutor) pool).getMaximumPoolSize() != size) {
            log.info("dispatchFlcExecutor, 线程池最大值重新设置, maxSize = {}", size);
            ((ThreadPoolExecutor) pool).setMaximumPoolSize(size);
        }
    }
}