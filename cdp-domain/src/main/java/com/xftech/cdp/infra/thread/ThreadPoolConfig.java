package com.xftech.cdp.infra.thread;

import com.xftech.base.exception.UdpTranException;
import com.xftech.base.thread.ExecutorManager;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Aven
 */
@Configuration
public class ThreadPoolConfig {

    @Autowired
    private ThreadPoolProperties crowdPoolProperties;

    @Bean("crowdPoolExecutor")
    public ExecutorService crowdPoolExecutor() {
        BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(crowdPoolProperties.getQueueSize());
        return new ThreadPoolExecutorMdcWrapper(crowdPoolProperties.getCorePoolSize(), crowdPoolProperties.getMaximumPoolSize(),
                crowdPoolProperties.getKeepAliveTime(), TimeUnit.SECONDS, workQueue, new DefaultThreadFactory(crowdPoolProperties.getPoolName()), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean("crowdPoolFixedNumberPageExecutor")
    public ExecutorService crowdPoolFixedNumberPageExecutor() {
        BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(crowdPoolProperties.getQueueSize());
        return new ThreadPoolExecutorMdcWrapper(crowdPoolProperties.getCorePoolSize(), crowdPoolProperties.getMaximumPoolSize(),
                crowdPoolProperties.getKeepAliveTime(), TimeUnit.SECONDS, workQueue, new DefaultThreadFactory("crowdPoolFixedNumberPageExecutor"), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean(name = "threadPoolTaskExecutor")
    public ExecutorService threadPoolTaskExecutor() throws UdpTranException {
        return ExecutorManager.getInstance().createExecutor(Integer.MAX_VALUE, "crowd-upload-service");
    }

    @Bean(name = "strategyBlankTaskExecutor")
    public ExecutorService strategyBlankTaskExecutor() throws UdpTranException {
        return ExecutorManager.getInstance().createExecutor(Integer.MAX_VALUE, "strategy-blank-task-executor");
    }

    @Bean(name = "strategyCountTaskExecutor")
    public ExecutorService strategyCountTaskExecutor() throws UdpTranException {
        return ExecutorManager.getInstance().createExecutor(Integer.MAX_VALUE, "strategy-count-task-executor");
    }

    @Bean(name = "refreshRiskScoreTaskExecutor")
    public ExecutorService refreshRiskScoreTaskExecutor() throws UdpTranException {
        return ExecutorManager.getInstance().createExecutor(Integer.MAX_VALUE, "refresh-risk-score-task-executor");
    }

    @Bean(name = "adminTaskExecutor")
    public ExecutorService adminTaskExecutor() throws UdpTranException {
        return ExecutorManager.getInstance().createExecutor(Integer.MAX_VALUE, "adminTaskExecutor");
    }

    @Bean(name = "dispatchCrowdExecutorWrapper")
    public ExecutorService dispatchCrowdExecutorWrapper() {
        return DispatchCrowdExecutor.getPool();
    }

    @Bean(name = "dispatchEngineExecutorWrapper")
    public ExecutorService dispatchEngineExecutorWrapper() {
        return DispatchEngineExecutor.getPool();
    }

    @Bean(name = "appBannerRecommendExecutorWrapper")
    public ExecutorService appBannerRecommendExecutorWrapper() {
        return AppBannerSaveLogsExecutor.getPool();
    }

    @Bean(name = "appBannerExecExecutorWrapper")
    public ExecutorService appBannerExecExecutorWrapper() {
        return AppBannerExecExecutor.getPool();
    }

    @Bean(name = "decideLogExecutorWrapper")
    public ExecutorService decideLogExecutorWrapper() {
        return DecideLogExecutor.getPool();
    }

    @Bean(name = "decideExecExecutorWrapper")
    public ExecutorService decideExecExecutorWrapper() {
        return DecideExecExecutor.getPool();
    }

    @Bean(name = "dispatchUserDelayExecutorWrapper")
    public ExecutorService dispatchUserDelayExecutorWrapper() {
        return DispatchUserDelayExecutor.getPool();
    }

    @Bean(name = "dataFeatureQueryExecutorWrapper")
    public ExecutorService dataFeatureQueryExecutorWrapper() {
        return DataFeatureQueryExecutor.getPool();
    }

    @Bean(name = "kafkaT0EventCountExecutorWrapper")
    public ExecutorService kafkaT0EventCountExecutorWrapper() {
        return KafkaT0EventCountExecutor.getPool();
    }

}
