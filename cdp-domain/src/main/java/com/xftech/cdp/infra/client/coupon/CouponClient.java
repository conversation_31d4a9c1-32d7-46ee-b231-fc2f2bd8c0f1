package com.xftech.cdp.infra.client.coupon;

import java.time.Instant;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import com.xftech.cdp.infra.client.Signer;
import com.xftech.cdp.infra.client.coupon.conig.CouponConfig;
import com.xftech.cdp.infra.client.coupon.model.req.BaseCouponRequester;
import com.xftech.cdp.infra.client.coupon.model.req.CouponActivityListReq;
import com.xftech.cdp.infra.client.coupon.model.req.CouponSendBatchReq;
import com.xftech.cdp.infra.client.coupon.model.req.getactivitycoupon.GetActivityCouponDetailRequest;
import com.xftech.cdp.infra.client.coupon.model.req.getusercouponlist.GetUserCouponListReq;
import com.xftech.cdp.infra.client.coupon.model.req.sendusercoupon.SendUserCouponReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.coupon.model.resp.CouponActivityListResp;
import com.xftech.cdp.infra.client.coupon.model.resp.getactivitycoupon.GetActivityCouponDetailResponse;
import com.xftech.cdp.infra.client.coupon.model.resp.getactivitylist.GetActivityListResp;
import com.xftech.cdp.infra.client.coupon.model.resp.getusercouponlist.GetUserCouponListResp;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CouponClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(CouponClient.class);

    @Resource
    private Signer signer;
    @Autowired
    private CouponConfig couponConfig;
    @Autowired
    private HttpClientUtil httpClientUtil;

    public BaseCouponResponse<CouponActivityListResp> activityList(BaseCouponRequester<CouponActivityListReq> requester) {
        String request = request(requester, couponConfig.getActivityListRoute());
        return JSON.parseObject(request, new TypeReference<BaseCouponResponse<CouponActivityListResp>>() {
        });
    }

    public BaseCouponResponse<Object> sendBatch(BaseCouponRequester<CouponSendBatchReq> requester) {
        String request = request(requester, couponConfig.getSendBatchRoute());
        return JSON.parseObject(request, new TypeReference<BaseCouponResponse<Object>>() {
        });
    }

    /**
     * 查询用户优惠券
     *
     * @param requester
     * @return
     */
    public BaseCouponResponse<GetUserCouponListResp> getUserCouponList(BaseCouponRequester<GetUserCouponListReq> requester) {
        String request = request(requester, couponConfig.getGetUserCouponList());
        return JSON.parseObject(request, new TypeReference<BaseCouponResponse<GetUserCouponListResp>>() {
        });
    }

    /**
     * 查询活动(券)信息
     *
     * @param requester
     * @return
     */
    public BaseCouponResponse<GetActivityListResp> getActivityList(BaseCouponRequester<Set<Long>> requester) {
        String request = request(requester, couponConfig.getGetActivityList());
        return JSON.parseObject(request, new TypeReference<BaseCouponResponse<GetActivityListResp>>() {
        });
    }

    /**
     * 查询活动(券)详情信息
     *
     * @param requester
     * @return
     */
    public BaseCouponResponse<List<GetActivityCouponDetailResponse>> getActivityCouponList(BaseCouponRequester<GetActivityCouponDetailRequest> requester) {
        String request = request(requester, couponConfig.getGetActivityCouponList());
        return JSON.parseObject(request, new TypeReference<BaseCouponResponse<List<GetActivityCouponDetailResponse>>>() {
        });
    }

    /**
     * 发券
     *
     * @param requester
     * @return
     */
    public BaseCouponResponse sendUserCoupon(BaseCouponRequester<SendUserCouponReq> requester) {
        String request = request(requester, couponConfig.getSendUserCoupon());
        return JSON.parseObject(request, new TypeReference<BaseCouponResponse>() {
        });
    }

    private <T extends BaseCouponRequester<?>> String request(T requester, String uri) {
        String url = couponConfig.getHost() + "/" + uri;
        try {
            setSign(requester, uri);
            LOGGER.info("coupon send, url:{}, request:{}", url, JsonUtil.toJson(requester));
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("coupon send, url:{}, request:{}, resp:{}, time：{}ms", url, JsonUtil.toJson(requester), JsonUtil.toJson(result), Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (Exception e) {
            LOGGER.error("coupon send error, url:{}, params:{}", url, JsonUtil.toJson(requester), e);
            throw e;
        }
    }

    private <T extends BaseCouponRequester<?>> void setSign(T requester, String route) {
        requester.setUa(signer.getUa());
        requester.setSign(signer.sign(route, requester.getArgs(), couponConfig.getKey()));
    }
}
