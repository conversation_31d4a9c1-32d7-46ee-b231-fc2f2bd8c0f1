package com.xftech.cdp.infra.client;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.UnicodeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;

import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.InfraException;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.xml.bind.DatatypeConverter;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 18:09:13
 */
@Setter
@Getter
@Component
public class Signer {
    private static final Logger LOGGER = LoggerFactory.getLogger(Signer.class);

    public String getUa() {
        return Constants.APP_NAME;
    }

    public String getSignKey(String key) {
        return getUa() + key + getUa();
    }

    public <T> String sign(String route, T t, String key) {
        try {
            String param = t instanceof String ? String.valueOf(t) : JSON.toJSONString(t, SerializerFeature.SortField);
            String signStr = UnicodeUtil.toUnicode(getSignKey(key) + route + getSignKey(key) + param + getSignKey(key));
            return DatatypeConverter.printHexBinary(MessageDigest.getInstance("MD5").digest(StringUtils.replace(signStr, "\\\\", "\\").getBytes(Charset.defaultCharset()))).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            throw new InfraException(e);
        }
    }

    public <T> String sign(String route, T t, String key, String ua) {
        try {
            String param = t instanceof String ? String.valueOf(t) : JSON.toJSONString(t, SerializerFeature.SortField);
            String signStr = UnicodeUtil.toUnicode(getSignKey(key, ua) + route + getSignKey(key, ua) + param + getSignKey(key, ua));
            String md5 = DatatypeConverter.printHexBinary(MessageDigest.getInstance("MD5").digest(StringUtils.replace(signStr, "\\\\", "\\").getBytes(Charset.defaultCharset()))).toLowerCase();
            LogUtil.logDebug("Signer sign route={} t={} key={} ua={} md5={}", route, t, key, ua, md5);
            return md5;
        } catch (NoSuchAlgorithmException e) {
            throw new InfraException(e);
        }
    }

    public String getSignKey(String key, String ua) {
        return ua + key + ua;
    }

}
