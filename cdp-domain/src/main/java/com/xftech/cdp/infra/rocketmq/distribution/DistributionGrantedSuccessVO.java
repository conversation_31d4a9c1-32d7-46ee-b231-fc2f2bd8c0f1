package com.xftech.cdp.infra.rocketmq.distribution;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2024/9/17
 * @description 分发授信成功事件消息上报
 */
@Data
public class DistributionGrantedSuccessVO {

    /**
     * 订单状态 12=进件成功
     */
    private Integer status;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 用户号
     */
    private String userNo;
    private String app;
    /**
     * 分发产品
     */
    private String productNo;

    private String flowNumber;

    private String innerApp;

}
