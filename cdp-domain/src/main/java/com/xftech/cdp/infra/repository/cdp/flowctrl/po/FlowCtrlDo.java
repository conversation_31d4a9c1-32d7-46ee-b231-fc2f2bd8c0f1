package com.xftech.cdp.infra.repository.cdp.flowctrl.po;

import com.xftech.cdp.domain.flowctrl.model.enums.ChannelTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.infra.repository.Do;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 流控规则表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowCtrlDo extends Do {

    /**
     * 规则类型 1-策略 2-渠道 3-多策略
     */
    private Integer type;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则描述
     */
    private String description;

    /**
     * 规则状态 0-初始化 1-生效中 2-已关闭
     */
    private Integer status;

    /**
     * 生效策略 0-全部，多个逗号分隔
     */
    private String effectiveStrategy;

    /**
     * 生效渠道 0-全部 1-短信 2电销，多个逗号分隔
     */
    private String effectiveChannel;

    /**
     * 日触达次数
     */
    private Integer dayCount;

    /**
     * 周触达次数
     */
    private Integer weekCount;

    /**
     * 月触达次数
     */
    private Integer monthCount;

    /**
     * 优先级 1-【渠道规则-特殊规则】、2-【渠道规则-兜底规则】、3-【策略规则-特殊规则】、4-【策略规则-兜底规则】
     */
    private Integer priority;

    /**
     * 策略类型  1离线策略 2 事件策略
     */
    private Integer strategyType;


    /**
     * 触达限制天数
     */
    private Integer limitDays;

    /**
     * 触达限制次数
     */
    private Integer limitTimes;

    /**
     * 业务类型 old-cust new-cust
     */
    private String bizType;


    private static final long serialVersionUID = 1L;

    public String getEffectiveContent(Map<Long, StrategyDo> strategyDoMap) {
        if (this.type.equals(FlowCtrlTypeEnum.STRATEGY.getType()) || this.type.equals(FlowCtrlTypeEnum.MULTI_STRATEGY.getType())) {
            String[] strategyArr = this.effectiveStrategy.split(",");
            List<String> strategyList = new ArrayList<>();
            for (String strategy : strategyArr) {
                if (strategy.equals("0")) {
                    strategyList.add("全部策略");
                } else {
                    if (strategyDoMap != null && strategyDoMap.get(Long.parseLong(strategy)) != null) {
                        strategyList.add(strategy + "_" + strategyDoMap.get(Long.parseLong(strategy)).getName());
                    }
                }
            }
            return String.join(",", strategyList);
        } else {
            String[] channelArr = this.effectiveChannel.split(",");
            List<String> channelList = new ArrayList<>();
            for (String channel : channelArr) {
                channelList.add(ChannelTypeEnum.getInstance(Integer.parseInt(channel)).getDesc());
            }
            return String.join(",", channelList);
        }
    }
}