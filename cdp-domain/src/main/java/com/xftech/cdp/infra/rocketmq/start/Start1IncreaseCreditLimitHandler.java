/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.start;

import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.MessageTopicConstants;
import com.xftech.cdp.infra.rocketmq.dto.Login1IncreaseCreditLimitVO;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class Start1IncreaseCreditLimitHandler implements MessageHandler {

    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return MessageTopicConstants.START1_INCREASE_CREDIT;
    }

    @Override
    public boolean execute(String messageId, Object message) {
        Login1IncreaseCreditLimitVO messageVO = JSONObject.parseObject(message.toString(), Login1IncreaseCreditLimitVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        bizEventMessageVO.setBizEventType("Start1IncreaseCreditLimit");
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(Login1IncreaseCreditLimitVO bizEventRocketMessageVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setApp(bizEventRocketMessageVO.getApp());
        bizEventMessageVO.setInnerApp(bizEventRocketMessageVO.getInnerApp());
        bizEventMessageVO.setAppUserId(Long.parseLong(bizEventRocketMessageVO.getAppUserId()));
        bizEventMessageVO.setCreditUserId(Long.parseLong(bizEventRocketMessageVO.getUserId()));
        bizEventMessageVO.setDeviceId(bizEventRocketMessageVO.getDeviceId());
        bizEventMessageVO.setSourceType(bizEventRocketMessageVO.getSourceType());
        bizEventMessageVO.setIp(bizEventRocketMessageVO.getIp());
        bizEventMessageVO.setTriggerDatetime(bizEventRocketMessageVO.getRequestedTime());
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setSourceType(bizEventRocketMessageVO.getSourceType());
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}