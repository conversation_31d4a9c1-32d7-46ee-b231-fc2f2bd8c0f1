package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <AUTHOR>
 * @version $ AccessControlDiversionVO, v 0.1 2024/11/11 11:18 tianshuo.qiu Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccessControlDiversionVO {
    /**
     * 客户号
     */
    @JSONField(name = "cust_no")
    @JsonProperty("cust_no")
    private String custNo;

    /**
     * 用户id
     */
    @JSONField(name = "user_id")
    @JsonProperty("user_id")
    private String userId;
    /**
     * app
     */
    @JSONField(name = "app")
    @JsonProperty("app")
    private String app;
    /**
     * inner_app
     */
    @JSONField(name = "inner_app")
    @JsonProperty("inner_app")
    private String innerApp;


    /**
     * 业务类型
     */
    @J<PERSON>NField(name = "biz_type")
    @JsonProperty("biz_type")
    private String bizType;
    /**
     * 产品类型
     */
    @JSONField(name = "loan_type")
    @JsonProperty("loan_type")
    private String loanType;

    /**
     * 流水号
     */
    @JSONField(name = "flow_no")
    @JsonProperty("flow_no")
    private String flowNo;

    /**
     * 业务流水号
     */
    @JSONField(name = "biz_flow_number")
    @JsonProperty("biz_flow_number")
    private String bizFlowNumber;


    /**
     * 禁申天数
     */
    @JSONField(name = "forbidden_apply_day")
    @JsonProperty("forbidden_apply_day")
    private Integer forbiddenApplyDay;

    /**
     * 禁申过期时间
     */
    @JSONField(name = "forbidden_expiration_time")
    @JsonProperty("forbidden_expiration_time")
    private String forbiddenExpirationTime;

    /**
     * 解除禁申时间
     */
    @JSONField(name = "lift_forbidden_time")
    @JsonProperty("lift_forbidden_time")
    private String liftForbiddenTime;
    /**
     * 禁申标签
     */
    @JSONField(name = "diversion_label")
    @JsonProperty("diversion_label")
    private String diversionLabel;

    /**
     * 禁申动作 0禁申，1解除禁申
     */
    @JSONField(name = "access_action")
    @JsonProperty("access_action")
    private String accessAction;
}
