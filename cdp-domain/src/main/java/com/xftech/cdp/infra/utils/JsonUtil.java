package com.xftech.cdp.infra.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

public class JsonUtil {

    private static final SerializerFeature[] features = {
            SerializerFeature.WriteMapNullValue,
            SerializerFeature.DisableCircularReferenceDetect,
    };

    public static String toJson(Object object) {
        if (object == null) {
            return null;
        }
        if (object instanceof String) {
            return (String) object;
        }
        return JSON.toJSONString(object, features);
    }


    public static Object parse(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JSON.parse(json);
    }

    public static <T> T parse(String json, Class<T> cls) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JSON.parseObject(json, cls);
    }

    @SuppressWarnings("unchecked")
    public static <T> Map<String, T> toMap(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return (Map<String, T>) JSONObject.parseObject(json);
    }


    public static <T> List<T> toList(String json, Class<T> cls) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        return JSON.parseArray(json, cls);
    }
}
