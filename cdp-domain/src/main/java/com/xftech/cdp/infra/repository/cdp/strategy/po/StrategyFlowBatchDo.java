package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class StrategyFlowBatchDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_batch.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_batch.flow_no
     *
     * @mbg.generated
     */
    private String flowNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_batch.batch_no
     *
     * @mbg.generated
     */
    private String batchNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_batch.date_value
     *
     * @mbg.generated
     */
    private Integer dateValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_batch.d_flag
     *
     * @mbg.generated
     */
    private Short dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_batch.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_batch.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table strategy_flow_batch
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_batch.id
     *
     * @return the value of strategy_flow_batch.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_batch.id
     *
     * @param id the value for strategy_flow_batch.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_batch.flow_no
     *
     * @return the value of strategy_flow_batch.flow_no
     *
     * @mbg.generated
     */
    public String getFlowNo() {
        return flowNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_batch.flow_no
     *
     * @param flowNo the value for strategy_flow_batch.flow_no
     *
     * @mbg.generated
     */
    public void setFlowNo(String flowNo) {
        this.flowNo = flowNo == null ? null : flowNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_batch.batch_no
     *
     * @return the value of strategy_flow_batch.batch_no
     *
     * @mbg.generated
     */
    public String getBatchNo() {
        return batchNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_batch.batch_no
     *
     * @param batchNo the value for strategy_flow_batch.batch_no
     *
     * @mbg.generated
     */
    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo == null ? null : batchNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_batch.date_value
     *
     * @return the value of strategy_flow_batch.date_value
     *
     * @mbg.generated
     */
    public Integer getDateValue() {
        return dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_batch.date_value
     *
     * @param dateValue the value for strategy_flow_batch.date_value
     *
     * @mbg.generated
     */
    public void setDateValue(Integer dateValue) {
        this.dateValue = dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_batch.d_flag
     *
     * @return the value of strategy_flow_batch.d_flag
     *
     * @mbg.generated
     */
    public Short getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_batch.d_flag
     *
     * @param dFlag the value for strategy_flow_batch.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Short dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_batch.created_time
     *
     * @return the value of strategy_flow_batch.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_batch.created_time
     *
     * @param createdTime the value for strategy_flow_batch.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_batch.updated_time
     *
     * @return the value of strategy_flow_batch.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_batch.updated_time
     *
     * @param updatedTime the value for strategy_flow_batch.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow_batch
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StrategyFlowBatchDo other = (StrategyFlowBatchDo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFlowNo() == null ? other.getFlowNo() == null : this.getFlowNo().equals(other.getFlowNo()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getDateValue() == null ? other.getDateValue() == null : this.getDateValue().equals(other.getDateValue()))
            && (this.getdFlag() == null ? other.getdFlag() == null : this.getdFlag().equals(other.getdFlag()))
            && (this.getCreatedTime() == null ? other.getCreatedTime() == null : this.getCreatedTime().equals(other.getCreatedTime()))
            && (this.getUpdatedTime() == null ? other.getUpdatedTime() == null : this.getUpdatedTime().equals(other.getUpdatedTime()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow_batch
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFlowNo() == null) ? 0 : getFlowNo().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getDateValue() == null) ? 0 : getDateValue().hashCode());
        result = prime * result + ((getdFlag() == null) ? 0 : getdFlag().hashCode());
        result = prime * result + ((getCreatedTime() == null) ? 0 : getCreatedTime().hashCode());
        result = prime * result + ((getUpdatedTime() == null) ? 0 : getUpdatedTime().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow_batch
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowNo=").append(flowNo);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", dateValue=").append(dateValue);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}