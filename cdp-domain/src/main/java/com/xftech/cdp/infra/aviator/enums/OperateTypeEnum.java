package com.xftech.cdp.infra.aviator.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {

    ADD(1, "新增操作"),

    UPDATE(2, "修改操作"),

    DELETE(3, "删除"),

    OPERATE(4, "启停等操作按钮"),

    PUBLISH(5, "发布or重复发布"),

    RETRIEVE(6, "检索操作"),

    ;

    private final int code;
    private final String desc;

    public static OperateTypeEnum getInstance(Integer code) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (Objects.equals(operateTypeEnum.getCode(), code)) {
                return operateTypeEnum;
            }
        }
        return null;
    }
}
