package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class StrategyExecCycleDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.strategy_id
     *
     * @mbg.generated
     */
    private Long strategyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.cur_val
     *
     * @mbg.generated
     */
    private Integer curVal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.total_val
     *
     * @mbg.generated
     */
    private Integer totalVal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.date_value
     *
     * @mbg.generated
     */
    private Integer dateValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.d_flag
     *
     * @mbg.generated
     */
    private Byte dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_exec_cycle.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table strategy_exec_cycle
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.id
     *
     * @return the value of strategy_exec_cycle.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.id
     *
     * @param id the value for strategy_exec_cycle.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.strategy_id
     *
     * @return the value of strategy_exec_cycle.strategy_id
     *
     * @mbg.generated
     */
    public Long getStrategyId() {
        return strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.strategy_id
     *
     * @param strategyId the value for strategy_exec_cycle.strategy_id
     *
     * @mbg.generated
     */
    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.cur_val
     *
     * @return the value of strategy_exec_cycle.cur_val
     *
     * @mbg.generated
     */
    public Integer getCurVal() {
        return curVal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.cur_val
     *
     * @param curVal the value for strategy_exec_cycle.cur_val
     *
     * @mbg.generated
     */
    public void setCurVal(Integer curVal) {
        this.curVal = curVal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.total_val
     *
     * @return the value of strategy_exec_cycle.total_val
     *
     * @mbg.generated
     */
    public Integer getTotalVal() {
        return totalVal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.total_val
     *
     * @param totalVal the value for strategy_exec_cycle.total_val
     *
     * @mbg.generated
     */
    public void setTotalVal(Integer totalVal) {
        this.totalVal = totalVal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.date_value
     *
     * @return the value of strategy_exec_cycle.date_value
     *
     * @mbg.generated
     */
    public Integer getDateValue() {
        return dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.date_value
     *
     * @param dateValue the value for strategy_exec_cycle.date_value
     *
     * @mbg.generated
     */
    public void setDateValue(Integer dateValue) {
        this.dateValue = dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.d_flag
     *
     * @return the value of strategy_exec_cycle.d_flag
     *
     * @mbg.generated
     */
    public Byte getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.d_flag
     *
     * @param dFlag the value for strategy_exec_cycle.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Byte dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.created_time
     *
     * @return the value of strategy_exec_cycle.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.created_time
     *
     * @param createdTime the value for strategy_exec_cycle.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_exec_cycle.updated_time
     *
     * @return the value of strategy_exec_cycle.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_exec_cycle.updated_time
     *
     * @param updatedTime the value for strategy_exec_cycle.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_exec_cycle
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", strategyId=").append(strategyId);
        sb.append(", curVal=").append(curVal);
        sb.append(", totalVal=").append(totalVal);
        sb.append(", dateValue=").append(dateValue);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}