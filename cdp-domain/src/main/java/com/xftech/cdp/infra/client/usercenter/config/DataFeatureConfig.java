/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $ DataFeatureConfig, v 0.1 2024/4/22 13:36 yye.xu Exp $
 */

@RefreshScope
@Data
@Component
public class DataFeatureConfig {
    @Value("${pocketId:6a87407b155515164ee565a838df30ed}")
    private String pocketId;

    @Value("${datafeaturecore.Url}")
    private String datafeaturecoreUrl;
}