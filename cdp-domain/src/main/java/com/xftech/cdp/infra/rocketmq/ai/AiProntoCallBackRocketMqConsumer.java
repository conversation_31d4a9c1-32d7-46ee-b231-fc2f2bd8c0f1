/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.ai;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ AiProntoCallBackRoketMqConsumer, v 0.1 2024/8/26 10:31 lingang.han Exp $
 */

@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_telemkt_name_ai_call_result", topic = "tp_telemkt_name_ai_call_result", consumeMode = ConsumeMode.CONCURRENTLY)
public class AiProntoCallBackRocketMqConsumer extends MqConsumerListener<String> {

    @Autowired
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        log.info("aiProntoCallBackRocketMqConsumer receive message topic={},messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage);
        try {
            handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
        } catch (Exception e) {
            log.error("aiProntoCallBackRocketMqConsumer consumer error,topic={} messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage, e);
        }
    }
}