package com.xftech.cdp.infra.client.platform;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.xftech.cdp.infra.client.BaseRequest;
import com.xftech.cdp.infra.client.BaseResponse;
import com.xftech.cdp.infra.client.Signer;
import com.xftech.cdp.infra.client.coupon.conig.CouponConfig;
import com.xftech.cdp.infra.client.platform.request.GetByOrderNoRequest;
import com.xftech.cdp.infra.client.platform.response.GetByOrderNoResponse;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/23
 * @description PlatFormClient
 */
@Slf4j
@Service
public class PlatFormClient {

    @Value("${http.platform-service.host:https://qa1-api.testxinfei.cn/platform-service-api}")
    private String platformServiceHost;

    @Value("${http.platform-service.ua:xyf-cdp}")
    private String platformServiceUa;

    @Value("${http.platform-service.key:xyf-cdp}")
    private String platformServiceKey;

    private static final String GET_BY_ORDER_NO = "order/get-by-order-no";

    @Resource
    private Signer signer;
    @Resource
    private CouponConfig couponConfig;
    @Resource
    private HttpClientUtil httpClientUtil;

    public BaseResponse<GetByOrderNoResponse> getByOrderNo(BaseRequest<GetByOrderNoRequest> request) {
        String response = request(request, GET_BY_ORDER_NO);
        return JSON.parseObject(response, new TypeReference<BaseResponse<GetByOrderNoResponse>>() {
        });
    }

    private <T extends BaseRequest<?>> String request(T request, String uri) {
        String url = platformServiceHost + "/v1/" + uri;
        try {
            setSign(request, uri);
            long startTime = Instant.now().toEpochMilli();
            Map<String, String> headers = new HashMap<>();
            headers.put("trace-id", IdUtil.fastSimpleUUID());
            String response = httpClientUtil.postJson(url, request, headers);
            log.info("PlatFormClient url={}, request={}, headers={} response={}, time={}ms", url, JsonUtil.toJson(request), JsonUtil.toJson(headers), JsonUtil.toJson(response), Instant.now().toEpochMilli() - startTime);
            return response;
        } catch (Exception e) {
            log.error("PlatFormClient error, url={}, request={}", url, JsonUtil.toJson(request), e);
            throw e;
        }
    }

    private <T extends BaseRequest<?>> void setSign(T request, String route) {
        request.setUa(platformServiceUa);
        request.setSign(signer.sign(route, request.getArgs(), platformServiceKey, platformServiceUa));
    }

}
