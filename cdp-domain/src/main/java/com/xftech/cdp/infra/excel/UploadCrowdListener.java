package com.xftech.cdp.infra.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/17
 */
@Slf4j
public class UploadCrowdListener implements ReadListener<UploadCrowdModel> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    public final CrowdDetailRepository crowdDetailRepository;
    public Long crowdId;
    private List<UploadCrowdModel> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    public UploadCrowdListener(CrowdDetailRepository crowdDetailRepository, Long crowdId) {
        this.crowdId = crowdId;
        this.crowdDetailRepository = crowdDetailRepository;
    }

    @Override
    public void invoke(UploadCrowdModel uploadCrowdModel, AnalysisContext analysisContext) {
        cachedDataList.add(uploadCrowdModel);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        saveData();
    }

    public void saveData() {
        List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
        for (UploadCrowdModel crowdModel : cachedDataList) {
            CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
            crowdDetailDo.setCrowdId(crowdId);
            crowdDetailDo.setUserId(Long.parseLong(crowdModel.getUserId()));
            crowdDetailDoList.add(crowdDetailDo);
        }
        crowdDetailRepository.saveBatch(crowdDetailDoList);
    }
}
