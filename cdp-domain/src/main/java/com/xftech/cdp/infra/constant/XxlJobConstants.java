package com.xftech.cdp.infra.constant;

/**
 * <AUTHOR> wh
 * @since : 2022/10/20 16:02
 */
public interface XxlJobConstants {

    String CROWD_DISPATCH = "CROWD_DISPATCH";


    String CROWD_WAREHOUSE_ALARM = "CROWD_WAREHOUSE_ALARM";

    String CROWD_RESET_EVERYDAY = "CROWD_RESET_EVERYDAY";

    String CROWD_PUSH_RESULT_QUERY = "CROWD_PUSH_RESULT_QUERY";

    String CLEAR_HISTORY_CROWD_DETAIL = "C<PERSON><PERSON>_HISTORY_CROWD_DETAIL";

    String CLEAR_HISTORY_CROWD_DETAIL_BY_TRUNCATE = "CLEAR_HISTORY_CROWD_DETAIL_BY_TRUNCATE";

    String RESET_BIGDATA_ERROR_RESULT_CROWD_PACKS = "RESET_BIGDATA_ERROR_RESULT_CROWD_PACKS";

    // 生成策略任务
    String STRATEGY_DISPATCH_TASK_GENERATOR = "STRATEGY_DISPATCH_TASK_GENERATOR";
    String STRATEGY_DISPATCH = "STRATEGY_DISPATCH";

    String STRATEGY_DISPATCH_TASK_EXECCUTE = "STRATEGY_DISPATCH_TASK_EXECCUTE";
    // 电销策略任务
    String TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE = "TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE";

    String NOT_TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE = "NOT_TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE";

    String STRATEGY_DISPATCH_OFFLINE_ENGINE = "STRATEGY_DISPATCH_OFFLINE_ENGINE";

    String STRATEGY_DISPATCH_TASK_EXECCUTE_TIMEOUT_ALARM = "STRATEGY_DISPATCH_TASK_EXECCUTE_TIMEOUT_ALARM";

    String STRATEGY_DISPATCH_RETRY = "STRATEGY_DISPATCH_RETRY";

    String STRATEGY_REFRESH_STATUS = "STRATEGY_REFRESH_STATUS";

    String STRATEGY_EVENT_REFRESH_STATUS = "STRATEGY_EVENT_REFRESH_STATUS";

    String STRATEGY_EVENT_30_MIN_ALARM = "STRATEGY_EVENT_30_MIN_ALARM";

    String STRATEGY_EVENT_REFRESH_EXEC_LOG_STATUS = "STRATEGY_EVENT_REFRESH_EXEC_LOG_STATUS";

    String STRATEGY_EVENT_CATCH_METADATA = "STRATEGY_EVENT_CATCH_METADATA";

    String STRATEGY_EVENT_EXEC_LOG_REFRESH = "STRATEGY_EVENT_EXEC_LOG_REFRESH";

    String RABBIT_MQ_DISPATCH = "RABBIT_MQ_DISPATCH";

    String STRATEGY_REFRESH_CACHE_DATA = "STRATEGY_REFRESH_CACHE_DATA";

    String STRATEGY_HISTORY_NO_MARKET_DATA = "STRATEGY_HISTORY_NO_MARKET_DATA";

    String T0_STRATEGY_DISPATCH_USER_NUM_ALARM = "T0_STRATEGY_DISPATCH_USER_NUM_ALARM";

    String RISK_FORECAST = "RISK_FORECAST";

    String RISK_FORECAST_NEW = "RISK_FORECAST_NEW";

    String TEST = "TEST";

    /**
     * T+0实时策略流程数据统计
     */
    String STAT_REALTIME_STRATEGY_FLOW_DATA = "STAT_REALTIME_STRATEGY_FLOW_DATA";
    String STAT_REALTIME_STRATEGY_FLOW_DATA_YESTERDAY = "STAT_REALTIME_STRATEGY_FLOW_DATA_YESTERDAY";
    /**
     * 手动统计实时策略流程数据
     * 参数： 日期,策略id
     * 策略id可为空
     */
    String MANUAL_REALTIME_STRATEGY_FLOW_DATA = "MANUAL_REALTIME_STRATEGY_FLOW_DATA";

    /**
     * 更新超过回执推送时间，用户状态为失败
     */
    String DISPATCH_FAIL_USER_UPDATE = "DISPATCH_FAIL_USER_UPDATE";

    String EXIST_STRATEGY_FLOW_CTRL_UPDATE = "EXIST_STRATEGY_FLOW_CTRL_UPDATE";


    String STAT_OFFLINE_STRATEGY_FLOW_DATA = "STAT_OFFLINE_STRATEGY_FLOW_DATA";


    /**
     * 策略分组数据统计Job
     */
    String STAT_STRATEGY_GROUP_DATA_JOB = "STAT_STRATEGY_GROUP_DATA_JOB";

    /**
     * 策略分组数据统计Job-当天结束状态
     */
    String STAT_STRATEGY_GROUP_DATA_END_CURRENT_STATUS_JOB = "STAT_STRATEGY_GROUP_DATA_END_CURRENT_STATUS_JOB";

    /**
     * 策略分组数据统计Job-昨日
     */
    String STAT_STRATEGY_GROUP_DATA_YESTERDAY_JOB = "STAT_STRATEGY_GROUP_DATA_YESTERDAY_JOB";

    /**
     * 画布报表数据统计Job
     */
    String STAT_STRATEGY_FLOW_DATA_JOB = "STAT_STRATEGY_FLOW_DATA_JOB";

    String CROWD_REFRESH_TIMEOUT_ALARM = "CROWD_REFRESH_TIMEOUT_ALARM";

    String PUSH_CROWD_TOTAL = "PUSH_CROWD_TOTAL";

    String PUSH_CROWDLIST = "PUSH_CROWDLIST";

    String PUSH_CROWDLIST_RETRY = "PUSH_CROWDLIST_RETRY";

    String PUSH_CROWD_RECORD_LOG = "PUSH_CROWD_RECORD_LOG";

    String OFFLINE_STRATEGY_CROWD_STATUS_ALARM = "OFFLINE_STRATEGY_CROWD_STATUS_ALARM";

    String STRATEGY_CROWD_PACK_EXPIRE_ALARM = "STRATEGY_CROWD_PACK_EXPIRE_ALARM";
    String REPORT_DAILY_STRATEGY = "REPORT_DAILY_STRATEGY";
    String REPORT_DAILY_CROWD = "REPORT_DAILY_CROWD";
    String REPORT_DAILY_STRATEGY_ALARM = "REPORT_DAILY_STRATEGY_ALARM";
    String REPORT_DAILY_CROWD_ALARM = "REPORT_DAILY_CROWD_ALARM";

    /**************画布相关任务处理****************/
    String FLOW_STATUS_CHANGED = "FLOW_STATUS_CHANGED";
    String FLOW_DISPATCH_TASK_PRODUCER = "FLOW_DISPATCH_TASK_PRODUCER";
    String FLOW_DISPATCH_TASK_CONSUMER = "FLOW_DISPATCH_TASK_CONSUMER";
    /*******************************************/

    String LABEL_SYNC_TASK = "LABEL_SYNC_TASK";

    String TABLE_CREATOR = "TABLE_CREATOR";

    /***************分片执行离线引擎任务*************************/
    String OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE = "OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE";
    String OFFLINE_ENGINE_DISTRIBUTE_EXECUTE = "OFFLINE_ENGINE_DISTRIBUTE_EXECUTE";
}
