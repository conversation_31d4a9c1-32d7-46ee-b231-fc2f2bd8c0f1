package com.xftech.cdp.infra.utils;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 分页查询工具类
 *
 * @<NAME_EMAIL>
 * @date 2023/3/14 15:30
 */
public class PageUtil {

    /**
     * 分页操作（正常分页）
     *
     * @param invokePage 分页参数
     * @param supplier   分页查询
     * @param consumer   按页消费
     */
    public static <S extends Do> void invokePagination(PaginationParam invokePage, Supplier<List<S>> supplier, Consumer<List<S>> consumer) {
        invokePagination(PaginationTypeEnum.NORMAL, invokePage, supplier, consumer);
    }

    /**
     * 分页操作
     *
     * @param invokePage 分页参数
     * @param supplier   分页查询
     * @param consumer   按页消费
     */
    public static <S extends Do> void invokePagination(PaginationTypeEnum paginationTypeEnum, PaginationParam invokePage,
                                                       Supplier<List<S>> supplier, Consumer<List<S>> consumer) {
        while (true) {
            List<S> bos = supplier.get();
            if (CollectionUtils.isEmpty(bos)) {
                break;
            }
            consumer.accept(bos);
            if (bos.size() < invokePage.getPageSize()) {
                break;
            }

            if (paginationTypeEnum == PaginationTypeEnum.ID_PAGING) {
                invokePage.setId(bos.get(bos.size() - 1).getId());
            } else {
                invokePage.setPage(++invokePage.page);
            }
        }
    }


    public static <S extends Do> void invokePaginationNew(PaginationTypeEnum paginationTypeEnum, PaginationParam invokePage,
                                                          Supplier<ImmutableTriple<List<S>, Integer,Long>> supplier, Consumer<List<S>> consumer) {

        while (true) {

            ImmutableTriple<List<S>, Integer, Long> trip = supplier.get();
            List<S> bos = trip.getLeft();
            Integer total = trip.getMiddle();
            Long lastIndex = trip.getRight();

            if (!CollectionUtils.isEmpty(bos)) {
                consumer.accept(bos);
            }
            if (total <= 0) {
                break;
            }
            if (paginationTypeEnum == PaginationTypeEnum.ID_PAGING) {
                invokePage.setId(lastIndex);
            } else {
                invokePage.setPage(++invokePage.page);
            }
        }
    }

    @Data
    @NoArgsConstructor
    public static class PaginationParam {

        private long id = 0;

        private long page = 1;

        private long pageSize = 1000;

        public PaginationParam(long pageSize) {
            this.pageSize = pageSize;
        }
    }

    public enum PaginationTypeEnum {
        /**
         * 正常 LIMIT分页
         */
        NORMAL,
        /**
         * 自增ID排序分页
         */
        ID_PAGING
    }

}
