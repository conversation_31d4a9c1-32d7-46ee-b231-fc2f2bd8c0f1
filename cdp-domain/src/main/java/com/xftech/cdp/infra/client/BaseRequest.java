package com.xftech.cdp.infra.client;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BaseRequest<T> {

    @JsonProperty("ua")
    @JSONField(name = "ua")
    private String ua;

    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;

    @JsonProperty("args")
    @JSONField(name = "args")
    private T args;

    public BaseRequest(T args) {
        this.args = args;
    }

}
