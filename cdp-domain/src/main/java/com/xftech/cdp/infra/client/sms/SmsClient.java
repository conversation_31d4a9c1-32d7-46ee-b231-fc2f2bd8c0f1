package com.xftech.cdp.infra.client.sms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.cat.proxy.Tracer;
import com.xftech.cdp.infra.client.Signer;
import com.xftech.cdp.infra.client.sms.config.SmsConfig;
import com.xftech.cdp.infra.client.sms.model.BaseSmsRequester;
import com.xftech.cdp.infra.client.sms.model.SmsBatchCancelRequester;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamRequester;
import com.xftech.cdp.infra.client.sms.model.SmsGroupRequester;
import com.xftech.cdp.infra.client.sms.model.SmsItemRequester;
import com.xftech.cdp.infra.client.sms.model.SmsSendRequester;
import com.xftech.cdp.infra.client.sms.model.SmsSingleSendRequester;
import com.xftech.cdp.infra.client.sms.model.constant.SmsConstants;
import com.xftech.cdp.infra.client.sms.model.resp.BaseSmsResp;
import com.xftech.cdp.infra.client.sms.model.resp.SmsBatchCancelResp;
import com.xftech.cdp.infra.client.sms.model.resp.SmsBatchReportResp;
import com.xftech.cdp.infra.client.sms.model.resp.SmsGroupResp;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItemResp;
import com.xftech.cdp.infra.client.sms.model.resp.SmsSingleSendResp;
import com.xftech.cdp.infra.redis.sequence.Sequence;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 16:03:10
 */
@Component
public class SmsClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(SmsClient.class);

    @Resource
    private Signer signer;
    @Resource
    private SmsConfig smsConfig;
    @Resource
    private HttpClientUtil httpClientUtil;


    /**
     * 单条短信发送（支持带参）
     *
     * @param requester
     * @return
     */
    public SmsSingleSendResp sendSingleSms(SmsSingleSendRequester requester) {
        String result = request(requester, smsConfig.getSingleSendRoute(), false);
        SmsSingleSendResp resp = JSON.parseObject(result, SmsSingleSendResp.class);
        if (resp != null) {
            Tracer.logEvent("SmsSingleSendResp", resp.getStatus() == null ? "null" :
                    resp.getStatus().toString());
        }
        return resp;
    }

    public BaseSmsResp<?> doBatchSendSms(SmsSendRequester requester) {
        String result = request(requester, smsConfig.getBatchSendRoute(), false);
        return JSON.parseObject(result, BaseSmsResp.class);
    }

    /**
     * 批量短信带参
     * @param requester
     * @return
     */
    public BaseSmsResp<?> smsBatchWithParam(SmsBatchWithParamRequester requester) {
        String result = personalRequest(requester, smsConfig.getSmsBatchWithParamRoute(), false);
        return JSON.parseObject(result, BaseSmsResp.class);
    }

    public SmsGroupResp queryGroup(SmsGroupRequester requester) {
        String result = request(requester, smsConfig.getBatchTypeQueryRoute(), false);
        return JSON.parseObject(result, SmsGroupResp.class);
    }

    public SmsItemResp queryItem(SmsItemRequester requester) {
        String result = request(requester, smsConfig.getBatchTemplateQueryRoute(), false);
        return JSON.parseObject(result, SmsItemResp.class);
    }

    public SmsItemResp queryDistinctItem(SmsItemRequester requester) {
        String result = request(requester, smsConfig.getBatchDistinctTemplateQueryRoute(), false);
        return JSON.parseObject(result, SmsItemResp.class);
    }

    public SmsBatchCancelResp batchCancel(SmsBatchCancelRequester requester) {
        String result = request(requester, smsConfig.getBatchCancelRoute(), false);
        return JSON.parseObject(result, SmsBatchCancelResp.class);
    }

    public SmsBatchReportResp batchReport(SmsSendRequester requester) {
        String result = request(requester, smsConfig.getBatchReport(), false);
        return JSON.parseObject(result, SmsBatchReportResp.class);
    }

    private <T extends BaseSmsRequester<?>> String request(T requester, String uri, boolean needLog) {
        String url = smsConfig.getSmsHost() + "/" + uri;
        Map<String, String> headers = headers();
        try {
            setSign(requester, uri);
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester, headers);
            LOGGER.info("sms send, url:{}, time:{}ms, header:{}, req:{}, resp:{}", url, Instant.now().toEpochMilli() - startTime, JsonUtil.toJson(headers),
                    JsonUtil.toJson(requester), JsonUtil.toJson(result));
            return result;
        } catch (Exception e) {
            LOGGER.error("sms send error,url:{},headers:{},params:{}", url, headers, requester, e);
            throw e;
        }
    }

    private <T extends BaseSmsRequester<?>> String personalRequest(T requester, String uri, boolean needLog) {
        String url = smsConfig.getSmsPersonalHost() + "/" + uri;
        Map<String, String> headers = headers();
        try {
            setSign(requester, uri);
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester, headers);
            LOGGER.info("sms send, url:{}, time:{}ms, header:{}, req:{}, resp:{}", url, Instant.now().toEpochMilli() - startTime, JsonUtil.toJson(headers),
                    JsonUtil.toJson(requester), JsonUtil.toJson(result));
            return result;
        } catch (Exception e) {
            LOGGER.error("sms send error,url:{},headers:{},params:{}", url, headers, requester, e);
            throw e;
        }
    }

    private Map<String, String> headers() {
        Map<String, String> headers = new HashMap<>();
        headers.put(SmsConstants.HEADER_REQUEST_FLOAT_NUMBER, String.valueOf(Sequence.nextId()));
        return headers;
    }

    private <T extends BaseSmsRequester<?>> void setSign(T requester, String route) {
        requester.setUa(signer.getUa());
        requester.setSign(signer.sign(route, requester.getArgs(), smsConfig.getKey()));
    }

}
