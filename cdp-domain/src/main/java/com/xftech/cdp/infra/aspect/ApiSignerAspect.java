package com.xftech.cdp.infra.aspect;

import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;
import com.xftech.cdp.api.dto.req.external.IncreaseAmtCallbakRequest;
import com.xftech.cdp.infra.annotation.ApiSigner;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.SysConstants;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.cdp.infra.utils.HttpContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部接口验签
 *
 * @<NAME_EMAIL>
 * @date 2023/4/4 11:53
 */
@Slf4j
@Aspect
@Component
public class ApiSignerAspect {

    @Autowired
    private Config config;

    @Pointcut("@annotation(com.xftech.cdp.infra.annotation.ApiSigner)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        if (config.isExtApiSignEnable()) {
            HttpServletRequest request = HttpContextUtil.getHttpServletRequest();
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            ApiSigner syslog = method.getAnnotation(ApiSigner.class);
            if (point.getArgs()[0] instanceof IncreaseAmtCallbakRequest){
                log.info("忽略签名");
            }else {
                ExternalBaseRequest<?> params = (ExternalBaseRequest<?>) point.getArgs()[0];
                if (!checkSign(request, syslog, params)) {
                    throw new InfraException(500, "验签失败");
                }
            }
        }
        return point.proceed();
    }

    private boolean checkSign(HttpServletRequest request, ApiSigner syslog, ExternalBaseRequest<?> params) {
        if (Arrays.stream(syslog.ua()).map(SysConstants.SignerEnum::getUa).anyMatch(ua -> StringUtils.equals(params.getUa(), ua))) {
            if (SysConstants.SignerEnum.TEST.getUa().equals(params.getUa())) {
                return true;
            }
            String uri = request == null ? null : request.getRequestURI();
            List<String> signList = sign(uri, params.getArgs(), syslog.ua());
            return signList.contains(params.getSign());
        }
        return false;
    }

    private <T> List<String> sign(String uri, T t, SysConstants.SignerEnum[] signerEnums) {
        LogUtil.logDebug("ApiSignerAspect sign uri={}, t={} signerEnums={}", uri, JSONObject.toJSONString(t), signerEnums);
        return Arrays.stream(signerEnums).map(signerEnum -> sign(uri, t, signerEnum)).collect(Collectors.toList());
    }

    private <T> String sign(String uri, T t, SysConstants.SignerEnum signerEnum) {
        String ua = signerEnum.getUa();
        String key = signerEnum.getKey();
        String str = null;
        if (t instanceof String) {
            str = (String) t;
        } else {
            str = JSON.toJSONString(t, SerializerFeature.SortField);
        }
        String signKey = getSign(ua, key);
        String signStr = UnicodeUtil.toUnicode(signKey + uri + signKey + str + signKey);
        return SecureUtil.md5(signStr);
    }

    private String getSign(String ua, String key) {
        return ua + key + ua;
    }
}
