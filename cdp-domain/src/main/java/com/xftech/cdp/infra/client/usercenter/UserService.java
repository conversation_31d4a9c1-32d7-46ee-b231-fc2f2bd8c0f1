/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.utils.DateUtil;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.proxy.Tracer;
import com.google.common.collect.Maps;
import com.xyf.user.device.facade.response.DeviceInfoResponse;
import com.xyf.user.facade.common.model.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * <AUTHOR>
 * @version $ CisService, v 0.1 2023/11/14 16:38 yye.xu Exp $
 */

@Slf4j
@Service
public class UserService {

    @Value("${xf.user-device-service.url}")
    private String userServiceUrl;

    @Resource
    private RestTemplate restTemplate;

    public com.alibaba.fastjson2.JSONObject queryLastDeviceByCustNoAndApp(String app, String custNo) {
        if (StringUtils.isAnyBlank(app, custNo)) {
            return null;
        }

        HttpEntity httpEntity = new HttpEntity(getHeader());
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(userServiceUrl + "/device/queryLastDeviceByCustNoAndApp");
        uriBuilder.queryParam("app", app);
        uriBuilder.queryParam("custNo", custNo);
        String url = uriBuilder.toUriString();
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    httpEntity,
                    String.class);
            log.info("UserService queryLastDeviceByCustNoAndApp request={}, response={}", url, responseEntity.getBody());
            BaseResponse<DeviceInfoResponse> response = JSON.parseObject(responseEntity.getBody(), new TypeReference<BaseResponse<DeviceInfoResponse>>() {
            });
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData().getDeviceDetailInfo();
            }
        } catch (Exception e) {
            Tracer.logEvent("UserServiceError", "queryLastDeviceByCustNoAndApp");
            log.error("UserService queryLastDeviceByCustNoAndApp error request={}", url, e);
        }
        return null;
    }

    public static Map<String, Object> getHeader() {
        Map<String, Object> header = Maps.newHashMap();
        header.put("TRACE_ID", IdUtil.fastSimpleUUID());
        header.put("upstreamService", Constants.APP_NAME);
        header.put("requestTime", DateFormatUtils.format(new Date(), DateUtil.NOMAL_DATE_FORMAT));
        return header;
    }

}