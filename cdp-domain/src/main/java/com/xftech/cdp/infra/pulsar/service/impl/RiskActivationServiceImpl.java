/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.pulsar.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version $ RiskActivationServiceImpl, v 0.1 2023/9/18 14:00 yye.xu Exp $
 */

@Slf4j
@Service("riskActivationService")
@AllArgsConstructor
public class RiskActivationServiceImpl implements CdpPulsarService {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public Optional<BizEventMessageVO> toBizEventMessageVO(String eventMessage) {
        JSONObject jsonObject = JSON.parseObject(eventMessage);
        String innerApp = (String) jsonObject.get("inner_app");
        if (StringUtils.isBlank(innerApp)) {
            log.info("riskActivation eventMessage not have inner_app, eventMessage:{}", eventMessage);
            return Optional.empty();
        }
        return Optional.ofNullable(JSON.parseObject(eventMessage, BizEventMessageVO.class));
    }

    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("RiskActivation eventMessage toBizEventMessageVO={}", eventMsg);
            mqConsumeService.bizEventProcess(messageId, eventMsg);
        });
    }
}