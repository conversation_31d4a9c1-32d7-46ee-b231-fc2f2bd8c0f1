package com.xftech.cdp.infra.repository.subtable.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-03-20
 */
@Data
public class SplitTableRoute implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    private Long id;
    /**
     * 人群包ID
     */
    private Long crowdId;
    /**
     * 人群包执行日志记录ID
     */
    private Long crowdExecLogId;
    /**
     * 执行日期
     */
    private LocalDate execDate;
    /**
     * 主表名
     */
    private String tableName;
    /**
     * 分表序号
     */
    private Integer tableNo;
    /**
     * 是否删除（0：否 1：是）
     */
    private Short dFlag;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}