/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.increaseamt.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ AccessControlQueryReq, v 0.1 2024/7/3 17:45 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccessControlQueryReq implements Serializable {

    private static final long serialVersionUID = 3640847636590118750L;


    /**
     * 身份证
     * 必传
     */
    private String id_card_number;

    /**
     * 客户号
     * 五要素后必传，能加先加
     */
    private String cust_no;

    /**
     * 申请类型
     * cash_loan
     * personal_loan
     */
    private String loan_type;

}