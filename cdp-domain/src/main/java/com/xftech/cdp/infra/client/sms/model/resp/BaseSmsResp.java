package com.xftech.cdp.infra.client.sms.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 14:09:57
 */
@Setter
@Getter
public class BaseSmsResp<Response> {

    private static final long serialVersionUID = 1L;

    @JsonProperty("status")
    @JSONField(name = "status")
    private Integer status;

    @JsonProperty("message")
    @JSONField(name = "message")
    private String message;

    @JsonProperty("time")
    @JSONField(name = "time")
    private Long time;

    @JsonProperty("response")
    @JSONField(name = "response")
    private Response response;

    /**
     * '1' => 'success',
     * '-1' => 'failed',
     * '499100' => '手机号无效',
     * '499101' => '短信提交失败',
     * '499102' => '短信发送失败',
     * '499103' => '模板解析错误',
     * '499104' => '短信发送限制',
     * '499105' => '手机号无效',
     * '499106' => '无效模板ID',
     * '499107' => '模板状态停用',
     * '499108' => '营销短信黑名单',
     * '499109' => '停催手机号名单',
     * ===========================
     * '499200' => '缺少参数：',
     * '499201' => '无效参数：',
     * '499202' => '必填参数：',
     * '499203' => '错误参数：',
     * '400000' => '签名错误',
     */
    public boolean isSuccess() {
        return status != null && status.equals(1);
    }

    @JsonIgnore
    @JSONField(serialize = false)
    public static Integer getSucceedStatus(){
        return 1;
    }
}
