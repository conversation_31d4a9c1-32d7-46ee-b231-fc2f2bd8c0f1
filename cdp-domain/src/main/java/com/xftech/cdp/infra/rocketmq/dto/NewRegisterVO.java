package com.xftech.cdp.infra.rocketmq.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @date 2023-07-26 15:46
 */
@Data
public class NewRegisterVO {
    @JsonProperty("requestBody")
    private DirectRequest requestBody;

    @JsonProperty("creditUserId")
    private Long creditUserId;

    @Data
    public static class DirectRequest {
        private String app;

        private String mobile;

        private String ip;

        private String os;

        private String channel;

        @JsonProperty("inner_app")
        @JSONField(name = "inner_app")
        private String innerApp;

        @JsonProperty("mobile_protyle")
        @JSONField(name = "mobile_protyle")
        private String mobileProtyle;

        @JsonProperty("source_type")
        @JSONField(name = "source_type")
        private String sourceType;

        @JsonProperty("utm_source")
        @JSONField(name = "utm_source")
        private String utmSource;

        @JsonProperty("current_utm_source")
        @JSONField(name = "current_utm_source")
        private String currentUtmSource;

        private String appVersion;

        @JsonProperty("created_time")
        @JSONField(name = "created_time")
        private String createdTime;

        private String appSource;

        @JsonProperty("biz_event_data")
        @JSONField(name = "biz_event_data")
        private String bizEventData;

        private String deviceId;

        private String invitationCode;

        @JsonProperty("version_code")
        @JSONField(name = "version_code")
        private String versionCode;

        private String registerAsLogin;
    }
}
