package com.xftech.cdp.infra.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.infra.config.HttpCustomConfig;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.zipkin.brave.HttpClientConfig;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * Http 工具类 自定义超时时间
 */
@Component
public class HttpCustomUtil {
    private static final IUdpLogger LOGGER = LogUtil.getLogger(HttpCustomUtil.class);

    @Autowired
    private HttpClientConfig httpClientConfig;
    @Autowired
    private HttpCustomConfig httpCustomConfig;


    private CloseableHttpClient httpClient;
    private RequestConfig requestConfig;

    @PostConstruct
    public void init() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        httpClient = httpClientConfig.build();
        requestConfig = httpCustomConfig.getCustomRequestConfig();
    }

    /**
     * 发送POST请求（JSON形式）
     */
    public <T> String postJson(String path, T param) throws InfraException {
        return postJson(path, param, new HashMap<>());
    }

    /**
     * 发送POST请求（JSON形式）
     */
    public <T> String postJson(String path, T param, Map<String, String> headers) {
        StringEntity entity = new StringEntity(JSON.toJSONString(param, SerializerFeature.SortField), StandardCharsets.UTF_8);
        return postRequest(path, "application/json", entity, headers);
    }

    /**
     * 发送POST请求
     */
    private String postRequest(String path, String mediaType, HttpEntity entity, Map<String, String> headers) {
        LOGGER.debug("[postRequest] resourceUrl: {}", path);
        HttpPost post = null;
        try {
            post = getHttpPost(path, mediaType, entity, headers);
            HttpResponse response = httpClient.execute(post);
            int code = response.getStatusLine().getStatusCode();
            if (code >= HttpStatus.SC_BAD_REQUEST) {
                throw new InfraException(EntityUtils.toString(response.getEntity()));
            }
            return EntityUtils.toString(response.getEntity());
        } catch (ClientProtocolException e) {
            throw new InfraException("postRequest -- Client protocol exception!", e);
        } catch (IOException e) {
            throw new InfraException(HttpStatus.SC_REQUEST_TIMEOUT, "postRequest -- IO error!", e);
        } catch (Exception e) {
            throw new InfraException(e);
        }
    }


    private HttpPost getHttpPost(String path, String mediaType, HttpEntity entity, Map<String, String> headers) {
        HttpPost post = new HttpPost(path);
        post.setConfig(requestConfig);
        post.setEntity(entity);
        post.addHeader("Content-Type", mediaType);
        post.addHeader("Accept", "application/json");
        headers.forEach(post::addHeader);
        return post;
    }

}