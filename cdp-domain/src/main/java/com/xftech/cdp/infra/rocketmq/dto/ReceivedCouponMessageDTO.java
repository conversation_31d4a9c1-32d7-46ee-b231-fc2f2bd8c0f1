package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025年6月11日11:12:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivedCouponMessageDTO {
    /**
     * id
     */
    private Long id;
    /**
     * 麻雀批次号
     */
    private String sparrowBatchNum;
    /**
     * userId
     */
    private Long userId;
    /**
     * mobile
     */
    private String mobile;
    /**
     * name
     */
    private String name;
    /**
     * app
     */
    private String app;

    /**
     * innerApp
     */
    private String innerApp;

    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 优惠券id
     */
    private Long templateId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 扩展字段
     */
    private String useDetail;
    /**
     * 券类型 1:借款免息券 2:还款立减金 3:限时提额券
     */
    private Integer couponType;
    /**
     * 券归属方1：首贷运营2：复贷运营3：客服4：变现运营5：产品测试
     */
    private Integer belongsTo;


    /**
     * 优惠方式 1:立减 2:折扣
     */
    private Integer discountType;

    /**
     * 优惠金额|提额金额
     */
    private Integer discountAmount;

    /**
     * 实际抵扣金额 单位:分
     */
    private Integer actualDiscountAmount;

    /**
     * 优惠折扣 50=打5折
     */
    private BigDecimal discountRate;

    /**
     * 优惠期数
     */
    private Integer discountPeriods;

    /**
     * 产品期数
     */
    private Integer productPeriods;

    /**
     * 优惠券有效天数
     */
    private Integer validDays;

    /**
     * 管理员姓名
     */
    private String adminName;


    /**
     * 产品类型 1:全部 2:个人分期 3:现金分期
     */
    private Integer productType;

    /**
     * 起借金额 单位:分
     */
    private Integer lowerPrice;

    /**
     * 跳转地址
     */
    private String jumpUrl;

    /**
     * 是否发送短信提醒 1:已发送 0:未发送
     */
    private Integer isSend;

    /**
     * 跳转类型 0:无 1:url 2:app 3:sdk
     */
    private Integer jumpType;


    /**
     * 发送方
     */
    private String sendFrom;

    /**
     * 外部单号
     */
    private String externalNo;

    /**
     * 外部单号来源
     */
    private String externalNoSource;

    /**
     * 外部流水号
     */
    private String externalFlowNo;

    /**
     * 最大可借金额 单位:分
     */
    private Integer higherPrice;
}
