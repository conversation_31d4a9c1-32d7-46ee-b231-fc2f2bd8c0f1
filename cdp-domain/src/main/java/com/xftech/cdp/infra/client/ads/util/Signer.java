package com.xftech.cdp.infra.client.ads.util;

import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.xftech.cdp.infra.constant.Constants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/4 15:11
 */
@Component("adsSigner")
public class Signer {

    public String getUa() {
        return Constants.APP_NAME;
    }

    public String getKey() {
        String active = SpringUtil.getProperty("spring.profiles.active");
        return StringUtils.equalsIgnoreCase(active, "PRO") ? "prod" + getUa() + "fly&8&toxyf-dps%" : "qa1" + getUa() + "fly&8&toxyf-dps%";
    }

    private String signKey() {
        return getUa() + getKey() + getUa();
    }

    public String sign(String uri) {
        return SecureUtil.md5(UnicodeUtil.toUnicode(signKey() + uri + signKey() + signKey()));
    }
}
