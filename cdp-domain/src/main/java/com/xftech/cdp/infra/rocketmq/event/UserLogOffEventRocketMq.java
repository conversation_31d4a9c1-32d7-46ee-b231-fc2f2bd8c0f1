/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.event;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.dto.BizEventRocketMessageVO;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ UserLogOffEventRocketMq, v 0.1 2024/3/18 10:27 lingang.han Exp $
 */

@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_custprod_logoff_apply", topic = "tp_custprod_logoff_apply", consumeMode = ConsumeMode.CONCURRENTLY)
@Slf4j
public class UserLogOffEventRocketMq extends MqConsumerListener<String> {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;

    @Override
    protected void doMessage(String s, String s1, MessageExt messageExt) {
        String lockKey = null;
        if (StringUtils.isNotBlank(messageExt.getMsgId())) {
            lockKey = SecureUtil.md5("cg:userLogOff:" + messageExt.getMsgId());
        }
        log.info("userLogOffEventRocketMqConsumer receive message topic={},messageId={},body={}", s, messageExt.getMsgId(), s1);
        try {
            try {
                if (StringUtils.isNotBlank(lockKey) && !redisUtils.lock(lockKey, "0", 5, TimeUnit.MINUTES)) {
                    log.warn("userLogOffEventRocketMqConsumer 重复消息,messageId={},body={}", messageExt.getMsgId(), s1);
                    return;
                }
            } catch (Exception e) {
                log.error("userLogOffEventRocketMqConsumer,处理异常", e);
            }
            BizEventRocketMessageVO messageVO = JSONObject.parseObject(s1, BizEventRocketMessageVO.class);
            BizEventMessageVO bizEventMessageVO = transform(messageVO);
            bizEventMessageVO.setBizEventType("UserLogOff");

            strategyEventDispatchService.prescreen(messageExt.getMsgId(), bizEventMessageVO);

        } catch (Exception e) {
            if (StringUtils.isNotEmpty(lockKey)) {
                redisUtils.unlock(lockKey);
            }
            log.error("userLogOffEventRocketMqConsumer consumer error, messageid={},body={}", messageExt.getMsgId(), s1, e);
        }
    }

    private BizEventMessageVO transform(BizEventRocketMessageVO bizEventRocketMessageVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        BeanUtils.copyProperties(bizEventRocketMessageVO, bizEventMessageVO);
        bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(new Date(bizEventRocketMessageVO.getEventTime()), null));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setSource(bizEventRocketMessageVO.getExtraData().getSource());
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}