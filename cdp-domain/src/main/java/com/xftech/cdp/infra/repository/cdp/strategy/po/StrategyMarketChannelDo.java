package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalTime;

/**
 * <p>
 * 策略营销通道表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StrategyMarketChannelDo extends Do {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 策略分组id
     */
    private Long strategyGroupId;

    /**
     * 营销渠道，0: 不营销，1:短信，2:电销，3:优惠券，4:新电销，5:push，100:app资源位，
     */
    private Integer marketChannel;

    /**
     * 模版id
     */
    private String templateId;

    /**
     * app
     */
    private String app;

    /**
     * 触达app
     */
    private String dispatchApp;

    /**
     * 发送时间
     */
    private LocalTime sendTime;

    /**
     * cron表达式
     */
    private String cron;

    /**
     * xxl_job任务id
     */
    private Integer xxlJobId;
    
    /**
     * 扩展字段:例如类型ID
     */
    private String extInfo;


}
