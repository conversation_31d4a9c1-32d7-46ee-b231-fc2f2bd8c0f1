package com.xftech.cdp.infra.repository.cdp.marketing.po;

import java.util.Date;

import lombok.Data;

/**
 * 营销活动用户报名表
 *
 * @TableName marketing_activity_user_register
 */
@Data
public class MarketingActivityUserRegisterDo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否删除 0:未删除;1:已删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

}