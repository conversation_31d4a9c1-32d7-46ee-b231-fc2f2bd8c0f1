package com.xftech.cdp.infra.rocketmq.repay;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ RepayResultNotifyMessageDTO, v 0.1 2025/3/4 15:31 tianshuo.qiu Exp $
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RepayResultNotifyMessageDTO  {
    private String requestNo;
    private String outBizNo;
    private String userNo;
    private String acctNo;
    private String custNo;
    private String app;
    private String innerApp;
    private String fundSource;
    private boolean isSettle;
    private String payType;
    private String deductionStatus;
    private String failReason;

    private String loanNo;
    private Map<String, String> extInfo;
    private String paymentNo;
    private String channelNo;
    private String rpyType;
    private String payChannel;

    private String errorCode;


    private String productCode;
    private String subProductCode;
    private String thirdCode;
    private String paymentDeadlineDate;
    private String repaymentStartDate;
    private String debitOrderExpirationDate;
    private boolean isLast;

    private String loanStatus;
    private Boolean isBuyback;
    private String bankCardId;
    private String repaymentStatus;
    private String repayInnerApp;
    private long surplusTransAmt;
    private long transAmt;
    private long transDeductAmt;
    private String transDate;
    private String couponId;
    private boolean isCurrentlySettled;
    private long actualTransAmt;
    private Integer term;
    private String deductionNo;
    private String repaymentNo;
    private String relationNo;
    private String rpAcct;
    private String rpAcctName;
    private String withholdType;
    private List<String> repayTermList;
    private List<String> planNoList;
}
