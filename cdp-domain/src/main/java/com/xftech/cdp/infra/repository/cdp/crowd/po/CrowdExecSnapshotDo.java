package com.xftech.cdp.infra.repository.cdp.crowd.po;


import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <p>
 * 人群包执行快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdExecSnapshotDo extends Do {

    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 人群包执行日志id
     */
    private Long crowdExecLogId;

    /**
     * 快照明细数据
     */
    private String snapshotDetail;

    public static CrowdExecSnapshotDo beginExecute(Long crowdId, Long crowdExecLogId, String snapshotDetail) {
        CrowdExecSnapshotDo execSnapshotDo = new CrowdExecSnapshotDo();
        execSnapshotDo.setCrowdId(crowdId);
        execSnapshotDo.setSnapshotDetail(snapshotDetail);
        execSnapshotDo.setCrowdExecLogId(crowdExecLogId);
        return execSnapshotDo;
    }
}
