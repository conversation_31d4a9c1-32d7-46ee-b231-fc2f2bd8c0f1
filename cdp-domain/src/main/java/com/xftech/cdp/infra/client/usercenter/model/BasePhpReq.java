package com.xftech.cdp.infra.client.usercenter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/12/6 11:34
 */
@Getter
@Setter
public class BasePhpReq<Args> {

    @JsonProperty("ua")
    private String ua;

    @JsonProperty("args")
    private Args args;

    @JsonProperty("sign")
    private String sign;

    @JsonProperty("timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000L;
}
