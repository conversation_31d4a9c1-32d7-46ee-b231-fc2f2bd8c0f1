package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class AdminAuditLogDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.op_type
     *
     * @mbg.generated
     */
    private String opType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.op_name
     *
     * @mbg.generated
     */
    private String opName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.op_mobile
     *
     * @mbg.generated
     */
    private String opMobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.d_flag
     *
     * @mbg.generated
     */
    private Byte dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column admin_audit_log.op_content
     *
     * @mbg.generated
     */
    private String opContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table admin_audit_log
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.id
     *
     * @return the value of admin_audit_log.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.id
     *
     * @param id the value for admin_audit_log.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.op_type
     *
     * @return the value of admin_audit_log.op_type
     *
     * @mbg.generated
     */
    public String getOpType() {
        return opType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.op_type
     *
     * @param opType the value for admin_audit_log.op_type
     *
     * @mbg.generated
     */
    public void setOpType(String opType) {
        this.opType = opType == null ? null : opType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.op_name
     *
     * @return the value of admin_audit_log.op_name
     *
     * @mbg.generated
     */
    public String getOpName() {
        return opName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.op_name
     *
     * @param opName the value for admin_audit_log.op_name
     *
     * @mbg.generated
     */
    public void setOpName(String opName) {
        this.opName = opName == null ? null : opName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.op_mobile
     *
     * @return the value of admin_audit_log.op_mobile
     *
     * @mbg.generated
     */
    public String getOpMobile() {
        return opMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.op_mobile
     *
     * @param opMobile the value for admin_audit_log.op_mobile
     *
     * @mbg.generated
     */
    public void setOpMobile(String opMobile) {
        this.opMobile = opMobile == null ? null : opMobile.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.d_flag
     *
     * @return the value of admin_audit_log.d_flag
     *
     * @mbg.generated
     */
    public Byte getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.d_flag
     *
     * @param dFlag the value for admin_audit_log.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Byte dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.created_time
     *
     * @return the value of admin_audit_log.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.created_time
     *
     * @param createdTime the value for admin_audit_log.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.updated_time
     *
     * @return the value of admin_audit_log.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.updated_time
     *
     * @param updatedTime the value for admin_audit_log.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column admin_audit_log.op_content
     *
     * @return the value of admin_audit_log.op_content
     *
     * @mbg.generated
     */
    public String getOpContent() {
        return opContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column admin_audit_log.op_content
     *
     * @param opContent the value for admin_audit_log.op_content
     *
     * @mbg.generated
     */
    public void setOpContent(String opContent) {
        this.opContent = opContent == null ? null : opContent.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table admin_audit_log
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AdminAuditLogDo other = (AdminAuditLogDo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOpType() == null ? other.getOpType() == null : this.getOpType().equals(other.getOpType()))
            && (this.getOpName() == null ? other.getOpName() == null : this.getOpName().equals(other.getOpName()))
            && (this.getOpMobile() == null ? other.getOpMobile() == null : this.getOpMobile().equals(other.getOpMobile()))
            && (this.getdFlag() == null ? other.getdFlag() == null : this.getdFlag().equals(other.getdFlag()))
            && (this.getCreatedTime() == null ? other.getCreatedTime() == null : this.getCreatedTime().equals(other.getCreatedTime()))
            && (this.getUpdatedTime() == null ? other.getUpdatedTime() == null : this.getUpdatedTime().equals(other.getUpdatedTime()))
            && (this.getOpContent() == null ? other.getOpContent() == null : this.getOpContent().equals(other.getOpContent()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table admin_audit_log
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOpType() == null) ? 0 : getOpType().hashCode());
        result = prime * result + ((getOpName() == null) ? 0 : getOpName().hashCode());
        result = prime * result + ((getOpMobile() == null) ? 0 : getOpMobile().hashCode());
        result = prime * result + ((getdFlag() == null) ? 0 : getdFlag().hashCode());
        result = prime * result + ((getCreatedTime() == null) ? 0 : getCreatedTime().hashCode());
        result = prime * result + ((getUpdatedTime() == null) ? 0 : getUpdatedTime().hashCode());
        result = prime * result + ((getOpContent() == null) ? 0 : getOpContent().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table admin_audit_log
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", opType=").append(opType);
        sb.append(", opName=").append(opName);
        sb.append(", opMobile=").append(opMobile);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", opContent=").append(opContent);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}