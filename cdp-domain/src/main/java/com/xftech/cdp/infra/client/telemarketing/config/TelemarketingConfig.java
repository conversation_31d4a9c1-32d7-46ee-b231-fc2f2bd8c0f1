package com.xftech.cdp.infra.client.telemarketing.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/31 14:16
 */
@RefreshScope
@Component
@Setter
@Getter
public class TelemarketingConfig {

    @Value("${cdp.tele.host}")
    private String host;

    @Value("${cdp.tele.newHost}")
    private String newHost;

    @Value("${cdp.tele.route.nameType:name-type/page-list}")
    private String nameType;

    @Value("${cdp.tele.route.nameDetail:policy-config/get-detail}")
    private String nameDetail;

    @Value("${cdp.tele.route.nameConfig:policy-config/get-item-config}")
    private String nameConfig;

    @Value("${cdp.tele.route.saveNameConfig:policy-config/operate}")
    private String saveNameConfig;

    @Value("${cdp.tele.route.priorityConfig:rpc/policy-config/priority}")
    private String priorityConfig;

    @Value("${cdp.tele.route.pushNameData:name/push}")
    private String pushNameData;

    @Value("${cdp.tele.route.name.list:tel-names/list}")
    private String nameListRoute;

    @Value("${cdp.tele.route.save.list:name-import/save}")
    private String saveBatchRoute;

    @Value("${cdp.tele.uniqueKey:6Bsaq24YEXqnZ9Mk}")
    private String key;
    
    @Value("${cdp.tele.route.policyNameList:rpc/policy-config/page-list}")
    private String policyNameList;
}
