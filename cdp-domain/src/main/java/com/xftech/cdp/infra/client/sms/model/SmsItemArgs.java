package com.xftech.cdp.infra.client.sms.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/28 14:56:09
 */
@Getter
@Setter
public class SmsItemArgs {


    @JsonProperty("page_size")
    @JSONField(name = "page_size")
    private Integer pageSize;

    @JsonProperty("page")
    @JSONField(name = "page")
    private Integer page;

    @JsonProperty("app")
    @JSONField(name = "app")
    private String app;

    @JsonProperty("group")
    @JSONField(name = "group")
    private String group;

    @JsonProperty("template_id")
    @JSONField(name = "template_id")
    private String templateId;

    @JsonProperty("content")
    @JSONField(name = "content")
    private String content;

}
