package com.xftech.cdp.infra.pulsar.service.impl;

import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @<NAME_EMAIL>
 * @date 2023-08-14 10:57
 */
@Slf4j
@Service("repaySuccessService")
public class RepaySuccessServiceImpl implements CdpPulsarService {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public void consumer(String messageId, String eventMessage) {
        toBizEventMessageVO(eventMessage).ifPresent(eventMsg -> {
            log.info("RepaySuccess eventMessage toBizEventMessageVO={}", eventMsg);
            mqConsumeService.bizEventProcess(messageId, eventMsg);
        });
    }
}
