package com.xftech.cdp.infra.client.sms.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
@RefreshScope
@Component
@Setter
@Getter
public class SmsConfig {


    @Value("${cdp.sms.host}")
    private String smsHost;

    @Value("${cdp.sms.personal.host}")
    private String smsPersonalHost;

    @Value("${cdp.sms.send.single.route:sms/send}")
    private String singleSendRoute;

    @Value("${cdp.sms.send.batch.route:sms/batch-send}")
    private String batchSendRoute;

    @Value("${cdp.sms.batch.with.param.route:sms/personalizedSms}")
    private String smsBatchWithParamRoute;

    //获取所有短信模板
    @Value("${cdp.sms.query.item.batch.route:admin/sms/get-tpl-list}")
    private String batchTemplateQueryRoute;

    @Value("${cdp.sms.query.distinct.batch.route:admin/sms/get-distinct-tpl-list}")
    private String batchDistinctTemplateQueryRoute;

    //获取所有短信类型 。single 单条类型； batch 批量类型；all: 所有类型
    @Value("${cdp.sms.query.group.batch.route:admin/sms/get-tpl-group}")
    private String batchTypeQueryRoute;

    @Value("${cdp.sms.cancel.batch.route:sms/cancel-batch-send}")
    private String batchCancelRoute;

    @Value("${cdp.sms.send.batch.route:sms/batch-report}")
    private String batchReport;

    @Value("${cdp.sms.doRealSend:false}")
    private Boolean doRealSend;

    @Value("${cdp.sms.uniqueKey:SBeWgAmg5NdFM}")
    private String key;
}
