/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.rcspt;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ CxhRepayXyf01Increase, v 0.1 2024/7/1 15:25 lingang.han Exp $
 */
@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tg_cxh_repay_xyf01_increase_amount", topic = "tp_rcspt_risk_amount_change_message", selectorExpression = "tg_cxh_repay_xyf01_increase_amount", consumeMode = ConsumeMode.CONCURRENTLY)
public class CxhRepayXyf01IncreaseCreditRocketMqConsumer extends MqConsumerListener<String> {

    @Autowired
    private MessageHandlerSelector handlerSelector;

    @Override
    protected void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        log.info("cxhRepayXyf01IncreaseCreditRocketMqConsumer receive message topic={},messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage);
        try {
            handlerSelector.doMessage(topic, messageExt.getTags(), messageExt.getMsgId(), bodyMessage);
        } catch (Exception e) {
            log.error("cxhRepayXyf01IncreaseCreditRocketMqConsumer consumer error,topic={} messageId={},body={}", topic, messageExt.getMsgId(), bodyMessage, e);
        }
    }
}