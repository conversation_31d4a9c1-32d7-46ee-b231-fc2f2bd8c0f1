package com.xftech.cdp.infra.client.appcore.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ BalanceOptReq, v 0.1 2025/1/3 21:38 mingwen.zang Exp $
 */
@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class BalanceOptReq {
    /**
     * 用户id
     */
    private Long userNo;

    /**
     * 金额（单位，分）
     */
    private BigDecimal amount;

    /**
     * 增加余额的类型
     */
    private String refType = "活动返现";

    /**
     * 变动描述
     */
    private String description;
}
