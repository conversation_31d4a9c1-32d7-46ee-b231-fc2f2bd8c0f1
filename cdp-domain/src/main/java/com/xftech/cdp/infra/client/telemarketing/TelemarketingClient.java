package com.xftech.cdp.infra.client.telemarketing;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.infra.client.Signer;
import com.xftech.cdp.infra.client.telemarketing.config.TelemarketingConfig;
import com.xftech.cdp.infra.client.telemarketing.model.*;
import com.xftech.cdp.infra.client.telemarketing.model.resp.*;
import com.xftech.cdp.infra.log.DigestLogUtil;
import com.xftech.cdp.infra.rocketmq.telemkt.TelePushMqProducer;
import com.xftech.cdp.infra.utils.HttpClientUtil;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.google.common.base.Stopwatch;
import org.apache.rocketmq.client.producer.SendResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/31 14:14
 */
@Component
public class TelemarketingClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(TelemarketingClient.class);

    @Resource
    private Signer signer;
    @Resource
    private TelemarketingConfig telemarketingConfig;
    @Resource
    private HttpClientUtil httpClientUtil;
    
    @Autowired
    private TelePushMqProducer telePushMqProducer;

    public TeleNameListResp queryNameList(TeleNameListRequest requester) {
        String request = request(requester, telemarketingConfig.getNameListRoute());
        return JSON.parseObject(request, TeleNameListResp.class);
    }

    public TeleSaveBatchResp saveBatch(TeleSaveBatchRequest requester) {
        String request = request(requester, telemarketingConfig.getSaveBatchRoute());
        return JSON.parseObject(request, TeleSaveBatchResp.class);
    }

    public TeleNameTypeResp getNameType(TeleNameTypeArgs requester){
        String request = request(telemarketingConfig.getNewHost(),requester, telemarketingConfig.getNameType());
        return JSON.parseObject(request, TeleNameTypeResp.class);
    }
    public TeleNameConfigResp getNameConfig(){
        String request = request(telemarketingConfig.getNewHost(),null, telemarketingConfig.getNameConfig());
        return JSON.parseObject(request, TeleNameConfigResp.class);
    }

    public TeleNameSaveResp saveNameConfig(String parm){
        String request = requestForJson(telemarketingConfig.getNewHost(),parm, telemarketingConfig.getSaveNameConfig());
        return JSON.parseObject(request, TeleNameSaveResp.class);
    }

    public TeleUpdatePriorityResp updatePolicyPriority(String param){
        String result = requestForJson(telemarketingConfig.getNewHost(), param, telemarketingConfig.getPriorityConfig());
        return JSON.parseObject(result, TeleUpdatePriorityResp.class);
    }

    public String getPolicyDetail(TeleNameConfigDetailReq req){
        return request(telemarketingConfig.getNewHost(),req, telemarketingConfig.getNameDetail());
    }

    public TelePushResp pushTeleData(TelePushArgs telePushArgs){
        //修改到mq操作
        TelePushResp telePushResp = new TelePushResp();
        telePushResp.setStatus(1);
        Stopwatch stopwatch = Stopwatch.createStarted();
        SendResult sendResult = telePushMqProducer.asyncSend(telePushArgs);
        LOGGER.info("telemarketing pushTeleData, sendResult：{}", JsonUtil.toJson(sendResult));
        // 电销摘要日志
        DigestLogUtil.telDigestLog(telePushArgs, sendResult, stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        if(!Objects.isNull(sendResult) && !Objects.isNull(sendResult.getSendStatus())){
            return telePushResp;
        }
        telePushResp.setStatus(2);
        return telePushResp;
//        String request = request(telemarketingConfig.getNewHost(),telePushArgs, telemarketingConfig.getPushNameData());
//        return JSON.parseObject(request, TelePushResp.class);
    }
    
    /**
     * 获取策略分页
     *
     * @param req          入参
     * @return 策略列表
     */
    public TelePolicyConfigListResp getPolicyList(TelePolicyConfigListRequest req){
        String request =  request(telemarketingConfig.getNewHost(),req, telemarketingConfig.getPolicyNameList());
        return JSON.parseObject(request, TelePolicyConfigListResp.class);
    }
    

    private <T extends BaseTeleRequester<?>> String request(T requester, String uri) {
        String url = telemarketingConfig.getHost() + "/" + uri;
        try {
            setSign(requester, uri);
            LOGGER.info("telemarketing send, url：{}", url);
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("telemarketing send, url:{}, request:{}, resp:{}, time：{}ms", url, JsonUtil.toJson(requester), JsonUtil.toJson(result), Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (Exception e) {
            LOGGER.error("telemarketing send error,url:{}, params:{}", url, JsonUtil.toJson(requester), e);
            throw e;
        }
    }

    private <T> String request(String host, T requester, String uri) {
        String url = host + "/" + uri;
        try {
            LOGGER.info("new telemarketing send, url：{}", url);
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postJson(url, requester);
            LOGGER.info("new telemarketing send, url:{}, request:{}, resp:{}, time：{}ms", url, JsonUtil.toJson(requester), JsonUtil.toJson(result), Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (Exception e) {
            LOGGER.error("new telemarketing send error,url:{}, params:{}", url, JsonUtil.toJson(requester), e);
            throw e;
        }
    }

    private  String requestForJson(String host, String requester, String uri) {
        String url = host + "/" + uri;
        try {
            LOGGER.info("new telemarketing config send, url：{}", url);
            long startTime = Instant.now().toEpochMilli();
            String result = httpClientUtil.postForJson(url, requester,new HashMap<>());
            LOGGER.info("new telemarketing config send, url:{}, request:{}, resp:{}, time：{}ms", url, JsonUtil.toJson(requester), JsonUtil.toJson(result), Instant.now().toEpochMilli() - startTime);
            return result;
        } catch (Exception e) {
            LOGGER.error("new telemarketing config send error,url:{}, params:{}", url, JsonUtil.toJson(requester), e);
            throw e;
        }
    }
    private <T extends BaseTeleRequester<?>> void setSign(T requester, String route) {
        requester.setCall(route);
        requester.setUa(signer.getUa());
        requester.setSign(signer.sign(route, requester.getArgs(), telemarketingConfig.getKey()));
    }
}
