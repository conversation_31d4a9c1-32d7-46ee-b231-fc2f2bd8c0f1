package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 策略实时标签表
 *
 * @TableName strategy_instant_label
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StrategyInstantLabelDo extends Do {
    /**
     * 前端交互名称
     */
    private String frontDesc;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签英文名
     */
    private String labelDesc;

    /**
     * 标签查询请求参数，多个逗号分隔
     */
    private String labelReqParam;

    /**
     * 标签值数据类型
     */
    private String labelValueType;

    /**
     * 是否需要自定义加工  0 不需要 1需要
     */
    private Integer customProcess;

    /**
     * 操作类型 多个用逗号分开
     */
    private String operateType;

    /**
     * 填充类型 -1无 1下拉框单选  2输入填充 3 下拉多选
     */
    private Integer fillType;

    /**
     * 是否可选 1-可选择标签  2 默认排除项标签(不可选) 3排除项标签（可选）
     */

    private Integer optional;

    /**
     * 标签类型 1事件标签，2短信模板标签
     */
    private Integer labelType;

    /**
     * 策略类型 1-离线策略 2-T0策略
     */
    private Integer strategyType;

    private Integer excludeType;

    /**
     * 策略模型：1:常规策略(单策略,根节点画布策略) 2:非根节点画布策略
     */
    private Integer strategyModel;

    private static final long serialVersionUID = 1L;
}