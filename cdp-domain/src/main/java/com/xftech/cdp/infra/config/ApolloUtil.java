package com.xftech.cdp.infra.config;


import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public final class ApolloUtil {

    private static final String OK = "1";

    private static final String TRUE = "true";

    private ApolloUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static String getAppProperty(String para) {
        Config config = ConfigService.getAppConfig();
        return config.getProperty(para, null);
    }

    public static String getAppProperty(String para, String defaultValue) {
        if (StringUtils.isBlank(para)) {
            return defaultValue;
        }
        Config config = ConfigService.getAppConfig();
        return config.getProperty(para, defaultValue);
    }

    public static String getNameSpaceProperty(String namespace, String key) {
        try {
            Config config = ConfigService.getConfig(namespace);
            return config.getProperty(key, null);
        } catch (Exception ex) {
            return null;
        }
    }

    public static boolean switchStatus(String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        String switchStatus = ApolloUtil.getAppProperty(key);
        return OK.equals(switchStatus) || TRUE.equalsIgnoreCase(switchStatus);
    }

    public static boolean switchStatusNameSpace(String namespace, String key) {
        String switchStatus = ApolloUtil.getNameSpaceProperty(namespace, key);
        return OK.equals(switchStatus) || TRUE.equalsIgnoreCase(switchStatus);
    }

    public static List<String> getAppListProperty(String key) {
        if (StringUtils.isNotBlank(key)) {
            String value = ApolloUtil.getAppProperty(key);
            if (StringUtils.isNotBlank(value)) {
                return Arrays.asList(value.split("[,;|]"));
            }
        }
        return Lists.newArrayList();
    }

    public static <T> boolean isInTheWhitelist(String apolloKey, T value) {
        try {
            if (StringUtils.isBlank(apolloKey) || Objects.isNull(value)) {
                return false;
            }

            String apolloValue = ApolloUtil.getAppProperty(apolloKey);
            if (StringUtils.isBlank(apolloValue)) {
                return false;
            }

            List<T> whitListValues = JSONObject.parseObject(apolloValue, new TypeReference<List<T>>() {
            });
            if (value instanceof String && whitListValues.stream().anyMatch(x -> StringUtils.equalsIgnoreCase(String.valueOf(x), "ALL"))) {
                return true;
            }
            if (value instanceof Number && whitListValues.stream().anyMatch(x -> Objects.equals(x, -1))) {
                return true;
            }

            return whitListValues.stream().anyMatch(x -> Objects.equals(String.valueOf(x), String.valueOf(value)));
        } catch (Exception e) {
            log.error("ApolloUtil isInTheWhitelist error", e);
        }
        return false;
    }

    public static void main(String[] args) {
        //String configValue = "[1,2,3,-1]";
        //long value = 4;

        String configValue = "[\"aaa\",\"bbb\",\"ccc\",\"ALl\"]";
        String value = "abc";
        System.out.println(isInTheWhitelist(configValue, value));
    }

}
