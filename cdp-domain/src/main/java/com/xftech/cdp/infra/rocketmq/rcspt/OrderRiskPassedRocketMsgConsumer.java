/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.rcspt;

import com.xftech.cdp.infra.rocketmq.MessageHandlerSelector;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
@Slf4j
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_rcspt_risk_withdraw_audit_message", topic = "tp_rcspt_risk_withdraw_audit_message", consumeMode = ConsumeMode.CONCURRENTLY)
public class OrderRiskPassedRocketMsgConsumer extends MqConsumerListener<String> {

    @Resource
    private MessageHandlerSelector handlerSelector;

    @Override
    public void doMessage(String topic, String bodyMessage, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("OrderRiskPassedRocketMsgConsumerEnable")) {
            log.info("OrderRiskPassedRocketMsgConsumer doMessage topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage);
            try {
                handlerSelector.doMessage(topic, null, messageExt.getMsgId(), bodyMessage);
            } catch (Exception e) {
                log.error("OrderRiskPassedRocketMsgConsumer doMessage error topic={} messageId={} body={}", topic, messageExt.getMsgId(), bodyMessage, e);
            }
        }
    }

}