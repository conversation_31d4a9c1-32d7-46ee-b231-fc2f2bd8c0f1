package com.xftech.cdp.infra.pulsar.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.pulsar.dto.RegisterEventDTO;
import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 注册事件，用户中心上报
 *
 * @<NAME_EMAIL>
 * @date 2023-07-26 15:51
 */
@Slf4j
@Service("registerEventService")
public class RegisterEventServiceImpl implements CdpPulsarService {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public void consumer(String messageId, String eventMessage) {
        RegisterEventDTO registerEvent = JSONObject.parseObject(eventMessage, RegisterEventDTO.class);

        toBizEventMessageVO(registerEvent).ifPresent(eventMsg -> {
            log.info("toBizEventMessageVO={}", eventMsg);
            mqConsumeService.bizEventProcess(messageId, eventMsg);
        });
    }
}
