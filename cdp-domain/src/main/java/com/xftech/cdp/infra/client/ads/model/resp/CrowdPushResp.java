/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.ads.model.resp;

import lombok.Data;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ CrowdPushResp, v 0.1 2023/11/21 14:12 wancheng.qu Exp $
 */
@Data
public class CrowdPushResp {

   private Long crowdId;//	人群包ID	Long
   private Integer status;//	提交状态，0:失败, 1:成功


   public boolean isSuccess(){
      return Objects.equals(1,status);
   }

}