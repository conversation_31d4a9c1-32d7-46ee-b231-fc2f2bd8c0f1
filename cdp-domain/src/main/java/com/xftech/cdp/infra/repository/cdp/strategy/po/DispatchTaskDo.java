package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class DispatchTaskDo implements Serializable {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.biz_id
     *
     * @mbg.generated
     */
    private String bizId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.association_id
     *
     * @mbg.generated
     */
    private String associationId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.dispatch_time
     *
     * @mbg.generated
     */
    private Date dispatchTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.date_value
     *
     * @mbg.generated
     */
    private Integer dateValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.retry_times
     *
     * @mbg.generated
     */
    private Integer retryTimes;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.next_dispatch_time
     *
     * @mbg.generated
     */
    private Date nextDispatchTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.biz_type
     *
     * @mbg.generated
     */
    private Byte bizType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.exec_ret_msg
     *
     * @mbg.generated
     */
    private String execRetMsg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.last_finish_time
     *
     * @mbg.generated
     */
    private Date lastFinishTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.serial_no
     *
     * @mbg.generated
     */
    private String serialNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.d_flag
     *
     * @mbg.generated
     */
    private Byte dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column dispatch_task.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    private String extDetail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table dispatch_task
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.id
     *
     * @return the value of dispatch_task.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.id
     *
     * @param id the value for dispatch_task.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.biz_id
     *
     * @return the value of dispatch_task.biz_id
     *
     * @mbg.generated
     */
    public String getBizId() {
        return bizId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.biz_id
     *
     * @param bizId the value for dispatch_task.biz_id
     *
     * @mbg.generated
     */
    public void setBizId(String bizId) {
        this.bizId = bizId == null ? null : bizId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.association_id
     *
     * @return the value of dispatch_task.association_id
     *
     * @mbg.generated
     */
    public String getAssociationId() {
        return associationId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.association_id
     *
     * @param associationId the value for dispatch_task.association_id
     *
     * @mbg.generated
     */
    public void setAssociationId(String associationId) {
        this.associationId = associationId == null ? null : associationId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.dispatch_time
     *
     * @return the value of dispatch_task.dispatch_time
     *
     * @mbg.generated
     */
    public Date getDispatchTime() {
        return dispatchTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.dispatch_time
     *
     * @param dispatchTime the value for dispatch_task.dispatch_time
     *
     * @mbg.generated
     */
    public void setDispatchTime(Date dispatchTime) {
        this.dispatchTime = dispatchTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.date_value
     *
     * @return the value of dispatch_task.date_value
     *
     * @mbg.generated
     */
    public Integer getDateValue() {
        return dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.date_value
     *
     * @param dateValue the value for dispatch_task.date_value
     *
     * @mbg.generated
     */
    public void setDateValue(Integer dateValue) {
        this.dateValue = dateValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.retry_times
     *
     * @return the value of dispatch_task.retry_times
     *
     * @mbg.generated
     */
    public Integer getRetryTimes() {
        return retryTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.retry_times
     *
     * @param retryTimes the value for dispatch_task.retry_times
     *
     * @mbg.generated
     */
    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.next_dispatch_time
     *
     * @return the value of dispatch_task.next_dispatch_time
     *
     * @mbg.generated
     */
    public Date getNextDispatchTime() {
        return nextDispatchTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.next_dispatch_time
     *
     * @param nextDispatchTime the value for dispatch_task.next_dispatch_time
     *
     * @mbg.generated
     */
    public void setNextDispatchTime(Date nextDispatchTime) {
        this.nextDispatchTime = nextDispatchTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.status
     *
     * @return the value of dispatch_task.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.status
     *
     * @param status the value for dispatch_task.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.biz_type
     *
     * @return the value of dispatch_task.biz_type
     *
     * @mbg.generated
     */
    public Byte getBizType() {
        return bizType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.biz_type
     *
     * @param bizType the value for dispatch_task.biz_type
     *
     * @mbg.generated
     */
    public void setBizType(Byte bizType) {
        this.bizType = bizType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.exec_ret_msg
     *
     * @return the value of dispatch_task.exec_ret_msg
     *
     * @mbg.generated
     */
    public String getExecRetMsg() {
        return execRetMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.exec_ret_msg
     *
     * @param execRetMsg the value for dispatch_task.exec_ret_msg
     *
     * @mbg.generated
     */
    public void setExecRetMsg(String execRetMsg) {
        this.execRetMsg = execRetMsg == null ? null : execRetMsg.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.last_finish_time
     *
     * @return the value of dispatch_task.last_finish_time
     *
     * @mbg.generated
     */
    public Date getLastFinishTime() {
        return lastFinishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.last_finish_time
     *
     * @param lastFinishTime the value for dispatch_task.last_finish_time
     *
     * @mbg.generated
     */
    public void setLastFinishTime(Date lastFinishTime) {
        this.lastFinishTime = lastFinishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.serial_no
     *
     * @return the value of dispatch_task.serial_no
     *
     * @mbg.generated
     */
    public String getSerialNo() {
        return serialNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.serial_no
     *
     * @param serialNo the value for dispatch_task.serial_no
     *
     * @mbg.generated
     */
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo == null ? null : serialNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.d_flag
     *
     * @return the value of dispatch_task.d_flag
     *
     * @mbg.generated
     */
    public Byte getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.d_flag
     *
     * @param dFlag the value for dispatch_task.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Byte dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.created_time
     *
     * @return the value of dispatch_task.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.created_time
     *
     * @param createdTime the value for dispatch_task.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column dispatch_task.updated_time
     *
     * @return the value of dispatch_task.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column dispatch_task.updated_time
     *
     * @param updatedTime the value for dispatch_task.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getExtDetail() {
        return extDetail;
    }

    public void setExtDetail(String extDetail) {
        this.extDetail = extDetail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table dispatch_task
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bizId=").append(bizId);
        sb.append(", associationId=").append(associationId);
        sb.append(", dispatchTime=").append(dispatchTime);
        sb.append(", dateValue=").append(dateValue);
        sb.append(", retryTimes=").append(retryTimes);
        sb.append(", nextDispatchTime=").append(nextDispatchTime);
        sb.append(", status=").append(status);
        sb.append(", bizType=").append(bizType);
        sb.append(", execRetMsg=").append(execRetMsg);
        sb.append(", lastFinishTime=").append(lastFinishTime);
        sb.append(", serialNo=").append(serialNo);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", extDetail=").append(extDetail);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}