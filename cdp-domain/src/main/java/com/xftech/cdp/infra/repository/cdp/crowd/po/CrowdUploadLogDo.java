package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 人群上传日志
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdUploadLogDo extends Do {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 上传文件名
     */
    private String fileName;

    /**
     * oss文件名
     */
    private String ossFileName;

    /**
     * OSS存储路径
     */
    private String ossUrl;

    /**
     * 解析时间
     */
    private Date parsingTime;
}
