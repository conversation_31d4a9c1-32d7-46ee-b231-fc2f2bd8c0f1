package com.xftech.cdp.infra.client.loanmarket.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-05-16
 */
@Data
public class GetUtmSourceResp {
    @JsonProperty("id")
    @JSONField(name = "id")
    private String id;

    @JsonProperty("utm_source")
    @JSONField(name = "utm_source")
    private String utmSource;

    /**
     * 首贷运营规则（可营销——0；进件不可营销——1；注册不可营销——2；放款不可营销——3
     */
    @JsonProperty("new_marketing_type")
    @JSONField(name = "new_marketing_type")
    private String newMarketingType;

    /**
     * 复贷运营规则（可营销——0；进件不可营销——1；注册不可营销——2；放款不可营销——3
     */
    @JsonProperty("old_marketing_type")
    @JSONField(name = "old_marketing_type")
    private String oldMarketingType;

    /**
     * 首贷注册N天后可营销，-1 默认值（类型为1时，注册一直处于不可营销）
     */
    @JsonProperty("new_no_marketing_days")
    @JSONField(name = "new_no_marketing_days")
    private Integer newNoMarketingDays;

    /**
     * 复贷注册N天后可营销，-1 默认值（类型为1时，注册一直处于不可营销）
     */
    @JsonProperty("old_no_marketing_days")
    @JSONField(name = "old_no_marketing_days")
    private Integer oldNoMarketingDays;
}
