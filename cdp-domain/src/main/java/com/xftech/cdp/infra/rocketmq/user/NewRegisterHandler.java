package com.xftech.cdp.infra.rocketmq.user;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.InfraException;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.NewRegisterVO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ThreadUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * @version $ NewTrackingCardClickHandler, v 0.1 2025/3/25 16:40 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class NewRegisterHandler implements MessageHandler {
    @Resource
    private MqConsumeService mqConsumeService;

    @Autowired
    private UserCenterClient userCenterClient;

    @Value("${NewRegisterHandler.count:3}")
    private Integer count;

    @Value("${NewRegisterHandler.sleepTime:100}")
    private Integer sleepTime;

    @Override
    public String getTopic() {
        return "tp_user_register";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        NewRegisterVO messageVO = JSONObject.parseObject(message.toString(), NewRegisterVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);

        if (bizEventMessageVO == null) {
            return false;
        }

        bizEventMessageVO.setBizEventType("NewRegister-useractionlog");
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(NewRegisterVO newRegisterVO) {
        if (newRegisterVO == null || newRegisterVO.getRequestBody() == null || StringUtils.isAnyBlank(newRegisterVO.getRequestBody().getApp()) || StringUtils.isAnyBlank(newRegisterVO.getRequestBody().getSourceType()) || StringUtils.isAnyBlank(newRegisterVO.getRequestBody().getUtmSource()) || StringUtils.isAnyBlank(newRegisterVO.getRequestBody().getCurrentUtmSource())) {
            return null;
        }

        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();


        //xyf转xyf01
        if (Constants.XYF.equals(newRegisterVO.getRequestBody().getApp())) {
            if (StringUtils.isBlank(newRegisterVO.getRequestBody().getMobile())) {
                LogUtil.logDebug("NewRegister-useractionlog mobile is null");
                return null;
            }

            /* 循环start */
            int retryCount = 1;
            UserInfoResp userInfoResp = null;
            while (userInfoResp == null && retryCount <= count) {
                try {
                    userInfoResp = userCenterClient.getUserByMobile(newRegisterVO.getRequestBody().getMobile(), Constants.XYF01);
                    if (userInfoResp != null) {
                        bizEventMessageVO.setApp(Constants.XYF01);
                        bizEventMessageVO.setCreditUserId(userInfoResp.getCreditUserId());
                        bizEventMessageVO.setAppUserId(userInfoResp.getCreditUserId());
                        bizEventMessageVO.setInnerApp(userInfoResp.getInnerApp());
                        break;
                    }
                } catch (Exception e) {
                    log.warn("NewRegisterHandler retry exception", e);
                }

                try {
                    Thread.sleep(sleepTime);
                } catch (Exception ignore) {

                }

                retryCount++;
            }
            /* 循环end */

            if (userInfoResp == null) {
                return null;
            }
        } else {
            bizEventMessageVO.setAppUserId(newRegisterVO.getCreditUserId());
            bizEventMessageVO.setApp(newRegisterVO.getRequestBody().getApp());
            bizEventMessageVO.setInnerApp(newRegisterVO.getRequestBody().getInnerApp());
        }


        if (StringUtils.isNotBlank(newRegisterVO.getRequestBody().getCreatedTime())) {
            bizEventMessageVO.setTriggerDatetime(newRegisterVO.getRequestBody().getCreatedTime());
        } else {
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        }

        bizEventMessageVO.setUtmSource(newRegisterVO.getRequestBody().getUtmSource());
        bizEventMessageVO.setSourceType(newRegisterVO.getRequestBody().getSourceType());
        bizEventMessageVO.setOs(newRegisterVO.getRequestBody().getOs());

        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();

        extrData.setCreatedTime(newRegisterVO.getRequestBody().getCreatedTime());
        extrData.setSourceType(newRegisterVO.getRequestBody().getSourceType());
        extrData.setCurrentUtmSource(newRegisterVO.getRequestBody().getCurrentUtmSource());

        try {
            //biz_event_data不会必传
            if (StringUtils.isNotBlank(newRegisterVO.getRequestBody().getBizEventData())) {
                JSONObject bizEventDataJson = JSONObject.parseObject(newRegisterVO.getRequestBody().getBizEventData());

                Integer xcMk = bizEventDataJson.getInteger("xc_mk");
                if (xcMk != null) {
                    extrData.setUserRegisterSource(xcMk);
                    if (xcMk == 1) {
                        Integer xcRgs = bizEventDataJson.getInteger("xc_rgs");
                        if (xcRgs != null) {
                            extrData.setEquityType(xcRgs);
                        }
                    }
                }
            }
        } catch (JSONException e) {
            log.error("Failed to parse bizEventData JSON string: " + newRegisterVO.getRequestBody().getBizEventData(), e);
        }
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}
