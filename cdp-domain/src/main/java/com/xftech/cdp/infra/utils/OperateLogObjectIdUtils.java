/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.utils;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ OperateLogObjectIdHolder, v 0.1 2024/5/24 11:21 lingang.han Exp $
 */

public class OperateLogObjectIdUtils {

    private static final ThreadLocal<List<Long>> OPERATE_LOG_OBJ_ID = new ThreadLocal<>();

    public static void set(List<Long> objectIds) {
        OPERATE_LOG_OBJ_ID.set(objectIds);
    }

    public static List<Long> get() {
        return OPERATE_LOG_OBJ_ID.get();
    }

    public static void clear(){
        OPERATE_LOG_OBJ_ID.remove();
    }
}