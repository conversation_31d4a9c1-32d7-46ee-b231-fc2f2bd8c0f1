package com.xftech.cdp.infra.client.appcore;


import com.xftech.cdp.domain.strategy.model.enums.BalanceInvokeStatusEnum;
import com.xftech.cdp.infra.client.appcore.request.BalanceOptReq;
import com.xftech.cdp.infra.client.appcore.response.BalanceOptResp;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xinfei.appcore.facade.rr.base.AppBaseResponse;
import com.xinfei.appcore.facade.rr.enums.IncreaseBalanceRefType;
import com.xinfei.appcore.facade.rr.request.IncreaseBalanceRequest;
import com.xinfei.appcore.facade.xyf.BalanceFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ UserPlatformClient, v 0.1 2025/1/3 21:35 mingwen.zang Exp $
 */
@Component
@Slf4j
public class UserCoreClient {

    @Resource
    private BalanceFacade balanceFacade;

    static BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

    public BalanceOptResp increaseBalance(BalanceOptReq req) {
        BalanceOptResp.BalanceOptRespBuilder builder = BalanceOptResp.builder();
        IncreaseBalanceRequest request = new IncreaseBalanceRequest();
        request.setUserNo(req.getUserNo());
        // amount 单位是（分）, 所以需要转换
        request.setAmount(req.getAmount().multiply(ONE_HUNDRED).longValue());
        request.setRefType(IncreaseBalanceRefType.ACTIVITY_REWARD.getCode());
        request.setDescription(req.getDescription());

        try {
            log.info("UserCoreClient request:{}", req);
            AppBaseResponse<Boolean> response = balanceFacade.increase(request);
            log.info("UserCoreClient response:{}", response);
            if (response != null && Boolean.TRUE.equals(response.getData())) {
                builder.status(BalanceOptResp.getFromBooleanStatus(response.getData(), BalanceInvokeStatusEnum.BUSINESS_FAIL.getStatus()));
            }

        } catch (Exception e) {
            builder.status(BalanceInvokeStatusEnum.REMOTE_FAIL.getStatus());
            log.error("调用用户平台增加用户月接口异常, req: {}", req, e);
        }
        return builder.build();
    }

}
