package com.xftech.cdp.infra.aviator.function;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.xftech.cdp.api.dto.base.TimeFormat;

import java.time.LocalDate;
import java.util.Map;

/**
 * 自定义方法 getDate(int)  0：当天  -n：往前n天  +n：往后n天
 * <p>
 * 格式：yyyy-MM-dd
 *
 * @<NAME_EMAIL>
 * @date 2023/5/20 13:54
 */
public class DateFunction extends AbstractFunction {

    public static final String NAME = "getDate";

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Integer plusDays = FunctionUtils.getNumberValue(arg1, env).intValue();
        return new AviatorString(LocalDateTimeUtil.format(LocalDate.now().plusDays(plusDays), TimeFormat.DATE));
    }

    @Override
    public String getName() {
        return NAME;
    }
}
