package com.xftech.cdp.infra.exception;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/26 14:38:55
 */
@Getter
@Setter
public class InterfaceException extends RuntimeException {

    private int code;

    public InterfaceException(String message) {
        super(message);
    }

    public InterfaceException(int code, String message) {
        super(message);
        this.code = code;
    }

    public InterfaceException(String message, Throwable cause) {
        super(message, cause);
    }

    public InterfaceException(Throwable cause) {
        super(cause);
    }

}
