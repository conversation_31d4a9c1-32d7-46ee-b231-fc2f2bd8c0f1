package com.xftech.cdp.infra.aviator.function;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.domain.strategy.model.enums.label.OperatorEnum;
import com.xftech.cdp.domain.strategy.model.enums.label.ValueTypeEnum;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 日期时间比较函数
 */
public class TimeCompareFunction extends AbstractFunction {

    private static final IUdpLogger logger = LogUtil.getLogger(TimeCompareFunction.class);

    public static final String NAME = "time.compare";

    /**
     *  时间比较，只支持固定时间格式和时间戳(long)
     * @param env
     * @param arg1  左边字段    -- Date类型/时间戳
     * @param arg2  比较符
     * @param arg3  右边字段    -- Date类型/时间戳
     * @param arg4  之间时右边第二个字段
     * @return  计算存在异常直接返回false
     */
    @Override
    public AviatorObject call(Map<String, Object> env,
                              AviatorObject arg1,
                              AviatorObject arg2,
                              AviatorObject arg3,
                              AviatorObject arg4) {
        //比较符
        String operator = FunctionUtils.getStringValue(arg2,env);
        //操作符不能为空
        if(StringUtils.isEmpty(FunctionUtils.getStringValue(arg2,env))){
            return AviatorBoolean.FALSE;
        }

        //有值判断
        if(operator.equals(OperatorEnum.HASVAL.getCode())){
            return AviatorBoolean.valueOf(!arg1.isNull(env));
        //没值判断
        }else if(operator.equals(OperatorEnum.NOHASVAL.getCode())){
            return AviatorBoolean.valueOf(arg1.isNull(env));
        //其他判断
        }else {
            //左侧时间字段、右侧时间字段 值不能为空
            boolean param = arg1.getValue(env) != null && arg3.getValue(env) != null;
            if(param){
                try {
                    // 左边字段值
                    Date leftValue = convertDate(arg1,env);
                    // 右边字段值
                    Date rightValue = convertDate(arg3,env);

                    // 之间判断
                    if(operator.equals(OperatorEnum.BETWEEN.getCode())){
                        //右边第二个字段值不能为空
                        if(arg4.getValue(env) == null){
                            return AviatorBoolean.FALSE;
                        }

                        // 右边第二个字段值字符串
                        Date rightValue2 = convertDate(arg4,env);

                        try {
                            LocalDateTime l = DateTimeUtil.getDateTime(leftValue,null,null,null);
                            LocalDateTime r1 = DateTimeUtil.getDateTime(rightValue,null,null,null);
                            LocalDateTime r2 = DateTimeUtil.getDateTime(rightValue2,null,null,null);

                            return operatorDate(l,r1,r2,operator);
                        }catch (Exception e){
                            logger.warn("time format error, leftValue:{}, rightValue:{}, rightValue2:{}", arg1.getValue(env), arg3.getValue(env), arg4.getValue(env));
                            return AviatorBoolean.FALSE;
                        }
                    }else {
                        try {
//                            Object num = arg5.getValue(env);
                            LocalDateTime l = DateTimeUtil.getDateTime(leftValue,null,null,null);
                            LocalDateTime r = DateTimeUtil.getDateTime(rightValue,null,null,null);
//                            LocalDateTime r = DateTimeUtil.getDateTime(rightValue,FunctionUtils.getStringValue(arg4,env),num.toString(),FunctionUtils.getStringValue(arg6,env));

                            return operatorDate(l,r,operator);
                        }catch (Exception e){
                            logger.warn(e.getMessage(),e);
                            logger.warn("time format error, leftValue:{}, rightValue:{}", arg1.getValue(env), arg3.getValue(env));
                            return AviatorBoolean.FALSE;
                        }
                    }
                }catch (Exception e){
                    return AviatorBoolean.FALSE;
                }
            }else {
                return AviatorBoolean.FALSE;
            }
        }
    }

    @Override
    public String getName() {
        return NAME;
    }

    /**
     * 对参数进行转换
     */
    private Date convertDate(AviatorObject arg, Map<String, Object> env){

        Object val = arg.getValue(env);
        if(val instanceof Long){
            return new Date((Long)FunctionUtils.getNumberValue(arg,env));
        } else if(val instanceof Date){
            return (Date) FunctionUtils.getJavaObject(arg,env);
        } else if(val instanceof String){
            return (Date) ValueTypeEnum.DATE.normalizeValue(val,null);
        }else {
            throw new RuntimeException("error time type : " + val);
        }
    }

    private AviatorBoolean operatorDate(LocalDateTime l, LocalDateTime r, String operator){
        return operatorDate(l,r,null,operator);
    }

    private AviatorBoolean operatorDate(LocalDateTime l, LocalDateTime r, LocalDateTime r2, String operator){

        if(operator.equals(OperatorEnum.EQ.getCode())){

            return AviatorBoolean.valueOf(l.equals(r));
        }else if(operator.equals(OperatorEnum.NEQ.getCode())){

            return AviatorBoolean.valueOf(!l.equals(r));
        }else if(operator.equals(OperatorEnum.GT.getCode())){

            return AviatorBoolean.valueOf(l.isAfter(r));
        }else if(operator.equals(OperatorEnum.GE.getCode())){

            return AviatorBoolean.valueOf(l.isAfter(r) || l.equals(r));
        }else if(operator.equals(OperatorEnum.LT.getCode())){

            return AviatorBoolean.valueOf(l.isBefore(r));
        }else if(operator.equals(OperatorEnum.LE.getCode())){

            return AviatorBoolean.valueOf(l.isBefore(r) || l.equals(r));
        }else if(operator.equals(OperatorEnum.BETWEEN.getCode())){

            return AviatorBoolean.valueOf(l.isAfter(r) && l.isBefore(r2));
        }else {
            logger.warn("error operator: {}", operator);
            return AviatorBoolean.FALSE;
        }
    }
}
