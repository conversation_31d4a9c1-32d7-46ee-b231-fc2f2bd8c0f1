/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy.label.handler;

import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.increaseamt.RcsProviderClient;
import com.xftech.cdp.infra.client.increaseamt.model.req.AccessControlQueryReq;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.AccessControlResp;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Response;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ BanDistributionTruncationStrategyLabelHandler, v 0.1 2024/7/3 16:56 lingang.han Exp $
 */
@Slf4j
@Component
public class BanDistributionUnTruncationStrategyLabelHandler implements LabelHandler {
    @Autowired
    private RcsProviderClient rcsProviderClient;
    @Autowired
    private CisService cisService;

    @Override
    public String getLabel() {
        return "ban_distribution_un_truncation_strategy";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<AdsLabelResp.Param> result = new ArrayList<>();
        for (Long userNo : batchAdsLabelVO.getUserNoList()) {
            BaseCisResp<RegisterInfoByUserNo.RespDto> respDtoBaseCisResp = cisService.queryRegisterInfoByUserNo(userNo);
            log.info("贷超实时标签查询cis接口req:{},resp:{}", userNo, JsonUtil.toJson(respDtoBaseCisResp));
            if (respDtoBaseCisResp != null && respDtoBaseCisResp.getData() != null
                    && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getCustNo())
                    && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getIdCardNumber())) {
                RegisterInfoByUserNo.RespDto userInfo = respDtoBaseCisResp.getData();
                String orderNo = String.format("%s_%s", DateTimeUtil.formatDateToStr(new Date(), null), userInfo.getUserNo());
                RequestHeader requestHeader = rcsProviderClient.buildAccessControlHeader(orderNo, userInfo.getApp(), userInfo.getSourceChannel());
                AccessControlQueryReq accessControlQueryReq = new AccessControlQueryReq(userInfo.getIdCardNumber(), userInfo.getCustNo(), null);
                Response<AccessControlResp> accessControlRespResponse = rcsProviderClient.accessControlQuery(requestHeader, accessControlQueryReq);
                //非分发结尾策略 forbidden_status=true && !Objects.equals(forbidden_reason,"RTM3015") && Objects.equals(diversion_label,"DL01")
                if (accessControlRespResponse != null && accessControlRespResponse.getData() != null) {
                    AccessControlResp data = accessControlRespResponse.getData();
                    if (data.getForbidden_status() && !Objects.equals(data.getForbidden_reason(), "RTM3015") && Objects.equals(data.getDiversion_label(), "DL01")) {
                        result.add(new AdsLabelResp.Param(userNo, null, "1"));
                    } else {
                        result.add(new AdsLabelResp.Param(userNo, null, "0"));
                    }
                }
            } else {
                result.add(new AdsLabelResp.Param(userNo, null, null));
            }
        }
        return new AdsLabelResp(label, result);
    }


}