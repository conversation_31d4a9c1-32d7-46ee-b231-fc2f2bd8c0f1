package com.xftech.cdp.adapter.crowd;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.proxy.Tracer;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.crowd.CrowdXxlJobParam;
import com.xftech.cdp.application.CrowdHandler;
import com.xftech.cdp.application.StrategyHandler;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdConstant;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.domain.crowd.repository.AdsLabelMonitorDfRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.CrowdPushBatchService;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@Slf4j
@Component
public class CrowdDispatch {

    private final CrowdHandler crowdHandler;
    private final StrategyHandler strategyHandler;

    @Autowired
    CrowdConfig crowdConfig;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Resource
    private AdsLabelMonitorDfRepository adsLabelMonitorDfRepository;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private CrowdPushBatchService crowdPushBatchService;

    public CrowdDispatch(CrowdHandler crowdHandler, StrategyHandler strategyHandler) {
        this.crowdHandler = crowdHandler;
        this.strategyHandler = strategyHandler;
    }

    @XxlJob(XxlJobConstants.CROWD_DISPATCH)
    public ReturnT<String> crowdDispatch(String param) {
        log.info("crowdDispatch begin param:{}", param);
        XxlJobLogger.log("crowdDispatch begin param:{}", param);
        if (StringUtils.isEmpty(param)) {
            log.warn("crowdDispatch param error param:{}", param);
            XxlJobLogger.log("crowdDispatch param error param:{}", param);
            return ReturnT.FAIL;
        }
        try {
            CrowdXxlJobParam crowdXxlJobParam = JSON.parseObject(param, CrowdXxlJobParam.class);
            crowdHandler.execute(crowdXxlJobParam);
        } catch (Exception e) {
            log.warn("crowdDispatch execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }


    @XxlJob(XxlJobConstants.CROWD_RESET_EVERYDAY)
    public ReturnT<String> crowdReset(String param) {
        log.info("crowdReset param:{}", param);
        XxlJobLogger.log("crowdReset param:{}", param);
        if (StringUtils.isEmpty(param)) {
            log.warn("crowdReset execute error param:{}: ", param);
            XxlJobLogger.log("crowdReset execute error param:{}: ", param);
            return ReturnT.FAIL;
        }
        try {
            crowdHandler.crowdReset();
        } catch (Exception e) {
            Tracer.logEvent("crowdReset", "error");
            log.error("crowdReset execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            throw e;
        }
        return ReturnT.SUCCESS;
    }


    @XxlJob(XxlJobConstants.CROWD_WAREHOUSE_ALARM)
    public ReturnT<String> crowdWareHouseAlarm(String param) {
        log.info("crowdWareHouseAlarm param:{}", param);
        XxlJobLogger.log("crowdWareHouseAlarm param:{}", param);
        try {
            // todo 清除任务
            if (crowdConfig.isCrowdJobStop()) {
                log.info("放弃任务不执行");
                return ReturnT.SUCCESS;
            }
            crowdHandler.crowdWareHouseAlarm();
        } catch (Exception e) {
            Tracer.logEvent("crowdWareHouseAlarm", "error");
            log.error("crowdWareHouseAlarm execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            throw e;
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.CROWD_PUSH_RESULT_QUERY)
    public ReturnT<String> crowdPushResultQuery(String param) {
        log.info("crowdPushResultQuery begin param:{}", param);
        XxlJobLogger.log("crowdPushResultQuery begin param:{}", param);
        try {
            // 批次记录处理
            crowdHandler.crowdPushResultQuery();
            // 策略当天数据统计
            strategyHandler.countTodayDispatch();
        } catch (Exception e) {
            log.warn("crowdPushResultQuery execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.CLEAR_HISTORY_CROWD_DETAIL)
    public ReturnT<String> clearHistoryCrowdDetail(String param) {
        log.info("clearHistoryCrowdDetail begin param:{}", param);
        XxlJobLogger.log("clearHistoryCrowdDetail begin param:{}", param);
        try {
            crowdPackService.clearHistoryCrowdDetail();
        } catch (Exception e) {
            log.warn("clearHistoryCrowdDetail execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.CLEAR_HISTORY_CROWD_DETAIL_BY_TRUNCATE)
    public ReturnT<String> clearHistoryCrowdDetailTruncate(String param) {
        log.info("clearHistoryCrowdDetail begin param:{}", param);
        XxlJobLogger.log("clearHistoryCrowdDetail begin param:{}", param);
        try {
            crowdPackService.clearHistoryCrowdDetailByTruncate();
        } catch (Exception e) {
            log.warn("clearHistoryCrowdDetail execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.TEST)
    public ReturnT<String> test(String param) {
        try {
            Boolean exist = adsLabelMonitorDfRepository.selectExistByDataDate(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), CrowdConstant.MONITOR);
            XxlJobLogger.log("test:{}", exist);
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 人群包刷新超时告警
     */
    @XxlJob(XxlJobConstants.CROWD_REFRESH_TIMEOUT_ALARM)
    public ReturnT<String> refreshTimeoutAlarm(String param) {
        try {
            List<String> refreshTimeoutAtMobiles = crowdConfig.getCrowdRefreshTimeoutAtMobiles();
            Integer refreshTimeoutLimit = crowdConfig.getCrowdRefreshTimeoutLimit();
            String alarmTips = CharSequenceUtil.format("今日超过{}分钟未完成刷新，请技术处理", refreshTimeoutLimit);

            crowdPackService.selectTodayByStatus(CrowdStatusEnum.EXECUTING, CrowdStatusEnum.SUCCESS, CrowdStatusEnum.FAILED).forEach(crowdPackDo -> {
                String refreshTimeoutAlarmKey = RedisKeyUtils.genCrowdRefreshTimeoutAlarmKey(crowdPackDo.getId());
                if (redisUtils.hasKey(refreshTimeoutAlarmKey)) {
                    return;
                }

                String refreshTime = redisUtils.get(RedisKeyUtils.genCrowdLatestRefreshTimeKey(crowdPackDo.getId()));
                if (org.apache.commons.lang3.StringUtils.isNotBlank(refreshTime)) {
                    LocalDateTime latestRefreshTime = LocalDateTimeUtil.parse(refreshTime, TimeFormat.DATE_TIME);
                    log.info("群包刷新超时告警，人群包ID:{}，刷新开始时间：{}", crowdPackDo.getId(), latestRefreshTime);
                    LocalDateTime now = LocalDateTime.now();
                    if (Duration.between(latestRefreshTime, now).toMinutes() > refreshTimeoutLimit) {
                        refreshTimeoutAtMobiles.add(crowdPackDo.getUpdatedOpMobile());
                        DingTalkUtil.dingTalk(dingTalkConfig, crowdPackDo, alarmTips, refreshTimeoutAtMobiles);

                        LocalDateTime endTime = LocalDateTime.now().with(LocalTime.of(23, 59, 59));
                        redisUtils.set(refreshTimeoutAlarmKey, now, Duration.between(now, endTime).getSeconds());
                        log.info("群包刷新超时告警，人群包ID:{}，刷新开始时间：{}，告警时间：{}", crowdPackDo.getId(), latestRefreshTime, now);
                    }
                }
            });
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    /**
     * @param null:
     * @return String
     * <AUTHOR>
     * @description 人群包任务跑批结果查询
     * @date 2023/11/21 20:27
     */
    @XxlJob(XxlJobConstants.PUSH_CROWD_TOTAL)
    public ReturnT<String> pushCrowdTotal(String param) {
        log.info("pushCrowdTotal start !");
        try {
            crowdPushBatchService.getAllCrowTotal();
        } catch (Exception e) {
            log.error("pushCrowdTotal error",e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * @param null:
     * @return null
     * <AUTHOR>
     * @description 推送人群数据到数仓
     * @date 2023/11/21 20:41
     */
    @XxlJob(XxlJobConstants.PUSH_CROWDLIST)
    public ReturnT<String> pushCrowdList(String param) {
        log.info("pushCrowdList start !");
        try {
            crowdPackService.operateSql();
            crowdPushBatchService.pushCrowdList();
        } catch (Exception e) {
            log.error("pushCrowdList error",e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**人群包无状态重试*/
    @XxlJob(XxlJobConstants.PUSH_CROWDLIST_RETRY)
    public ReturnT<String> pushCrowdListRetry(String param) {
        log.info("pushCrowdListRetry start !");
        try {
            crowdPushBatchService.pushCrowdListRetry();
        } catch (Exception e) {
            log.error("pushCrowdList error",e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 新方案人群包记录crowd_exec_log
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.PUSH_CROWD_RECORD_LOG)
    public ReturnT<String> pushCrowdListLog(String param) {
        log.info("pushCrowdListCrowdExecLog start !");
        try {
            crowdPushBatchService.pushCrowdListLog();
        } catch (Exception e) {
            log.error("pushCrowdListCrowdExecLog error",e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 人群执行结果统计报表
     */
    @XxlJob(XxlJobConstants.REPORT_DAILY_CROWD)
    public ReturnT<String> reportDailyCrowdJob(String param){
        try {
            crowdHandler.reportDailyCrowd();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.warn("reportDailyCrowdJob execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 大数据错误人群包状态重置
     */
    @XxlJob(XxlJobConstants.RESET_BIGDATA_ERROR_RESULT_CROWD_PACKS)
    public ReturnT<String> resetBigDataErrorResultCrowdPacks(String param){
        try {
            log.info("CrowdDispatch RESET_BIGDATA_ERROR_RESULT_CROWD_PACKS start param={}", param);
            crowdHandler.resetBigDataErrorResultCrowdPacks();
            log.info("CrowdDispatch RESET_BIGDATA_ERROR_RESULT_CROWD_PACKS finish param={}", param);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.warn("CrowdDispatch RESET_BIGDATA_ERROR_RESULT_CROWD_PACKS execute error", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

}
