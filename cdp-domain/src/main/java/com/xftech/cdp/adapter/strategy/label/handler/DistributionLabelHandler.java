/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy.label.handler;

import java.util.List;

import javax.annotation.Resource;

import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.infra.client.BaseRequest;
import com.xftech.cdp.infra.client.BaseResponse;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.platform.PlatFormClient;
import com.xftech.cdp.infra.client.platform.request.GetByOrderNoRequest;
import com.xftech.cdp.infra.client.platform.response.GetByOrderNoResponse;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DistributionLabelHandler implements LabelHandler {

    @Resource
    private PlatFormClient platFormClient;

    @Override
    public String getLabel() {
        return "get_status_by_order_no";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<AdsLabelResp.Param> result = Lists.newArrayList();
        List<Long> userNoList = batchAdsLabelVO.getUserNoList();

        BaseResponse<GetByOrderNoResponse> response = platFormClient.getByOrderNo(new BaseRequest<>(new GetByOrderNoRequest(batchAdsLabelVO.getOrderNo())));
        if (response.isSuccess() && response.getResponse() != null && response.getResponse().getStatus() != null && response.getResponse().getStatus() > 12) {
            result.add(new AdsLabelResp.Param(userNoList.get(0), null, "1"));
        } else {
            result.add(new AdsLabelResp.Param(userNoList.get(0), null, "0"));
        }

        return new AdsLabelResp(label, result);
    }

}