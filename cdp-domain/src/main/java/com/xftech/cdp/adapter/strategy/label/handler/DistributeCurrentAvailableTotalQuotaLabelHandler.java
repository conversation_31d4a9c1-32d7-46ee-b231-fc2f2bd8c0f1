/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy.label.handler;


import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.adapter.strategy.label.request.DistributeCurrentAvailableTotalQuotaRequest;
import com.xftech.cdp.adapter.strategy.label.request.DistributeCurrentAvailableTotalQuotaResponse;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class DistributeCurrentAvailableTotalQuotaLabelHandler implements LabelHandler {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String getLabel() {
        return "distribute_current_available_total_quota";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        try {
            List<AdsLabelResp.Param> result = new ArrayList<>();

            for (Long userNo : batchAdsLabelVO.getUserNoList()) {
                DistributeCurrentAvailableTotalQuotaRequest req = new DistributeCurrentAvailableTotalQuotaRequest();
                req.setUa(Constants.APP_NAME);
                DistributeCurrentAvailableTotalQuotaRequest.QuotaQueryDto args = new DistributeCurrentAvailableTotalQuotaRequest.QuotaQueryDto();
                args.setUserNo(userNo);
                args.setStatusList(Collections.singletonList(12));
                args.setMatchTypeList(Collections.singletonList("1"));
                req.setArgs(args);
                DistributeCurrentAvailableTotalQuotaResponse response = request(req);

                if (Objects.nonNull(response) && response.isSuc() && Objects.nonNull(response.getResult())) {
                    Long currentAvailableTotalQuota = response.getResult();
                    //分转元
                    BigDecimal currentAvailableTotalQuotaResult = new BigDecimal(currentAvailableTotalQuota).divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);

                    result.add(new AdsLabelResp.Param(userNo, null, currentAvailableTotalQuotaResult.toString()));
                }
            }
            return new AdsLabelResp(label, result);
        } catch (Exception e) {
            log.info("DistributeCurrentAvailableTotalQuotaLabelHandler执行execute方法时发生异常", e);
            return new AdsLabelResp(label, Collections.emptyList());
        }
    }


    private DistributeCurrentAvailableTotalQuotaResponse request(DistributeCurrentAvailableTotalQuotaRequest req) {
        String uri = ApolloUtil.getAppProperty("xf.distributecore.url", "http://distributecore.xinfei.io/quota/amountQuery");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(req, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
            DistributeCurrentAvailableTotalQuotaResponse resp = JsonUtil.parse(responseEntity.getBody(), DistributeCurrentAvailableTotalQuotaResponse.class);
            log.info("接口请求日志:[{}],url:{}, header:{}, request:{},resp:{}",
                    getLabel(),
                    uri, JsonUtil.toJson(headers), JsonUtil.toJson(req), JsonUtil.toJson(responseEntity));
            if (resp == null || !Objects.equals(responseEntity.getStatusCode(), HttpStatus.OK)) {
                log.error("接口返回错误码:[{}],url:{},header:{}, request:{},resp:{}",
                        getLabel(),
                        uri, JsonUtil.toJson(headers), JsonUtil.toJson(req),
                        JsonUtil.toJson(responseEntity), NoneException.catError());
            }
            return resp;
        } catch (Exception ex) {
            log.error("接口异常:[{}],url:{},request:{}", getLabel(), uri, JsonUtil.toJson(requestEntity), ex);
        }
        return null;
    }

}