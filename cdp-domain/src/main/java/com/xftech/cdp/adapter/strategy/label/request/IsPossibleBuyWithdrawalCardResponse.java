package com.xftech.cdp.adapter.strategy.label.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ IsPossibleBuyWithdrawalCardResponse, v 0.1 2024/12/24 10:29 tianshuo.qiu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IsPossibleBuyWithdrawalCardResponse {
    @ApiModelProperty(value = "异常code", required = true)
    private String code = "-1";

    @ApiModelProperty(value = "异常信息", required = true)
    private String message = "sys error";

    private Boolean data;
}
