/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.label.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.label.LabelConfigListReq;
import com.xftech.cdp.domain.label.dto.MetaLabelJoinLabelDto;
import com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * <AUTHOR>
 * @version $ MetaLabelRepository, v 0.1 2024/6/19 15:05 lingang.han Exp $
 */

@Repository
public class MetaLabelRepository {
    public void insert(MetaLabelDo metaLabelDo) {
        if (metaLabelDo == null) {
            return;
        }
        DBUtil.insert("metaLabelMapper.insertSelective", metaLabelDo);
    }

    public Boolean updateSelective(MetaLabelDo metaLabelDo) {
        return DBUtil.update("metaLabelMapper.updateByPrimaryKeySelective", metaLabelDo) > 0;
    }

    public MetaLabelDo queryByLabelCode(String labelCode) {
        if (StringUtils.isBlank(labelCode)) {
            return null;
        }
        return DBUtil.selectOne("metaLabelMapper.queryByLabelCode", labelCode);
    }

    public boolean exitsMetaLabel(MetaLabelDo metaLabelDo) {
        Integer num = DBUtil.selectOne("metaLabelMapper.exitsMetaLabel", metaLabelDo);
        return num > 0;
    }

    public boolean updateByLabelCode(MetaLabelDo metaLabelDo) {
        metaLabelDo.setUpdatedTime(new Date());
        return DBUtil.update("metaLabelMapper.updateByLabelCode", metaLabelDo) > 0;
    }

    public Page<MetaLabelJoinLabelDto> queryJoinLabelPage(LabelConfigListReq labelConfigListReq) {
        return DBUtil.selectPage("metaLabelMapper.queryJoinLabelPage", labelConfigListReq, labelConfigListReq.getBeginNum(), labelConfigListReq.getSize());
    }

    public Boolean clearCheckResultById(Long id) {
        return DBUtil.update("metaLabelMapper.clearCheckResultById", id) > 0;
    }
}