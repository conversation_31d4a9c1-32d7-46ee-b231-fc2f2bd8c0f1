package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.stat.entity.StatOfflineStrategyGroupDataEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/27 14:08
 */
@Component
public class StatOfflineStrategyGroupDataRepository {
    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public StatOfflineStrategyGroupDataEntity selectById(Long id) {
        return DBUtil.selectOne("statOfflineStrategyGroupDataMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StatOfflineStrategyGroupDataEntity param) {
        return DBUtil.insert("statOfflineStrategyGroupDataMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StatOfflineStrategyGroupDataEntity param) {
        return DBUtil.update("statOfflineStrategyGroupDataMapper.updateByPrimaryKeySelective", param) > 0;
    }

    public List<StatOfflineStrategyGroupDataEntity> selectByStrategyIdAndFlowDataId(Long strategyId, Long strategyFlowDataId) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("strategyFlowDataId", strategyFlowDataId);
        return DBUtil.selectList("statOfflineStrategyGroupDataMapper.selectByStrategyIdAndFlowDataId", param);
    }

    public StatOfflineStrategyGroupDataEntity getLastByStrategyGroupIdAndBizDate(Long strategyGroupId, LocalDateTime bizDate){
        Map<String, Object> param = new HashMap<>();
        param.put("strategyGroupId", strategyGroupId);
        param.put("bizDate", bizDate);
        return DBUtil.selectOne("statOfflineStrategyGroupDataMapper.getLastByStrategyGroupIdAndBizDate", param);
    }

    public StatOfflineStrategyGroupDataEntity getByStrategyGroupIdAndBizDateAndFlowDataId(Long strategyGroupId, LocalDateTime bizDate, Long strategyFlowDataId) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyGroupId", strategyGroupId);
        param.put("bizDate", bizDate);
        param.put("strategyFlowDataId", strategyFlowDataId);
        return DBUtil.selectOne("statOfflineStrategyGroupDataMapper.getByStrategyGroupIdAndBizDateAndFlowDataId", param);
    }
}
