/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.dispatch;

import cn.hutool.core.thread.ThreadUtil;
import com.dianping.cat.proxy.Tracer;
import com.dianping.cat.proxy.spi.Transaction;
import com.google.common.base.Stopwatch;
import com.xftech.cdp.domain.cache.CacheStrategyMarketChannelService;
import com.xftech.cdp.domain.crowd.service.CrowdPushBatchService;
import com.xftech.cdp.domain.dispatch.dto.BatchDispatchFlcResultDto;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.dispatch.dto.SingleDispatchFlcResultDto;
import com.xftech.cdp.domain.flowctrl.model.dto.FlowCtrlDto;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlCoreService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.impl.AbstractStrategyEventDispatchService;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import com.xftech.cdp.infra.thread.DispatchFlcExecutor;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *  新增触达流控
 * <AUTHOR>
 * @version $ DispatchFlcService, v 0.1 2023/9/21 16:11 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class DispatchFlcService {
    private final static String LOCK_KEY_FORMAT = "dispatchFlcLock:%s";

    private RedisUtils redisUtils;
    private AppConfigService appConfigService;
    private CacheStrategyMarketChannelService cacheStrategyMarketChannelService;
    private UserDispatchDetailService userDispatchDetailService;
    private FlowCtrlCoreService flowCtrlCoreService;
    private CrowdPushBatchService crowdPushBatchService;

    // 被流控返回true,  生产开关配置，可关闭
    public boolean dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail, AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
        try {
            StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(bizEventVO.getMarketChannel());
            boolean switchFlag = appConfigService.getSingleDispatchFlcSwitch(marketChannelEnum);
            if (!Objects.equals(true, switchFlag)) {
                log.info("dispatchFlc, 触达流控开关未打开, 策略id:{}, 用户id:{}, marketChannel = {}",
                        bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), marketChannelEnum.getCode());
                return false;
            }
            log.info("dispatchFlc, 触达开关打开开始流控, 策略id:{}, 用户id：{}, marketChannel = {}",
                    bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), marketChannelEnum.getCode());
            StrategyMarketChannelDo channelDo = cacheStrategyMarketChannelService.cacheSelectById(bizEventVO.getMarketChannelId());
            if (channelDo == null) {
                return false;
            }
            List<Integer> sucStatus = Arrays.asList(-1, 1);
            List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(bizEventVO.getMessageId(),
                    Optional.ofNullable(bizEventVO.getTriggerDatetime()).orElse(LocalDateTime.now()), channelDo, crowdDetail, sucStatus,bizEventVO.getBizEventType());
            if (CollectionUtils.isEmpty(crowdDetailList)) {
                Tracer.logMetricForCount(String.format("dispatchFlc:%s", bizEventVO.getMarketChannel()));
                Tracer.logEvent(String.format("dispatchFlc:%s", bizEventVO.getMarketChannel()),
                        String.valueOf(bizEventVO.getStrategyId()));
                log.warn("触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}, channelId:{}",
                        bizEventVO.getAppUserId(), bizEventVO.getMarketChannel(), bizEventVO.getStrategyId(), bizEventVO.getMarketChannelId());
                return true;
            }
            return false;
        } catch (Exception ex) {
            log.error("dispatchFlc", ex);
        }
        return false;
    }
    // 锁流控
    public boolean dispatchFlcLock(DispatchDto reach, EventPushBatchDo eventPushBatch, UserDispatchDetailDo dispatchDetail, String batchNum, CrowdDetailDo crowdDetail,
                                   AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
        Long userId = crowdDetail.getUserId();
        Long strategyId = reach.getStrategyId();
        Long marketChannelId = reach.getStrategyChannelId();
        Integer marketChannel = reach.getStrategyChannel();
        String messageId = reach.getMessageId();

        Triple<String, String, String> respPair = Triple.of("-1", "failed", null);
        StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(marketChannel);
        log.info("分布式流控:开始触发, userId = {}, strategyId = {}, channelEnum = {}", userId, strategyId,
                channelEnum.getDescription());
        boolean isStartFlc = appConfigService
                .getSingleDispatchFlcLockSwitch(channelEnum);
        if (!isStartFlc) {
            // 保证在未开启流控的时候，数据也是正常保存的;
            userDispatchDetailService.saveEventDispatchDetail(reach, eventPushBatch, dispatchDetail, batchNum, crowdDetail, respPair);
            log.info("分布式流控:开关未打开, 不流控, userId = {}, channelEnum = {}", userId, channelEnum.getDescription());
            return false;
        }
        if (Objects.equals("NOTIFY", reach.getDispatchType())){
            // 保证在未开启流控的时候，数据也是正常保存的;
            userDispatchDetailService.saveEventDispatchDetail(reach, eventPushBatch, dispatchDetail, batchNum, crowdDetail, respPair);
            log.info("分布式流控:通知策略, 不流控, userId = {}, channelEnum = {}", userId, channelEnum.getDescription());
            return false;
        }
        StrategyMarketChannelDo channelDo = reach.getStrategyMarketChannelDo();
        if (channelDo == null) {
            channelDo = cacheStrategyMarketChannelService
                    .cacheSelectById(marketChannelId);
        }
        if (channelDo == null) {
            userDispatchDetailService.saveEventDispatchDetail(reach, eventPushBatch, dispatchDetail, batchNum, crowdDetail, respPair);
            return false;
        }

        Transaction transaction = Tracer.newTransaction("DispatchFlcService", "dispatchFlcLock");
        String lockValue = String.valueOf(System.currentTimeMillis());
        tryLock(userId, lockValue);
        try {
            transaction.setStatus(Transaction.SUCCESS);
            List<Integer> sucStatus = Arrays.asList(-1, 1);
            List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(messageId,
                    Optional.ofNullable(reach.getTriggerDatetime()).orElse(LocalDateTime.now()), channelDo, crowdDetail, sucStatus,reach.getBizEventType());
            if (CollectionUtils.isEmpty(crowdDetailList)) {
                Tracer.logMetricForCount(String.format("dispatchFlcLock:%s", marketChannel));
                Tracer.logEvent(String.format("dispatchFlcLock:%s", marketChannel),
                        String.valueOf(strategyId));
                log.warn("分布式流控:触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}, channelId:{}",
                        userId, marketChannel, strategyId, marketChannelId);
                return true;
            }
            // 后置写入变量
            EventPushBatchDo eventPushBatchDo = userDispatchDetailService.saveEventDispatchDetail(reach, eventPushBatch, dispatchDetail, batchNum, crowdDetail, respPair);
            return false;
        } catch (Exception ex) {
            if (dispatchDetail.getId() == null || dispatchDetail.getId() <= 0) {
                userDispatchDetailService.saveEventDispatchDetail(reach, eventPushBatch, dispatchDetail, batchNum, crowdDetail, respPair);
            }
            log.error("dispatchFlcLock, userid = {}", userId, ex);
            transaction.setStatus(ex);
        } finally {
            unLock(userId, lockValue);
            transaction.complete();
        }
        return false;
    }

    /**
     * 离线批量流控
     * @param dispatchDto
     * @param crowdPushBatchDo
     * @param batchNum
     * @param app
     * @param innerApp
     * @param list
     * @param detailList
     * @param resp
     * @return
     * @param <T>
     */
    public <T> BatchDispatchFlcResultDto batchDispatchFlcLockThenReturnExcludeUsers(
            DispatchDto dispatchDto,
            CrowdPushBatchDo crowdPushBatchDo,
            String batchNum,
            String app,
            String innerApp,
            List<T> list,
            List<CrowdDetailDo> detailList,
            Pair<String, String> resp) {
        BatchDispatchFlcResultDto batchDispatchFlcResultDto = new BatchDispatchFlcResultDto(new ArrayList<>(8),
                new ArrayList<>(8));
        List<CrowdDetailDo> excludeCrowdDetails = new ArrayList<>();
        Integer marketChannel = dispatchDto.getStrategyChannel();
        Transaction transaction = Tracer.newTransaction("DispatchFlcService", "batchLock");
        try {
            StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(marketChannel);
            log.info("分布式批量流控:开始触发, strategyId = {}, 渠道 = {}, 数量:{}", dispatchDto.getStrategyId(),
                    channelEnum.getDescription(), detailList.size());
            boolean isStartFlc = appConfigService
                    .getBatchDispatchFlcLockSwitch(channelEnum);
            if (!isStartFlc) {
                List<UserDispatchDetailDo> userDispatchDetails = userDispatchDetailService.saveDispatchDetail(dispatchDto, crowdPushBatchDo, batchNum, app, innerApp, list, detailList, resp);
                log.info("分布式批量流控:开关未打开, 不流控, strategyId = {}, 渠道 = {}, 数量:{}", dispatchDto.getStrategyId(),
                        channelEnum.getDescription(), detailList.size());
                batchDispatchFlcResultDto.setUserDispatchDetailList(userDispatchDetails);
                transaction.setStatus(Transaction.SUCCESS);
                return batchDispatchFlcResultDto;
            }
            // 5.流量控制
            List<Integer> dispatchSucStatus = Arrays.asList(-1, 1);
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<Future> tasks = new ArrayList<>();
            List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();
            crowdPushBatchService.insert(dispatchDto, crowdPushBatchDo, app, innerApp, new ArrayList<>(0), resp.getLeft(), resp.getRight(), batchNum);
            for (CrowdDetailDo cd : detailList) {
                Future<SingleDispatchFlcResultDto> task = DispatchFlcExecutor.getPool().submit(() -> tryLockSingle(dispatchDto, crowdPushBatchDo, cd, dispatchSucStatus));
                tasks.add(task);
            }
            tasks.forEach(x -> {
                try {
                    SingleDispatchFlcResultDto singleDispatchFlcResultDto = (SingleDispatchFlcResultDto) x.get();
                    if (singleDispatchFlcResultDto.getUserDispatchDetailDo() != null) {
                        userDispatchDetailDoList.add(singleDispatchFlcResultDto.getUserDispatchDetailDo());
                    }
                    if (singleDispatchFlcResultDto.getCrowdDetailDo() != null) {
                        excludeCrowdDetails.add(singleDispatchFlcResultDto.getCrowdDetailDo());
                    }
                } catch (Exception e) {
                    log.error("batchLock", e);
                }
            });
            stopwatch.stop();
            log.info("分布式批量流控:批量流控执行时间统计, 总计耗时:{}ms, 策略id:{}, 本批次数量:{}, 渠道:{}", stopwatch.elapsed(TimeUnit.MILLISECONDS), dispatchDto.getStrategyId(), detailList.size(), marketChannel);
            batchDispatchFlcResultDto.setUserDispatchDetailList(userDispatchDetailDoList);
            batchDispatchFlcResultDto.setExcludeDetailsList(excludeCrowdDetails);
            log.info("分布式批量流控:触达流控, 拦截信息, 策略id:{},拦截数量:{}, 所有用户:{}, 被拦截的用户:{}", dispatchDto.getStrategyId(), excludeCrowdDetails.size(),
                    JsonUtil.toJson(detailList.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toList())),
                    JsonUtil.toJson(excludeCrowdDetails.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toList())));
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Throwable e) {
            log.error("", e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return batchDispatchFlcResultDto;
    }

    public <T> BatchDispatchFlcResultDto singleDispatchFlcLockThenReturnExcludeUsers(DispatchDto dispatchDto, CrowdPushBatchDo crowdPushBatchDo,
                                                                                    CrowdDetailDo crowdDetailDo) {
        BatchDispatchFlcResultDto batchDispatchFlcResultDto = new BatchDispatchFlcResultDto(new ArrayList<>(1),
                new ArrayList<>(1));
        Integer marketChannel = dispatchDto.getStrategyChannel();
        Transaction transaction = Tracer.newTransaction("DispatchFlcService", "singleDispatchFlcLockThenReturnExcludeUsers");
        try {
            UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
            StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(marketChannel);
            log.info("分布式批量流控:单个流控开始触发, strategyId:{}, 用户id:{}, 渠道:{}", dispatchDto.getStrategyId(), crowdDetailDo.getUserId(),
                    channelEnum.getDescription());
            boolean isStartFlc = appConfigService
                    .getBatchDispatchFlcLockSwitch(channelEnum);
            if (!isStartFlc) {
                userDispatchDetailService.saveDispatchDetail(dispatchDto.getDetailTableNo(), dispatchDto.getStrategyRulerEnum(), userDispatchDetailDo, crowdPushBatchDo, crowdDetailDo);
                log.info("分布式批量流控:单个流控开始触发, 不流控, strategyId:{}, 渠道:{}", dispatchDto.getStrategyId(),
                        channelEnum.getDescription());
                batchDispatchFlcResultDto.setUserDispatchDetailList(Arrays.asList(userDispatchDetailDo));
                return batchDispatchFlcResultDto;
            }
            // 5.流量控制
            List<Integer> dispatchSucStatus = Arrays.asList(-1, 1);
            List<CrowdDetailDo> excludeCrowdDetails = new ArrayList<>();
            List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();

            SingleDispatchFlcResultDto singleDispatchFlcResultDto = tryLockSingle(dispatchDto, crowdPushBatchDo, crowdDetailDo, dispatchSucStatus);
            if (singleDispatchFlcResultDto.getUserDispatchDetailDo() != null) {
                userDispatchDetailDoList.add(singleDispatchFlcResultDto.getUserDispatchDetailDo());
            }
            if (singleDispatchFlcResultDto.getCrowdDetailDo() != null) {
                excludeCrowdDetails.add(singleDispatchFlcResultDto.getCrowdDetailDo());
            }
            batchDispatchFlcResultDto.setUserDispatchDetailList(userDispatchDetailDoList);
            batchDispatchFlcResultDto.setExcludeDetailsList(excludeCrowdDetails);
            log.info("分布式批量流控:单个流控开始触发, 拦截信息, 策略id:{},拦截数量:{}, 所有用户:{}, 被拦截的用户:{}", dispatchDto.getStrategyId(), excludeCrowdDetails.size(),
                    JsonUtil.toJson(userDispatchDetailDoList.stream().map(UserDispatchDetailDo::getUserId).collect(Collectors.toList())),
                    JsonUtil.toJson(excludeCrowdDetails.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toList())));
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Throwable e) {
            log.error("", e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return batchDispatchFlcResultDto;
    }

    public SingleDispatchFlcResultDto tryLockSingle(DispatchDto dispatchDto,
                                                    CrowdPushBatchDo crowdPushBatchDo,
                                                    CrowdDetailDo cd,
                                                    List<Integer> dispatchSucStatus) {
        SingleDispatchFlcResultDto singleDispatchFlcResultDto = new SingleDispatchFlcResultDto();
        Long userId = cd.getUserId();
        Integer marketChannel = dispatchDto.getStrategyChannel();

        String lockValue = String.valueOf(System.currentTimeMillis());
        FlowCtrlDto flowCtrlDto = new FlowCtrlDto();
        flowCtrlDto.setTableNo(dispatchDto.getDetailTableNo());
        flowCtrlDto.setMarketChannelDo(dispatchDto.getStrategyMarketChannelDo());
        flowCtrlDto.setFlowCtrlRuleList(dispatchDto.getFlowCtrlList());
        flowCtrlDto.setStrategyRulerEnum(dispatchDto.getStrategyRulerEnum());
        tryLock(userId, lockValue);
        try {
            flowCtrlDto.setList(Collections.singletonList(cd));
            List<CrowdDetailDo> retDetailList = flowCtrlCoreService.flowCtrl(flowCtrlDto, dispatchSucStatus); // 流控逻辑
            if (CollectionUtils.isEmpty(retDetailList)) {
                Tracer.logEvent(String.format("batchDispatchFlcLock:%s", marketChannel),
                        String.valueOf(dispatchDto.getStrategyId()));
                log.warn("分布式批量流控:触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}, channelId:{}",
                        userId, marketChannel, dispatchDto.getStrategyId(), dispatchDto.getStrategyMarketChannelDo().getId());
                singleDispatchFlcResultDto.setCrowdDetailDo(cd);
                return singleDispatchFlcResultDto;
            }
            UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
            singleDispatchFlcResultDto.setUserDispatchDetailDo(userDispatchDetailDo);
            userDispatchDetailDo.setStrategyGroupId(dispatchDto.getStrategyGroupId());
            userDispatchDetailDo.setStrategyGroupName(dispatchDto.getStrategyGroupName());
            userDispatchDetailService.saveDispatchDetail(dispatchDto.getDetailTableNo(), dispatchDto.getStrategyRulerEnum(), userDispatchDetailDo, crowdPushBatchDo, cd);
        } catch (Exception ex) {
            log.error("分布式批量流控异常, 用户id:{}, 策略id:{}", userId, dispatchDto.getStrategyId(), ex);
        } finally {
            unLock(userId, lockValue);
        }
        return singleDispatchFlcResultDto;
    }

    private String getLockKey(Long userId) {
        return String.format(LOCK_KEY_FORMAT, userId);
    }

    private boolean tryLock(Long userId, String value) {
        boolean lockRet = false;
        final int lockSeconds = 60 * 6;
        final long sleepMills = appConfigService
                .getDispatchFlcLockPerWaitMills();
        String lockKey = getLockKey(userId);
        boolean isIgnore = appConfigService
                .getSingleIgnoreDispatchFlcLockSwitch();
        Transaction transaction = Tracer.newTransaction("DispatchFlcService", "tryLock");
        do {
            try {
                if (isIgnore) {
                    log.info("分布式流控:忽略加锁,直接返回成功, userId = {}", userId);
                    return true;
                }
                lockRet = redisUtils.lock(lockKey, value, lockSeconds);
                if (!lockRet) {
                    Tracer.logMetricForCount("DispatchFlcTryLockAgain");
                    Tracer.logEvent("DispatchFlcTryLockAgain", userId.toString());
                    isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();
                    ThreadUtil.sleep(sleepMills);
                }
            } catch (Exception ex) {
                log.error("分布式流控:加锁异常, userId = {}", userId, ex);
                isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();
            }
        } while (!lockRet);
        transaction.setStatus(Transaction.SUCCESS);
        transaction.complete();
        return true;
    }

    private void unLock(Long userId, String value) {
        String lockKey = getLockKey(userId);
        boolean isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();
        if (isIgnore){
            return;
        }
        try {
            if (StringUtils.equalsIgnoreCase(value,
                    redisUtils.get(lockKey))) {
                redisUtils.delete(lockKey);
            } else {
                Tracer.logEvent("DispatchFlcUnlockUserNotMatch", userId.toString());
            }
        } catch (Exception ex) {
            log.error("分布式流控:解锁异常, userId = {}", userId, ex);
            Tracer.logEvent("DispatchFlcUnlockError", userId.toString());
        }
    }
}