package com.xftech.cdp.domain.strategy.service.impl;

import apollo.com.google.gson.JsonObject;
import apollo.com.google.gson.JsonParser;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cronutils.builder.CronBuilder;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.field.expression.FieldExpression;
import com.cronutils.model.field.expression.FieldExpressionFactory;
import com.google.common.collect.Lists;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.AppBannerTemplateReq;
import com.xftech.cdp.api.dto.req.CouponListReq;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateListReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.strategy.StrategyXxlJobParam;
import com.xftech.cdp.distribute.crowd.enums.CrowdStatusEnum;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.domain.cache.CacheFlowCtrlSerivce;
import com.xftech.cdp.domain.cache.CacheStrategyGroupService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketChannelService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventConditionService;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.model.label.crowd.DataSegment;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.dispatch.dto.APIOpenAmountDto;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlRuleStatusEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyAbTestEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyFrequencyEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelOptionEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.label.OperatorEnum;
import com.xftech.cdp.domain.strategy.model.enums.label.ValueTypeEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyInstantLabelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventConditionRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.AppBannerService;
import com.xftech.cdp.domain.strategy.service.StrategyInstantLabelService;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.TelePushService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventCatchService;
import com.xftech.cdp.feign.PushFeignClient;
import com.xftech.cdp.feign.model.GoodsDetail;
import com.xftech.cdp.feign.model.PushTemplateDetail;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.TemplateDetailRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.infra.client.coupon.CouponClient;
import com.xftech.cdp.infra.client.coupon.model.req.BaseCouponRequester;
import com.xftech.cdp.infra.client.coupon.model.req.CouponActivityListReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.coupon.model.resp.CouponActivityListResp;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sms.model.SmsItemArgs;
import com.xftech.cdp.infra.client.sms.model.SmsItemRequester;
import com.xftech.cdp.infra.client.sms.model.resp.ItemPage;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItemResp;
import com.xftech.cdp.infra.client.telemarketing.TelemarketingClient;
import com.xftech.cdp.infra.client.telemarketing.model.TeleNameListArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleNameListRequest;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameList;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameListResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.SsoUtil;
import com.xftech.xxljob.XxlJobAdminClient;
import com.xftech.xxljob.model.XxlJobDto;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK;


/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Component
public class StrategyCommonService {

    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private CacheStrategyMarketChannelService cacheStrategyMarketChannelService;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private CacheStrategyGroupService cacheStrategyGroupService;
    @Autowired
    private XxlJobAdminClient xxlJobAdminClient;
    @Autowired
    private TelemarketingClient telemarketingClient;
    @Autowired
    private CouponClient couponClient;
    @Autowired
    private SmsClient smsClient;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;
    @Autowired
    private StrategyEventCatchService strategyEventCatchService;
    @Autowired
    private StrategyInstantLabelRepository strategyInstantLabelRepository;
    @Autowired
    private StrategyInstantLabelService strategyInstantLabelService;
    @Autowired
    private CacheStrategyMarketEventConditionService cacheStrategyMarketEventConditionService;
    @Autowired
    private StrategyMarketEventConditionRepository strategyMarketEventConditionRepository;
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private CacheFlowCtrlSerivce cacheFlowCtrlSerivce;
    @Autowired
    private FlowCtrlRepository flowCtrlRepository;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private TelePushService telePushService;
    @Autowired
    private PushFeignClient pushFeignClient;
    @Autowired
    private StrategyService strategyService;
    @Autowired
    private AppBannerService appBannerService;
    @Autowired
    private GoodsServiceImpl goodsService;
    @Autowired
    private CrowdInfoRepository crowdInfoRepository;

    public String getBizKey(Integer abType, String bizKey) {
        StrategyGroupTypeEnum groupTypeEnum = StrategyGroupTypeEnum.getInstance(abType);
        return groupTypeEnum == StrategyGroupTypeEnum.NEW_RANDOM ? bizKey : "";
    }

    public StrategyGroupDo updateStrategyGroup(StrategyCreateReq.StrategyGroup strategyGroup, Long strategyId) {
        StrategyGroupDo strategyGroupDo;
        //修改
        if (strategyGroup.getGroupId() != null) {
            strategyGroupDo = strategyGroupRepository.selectById(Long.valueOf(strategyGroup.getGroupId()));
            if (strategyGroupDo == null) {
                throw new StrategyException(strategyGroup.getGroupId() + "组不存在");
            }
        } else {
            strategyGroupDo = new StrategyGroupDo();
        }
        strategyGroupDo.setStrategyId(strategyId);
        strategyGroupDo.setGroupConfig(JSON.toJSONString(strategyGroup.getGroupConfig()));
        strategyGroupDo.setName(strategyGroup.getName());
        strategyGroupDo.setIsExecutable(Objects.nonNull(strategyGroup.getIsExecutable()) ? strategyGroup.getIsExecutable() : 1);
        strategyGroupDo.setExtInfo(strategyGroup.getExtInfo());

        if (strategyGroup.getGroupId() != null) {
            cacheStrategyGroupService.update(strategyGroupDo);
        } else {
            cacheStrategyGroupService.insert(strategyGroupDo);
        }
        return strategyGroupDo;

    }

    public StrategyMarketChannelDo updateStrategyMarketChannels(StrategyCreateReq.StrategyMarketChannel strategyMarketChannel, StrategyDo strategyDo, StrategyGroupDo strategyGroupDo, StrategyCreateReq.SendFrequency sendFrequency, String strategyName) {
        StrategyMarketChannelDo strategyMarketChannelDo;
        if (strategyMarketChannel.getChannelId() != null) {
            strategyMarketChannelDo = strategyMarketChannelRepository.selectById(Long.valueOf(strategyMarketChannel.getChannelId()));
            if (strategyMarketChannelDo == null) {
                throw new StrategyException(strategyMarketChannel.getChannelId() + "渠道不存在");
            }
        } else {
            strategyMarketChannelDo = strategyMarketChannelRepository.selectByStrategyGroupIdAndChannel(strategyGroupDo.getId(), strategyMarketChannel.getMarketChannel());
            if (strategyMarketChannelDo == null) {
                strategyMarketChannelDo = new StrategyMarketChannelDo();
            }
        }

        String extInfo = strategyMarketChannel.getExtInfo();
        if (Objects.equals(StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode(),
                strategyMarketChannel.getMarketChannel())) {
            if (!checkIncreaseAmtChannnel(extInfo)) {
                throw new StrategyException("提额渠道配置信息验证错误");
            }
        }
        if (Objects.equals(StrategyMarketChannelEnum.AI_PRONTO.getCode(),
                strategyMarketChannel.getMarketChannel())) {
            if (!checkAiProntoChannel(extInfo)) {
                throw new StrategyException("AI-即时触达配置信息验证错误");
            }
        }

        strategyMarketChannelDo.setStrategyId(strategyDo.getId());
        strategyMarketChannelDo.setStrategyGroupId(strategyGroupDo.getId());
        strategyMarketChannelDo.setSendTime(strategyMarketChannel.getSendTime());
        strategyMarketChannelDo.setMarketChannel(strategyMarketChannel.getMarketChannel());
        strategyMarketChannelDo.setTemplateId(strategyMarketChannel.getTemplateId());
        strategyMarketChannelDo.setApp(strategyMarketChannel.getApp());
        strategyMarketChannelDo.setDispatchApp(strategyMarketChannel.getDispatchApp());
        if (!StringUtils.isEmpty(extInfo)){
            strategyMarketChannelDo.setExtInfo(extInfo);
        }

        if (strategyMarketChannelDo.getId() != null) {
            cacheStrategyMarketChannelService.updateById(strategyMarketChannelDo);
        } else {
            cacheStrategyMarketChannelService.insert(strategyMarketChannelDo);
        }
        if (StringUtils.isNotEmpty(strategyDo.getFlowNo())){
            return strategyMarketChannelDo;
        }
        //如果渠道不是"app资源位"或“不营销”的，创建xxlJob
        if (strategyDo.getSendRuler() != 2 &&
                strategyMarketChannel.getMarketChannel() != StrategyMarketChannelEnum.NONE.getCode() &&
                strategyMarketChannel.getMarketChannel() != StrategyMarketChannelEnum.APP_BANNER.getCode()) {
            String cronStr = null;
            Cron cron = convertToCron(sendFrequency, strategyMarketChannel.getSendTime());
            if (cron != null){
                cronStr = cron.asString();
            }
            strategyMarketChannelDo.setCron(cronStr);
            cacheStrategyMarketChannelService.updateById(strategyMarketChannelDo);

            if (strategyDo.getType() == 1){
                return strategyMarketChannelDo;
            }

            if (strategyDo.getSendRuler() != StrategyRulerEnum.CYCLE_DAY.getCode()) {
                StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(strategyMarketChannel.getMarketChannel());
                String jobDesc = "策略组:" + strategyName + "_" + strategyGroupDo.getName() + "_" + strategyMarketChannelEnum.getDescription();
                this.createOrUpdateXxlJob(strategyMarketChannelDo, strategyDo, jobDesc, strategyMarketChannel.getChannelId());
            }
        }
        return strategyMarketChannelDo;
    }

    public void createOrUpdateXxlJob(StrategyMarketChannelDo strategyMarketChannelDo, StrategyDo strategyDo, String jobDesc, String channelId) {
        // 新建xxl-job
        XxlJobDto xxlJobDto = new XxlJobDto();
        StrategyXxlJobParam strategyXxlJobParam = new StrategyXxlJobParam(strategyMarketChannelDo.getId());
        xxlJobDto.setExecutorHandler(XxlJobConstants.STRATEGY_DISPATCH);
        xxlJobDto.setJobCron(strategyMarketChannelDo.getCron());
        xxlJobDto.setJobDesc(jobDesc);
        xxlJobDto.setAddTime(new Date());
        xxlJobDto.setExecutorParam(JSON.toJSONString(strategyXxlJobParam));
        xxlJobDto.setAuthor(SsoUtil.get().getName());

        if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
            xxlJobDto.setTriggerStatus(0);
        } else {
            xxlJobDto.setTriggerStatus(1);
        }

        xxlJobDto.setId(strategyMarketChannelDo.getXxlJobId());
        if (channelId == null) {
            int xxlJobId = xxlJobAdminClient.addJob(xxlJobDto);
            strategyMarketChannelDo.setXxlJobId(xxlJobId);
        } else {
            xxlJobAdminClient.updateJob(xxlJobDto);
        }
        cacheStrategyMarketChannelService.updateById(strategyMarketChannelDo);
    }

    public String getMarketChannelStr(List<StrategyCreateReq.StrategyGroup> strategyGroups) {
        List<Integer> channelIds = getMarketChannels(strategyGroups);
        return channelIds.stream().distinct().map(Object::toString).collect(Collectors.joining(","));
    }

    public List<Integer> getMarketChannels(List<StrategyCreateReq.StrategyGroup> strategyGroups){
        List<Integer> channelIds = new ArrayList<>();
        for (StrategyCreateReq.StrategyGroup strategyGroup : strategyGroups) {
            if (!CollectionUtils.isEmpty(strategyGroup.getStrategyMarketChannels())) {
                strategyGroup.getStrategyMarketChannels().forEach(item -> channelIds.add(item.getMarketChannel()));
            }
        }
        return channelIds;
    }

    public void verifyGroupConfig(List<StrategyCreateReq.StrategyGroup> strategyGroups, Integer abType) {
        if (abType == StrategyGroupTypeEnum.RANDOM.getCode()) {
            boolean betweenFlag = strategyGroups.stream().anyMatch(item -> item.getGroupConfig().getPositionEnd() - item.getGroupConfig().getPositionStart() > 1);
            if (betweenFlag) {
                throw new StrategyException("数据格式有误：选择2位时，仅支持选择相邻的两位，填写所需位数");
            }
            boolean compareFlag = strategyGroups.stream().anyMatch(item -> item.getGroupConfig().getPositionEnd() < item.getGroupConfig().getPositionStart());
            if (compareFlag) {
                throw new StrategyException("数据格式有误：起始位数不能大于结束位数");
            }
        }

        List<DataSegment.Segment> segments = new ArrayList<>();
        List<String> items = new ArrayList<>();
        getSegment(strategyGroups, segments, items);

        if (!CollectionUtils.isEmpty(segments)) {
            boolean overLenFlag = segments.stream().filter(item -> item.getMin() != null && item.getMax() != null).anyMatch(item -> String.valueOf(item.getMin()).length() > 18 || String.valueOf(item.getMax()).length() > 18);
            if (overLenFlag) {
                throw new StrategyException("分组配置最大长度为18位");
            }
            boolean flag = segments.stream().filter(item -> item.getMin() != null && item.getMax() != null).anyMatch(item -> item.getMin() > item.getMax());
            if (flag) {
                throw new StrategyException("区间配置存在最大值小于最小值");
            }
            existIntersection(segments);
        }
        if (!CollectionUtils.isEmpty(items) && items.size() != new HashSet<>(items).size()) {
            throw new StrategyException("分组区间存在交叉");
        }
    }

    public void getSegment(List<StrategyCreateReq.StrategyGroup> strategyGroups, List<DataSegment.Segment> segments, List<String> items) {
        for (StrategyCreateReq.StrategyGroup strategyGroup : strategyGroups) {
            StrategyCreateReq.GroupConfig groupConfig = strategyGroup.getGroupConfig();
            if (Objects.nonNull(groupConfig) && Objects.nonNull(groupConfig.getCrowdLabelOption())) {
                JSONObject object = JSON.parseObject(JSON.toJSONString(groupConfig.getCrowdLabelOption()));
                if (object.containsKey("segments")) {
                    segments.addAll(object.getJSONArray("segments").toJavaList(DataSegment.Segment.class));
                }
                if (object.containsKey("items")) {
                    items.addAll(object.getJSONArray("items").toJavaList(String.class));
                }
            }
        }
    }

    public void existIntersection(List<DataSegment.Segment> segments) {
        for (int i = 0; i < segments.size() - 1; i++) {
            DataSegment.Segment s1 = segments.get(i);
            if (s1.getMin() == null && s1.getMax() == null) {
                continue;
            }
            handleSegment(s1);
            for (int j = i + 1; j < segments.size(); j++) {
                DataSegment.Segment s2 = segments.get(j);
                if (s2.getMin() == null && s2.getMax() == null) {
                    continue;
                }
                handleSegment(s2);
                if (Math.max(s1.getMin(), s2.getMin()) <= Math.min(s1.getMax(), s2.getMax())) {
                    throw new StrategyException("分组区间存在交叉");
                }
            }
        }
    }

    public void handleSegment(DataSegment.Segment segment) {
        if (segment.getMin() == null) {
            segment.setMin(segment.getMax());
        }
        if (segment.getMax() == null) {
            segment.setMax(segment.getMin());
        }
    }

    public void verifyCrowIds(String crowdIdStr, String businessType) {
        if (StringUtils.isBlank(crowdIdStr)) {
            throw new StrategyException("人群包配置不能为空");
        }
        String[] crowdIds = crowdIdStr.split(";");
        if (crowdIds.length < 1 || crowdIds.length > 10) {
            throw new StrategyException(crowdIds.length > 10 ? "人群包配置不能超过10个" : "人群包配置不能为空");
        }
        verifyCrowds(crowdIds, businessType);
    }

    public void verifyCrowIdsV2(String crowdIdStr, String businessType) {
        if (StringUtils.isBlank(crowdIdStr)) {
            return;
        }
        String[] crowdIds = crowdIdStr.split(";");
        verifyCrowds(crowdIds, businessType);
    }

    private void verifyCrowds(String[] crowdIds, String businessType) {
        List<CrowdPackDo> crowdPackDoList = crowdPackRepository.selectCrowdPackByIdsAndBusinessType(crowdIds, businessType);
        log.info("StrategyCommonService verifyCrowds step1 crowdIds={} crowdPackDoList={}", JSONObject.toJSONString(crowdIds), JSONObject.toJSONString(crowdPackDoList));

        // 新增洞察平台人群包
        for (String crowdId : crowdIds) {
            if (ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK, Long.valueOf(crowdId))) {
                CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(Long.valueOf(crowdId));
                if (crowdInfoDo != null) {
                    crowdPackDoList.removeIf(x -> Objects.equals(x.getId(), crowdInfoDo.getCrowdId()));

                    CrowdPackDo crowdPackDo = new CrowdPackDo();
                    crowdPackDo.setId(crowdInfoDo.getCrowdId());
                    crowdPackDo.setFilterMethod(CrowdFilterMethodEnum.LABEL.getCode());
                    crowdPackDo.setStatus(crowdInfoDo.getCrowdStatus());
                    crowdPackDoList.add(crowdPackDo);
                }
            }
        }
        log.info("StrategyCommonService verifyCrowds step2 crowdIds={} crowdPackDoList={}", JSONObject.toJSONString(crowdIds), JSONObject.toJSONString(crowdPackDoList));

        if (crowdPackDoList.size() < crowdIds.length) {
            List<Long> crowdPackIdList = crowdPackDoList.stream().map(CrowdPackDo::getId).collect(Collectors.toList());
            String crowIds = StringUtils.join(Arrays.stream(crowdIds).filter(item -> !crowdPackIdList.contains(Long.valueOf(item))).collect(Collectors.toList()), ",");
            throw new StrategyException("人群包编号[" + crowIds + "]有误");
        }

        crowdPackDoList.forEach(crowdPackDo -> {
            if (CrowdFilterMethodEnum.UPLOAD.getCode() == crowdPackDo.getFilterMethod()) {
                return;
            }

            if (ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK, crowdPackDo.getId())) {
                if (!Objects.equals(crowdPackDo.getStatus(), CrowdStatusEnum.ONLINE.getStatus())) {
                    throw new StrategyException("洞察平台人群包[" + crowdPackDo.getId() + "]已下线!");
                }
            } else {
                boolean existExpiredCrowd = crowdPackRepository.existExpiredCrowd(crowdPackDo.getId());
                if (existExpiredCrowd) {
                    throw new StrategyException("人群包[" + crowdPackDo.getId() + "]已过期!");
                }
            }
        });
    }

    private FieldExpression buildDays(int[] days) {
        FieldExpression fieldExpression = null;
        for (int day : days) {
            if (fieldExpression == null) {
                fieldExpression = FieldExpressionFactory.on(day);
            } else {
                fieldExpression = fieldExpression.and(FieldExpressionFactory.on(day));
            }
        }
        return fieldExpression;
    }


    public Cron convertToCron(StrategyCreateReq.SendFrequency sendFrequency, LocalTime dateTime) {

        CronBuilder cronBuilder = CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
                .withHour(FieldExpressionFactory.on(dateTime.getHour()))
                .withMinute(FieldExpressionFactory.on(dateTime.getMinute()))
                .withSecond(FieldExpressionFactory.on(dateTime.getSecond()));

        if (sendFrequency.getType() == StrategyFrequencyEnum.EVERY_DAY.getCode()) {
            cronBuilder.withDoM(FieldExpressionFactory.always())
                    .withDoW(FieldExpressionFactory.questionMark())
                    .withYear(FieldExpressionFactory.always())
                    .withMonth(FieldExpressionFactory.always());
        } else if (sendFrequency.getType() == StrategyFrequencyEnum.EVERY_WEEK.getCode()) {
            cronBuilder.withDoM(FieldExpressionFactory.questionMark())
                    .withDoW(buildDays(sendFrequency.getValue().stream().mapToInt(Integer::intValue).toArray()))
                    .withYear(FieldExpressionFactory.always())
                    .withMonth(FieldExpressionFactory.always());
        } else if (sendFrequency.getType() == StrategyFrequencyEnum.EVERY_MONTH.getCode()) {
            cronBuilder.withDoM(buildDays(sendFrequency.getValue().stream().mapToInt(Integer::intValue).toArray()))
                    .withDoW(FieldExpression.questionMark())
                    .withYear(FieldExpressionFactory.always())
                    .withMonth(FieldExpressionFactory.always());
        } else if (sendFrequency.getType() == StrategyFrequencyEnum.CYCLE_DAY.getCode()) {
           return null;
        } else {
            throw new StrategyException("发送频率有误");
        }

        return cronBuilder.instance();
    }

    public String getTemplate(StrategyMarketChannelDo item) {
        String template = null;
        switch (StrategyMarketChannelEnum.getInstance(item.getMarketChannel())) {
            case SMS:
                SmsTemplateListReq smsTemplateListReq = new SmsTemplateListReq();
                smsTemplateListReq.setTemplateId(item.getTemplateId());
                List<SmsTemplateListResp> respList = this.smsTemplateList(smsTemplateListReq).getRecords();
                template = !CollectionUtils.isEmpty(respList) ? respList.get(0).getTemplate() : null;
                break;
            case VOICE:
                TeleNodeListReq teleNodeListReq = new TeleNodeListReq();
                teleNodeListReq.setNodeId(Convert.toLong(item.getTemplateId()));
                List<TeleNodeListResp.TeleNode> list = this.teleNodeList(teleNodeListReq).getList().getRecords();
                template = CollectionUtils.isEmpty(list) ? null : JSON.toJSONString(list.get(0));
                break;
            case VOICE_NEW:
                List<Integer> policyIdList = Arrays.stream(item.getTemplateId().split(","))
                        .mapToInt(Integer::parseInt)
                        .boxed()
                        .collect(Collectors.toList());
                List<NameTypeResp> nameTypeDetailList = new ArrayList<>(policyIdList.size());
                for (Integer policyId : policyIdList) {
                    NameTypeResp nameTypeDetail = strategyService.getNameTypeDetail(policyId);
                    nameTypeDetailList.add(nameTypeDetail);
                }

                int policyId = 0;
                if (!Objects.isNull(item.getExtInfo())) {
                    Map extInfo = JSONObject.parseObject(item.getExtInfo(), Map.class);
                    if (!Objects.isNull(extInfo) && extInfo.containsKey("policyId")) {
                        policyId = Integer.parseInt(String.valueOf(extInfo.get("policyId")));
                    }
                }

                template = getTeleTemp(policyId, nameTypeDetailList);
                break;
            case SALE_TICKET:
            case X_DAY_INTEREST_FREE:
                CouponListReq req = new CouponListReq();
                req.setActivityId(Convert.toLong(item.getTemplateId()));
                List<CouponListResp.Coupon> couponListResp = this.couponList(req).getList().getRecords();
                List<CouponListResp.CouponType> couponTypeResp = this.couponList(req).getCouponTypeList();
                if (!CollectionUtils.isEmpty(couponListResp)) {
                    String type = couponListResp.get(0).getCouponType();
                    Map<Integer, String> typeMap = couponTypeResp.stream().collect(Collectors.toMap(CouponListResp.CouponType::getValue, CouponListResp.CouponType::getName));
                    couponListResp.get(0).setCouponType(typeMap.get(Integer.parseInt(type)));
                    template = JSON.toJSONString(couponListResp.get(0));
                }
                break;
            case PUSH:
                PushBaseRequest<TemplateDetailRequest> request = new PushBaseRequest<>();
                TemplateDetailRequest detailRequest = new TemplateDetailRequest();
                detailRequest.setTemplateId(item.getTemplateId());
                detailRequest.setApp(item.getApp());
                request.setArgs(detailRequest);
                request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
                PushResponse<PushTemplateDetail> pushTemplateDetailPushResponse = pushFeignClient.loadPushTemplateDetail(request);
                log.info("loadPushTemplateDetail request:{},response:{}", request, pushTemplateDetailPushResponse);
                if (Objects.isNull(pushTemplateDetailPushResponse) || !pushTemplateDetailPushResponse.isSuccess() || Objects.isNull(pushTemplateDetailPushResponse.getResponse())) {
                    throw new StrategyException("查询push模板详情接口失败");
                }
                template = JSON.toJSONString(pushTemplateDetailPushResponse.getResponse());
                break;
            case LIFE_RIGHTS:
                String goodsId = item.getTemplateId();
                String activityId = String.valueOf(JSONObject.parseObject(item.getExtInfo(), Map.class).get("activityId"));
                GoodsDetail goodsDetail = goodsService.getGoodsDetailByGoodsId(Long.parseLong(goodsId));
                CouponListResp.Coupon couponDetail = null;
                try {
                    CouponListReq couponListReq = new CouponListReq();
                    couponListReq.setActivityId(Long.parseLong(activityId));
                    couponListReq.setType(2);
                    couponListReq.setCurrent(1);
                    couponListReq.setSize(1);
                    CouponListResp queryCouponList = this.couponList(couponListReq);
                    couponDetail = queryCouponList.getList().getRecords().get(0);
                } catch (Exception e) {
                    log.error("查询优惠券信息异常", e);
                    throw new StrategyException("查询优惠券列表异常");
                }
                Map<String, Object> lifeRights = new HashMap<>();
                lifeRights.put("goodsInfo", goodsDetail);
                lifeRights.put("couponInfo", couponDetail);
                template = JSON.toJSONString(lifeRights);
                break;
            case INCREASE_AMOUNT:
                String value = item.getExtInfo();
                if (!StringUtils.isEmpty(value)) {
                    IncreaseAmtDto increaseAmtDto = JsonUtil.parse(value, IncreaseAmtDto.class);
                    if (increaseAmtDto != null) {
                        return JsonUtil.toJson(increaseAmtDto.getIncreaseAmtConfig());
                    }
                }
                return null;
            case AI_PRONTO:
                template = item.getExtInfo();
                break;
            case APP_BANNER:
                String appBannerId = item.getTemplateId();
                AppBannerTemplateReq appBannerReq = new AppBannerTemplateReq();
                appBannerReq.setUtm_source("cdp");
                appBannerReq.setId(Integer.valueOf(appBannerId));
                appBannerReq.setPage(0);
                appBannerReq.setPage_size(1);
                PageResultResponse<AppBannerTemplateResp> appBannerList = appBannerService.getAppBannerList(appBannerReq);
                if (!appBannerList.getRecords().isEmpty()) {
                    return JsonUtil.toJson(appBannerList.getRecords().get(0));
                }
                break;
            default:
                break;

        }
        return template;
    }

    String getTeleTemp(Integer policyId, List<NameTypeResp> nameTypeDetailList) {
        String template = telePushService.getPolicyDetail(policyId);
        if ( !CollectionUtils.isEmpty(nameTypeDetailList)) {
            JsonParser parser = new JsonParser();
            JsonObject jsonTemplate = parser.parse(template).getAsJsonObject();

            List<Map<String, Object>> nameTypeDetailMaps = new ArrayList<>(nameTypeDetailList.size());
            for (NameTypeResp nameTypeDetail : nameTypeDetailList) {
                if (Objects.isNull(nameTypeDetail)) {
                    continue;
                }
                Map<String, Object> m = new HashMap<>(3);
                m.put("typeName",nameTypeDetail.getTypeName());
                m.put("businessType",nameTypeDetail.getBusinessType());
                m.put("nameTypeId", nameTypeDetail.getNameTypeId());
                nameTypeDetailMaps.add(m);
            }
            jsonTemplate.addProperty("nameTypeDetails", JsonUtil.toJson(nameTypeDetailMaps));
            template = jsonTemplate.toString();
        }
        return template;
    }

    String getRandomItem(String bizKey) {
        return randomNumService.getRandomItem(bizKey);
    }

    public PageResultResponse<SmsTemplateListResp> smsTemplateList(SmsTemplateListReq smsTemplateListReq) {
        SmsItemArgs args = new SmsItemArgs();
        args.setPageSize(smsTemplateListReq.getSize());
        args.setPage(smsTemplateListReq.getCurrent());
        args.setGroup(smsTemplateListReq.getType());
        args.setTemplateId(smsTemplateListReq.getTemplateId());

        SmsItemRequester requester = new SmsItemRequester();
        requester.setUa("");
        requester.setArgs(args);
        requester.setSign("");
        SmsItemResp smsItemResp = smsClient.queryDistinctItem(requester);
        if (!smsItemResp.isSuccess()) {
            throw new StrategyException("查询短信模板异常");
        }

        ItemPage itemPage = smsItemResp.getResponse();
        if (itemPage == null) {
            return new PageResultResponse<>(Lists.newArrayList(), smsTemplateListReq.getCurrent(), smsTemplateListReq.getSize(), 0);
        }
        List<SmsTemplateListResp> smsTemplateListRespList = itemPage.getList().stream().map(item -> {
            SmsTemplateListResp smsTemplateListResp = new SmsTemplateListResp();
            smsTemplateListResp.setId(item.getId());
            smsTemplateListResp.setGroup(item.getGroup());
            smsTemplateListResp.setApp(item.getApp());
            smsTemplateListResp.setTemplateId(item.getTemplateId());
            smsTemplateListResp.setTemplate(item.getTemplate());
            smsTemplateListResp.setDescription(item.getDescription());
            return smsTemplateListResp;
        }).collect(Collectors.toList());
        return new PageResultResponse<>(smsTemplateListRespList, Convert.toLong(itemPage.getPage()), Convert.toLong(itemPage.getPageSize()), itemPage.getTotal());
    }

    public TeleNodeListResp teleNodeList(TeleNodeListReq teleNodeListReq) {

        TeleNameListArgs args = new TeleNameListArgs();
        args.setUserType(teleNodeListReq.getNodeId());
        args.setName(teleNodeListReq.getNodeName());
        args.setType(teleNodeListReq.getBizType());
        args.setSubType(teleNodeListReq.getNameType());
        args.setPage(teleNodeListReq.getCurrent());
        args.setPageSize(teleNodeListReq.getSize());
        TeleNameListRequest requester = new TeleNameListRequest();
        requester.setArgs(args);
        TeleNameListResp teleNameListResp = telemarketingClient.queryNameList(requester);
        if (!teleNameListResp.isSuccess()) {
            throw new StrategyException("查询电销节点列表异常");
        }

        TeleNameList data = teleNameListResp.getData();

        TeleNodeListResp resp = new TeleNodeListResp();
        List<TeleNodeListResp.TeleType> bizTypeList = data.getNameType().stream().map(item -> {
            TeleNodeListResp.TeleType teleType = new TeleNodeListResp.TeleType();
            teleType.setName(item.getValue());
            teleType.setValue(item.getIndex());
            return teleType;
        }).collect(Collectors.toList());
        resp.setBizTypeList(bizTypeList);

        List<TeleNodeListResp.TeleType> nameTypeList = data.getNameSubType().stream().map(item -> {
            TeleNodeListResp.TeleType teleType = new TeleNodeListResp.TeleType();
            teleType.setName(item.getValue());
            teleType.setValue(item.getIndex());
            return teleType;
        }).collect(Collectors.toList());
        resp.setNameTypeList(nameTypeList);

        List<TeleNodeListResp.TeleNode> teleNodeList = data.getList().stream().map(item -> {
            TeleNodeListResp.TeleNode teleNode = new TeleNodeListResp.TeleNode();
            teleNode.setBizType(item.getType());
            teleNode.setNameType(item.getSubType());
            teleNode.setNodeId(Convert.toLong(item.getUserType()));
            teleNode.setNodeName(item.getName());
            return teleNode;
        }).collect(Collectors.toList());
        resp.setList(new PageResultResponse<>(teleNodeList, Convert.toLong(data.getPage()), Convert.toLong(data.getPageSize()), data.getTotal()));

        return resp;
    }

    public CouponListResp couponList(CouponListReq couponListReq) {
        CouponActivityListReq req = new CouponActivityListReq();
        req.setActivityId(couponListReq.getActivityId());
        req.setActivityName(couponListReq.getActivityName());
        req.setCouponType(couponListReq.getCouponType());
        req.setCouponId(couponListReq.getCouponId());
        req.setCouponName(couponListReq.getCouponName());
        req.setType(couponListReq.getType());
        req.setPage(couponListReq.getCurrent());
        req.setPageSize(couponListReq.getSize());
        BaseCouponResponse<CouponActivityListResp> response = couponClient.activityList(new BaseCouponRequester<>(req));
        log.info("StrategyCommonService couponList request={} response={}", JSONObject.toJSONString(req), JSONObject.toJSONString(response));
        boolean success = response.isSuccess();
        if (!success) {
            log.error("批量发送优惠券失败, req:{}, resp:{}", JsonUtil.toJson(couponListReq), JsonUtil.toJson(response));
            throw new StrategyException("查询优惠券列表异常");
        }

        CouponActivityListResp data = response.getResponse();

        CouponListResp resp = new CouponListResp();
        List<CouponListResp.Coupon> list = data.getResult().stream().map(item -> {
            CouponListResp.Coupon coupon = new CouponListResp.Coupon();
            coupon.setActivityId(item.getId());
            coupon.setActivityName(item.getName());
            coupon.setCouponName(item.getCouponName());
            coupon.setCouponId(item.getCouponId());
            coupon.setCouponType(item.getCouponType());
            coupon.setDiscountAmount(item.getDiscountAmount());
            coupon.setDiscountRate(item.getDiscountRate());
            coupon.setExpiredDays(Objects.equals(couponListReq.getType(), 2) ? item.getExpiredDays() : item.getValidDays());
            coupon.setRateEffectiveDays(getCouponRateEffectiveDays(item));
            coupon.setValidDays(item.getValidDaysAfter());
            return coupon;
        }).collect(Collectors.toList());

        CouponActivityListResp.PageInfo pageInfo = data.getPageInfo();
        resp.setList(new PageResultResponse<>(list, pageInfo.getPage(), pageInfo.getPageSize(), pageInfo.getCount()));

        List<CouponListResp.CouponType> couponTypeList = new ArrayList<>();
        data.getCouponTypeMap().forEach((k, v) -> {
            CouponListResp.CouponType couponType = new CouponListResp.CouponType();
            couponType.setName(v);
            couponType.setValue(Integer.valueOf(k));

            couponTypeList.add(couponType);
        });
        resp.setCouponTypeList(couponTypeList);
        return resp;
    }

    private String getCouponRateEffectiveDays(CouponActivityListResp.CouponInfo item) {
        try {
            if (StringUtils.isNotBlank(item.getDiscountRate()) && StringUtils.isNotBlank(item.getExpiredDays())) {
                return String.format("%s折 - 发放后%s天可用", new BigDecimal(item.getDiscountRate()).divide(new BigDecimal("10")), item.getExpiredDays());
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    void verifyStrategyName(StrategyDo strategyDo, String strategyName, Long updateStrategyId) {
        if (strategyDo == null || strategyDo.getId() == 0) {
            throw new StrategyException("策略不存在");
        }
        StrategyDo strategy = strategyRepository.getByNameAndBusinessType(strategyName, strategyDo.getBusinessType());
        if (strategy != null && !updateStrategyId.equals(strategy.getId())) {
            throw new StrategyException("策略[" + strategyName + "]已存在");
        }
    }

    public void verifyGroups(List<StrategyCreateReq.StrategyGroup> strategyGroups, Integer abTest, Integer abType, String bizKey) {
        if (StrategyAbTestEnum.YES.getCode() == abTest) {

            verifyBizKey(abType, bizKey);

            if (CollectionUtils.isEmpty(strategyGroups)) {
                throw new StrategyException("分组配置信息不能为空");
            }
            this.verifyGroupConfig(strategyGroups, abType);
        }
    }


    public void publish(StrategyDo strategyDo) {
        this.verifyValidEndTime(strategyDo.getValidityEnd());
        strategyDo.setStatus(StrategyStatusEnum.INIT.getCode());
        List<StrategyMarketEventDo> strategyMarketEventDo = strategyMarketEventRepository.selectByStrategyId(strategyDo.getId());
        strategyMarketEventDo.forEach(t->{
            this.eventStrategyExecuting(t.getEventName(), strategyDo);
        });

    }

    void eventStrategyExecuting(String eventName, StrategyDo strategy) {
        LocalDateTime now = LocalDateTime.now();
        if (strategy.getStatus().equals(StrategyStatusEnum.INIT.getCode()) && now.compareTo(strategy.getValidityBegin()) >= 0 && now.compareTo(strategy.getValidityEnd()) <= 0) {
            strategy.setStatus(StrategyStatusEnum.EXECUTING.getCode());
            strategyEventCatchService.resetEventTriggerTime(eventName, strategy);
            strategyEventCatchService.removeHasEventButNoMatchFlag(eventName, strategy.getId());
        }
    }
    void eventStrategyExecutingList(List<InstantStrategyCreateReq.EventSet> event, StrategyDo strategy){
        LocalDateTime now = LocalDateTime.now();
        if (strategy.getStatus().equals(StrategyStatusEnum.INIT.getCode()) && now.compareTo(strategy.getValidityBegin()) >= 0 && now.compareTo(strategy.getValidityEnd()) <= 0) {
            strategy.setStatus(StrategyStatusEnum.EXECUTING.getCode());
            event.forEach(s -> {
                strategyEventCatchService.resetEventTriggerTime(s.getEventName(), strategy);
                strategyEventCatchService.removeHasEventButNoMatchFlag(s.getEventName(), strategy.getId());
            });
        }
    }

    public void createFlowCtrlRule(String strategyTypeName, StrategyDo strategyDo, Integer limitDays, Integer limitTimes, StrategyTypeEnum strategyTypeEnum) {
        if (StringUtils.equalsIgnoreCase("NOTIFY", strategyDo.getDispatchType())) {
            return;
        }
        FlowCtrlDo flowCtrlDo = new FlowCtrlDo();
        flowCtrlDo.setName(strategyTypeName + "-" + strategyDo.getName() + "-" + strategyDo.getValidityBegin().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        flowCtrlDo.setEffectiveStrategy(String.valueOf(strategyDo.getId()));
        flowCtrlDo.setType(FlowCtrlTypeEnum.STRATEGY.getType());
        flowCtrlDo.setStrategyType(strategyTypeEnum.getCode());
        flowCtrlDo.setLimitDays(limitDays);
        flowCtrlDo.setLimitTimes(limitTimes);
        flowCtrlDo.setStatus(FlowCtrlRuleStatusEnum.EFFECTIVE.getCode());
        flowCtrlDo.setCreatedOp(SsoUtil.get().getName());
        flowCtrlDo.setUpdatedOp(SsoUtil.get().getName());
        flowCtrlRepository.insert(flowCtrlDo);
        strategyDo.setFlowCtrlId(flowCtrlDo.getId());
        strategyRepository.updateById(strategyDo);
        if (StrategyRulerEnum.EVENT.getCode() == strategyDo.getSendRuler()) {
            cacheFlowCtrlSerivce.updateFlowCtrlRuleCache(String.valueOf(strategyDo.getId()));
        }
    }

    void verifyValidEndTime(LocalDateTime validityEndTime) {
        if (validityEndTime.isBefore(LocalDateTime.of(LocalDate.now(), LocalTime.parse("00:00:00")))) {
            throw new StrategyException("策略执行时间错误！");
        }
    }

    public void saveMarketConditionInfo(StrategyDo strategyDo, List<InstantStrategyCreateReq.MarketCondition> marketConditionList, List<Integer> excludeType, String businessType, StrategyTypeEnum strategyTypeEnum) {
        List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList = marketConditionList.stream().map(item -> {
            StrategyMarketEventConditionDo strategyMarketEventConditionDo = new StrategyMarketEventConditionDo();
            strategyMarketEventConditionDo.setStrategyId(strategyDo.getId());
            strategyMarketEventConditionDo.setTimeType(item.getTimeType());
            strategyMarketEventConditionDo.setTimeValue(item.getTimeValue());
            strategyMarketEventConditionDo.setLabelName(item.getLabelName());
            strategyMarketEventConditionDo.setOperateType(item.getOperateType());

            StrategyInstantLabelDo strategyInstantLabelDo = strategyInstantLabelService.getByLabelNameAndLabelType(item.getLabelName(), StrategyInstantLabelTypeEnum.LABEL, strategyTypeEnum.getCode(), item.getOptional());
            strategyMarketEventConditionDo.setConditionValue(item.getConditionValue());
            strategyMarketEventConditionDo.setExpression(this.convertToExpression(item.getLabelName(), item.getOperateType(), item.getConditionValue(), strategyInstantLabelDo.getLabelValueType()));
            strategyMarketEventConditionDo.setRelationship(1);
            strategyMarketEventConditionDo.setOptional(strategyInstantLabelDo.getOptional());

            return strategyMarketEventConditionDo;
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(excludeType)) {
            //排除项
            List<StrategyInstantLabelDo> excludeLabel = strategyInstantLabelRepository.selectEventLabelByExcludeTypeAndBusinessType(excludeType, businessType, strategyTypeEnum.getCode());
            excludeLabel.forEach(item -> {
                StrategyMarketEventConditionDo strategyMarketEventConditionDo = new StrategyMarketEventConditionDo();
                strategyMarketEventConditionDo.setStrategyId(strategyDo.getId());
                strategyMarketEventConditionDo.setLabelName(item.getLabelName());
                strategyMarketEventConditionDo.setOperateType(item.getOperateType());
                strategyMarketEventConditionDo.setConditionValue("0");
                strategyMarketEventConditionDo.setExpression(this.convertToExpression(item.getLabelName(), OperatorEnum.EQ.name(), "0", item.getLabelValueType()));
                strategyMarketEventConditionDo.setRelationship(1);
                strategyMarketEventConditionDo.setOptional(item.getOptional());
                strategyMarketEventConditionDoList.add(strategyMarketEventConditionDo);
            });
        }
        if (!CollectionUtils.isEmpty(strategyMarketEventConditionDoList)) {
            cacheStrategyMarketEventConditionService.insertBatch(strategyMarketEventConditionDoList);
        }
    }


    public StrategyMarketEventConditionDo updateMarketConditionInfo(InstantStrategyCreateReq.MarketCondition marketCondition, Long strategyId, StrategyTypeEnum strategyTypeEnum) {
        StrategyMarketEventConditionDo strategyMarketEventConditionDo;
        if (marketCondition.getConditionId() != null) {
            strategyMarketEventConditionDo = strategyMarketEventConditionRepository.selectById(marketCondition.getConditionId());
            if (strategyMarketEventConditionDo == null) {
                throw new StrategyException(marketCondition.getConditionId() + "条件不存在");
            }
        } else {
            strategyMarketEventConditionDo = new StrategyMarketEventConditionDo();
        }
        strategyMarketEventConditionDo.setStrategyId(strategyId);
        strategyMarketEventConditionDo.setTimeType(marketCondition.getTimeType());
        strategyMarketEventConditionDo.setTimeValue(marketCondition.getTimeValue());
        strategyMarketEventConditionDo.setLabelName(marketCondition.getLabelName());
        strategyMarketEventConditionDo.setOperateType(marketCondition.getOperateType());

        StrategyInstantLabelDo strategyInstantLabelDo = strategyInstantLabelService.getByLabelNameAndLabelType(marketCondition.getLabelName(), StrategyInstantLabelTypeEnum.LABEL, strategyTypeEnum.getCode(), marketCondition.getOptional());
        strategyMarketEventConditionDo.setConditionValue(marketCondition.getConditionValue());
        strategyMarketEventConditionDo.setExpression(this.convertToExpression(marketCondition.getLabelName(), marketCondition.getOperateType(), marketCondition.getConditionValue(), strategyInstantLabelDo.getLabelValueType()));
        strategyMarketEventConditionDo.setRelationship(1);
        strategyMarketEventConditionDo.setOptional(strategyInstantLabelDo.getOptional());
        strategyMarketEventConditionDo.setUpdatedOp(SsoUtil.get().getName());

        if (marketCondition.getConditionId() != null) {
            strategyMarketEventConditionRepository.updateById(strategyMarketEventConditionDo);
        } else {
            strategyMarketEventConditionRepository.insert(strategyMarketEventConditionDo);
        }
        return strategyMarketEventConditionDo;
    }

    public void updateExcludeLabel(Long strategyId, List<Integer> excludeType, String businessType, StrategyTypeEnum strategyTypeEnum) {
        strategyMarketEventConditionRepository.deleteByStrategyIdAndOptional(strategyId, StrategyInstantLabelOptionEnum.EXCLUDE_OPTION.getCode());
        //排除项
        List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(excludeType)) {
            List<StrategyInstantLabelDo> excludeLabel = strategyInstantLabelRepository.selectEventLabelByExcludeTypeAndBusinessType(excludeType, businessType, strategyTypeEnum.getCode());
            excludeLabel.forEach(item -> {
                StrategyMarketEventConditionDo strategyMarketEventConditionDo = new StrategyMarketEventConditionDo();
                strategyMarketEventConditionDo.setStrategyId(strategyId);
                strategyMarketEventConditionDo.setLabelName(item.getLabelName());
                strategyMarketEventConditionDo.setOperateType(item.getOperateType());
                strategyMarketEventConditionDo.setConditionValue("0");
                strategyMarketEventConditionDo.setExpression(this.convertToExpression(item.getLabelName(), OperatorEnum.EQ.name(), "0", item.getLabelValueType()));
                strategyMarketEventConditionDo.setRelationship(1);
                strategyMarketEventConditionDo.setOptional(item.getOptional());
                strategyMarketEventConditionDoList.add(strategyMarketEventConditionDo);
            });
        }
        if (!CollectionUtils.isEmpty(strategyMarketEventConditionDoList)) {
            strategyMarketEventConditionRepository.insertBatch(strategyMarketEventConditionDoList);
        }
        cacheStrategyMarketEventConditionService.updateEventConditionCache(strategyId);
    }

    public Pair<List<InstantStrategyDetailResp.MarketCondition>, List<Integer>> getMarketConditionList(Long strategyId, StrategyTypeEnum strategyTypeEnum) {
        List<InstantStrategyDetailResp.MarketCondition> marketConditions = new ArrayList<>();
        List<Integer> excludeLabel = new ArrayList<>();
        List<StrategyMarketEventConditionDo> strategyMarketEventConditionList = strategyMarketEventConditionRepository.getByStrategyId(strategyId);
        strategyMarketEventConditionList.forEach(item -> {
            if (item.getOptional() == 1) {
                InstantStrategyDetailResp.MarketCondition marketCondition = new InstantStrategyDetailResp.MarketCondition();
                marketCondition.setLabelName(item.getLabelName());
                marketCondition.setConditionId(item.getId());
                marketCondition.setTimeType(item.getTimeType());
                marketCondition.setTimeValue(item.getTimeValue());
                marketCondition.setOperateType(item.getOperateType());
                marketCondition.setConditionValue(item.getConditionValue());
                marketCondition.setRelationship(item.getRelationship());
                marketCondition.setOptional(item.getOptional());
                marketConditions.add(marketCondition);
            } else {
                StrategyInstantLabelDo strategyInstantLabelDo = strategyInstantLabelService.getByLabelNameAndLabelType(item.getLabelName(), StrategyInstantLabelTypeEnum.LABEL, strategyTypeEnum.getCode(), item.getOptional());
                excludeLabel.add(strategyInstantLabelDo.getExcludeType());
            }
        });
        return Pair.of(marketConditions, excludeLabel.stream().distinct().collect(Collectors.toList()));
    }

    public String convertToExpression(String labelName, String operateType, String conditionValue, String labelValueType) {
        StringBuilder exp = new StringBuilder();
        log.info("testPoint:{}", labelValueType);
        ValueTypeEnum valueTypeEnum = ValueTypeEnum.getInstance(labelValueType);
        OperatorEnum operate = OperatorEnum.getInstance(operateType);

        if (valueTypeEnum == ValueTypeEnum.DATE || valueTypeEnum == ValueTypeEnum.TIMESTAMP) {
            String firstValue;
            String secondVal = "''";
            String[] values = conditionValue.split(",");
            firstValue = "'" + values[0] + "'";
            if (operate == OperatorEnum.BETWEEN) {
                if (values.length < 2) {
                    throw new StrategyException("区间必须包含两个值");
                }
                secondVal = "'" + values[1] + "'";
            }
            exp.append("time.compare(")
                    .append(labelName)
                    .append(",'")
                    .append(operate.getCode())
                    .append("',")
                    .append(firstValue)
                    .append(",")
                    .append(secondVal)
                    .append(")");
        } else {
            this.customExp(exp, operate, labelName, conditionValue, valueTypeEnum);
        }

        return exp.toString();
    }


    private void customExp(StringBuilder exp, OperatorEnum operate, String labelName, String conditionValue, ValueTypeEnum valueTypeEnum) {
        if (operate == OperatorEnum.CONTAIN || operate == OperatorEnum.NOTCONTAIN) {
            exp.append(operate.getCode()).append("(").append(labelName).append(",").append("'").append(conditionValue).append("'").append(")");
        } else if (operate == OperatorEnum.BETWEEN) {
            //必须要有两个值
            String[] vs = conditionValue.split(",");
            if (vs.length >= 2) {
                exp.append("(").append(labelName).append(OperatorEnum.GE.getCode()).append(toValue(labelName, valueTypeEnum, vs[0]))
                        .append(" && ").append(labelName).append(OperatorEnum.LE.getCode()).append(toValue(labelName, valueTypeEnum, vs[1])).append(")");
            } else if (vs.length >= 1) {
                exp.append(labelName).append(OperatorEnum.GE.getCode()).append(toValue(labelName, valueTypeEnum, vs[0]));
            }
        } else {
            if (Objects.equals(labelName, "registerTime") || Objects.equals(labelName, "temporaryQuotaEffectiveTime") || Objects.equals(labelName, "temporaryQuotaInvalidTime")) {
                exp.append(labelName).append(" ").append(operate.getCode()).append(" ").append("getDate").append("(").append(conditionValue).append(")");
            } else {
                exp.append(labelName).append(" ").append(operate.getCode()).append(" ").append(this.toValue(labelName, valueTypeEnum, conditionValue));
            }
        }
    }

    private Object toValue(String labelName, ValueTypeEnum valueType, String val) {
        if (valueType == ValueTypeEnum.Currency || valueType == ValueTypeEnum.DOUBLE ||
                valueType == ValueTypeEnum.INTEGER || valueType == ValueTypeEnum.LONG ||
                valueType == ValueTypeEnum.TIMESTAMP) {
            if (StringUtils.isEmpty(val)) {
                return 0;
            }
            try {
                return valueType.normalizeValue(val);
            } catch (NumberFormatException e) {
                log.warn(labelName + " 为数值型字段, 比较值为非数字: " + val);
            }
            return val;
        } else if (valueType == ValueTypeEnum.Boolean) {
            if (StringUtils.isEmpty(val)) {
                return 0;
            }
            String t = "1";
            String t2 = "true";
            return val.equals(t) || val.equals(t2);
        } else if (valueType == ValueTypeEnum.TIME_H_M_S) {
            return Integer.parseInt(val.replace(":", ""));
        } else if (valueType == ValueTypeEnum.NULL) {
            return "nil";
        } else {
            if (StringUtils.isEmpty(val)) {
                return "''";
            }
            return "'" + val + "'";
        }
    }

    public void verifyRuleConfig(Integer limitDays, Integer limitTimes) {
        Integer maxLimitDays = strategyConfig.getMaxLimitDays();
        Integer maxLimitTimes = strategyConfig.getMaxLimitTimes();
        if (limitDays == null || limitTimes == null) {
            throw new StrategyException("触达限制不能为空");
        }
        if (limitDays < 1 || limitDays > maxLimitDays) {
            throw new StrategyException("触达天数在[" + 1 + "," + maxLimitDays + "]之间");
        }
        if (limitTimes < 0 || limitTimes > maxLimitTimes) {
            throw new StrategyException("触达次数在[" + 0 + "," + maxLimitTimes + "]之间");
        }
    }

    private void verifyBizKey(Integer abType, String bizKey) {
        if (StrategyGroupTypeEnum.NEW_RANDOM.getCode() == abType && StringUtils.isBlank(bizKey)) {
            throw new StrategyException("新随机数场景bizKey不能为空");
        }
    }

    public boolean checkAPIOpenAmountExtInfo(String extInfo) {
        if (StringUtils.isEmpty(extInfo)) {
            return false;
        }

        APIOpenAmountDto apiOpenAmountDto = JsonUtil.parse(extInfo, APIOpenAmountDto.class);
        if (Objects.isNull(apiOpenAmountDto) ||
                StringUtils.isEmpty(apiOpenAmountDto.getInnerApps()) ||
                Objects.isNull(apiOpenAmountDto.getAfterDays()) ||
                apiOpenAmountDto.getAfterDays() < 0 || apiOpenAmountDto.getAfterDays() > 360) {
            return false;
        }

        return true;
    }

    public boolean checkAiProntoChannel(String extInfo) {
        if (StringUtils.isBlank(extInfo)) {
            return false;
        }
        AiProntoChannelDto aiProntoChannelDto = JsonUtil.parse(extInfo, AiProntoChannelDto.class);
        if (Objects.isNull(aiProntoChannelDto) || StringUtils.isBlank(aiProntoChannelDto.getNameTypeId())) {
            return false;
        }
        return true;
    }

    public boolean checkIncreaseAmtChannnel(String extra) {
        if (StringUtils.isEmpty(extra)) {
            return false;
        }
        IncreaseAmtDto increaseAmtDto = JsonUtil.parse(extra, IncreaseAmtDto.class);
        if (increaseAmtDto == null || Objects.isNull(increaseAmtDto.getIncreaseAmtConfig())) {
            log.info("提额配置验证失败");
            return false;
        }
        IncreaseAmtDto.IncreaseAmtConfig increaseAmtConfig = increaseAmtDto.getIncreaseAmtConfig();
        if (!IncreaseAmtDto.IncreaseType.increaseTypes.contains(increaseAmtConfig.getIncreaseType())) {
            return false;
        }
        if (StringUtils.equalsAny(increaseAmtConfig.getIncreaseType(), IncreaseAmtDto.IncreaseType.PERSONAL_API_FST_LOGIN_TEMP, IncreaseAmtDto.IncreaseType.LOAN_UPTO_FULLAMT)) {
            return true;
        }
        if (increaseAmtConfig.getIncreaseAmt() == null || BigDecimal.ZERO.compareTo(increaseAmtConfig.getIncreaseAmt()) > -1) {
            return false;
        }
        IncreaseAmtDto.IncreaseAmtConfig.IncreaseTimeConfig increaseTimeConfig = increaseAmtConfig.getIncreaseTimeConfig();
        String type = increaseTimeConfig.getType();
        if (!IncreaseAmtDto.IncreaseTimeType.increaseTimeTypes.contains(type)) {
            log.info("提额时间选项错误");
            return false;
        }
        if (Objects.equals(type, IncreaseAmtDto.IncreaseTimeType.FIXED_TIME)) {
            IncreaseAmtDto.FixedTimeDto fixedTimeDto = (IncreaseAmtDto.FixedTimeDto) increaseTimeConfig.convertToValueDto();
            if (fixedTimeDto == null || !fixedTimeDto.isValid()) {
                return false;
            }
        } else if (Objects.equals(type, IncreaseAmtDto.IncreaseTimeType.DYNAMIC_TIME)) {
            IncreaseAmtDto.DynamicValueDto dynamicValueDto = (IncreaseAmtDto.DynamicValueDto) increaseTimeConfig.convertToValueDto();
            if (dynamicValueDto == null || !dynamicValueDto.isValid()) {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }
}
