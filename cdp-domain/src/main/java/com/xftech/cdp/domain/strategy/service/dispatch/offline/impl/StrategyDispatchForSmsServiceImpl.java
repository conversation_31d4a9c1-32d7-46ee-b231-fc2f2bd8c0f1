package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushQueryStatusEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdPushBatchRepository;
import com.xftech.cdp.domain.dispatch.BatchDispatchService;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchForSmsService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sms.model.SmsBatchSendArgs;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.client.sms.model.SmsSendRequester;
import com.xftech.cdp.infra.client.sms.model.resp.SmsBatchReportItem;
import com.xftech.cdp.infra.client.sms.model.resp.SmsBatchReportResp;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 短信渠道下发逻辑
 *
 * <AUTHOR>
 * @since 2023/2/20
 */
@Slf4j
@RefreshScope
@Service(StrategyDispatchConstants.SMS_SERVICE)
public class StrategyDispatchForSmsServiceImpl extends AbstractStrategyDispatchService implements StrategyDispatchService, StrategyDispatchForSmsService {

    @Autowired
    private Config config;
    @Autowired
    private BatchDispatchService batchDispatchService;
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private CrowdPushBatchRepository crowdPushBatchRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private SmsClient smsClient;


    /**
     * 设置批次大小
     *
     * @return 批次大小
     */
    @Override
    protected Integer setBatchSize() {
        return strategyConfig.getSmsBatchSize();
    }

    /**
     * 下发
     *
     * @param strategyContext 策略执行初始化内容
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           按分组配置过滤出来的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<T> params) {
        innerApp = StringUtils.isBlank(innerApp) ? app : innerApp;
        return this.sendSms(strategyContext, app, innerApp, batch, Convert.convert(new TypeReference<List<SmsBatchWithParamArgs.Sms>>() {}, params));
    }

    /**
     * 重试下发
     *
     * @param context     策略执行初始化内容
     * @param tableNameNo 下发明细表序号
     * @param app         app
     * @param innerApp    innerApp
     * @param detailList  下发明细
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext context, String tableNameNo, String app, String innerApp, List<CrowdDetailDo> detailList, List<T> params) {
        if (Objects.isNull(innerApp)) {
            innerApp = app;
        }
        return sendSms(context, app, innerApp, detailList, Convert.convert(new TypeReference<List<SmsBatchWithParamArgs.Sms>>() {}, params));
    }

    /**
     * 下发短信
     *
     * @param strategyContext 策略上下文
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           手机号
     * @return 下发数量
     */
    public ImmutablePair<Integer, CrowdPushBatchDo> sendSms(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<SmsBatchWithParamArgs.Sms> params) {
        if (CollectionUtils.isEmpty(params)) {
            return batchDispatchService.sendSms(convertToDispatchDto(strategyContext), app, innerApp, batch);
        }
        return batchDispatchService.sendSmsWithParam(convertToDispatchDto(strategyContext), app, innerApp, batch, params);
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void smsStrategyPushBatchUpdate(CrowdPushBatchDo crowdPushBatchDo) {
        CrowdPushBatchDo updateCrowdPushBatchDo = new CrowdPushBatchDo();
        updateCrowdPushBatchDo.setId(crowdPushBatchDo.getId());
        updateCrowdPushBatchDo.setQueryStatus(CrowdPushQueryStatusEnum.EXECUTING.getCode());
        // 超过短信回执推送时间，查询状态更新为已完成
        if (crowdPushBatchDo.getCreatedTime().plusHours(config.getSmsReportOverTime()).isBefore(LocalDateTime.now())) {
            updateCrowdPushBatchDo.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode());
        }

        // 下游系统实际发送数量未更新
        if (crowdPushBatchDo.getSmsSendCount() == null || crowdPushBatchDo.getSmsSendCount().equals(0) || StringUtils.isBlank(crowdPushBatchDo.getDetailTableNo())) {
            // 调用短信中心查询指定批次短信发送报告
            SmsBatchSendArgs args = new SmsBatchSendArgs();
            args.setBatchNum(crowdPushBatchDo.getBatchNum());
            SmsSendRequester requester = new SmsSendRequester();
            requester.setArgs(args);
            SmsBatchReportResp baseSmsResp = smsClient.batchReport(requester);
            if (baseSmsResp.isSuccess() && baseSmsResp.getResponse() != null) {
                SmsBatchReportItem.Reports reports = baseSmsResp.getResponse().getReports();
                if (reports == null) {
                    log.info("批次短信报告内容为空，批次记录id:{}，批次号：{}", crowdPushBatchDo.getId(), crowdPushBatchDo.getBatchNum());
                    reports = new SmsBatchReportItem.Reports();
                }
                if (ObjectUtil.isNull(reports.getDelivered())) {
                    reports.setDelivered(0);
                }
                if (ObjectUtil.isNull(reports.getFailed())) {
                    reports.setFailed(0);
                }
                if (ObjectUtil.isNull(reports.getWaitDelivery())) {
                    reports.setWaitDelivery(0);
                }
                log.info("批次短信报告内容，批次记录id:{}，成功数量:{}，失败数量：{}，发送中数量：{}",
                        crowdPushBatchDo.getId(), reports.getDelivered(), reports.getFailed(), reports.getWaitDelivery());
                updateCrowdPushBatchDo.setSmsSendCount(reports.getDelivered() + reports.getFailed() + reports.getWaitDelivery());

                // 存量短信批次处理，无短信回执
                if (StringUtils.isBlank(crowdPushBatchDo.getDetailTableNo())) {
                    updateCrowdPushBatchDo.setSuccCount(reports.getDelivered());
                    updateCrowdPushBatchDo.setFailCount(reports.getFailed());
                    // 批次总数 <= 成功数 + 失败数
                    if (crowdPushBatchDo.getBatchTotal() <= (reports.getDelivered() + reports.getFailed())) {
                        // 批次完成查询状态：已完成
                        updateCrowdPushBatchDo.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode());
                    }
                    crowdPushBatchRepository.updateById(updateCrowdPushBatchDo);
                    Integer succCount = crowdPushBatchRepository.sumBatchSuccCount(crowdPushBatchDo.getStrategyExecLogId());
                    // 更新策略日志执行成功数量
                    StrategyExecLogDo updateStrategyExecLogDo = new StrategyExecLogDo();
                    updateStrategyExecLogDo.setId(crowdPushBatchDo.getStrategyExecLogId());
                    updateStrategyExecLogDo.setSuccCount(succCount);
                    strategyExecLogRepository.updateById(updateStrategyExecLogDo);
                    return;
                }
            }
        }

        // 增量短信批次处理，有短信回执
        Integer succArrive = userDispatchDetailRepository.countArriveStatus(crowdPushBatchDo.getDetailTableNo(), 1, crowdPushBatchDo.getBatchNum());
        Integer failArrive = crowdPushBatchDo.getSmsSendCount() > 0 ? (crowdPushBatchDo.getSmsSendCount() - succArrive) : 0;
        updateCrowdPushBatchDo.setSuccCount(succArrive);
        updateCrowdPushBatchDo.setFailCount(failArrive);
        crowdPushBatchRepository.updateById(updateCrowdPushBatchDo);

        StrategyExecLogDo batchCount = crowdPushBatchRepository.countCrowdPushCount(crowdPushBatchDo.getStrategyExecLogId());
        // 更新策略日志执行成功数量
        StrategyExecLogDo updateStrategyExecLogDo = new StrategyExecLogDo();
        updateStrategyExecLogDo.setId(crowdPushBatchDo.getStrategyExecLogId());
        // 下游接收人数
        updateStrategyExecLogDo.setReceiveCount(batchCount.getReceiveCount());
        // 供应商接收人数
        updateStrategyExecLogDo.setSupplierCount(batchCount.getSupplierCount());
        // 供应商实际发送人数
//        updateStrategyExecLogDo.setActualCount(batchCount.getActualCount());
        updateStrategyExecLogDo.setActualCount(batchCount.getSupplierCount());
        // 执行成功人数
        updateStrategyExecLogDo.setSuccCount(batchCount.getSuccCount());
        strategyExecLogRepository.updateById(updateStrategyExecLogDo);
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void couponStrategyPushBatchUpdate(CrowdPushBatchDo crowdPushBatchDo) {
        CrowdPushBatchDo updateCrowdPushBatchDo = new CrowdPushBatchDo();
        updateCrowdPushBatchDo.setId(crowdPushBatchDo.getId());
        // 超过报告查询天数，查询状态更新为已完成
        if (crowdPushBatchDo.getCreatedTime().toLocalDate().plusDays(config.getCouponQueryDay()).isBefore(LocalDateTime.now().toLocalDate())) {
            updateCrowdPushBatchDo.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode());
        }
        Integer succArrive = userDispatchDetailRepository.countArriveStatus(crowdPushBatchDo.getDetailTableNo(), 1, crowdPushBatchDo.getBatchNum());
        Integer failArrive = userDispatchDetailRepository.countArriveStatus(crowdPushBatchDo.getDetailTableNo(), 0, crowdPushBatchDo.getBatchNum());
        Integer usedArrive = userDispatchDetailRepository.countUsedCount(crowdPushBatchDo.getDetailTableNo(), 1, crowdPushBatchDo.getBatchNum());

        updateCrowdPushBatchDo.setSmsSendCount(succArrive + failArrive);
        updateCrowdPushBatchDo.setSuccCount(succArrive);
        updateCrowdPushBatchDo.setFailCount(failArrive);
        updateCrowdPushBatchDo.setUsedCount(usedArrive);
        updateCrowdPushBatchDo.setQueryStatus(CrowdPushQueryStatusEnum.EXECUTING.getCode());

        // 批次总数 <= 使用数
        if (crowdPushBatchDo.getBatchTotal() <= usedArrive) {
            updateCrowdPushBatchDo.setQueryStatus(CrowdPushQueryStatusEnum.COMPLETED.getCode());
        }
        crowdPushBatchRepository.updateById(updateCrowdPushBatchDo);

        StrategyExecLogDo batchCount = crowdPushBatchRepository.countCrowdPushCount(crowdPushBatchDo.getStrategyExecLogId());
        // 更新策略日志执行成功数量
        StrategyExecLogDo updateStrategyExecLogDo = new StrategyExecLogDo();
        updateStrategyExecLogDo.setId(crowdPushBatchDo.getStrategyExecLogId());
        // 下游接收人数
        updateStrategyExecLogDo.setReceiveCount(batchCount.getReceiveCount());
        // 供应商接收人数
        updateStrategyExecLogDo.setSupplierCount(batchCount.getSupplierCount());
        // 供应商实际发送人数
        updateStrategyExecLogDo.setActualCount(batchCount.getActualCount());
        // 执行成功人数
        updateStrategyExecLogDo.setSuccCount(batchCount.getSuccCount());
        // 使用数量
        updateStrategyExecLogDo.setUsedCount(batchCount.getUsedCount());
        strategyExecLogRepository.updateById(updateStrategyExecLogDo);
    }
}
