/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.stat.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @version $ StatRealtimeStrategyEngineFlowDataEntity, v 0.1 2023/12/12 10:47 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatStrategyEngineFlowDataEntity implements Serializable {

    private static final long serialVersionUID = 6125677496966505765L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 统计日期
     */
    private LocalDate bizDate;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 类型：1:离线,2:T0
     */
    private Integer type;

    /**
     * 人群包刷新完成时间
     */
    private LocalDateTime crowdRefreshTime;

    /**
     * 人群包用户数
     */
    private Integer crowdUserNum;

    /**
     * 触发事件总人次
     */
    private Integer eventSum;

    /**
     * 事件条件过滤人次
     */
    private Integer filterEventSum;

    /**
     * 分组后进入引擎人次
     */
    private Integer engineSum;

    /**
     * 分组后进入引擎人数
     */
    private Integer engineNum;

    /**
     * 决策结果为营销人次
     */
    private Integer marketSum;

    /**
     * 决策结果为营销人数
     */
    private Integer marketNum;

    /**
     * 排除标签过滤人次(决策结果为营销)
     */
    private Integer excludeMarketSum;

    /**
     * 流控过滤人次
     */
    private Integer flowControlSum;

    /**
     * 应发用户数
     */
    private Integer dispatchNum;

    /**
     * 决策结果为不营销人次
     */
    private Integer notMarketSum;

    /**
     * 决策结果为不营销人数
     */
    private Integer notMarketNum;


    /**
     * 排除标签过滤人次(决策结果为不营销)
     */
    private Integer excludeNotMarketSum;

    /**
     * 决策失败人次
     */
    private Integer decisionFailSum;

    /**
     * 决策失败人数
     */
    private Integer decisionFailNum;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 修改时间
     */
    private Date updatedTime;
}