package com.xftech.cdp.domain.crowd.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelRelationEnum;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.crowd.model.label.crowd.FixedCheckOption;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelPrimaryRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdLabelSubRepository;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.domain.crowd.service.CrowdLabelService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.*;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.ListUtils;
import com.xftech.cdp.infra.utils.MapUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.poi.hssf.record.DVALRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
@Service
public class CrowdLabelServiceImpl implements CrowdLabelService {

    @Autowired
    private CrowdLabelRepository crowdLabelRepository;
    @Autowired
    private CrowdLabelPrimaryRepository crowdLabelPrimaryRepository;
    @Autowired
    private CrowdLabelSubRepository crowdLabelSubRepository;
    @Autowired
    private LabelRepository labelRepository;
    @Autowired
    private RedisUtils redisUtils;


    @Override
    public CrowdLabelDo insert(CrowdLabelDo crowdLabelDo) {
        crowdLabelRepository.insert(crowdLabelDo);
        return crowdLabelDo;
    }

    @Override
    public List<List<String>> getNewRandomLabels(CrowdPackDo crowdPackDo) {
        List<List<String>> newRandomLabels = null;

        String redisKey = String.format("xyf_cdp:crowd:new_random:labels:%d", crowdPackDo.getId());
        String value = redisUtils.get(redisKey);
        if ( !StringUtils.isEmpty(value)) {
            JSONArray objects = JSONArray.parseArray(value);
            if (Objects.nonNull(objects) && objects.size() == 2) {
                newRandomLabels = new ArrayList<>(2);
                List<String> includeLabels = JSONArray.parseArray(objects.get(0).toString(), String.class);
                List<String> excludeLabels = JSONArray.parseArray(objects.get(1).toString(), String.class);
                newRandomLabels.add(includeLabels);
                newRandomLabels.add(excludeLabels);
                return newRandomLabels;
            }
        }

        newRandomLabels = getNewRandomLabelsDB(crowdPackDo);
        Object json = JSONArray.toJSON(newRandomLabels);
        redisUtils.set(redisKey, json, RedisUtils.DEFAULT_EXPIRE_SECONDS * 30);
        return newRandomLabels;
    }

    private List<List<String>> getNewRandomLabelsDB(CrowdPackDo crowdPackDo) {
        List<CrowdLabelDo> crowdLabelList = crowdLabelRepository.selectListByCrowdId(crowdPackDo.getId());
//        List<CrowdLabelPrimaryDo> crowdLabelPrimaryList = crowdLabelPrimaryRepository.selectListByCrowdId(crowdPackDo.getId());
        List<CrowdLabelSubDo> crowdLabeSublList = crowdLabelSubRepository.selectSubListByCrowdId(crowdPackDo.getId());

        List<Long> labelIds = ListUtils.distinctMap(crowdLabelList, CrowdLabelDo::getLabelId);
        labelIds.addAll(ListUtils.distinctMap(crowdLabeSublList, CrowdLabelSubDo::getLabelId));
        List<CrowdLabelPrimaryDo> crowdLabelPrimaryList = crowdLabelPrimaryRepository.selectListByCrowdId(crowdPackDo.getId());
        List<LabelDo> labelDos = labelRepository.selectBatchIds(labelIds);
        Map<Long, LabelDo> labelMap = MapUtils.listToMap(labelDos, LabelDo::getId);
        Map<String, LabelDo> labelNameMap = MapUtils.listToMap(labelDos, LabelDo::getLabelName);


        Map<Long, List<CrowdLabelDo>> crowdLabelMap = MapUtils.listGroup(crowdLabelList, CrowdLabelDo::getCrowdLabelPrimaryId);
        Map<Long, List<CrowdLabelSubDo>> crowdLabelSubMap = MapUtils.listGroup(crowdLabeSublList, CrowdLabelSubDo::getCrowdLabelId);

        Map<Integer, List<CrowdLabelPrimaryDo>> crowdLabelPrimaryMap = MapUtils.listGroup(crowdLabelPrimaryList, CrowdLabelPrimaryDo::getLabelGroupType);

        List<String> includeNewRandom = new ArrayList<>();
        List<String> excludeNewRandom = new ArrayList<>();

        crowdLabelPrimaryMap.forEach((labelGroupType, labelPrimaryList) -> {
            for (CrowdLabelPrimaryDo crowdLabelPrimary : labelPrimaryList) {

//                List<CrowdContext.Label> labels = new ArrayList<>();
                List<CrowdLabelDo> crowdLabels = crowdLabelMap.get(crowdLabelPrimary.getId());
                Map<Long, CrowdLabelDo> crowdLabelLabelIdMap = MapUtils.listToMap(crowdLabels, CrowdLabelDo::getLabelId);
                if (CollectionUtils.isEmpty(crowdLabels)) {
                    continue;
                }
                for (CrowdLabelDo crowdLabel : crowdLabels) {
                    LabelDo labelDo = labelMap.get(crowdLabel.getLabelId());
                    labelProcess(labelDo, labelNameMap, crowdLabelLabelIdMap, crowdLabel.getLabelId());
                    if (labelDo.getLabelType() == 1) {
                        if ("new_random".equals(labelDo.getDataWarehouseField())) {
                            filterNewRandom(labelDo.getDataWarehouseField(), crowdLabel.getLabelValue(), labelGroupType, includeNewRandom, excludeNewRandom);
                        }
                        // 无三级逻辑标签
                        if (CollectionUtils.isEmpty(crowdLabelSubMap.get(crowdLabel.getId()))) {
                            continue;
                        }
                    }

                    if (!CollectionUtils.isEmpty(crowdLabelSubMap.get(crowdLabel.getId()))) {
                        List<CrowdLabelSubDo> subLabels1 = crowdLabelSubMap.get(crowdLabel.getId());
                        for (CrowdLabelSubDo crowdLabelSubDo : subLabels1) {
                            LabelDo sublabelDo = labelMap.get(crowdLabelSubDo.getLabelId());
                            labelProcess(sublabelDo, labelNameMap, crowdLabelLabelIdMap, crowdLabelSubDo.getLabelId());
                            if (sublabelDo.getLabelType() == 1) {
                                if ("new_random".equals(sublabelDo.getDataWarehouseField())) {
                                    filterNewRandom(sublabelDo.getDataWarehouseField(), crowdLabelSubDo.getLabelValue(), labelGroupType, includeNewRandom, excludeNewRandom);
                                }
                            }
                        }
                    }
                }
            }
        });

        return Arrays.asList(includeNewRandom, excludeNewRandom);
    }

    private static void labelProcess(LabelDo labelDo, Map<String, LabelDo> labelNameMap, Map<Long, CrowdLabelDo> crowdLabelLabelIdMap, Long labelId) {
        if (labelDo == null) {
            throw new CrowdException(String.format("不存在该标签，标签id: %s", labelId));
        }
        if (labelDo.getLabelName().equals(LabelEnum.SecondaryLabelEnum.DROP_THE_NODE_DATE.getDescription())) {
            LabelDo dropNodeLabel = labelNameMap.get(LabelEnum.SecondaryLabelEnum.DROP_THE_NODE.getDescription());
            if (dropNodeLabel == null) {
                throw new CrowdException("存在脱落节点天数但不存在脱落节点！");
            }
            CrowdLabelDo dropCrowdLabel = crowdLabelLabelIdMap.get(dropNodeLabel.getId());
            if (dropCrowdLabel == null) {
                throw new CrowdException("存在脱落节点天数但不存在脱落节点！");
            }
            LabelDo replacelabelBo = labelNameMap.get(labelDo.getLabelName());
            labelDo.setDataWarehouseField((String) JSON.parseObject(replacelabelBo.getConfigurationReflect(), Map.class).get(JSON.parseObject(dropCrowdLabel.getLabelValue(), FixedCheckOption.class).getItems().get(0)));
        }
    }

    private static void filterNewRandom(String labelName, String labelValue, Integer labelGroupType, List<String> includeNewRandom, List<String> excludeNewRandom) {
        if (!"new_random".equals(labelName)) {
            return;
        }
        if (labelGroupType == CrowdLabelEnum.INCLUDE_LABEL.getCode()) {
            includeNewRandom.add(labelValue);
        } else {
            excludeNewRandom.add(labelValue);
        }
    }
}
