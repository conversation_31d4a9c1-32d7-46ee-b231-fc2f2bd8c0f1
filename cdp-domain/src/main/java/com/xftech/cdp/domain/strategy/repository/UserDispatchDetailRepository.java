package com.xftech.cdp.domain.strategy.repository;

import com.dianping.cat.proxy.Tracer;
import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchDetailDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/27 9:56
 */
@Slf4j
@Component
public class UserDispatchDetailRepository {

    /**
     * 根据用户ID分页查询下发明细
     *
     * @param tableNameNo 明细表序号
     * @param userIds     用户ID集合
     * @param page        页码
     * @param pageSize    每页大小
     * @return 下发明细
     */
    public Page<UserDispatchDetailDo> selectPage(String tableNameNo, List<Long> userIds, Integer page, Integer pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("userIds", userIds);
        return DBUtil.selectPage("userDispatchDetailMapper.selectPage", params, (page - 1) * pageSize, pageSize);
    }

    public List<UserDispatchDetailDo> selectList(String tableNameNo, List<Long> userIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("userIds", userIds);
        return DBUtil.selectList("userDispatchDetailMapper.selectList", params);
    }

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public UserDispatchDetailDo selectById(String tableNameNo, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("id", id);
        return DBUtil.selectOne("userDispatchDetailMapper.selectByPrimaryKey", params);
    }

//    /**
//     * 插入
//     *
//     * @param userDispatchDetailDo 对象
//     * @return 是否插入成功标识
//     */
//    public boolean insert(String tableNameNo, UserDispatchDetailDo userDispatchDetailDo) {
//        userDispatchDetailDo.setTableName(getTableName(tableNameNo));
//        return DBUtil.insert("userDispatchDetailMapper.insertSelective", userDispatchDetailDo) > 0;
//    }

    /**
     * 根据主键id更新
     *
     * @param userDispatchDetailDo 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(String tableNameNo, UserDispatchDetailDo userDispatchDetailDo) {
        userDispatchDetailDo.setTableName(getTableName(tableNameNo));
        return DBUtil.update("userDispatchDetailMapper.updateByPrimaryKeySelective", userDispatchDetailDo) > 0;
    }

    /**
     * 根据 批次号、手机号 更新用户明细
     *
     * @param userDispatchDetailDoList 对象
     * @return 是否更新成功标识
     */
    public boolean updateBybatchNumAndMobile(List<UserDispatchDetailDo> userDispatchDetailDoList) {
        if (CollectionUtils.isEmpty(userDispatchDetailDoList)) {
            return false;
        }
        AtomicInteger count = new AtomicInteger(0);
        try {
            return DBUtil.updateBatchWithoutTx("userDispatchDetailMapper.updateBybatchNumAndMobile", userDispatchDetailDoList) > 0;
        } catch (Exception ex) {
            log.warn("updateBybatchNumAndMobile处理异常, ex={}", ex.getMessage());
            userDispatchDetailDoList.forEach(item -> {
                try {
                    DBUtil.updateBatchWithoutTx("userDispatchDetailMapper.updateBybatchNumAndMobile", Arrays.asList(item));
                    count.getAndIncrement();
                } catch (Exception e) {
                    Tracer.logEvent("UserDispatchDetailError", String.valueOf(item.getMarketChannel()));
                    log.warn("updateBybatchNumAndMobile更新异常, ex={}, UserDispatchDetailDo:{}", e.getMessage(), JsonUtil.toJson(item));
                }
            });
        }
        return count.get() > 0;
    }

//    /**
//     * 批量保存用户下发明细
//     *
//     * @param tableNameNo 明细表序号
//     * @param list        用户下发明细
//     */
//    public void saveBatch(String tableNameNo, List<UserDispatchDetailDo> list) {
//        String tableName = getTableName(tableNameNo);
//        list.forEach(userDispatchDetailDo -> userDispatchDetailDo.setTableName(tableName));
//        DBUtil.insertBatch("userDispatchDetailMapper.insertSelective", list);
//    }

    /**
     * 批量保存用户下发明细
     * TODO 新增redis缓存,优化频控处理性能
     * @param tableNameNo 明细表序号
     * @param list        用户下发明细
     */
    public void saveBatchWithoutTx(String tableNameNo, List<UserDispatchDetailDo> list) {
        String tableName = getTableName(tableNameNo);
        list.forEach(userDispatchDetailDo -> userDispatchDetailDo.setTableName(tableName));
        DBUtil.insertBatchWithoutTx("userDispatchDetailMapper.insertSelective", list);
    }

    /**
     * 批量更新
     *
     * @param tableNameNo 明细表序号
     * @param indexList   用户下发明细
     * @return 更新成功数量
     */
    public Integer updateBatch(String tableNameNo, List<UserDispatchDetailDo> indexList) {
        for (UserDispatchDetailDo dispatchDetailDo : indexList) {
            dispatchDetailDo.setTableName(getTableName(tableNameNo));
        }
        Runnable runnable = () -> DBUtil.updateBatchWithoutTx("userDispatchDetailMapper.updateByBatchAndUserIdAndChannel", indexList);
        return TransactionUtil.transactional(runnable) ? indexList.size() : 0;
    }

    /**
     * 查询指定批次的用户下发明细
     *
     * @param tableNameNo   明细表序号
     * @param marketChannel 渠道
     * @param batchNum      批次号
     * @return 用户下发明细
     */
    public List<UserDispatchDetailDo> selectListByBatchNo(String tableNameNo, Integer marketChannel, String batchNum) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("marketChannel", marketChannel);
        params.put("batchNum", batchNum);
        return DBUtil.selectList("userDispatchDetailMapper.selectListByBatchNo", params);
    }

    /**
     * 批量获取用户流控次数指标
     *
     * @param tableNameNo 明细表序号
     * @param triple      left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIds     用户集合
     * @return 用户流控次数指标
     */
    public List<UserDispatchDetailDto> getIndexCount(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIds, List<Integer> statusList) {
        FlowCtrlDo flowCtrlDo = triple.getRight();
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("channel", triple.getMiddle());
        params.put("userIds", userIds);
        params.put("startDate", LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
        params.put("endDate", LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        params.put("statusList", statusList);
        params.put("strategyIdList", getStrategyIdList(triple.getLeft(), flowCtrlDo));
        //后面拓展，由入参传下来
        params.put("noFlcChannelList", StrategyMarketChannelEnum.getNoFlcCodes());
        return getIndex(params, flowCtrlDo.getType(), getEffectiveValue(flowCtrlDo));
    }

    /**
     * 批量获取用户流控次数指标
     *
     * @param tableNameNo 明细表序号
     * @param triple      left-策略ID Middle-渠道类型 Right-流控规则
     * @param userIds     用户集合
     * @return 用户流控次数指标
     */
    public List<UserDispatchDetailDto> getIndexCountNew(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple, List<Long> userIds, List<Integer> statusList) {
        FlowCtrlDo flowCtrlDo = triple.getRight();
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("channel", triple.getMiddle());
        params.put("userIds", userIds);
        params.put("startDate", LocalDateTime.of(LocalDate.now(), LocalTime.MIN));
        params.put("endDate", LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
        params.put("statusList", statusList);
        params.put("strategyIdList", getStrategyIdList(triple.getLeft(), flowCtrlDo));
        //后面拓展，由入参传下来
        params.put("noFlcChannelList", StrategyMarketChannelEnum.getNoFlcCodes());
        return getIndexNew(params, flowCtrlDo.getType(), getEffectiveValue(flowCtrlDo));
    }

    /**
     * 带开始结束时间参数的，灵活查询一天或者两天的触达数据
     * @param tableNameNo
     * @param triple
     * @param userIds
     * @param statusList
     * @param startDate
     * @param endDate
     * @return
     */
    public List<UserDispatchDetailDto> getIndexCountNewWithDate(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple,
                                                                List<Long> userIds,
                                                                List<Integer> statusList,
                                                                LocalDateTime startDate,
                                                                LocalDateTime endDate) {
        FlowCtrlDo flowCtrlDo = triple.getRight();
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("channel", triple.getMiddle());
        params.put("userIds", userIds);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("statusList", statusList);
        params.put("strategyIdList", getStrategyIdList(triple.getLeft(), flowCtrlDo));
//        params.put("noFlcChannelList", StrategyMarketChannelEnum.getNoFlcCodes());
        return getIndexNew(params, flowCtrlDo.getType(), getEffectiveValue(flowCtrlDo));
    }

    private List<Long> getStrategyIdList(Long strategyId, FlowCtrlDo flowCtrlDo) {
        List<Long> strategyIdList = Collections.singletonList(strategyId);
        if (FlowCtrlTypeEnum.getInstance(flowCtrlDo.getType()) == FlowCtrlTypeEnum.MULTI_STRATEGY) {
            List<String> list = Arrays.asList(flowCtrlDo.getEffectiveStrategy().split(","));
            strategyIdList = list.stream().map(Long::valueOf).collect(Collectors.toList());
        }
        return strategyIdList;
    }

    /**
     * 获取生效内容
     *
     * @param flowCtrlDo 流控规则
     * @return 生效内容
     */
    private String getEffectiveValue(FlowCtrlDo flowCtrlDo) {
        FlowCtrlTypeEnum typeEnum = FlowCtrlTypeEnum.getInstance(flowCtrlDo.getType());
        return typeEnum == FlowCtrlTypeEnum.STRATEGY ? flowCtrlDo.getEffectiveStrategy() : flowCtrlDo.getEffectiveChannel();
    }

    private List<UserDispatchDetailDto> getIndex(Map<String, Object> params, Integer flowCtrlType, String effectiveValue) {
        String sqlId = null;
        FlowCtrlTypeEnum flowCtrlTypeEnum = FlowCtrlTypeEnum.getInstance(flowCtrlType);
        if (StringUtils.equals(effectiveValue, "0")) { // 全部策略或全部渠道
            if (FlowCtrlTypeEnum.STRATEGY == flowCtrlTypeEnum) {
                sqlId = "userDispatchDetailMapper.getIndexForAllStrategy";
            } else {
                sqlId = "userDispatchDetailMapper.getIndexForAllChannel";
            }
        } else {
            if (FlowCtrlTypeEnum.STRATEGY == flowCtrlTypeEnum) {
                sqlId = "userDispatchDetailMapper.getIndexByStrategy";
            } else {
                sqlId = "userDispatchDetailMapper.getIndexByChannel";
            }
        }
        return DBUtil.selectList(sqlId, params);
    }

    private List<UserDispatchDetailDto> getIndexNew(Map<String, Object> params, Integer flowCtrlType, String effectiveValue) {
        String sqlId = null;
        FlowCtrlTypeEnum flowCtrlTypeEnum = FlowCtrlTypeEnum.getInstance(flowCtrlType);
        if (StringUtils.equals(effectiveValue, "0")) { // 全部策略或全部渠道，这两个SQL实际一样
            if (FlowCtrlTypeEnum.STRATEGY == flowCtrlTypeEnum) {
                sqlId = "userDispatchDetailMapper.getIndexForAllStrategy";
            } else {
                sqlId = "userDispatchDetailMapper.getIndexForAllChannel";
            }
        } else {
            if (FlowCtrlTypeEnum.STRATEGY == flowCtrlTypeEnum) {
                // userid, strategyid, channel
                sqlId = "userDispatchDetailMapper.getIndexByStrategyAndChannel";
            } else {
                // userid, channel
                sqlId = "userDispatchDetailMapper.getIndexByOneChannel";
            }
        }
        return DBUtil.selectList(sqlId, params);
    }

    /**
     * 统计状态用户数
     *
     * @param batchNum
     * @return
     */
    public Integer countArriveStatus(String tableNameNo, Integer status, String batchNum) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("status", status);
        params.put("batchNum", batchNum);
        return DBUtil.selectOne("userDispatchDetailMapper.countArriveStatus", params);
    }

    public Integer countArriveByStatusAndExecLogId(String tableNameNo, Integer status, Long execLogId) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("execLogId", execLogId);
        if (Objects.nonNull(status)) {
            params.put("status", status);
        }
        return DBUtil.selectOne("userDispatchDetailMapper.countArriveByStatusAndExecLogId", params);
    }

    /**
     * 统计使用用户数
     *
     * @param batchNum
     * @return
     */
    public Integer countUsedCount(String tableNameNo, Integer status, String batchNum) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("usedStatus", status);
        params.put("batchNum", batchNum);
        return DBUtil.selectOne("userDispatchDetailMapper.countUsedCount", params);
    }

    public Integer countUsedCountByExecLogId(String tableNameNo, Integer status, Long execLogId) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("usedStatus", status);
        params.put("execLogId", execLogId);
        return DBUtil.selectOne("userDispatchDetailMapper.countUsedCountByExecLogId", params);
    }


    public int getCount(String tableNameNo, Long strategyId, LocalDateTime startTime, LocalDateTime endTime, Integer status) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("strategyId", strategyId);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        if (Objects.nonNull(status)) {
            params.put("status", status);
        }
        return DBUtil.selectOne("userDispatchDetailMapper.countByStrategyIdAndDispatchTimeOrStatus", params);
    }

    /**
     * 根据表序号生成表名
     *
     * @param tableNameNo 表序号
     * @return 表名
     */
    private String getTableName(String tableNameNo) {
        return "user_dispatch_detail_" + tableNameNo;
    }

    private String genRedisKey(Long strategyId, LocalDateTime startTime, Integer status) {
        String date = startTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String key = String.format(RedisKeyConstants.STRATEGY_TODAY_DISPATCH_COUNT, strategyId, date);
        if (Objects.nonNull(status)) {
            key = key.concat(":").concat(String.valueOf(status));
        }
        return key;
    }

    public List<UserDispatchDetailDo> selectTodayDispatchStrategy(String tableNameNo, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return DBUtil.selectList("userDispatchDetailMapper.selectTodayDispatchStrategy", params);
    }

    //    public List<UserDispatchGroupNum> countStrategyGroupDispatchUserNum(String tableNameNo, Long strategyId, String startDate, String endDate) {
//        Map<String, Object> params = new HashMap<>();
//        params.put("tableName", getTableName(tableNameNo));
//        params.put("strategyId", strategyId);
//        params.put("startDate", startDate);
//        params.put("endDate", endDate);
//        return DBUtil.selectList("userDispatchDetailMapper.countStrategyGroupDispatchUserNum", params);
//    }
    public Integer countStrategyGroupDispatchUserNum(String tableNameNo, Long strategyId, List<Long> strategyChannelIds, String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("strategyId", strategyId);
        params.put("strategyChannelIds", strategyChannelIds);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return DBUtil.selectOne("userDispatchDetailMapper.countStrategyGroupDispatchUserNum", params);
    }

    public int countDispatchUserNum(String tableNo, Long strategyId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNo));
        params.put("strategyId", strategyId);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return DBUtil.selectOne("userDispatchDetailMapper.countDispatchUserNum", params);
    }

    public List<UserDispatchDetailDo> selectFailDispatchForUpdate(String tableNo, Integer overTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNo));
        params.put("overTime", LocalDateTime.now().plusHours(-overTime));
        return DBUtil.selectList("userDispatchDetailMapper.selectFailDispatchForUpdate", params);
    }

    public int dispatchFailUserUpdate(String tableNo, List<UserDispatchDetailDo> userDispatchDetailDoList) {
        for (UserDispatchDetailDo userDispatchDetailDo : userDispatchDetailDoList) {
            userDispatchDetailDo.setTableName(getTableName(tableNo));
        }
        return DBUtil.updateBatchWithoutTx("userDispatchDetailMapper.dispatchFailUserUpdate", userDispatchDetailDoList);
    }

    public int batchUpdateDispatchFail(String tableNo, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return 0;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNo));
        params.put("idList", idList);
        return DBUtil.update("userDispatchDetailMapper.batchUpdateDispatchFail", params);
    }

    public int batchUpdateDispatchFailFromInit(String tableNo, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return 0;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNo));
        params.put("idList", idList);
        return DBUtil.update("userDispatchDetailMapper.batchUpdateDispatchFailFromInit", params);
    }

    public int batchUpdateDispatchSucceedFromInit(String tableNo, List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return 0;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNo));
        params.put("idList", idList);
        return DBUtil.update("userDispatchDetailMapper.batchUpdateDispatchSucceedFromInit", params);
    }

    public int getIndexFromLocal(String tableNo, Long userId, Long strategyId, List<Integer> marketChannelList, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNo));
        params.put("userId", userId);
        params.put("strategyId", strategyId);
        params.put("marketChannelList", marketChannelList);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return DBUtil.selectOne("userDispatchDetailMapper.getIndexFromLocal", params);
    }

    // TODO 优化SQL or 切换为从库
    public Integer countByDetail(String tableNameNo, Long strategyDoId, Long strategyGroupId, String groupId, Integer marketChannel, String templateId, List<Integer> statusList, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("strategyDoId", strategyDoId);
        params.put("strategyGroupId", strategyGroupId);
        if (StringUtils.isNotBlank(groupId)){
            params.put("groupId", groupId);
        }
        params.put("marketChannel", marketChannel);
        params.put("templateId", templateId);
        if (!CollectionUtils.isEmpty(statusList)) {
            params.put("statusList", statusList);
        }
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return DBUtil.selectOne("userDispatchDetailMapper.countByDetail", params);
    }

    public boolean updateByBatchNumAndUserNo(List<UserDispatchDetailDo> userDispatchDetailDoList) {
        if (CollectionUtils.isEmpty(userDispatchDetailDoList)) {
            return false;
        }

        AtomicInteger count = new AtomicInteger(0);
        try {
            return DBUtil.updateBatchWithoutTx("userDispatchDetailMapper.updateByBatchNumAndUserNo", userDispatchDetailDoList) > 0;
        } catch (Exception ex) {
            log.warn("updateByBatchNumAndUserNo处理异常, ex={}", ex.getMessage());
            userDispatchDetailDoList.forEach(item -> {
                try {
                    DBUtil.updateBatchWithoutTx("userDispatchDetailMapper.updateByBatchNumAndUserNo", Arrays.asList(item));
                    count.getAndIncrement();
                } catch (Exception e) {
                    Tracer.logEvent("UserDispatchDetailError", String.valueOf(item.getMarketChannel()));
                    log.warn("updateByBatchNumAndUserNo更新异常, ex={}, UserDispatchDetailDo:{}", e.getMessage(), JsonUtil.toJson(item));
                }
            });
        }
        return count.get() > 0;
    }

    public LocalDateTime getMaxDispatchTime(String tableNameNo, Long appUserId, List<Long> strategyIdList, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("appUserId", appUserId);
        if (!CollectionUtils.isEmpty(strategyIdList)) {
            params.put("strategyIdList", strategyIdList);
        }
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        return DBUtil.selectOne("userDispatchDetailMapper.getMaxDispatchTime", params);
    }

    public UserDispatchDetailDo selectByBatchNoAndUserId(String tableNo, String batchNum, String userId) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNo));
        params.put("appUserId", userId);
        params.put("batchNum", batchNum);
        List<UserDispatchDetailDo> userDispatchDetailDoList = DBUtil.selectList("userDispatchDetailMapper.selectByBatchNoAndUserId", params);
        if (!CollectionUtils.isEmpty(userDispatchDetailDoList) && userDispatchDetailDoList.size()==1){
            return userDispatchDetailDoList.get(0);
        }
        return null;
    }
}
