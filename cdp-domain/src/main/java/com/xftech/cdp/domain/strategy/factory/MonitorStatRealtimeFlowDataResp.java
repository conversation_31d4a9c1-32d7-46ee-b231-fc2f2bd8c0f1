package com.xftech.cdp.domain.strategy.factory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @<NAME_EMAIL>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MonitorStatRealtimeFlowDataResp extends AbsMonitorListResp {
    @ApiModelProperty(value = "日期")
    @ExcelProperty(index = 0, value = "日期")
    private String bizDate;

    @ApiModelProperty(value = "触发事件总次数")
    @ExcelProperty(index = 1, value = "触发事件总次数")
    private Integer eventSum;

    @ApiModelProperty(value = "触发事件总用户数")
    @ExcelProperty(index = 2, value = "触发事件总用户数")
    private Integer userSum;

    @ApiModelProperty(value = "事件条件过滤用户数")
    @ExcelProperty(index = 3, value = "事件条件过滤用户数")
    private Integer filterEventNum;

    @ApiModelProperty(value = "注册时间过滤用户数")
    @ExcelProperty(index = 4, value = "注册时间过滤用户数")
    private String filterRegTimNum;

    @ApiModelProperty(value = "离线人群过滤用户数")
    @ExcelProperty(index = 5, value = "离线人群过滤用户数")
    private String filterCrowdNum;

    @ApiModelProperty(value = "实时标签过滤用户数")
    @ExcelProperty(index = 6, value = "实时标签过滤用户数")
    private Integer filterLabelNum;

    @ApiModelProperty(value = "实时排除项过滤用户数")
    @ExcelProperty(index = 7, value = "实时排除项过滤用户数")
    private Integer filterExcludeNum;

    @ApiModelProperty(value = "复筛通过用户数")
    @ExcelProperty(index = 8, value = "复筛通过用户数")
    private Integer passNum;

    @ApiModelProperty(value = "分组")
    @ExcelProperty(index = 9, value = "分组")
    private String groupName;

    @ApiModelProperty(value = "流控过滤用户数")
    @ExcelProperty(index = 10, value = "流控过滤用户数")
    private Integer flowControlNum;

    @ApiModelProperty(value = "总计应发用户数")
    @ExcelProperty(index = 11, value = "总计应发用户数")
    private Integer dispatchNum;

}
