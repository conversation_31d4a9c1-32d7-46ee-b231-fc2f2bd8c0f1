package com.xftech.cdp.domain.dict.fetch;


import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import com.xftech.cdp.domain.flowctrl.model.enums.ChannelTypeEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class FlowCtrlChannelFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<ChannelTypeEnum> channelTypeEnumList = Arrays.stream(ChannelTypeEnum.values()).collect(Collectors.toList());
        for (ChannelTypeEnum channelTypeEnum : channelTypeEnumList) {
            result.add(Dict.builder().dictCode(String.valueOf(channelTypeEnum.getType())).dictValue(channelTypeEnum.getDesc()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.FLOW_CTRL_CHANNEL_TYPE;
    }

    @Override
    public String getDescription() {
        return "流控渠道类型";
    }
}
