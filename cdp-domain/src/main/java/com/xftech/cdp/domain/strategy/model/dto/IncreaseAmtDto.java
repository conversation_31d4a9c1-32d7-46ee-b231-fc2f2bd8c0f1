/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ IncreaseAmtDto, v 0.1 2024/3/12 17:51 yye.xu Exp $
 */

@Data
public class IncreaseAmtDto {
    // 提额配置
    private IncreaseAmtConfig increaseAmtConfig;
    @Data
    public static class IncreaseAmtConfig {
        // 提额类型
        private String increaseType;
        // 单位元，提额金额
        private BigDecimal increaseAmt;
        // 提额时间
        private IncreaseTimeConfig increaseTimeConfig;
        @Data
        public static class IncreaseTimeConfig {
            // 时间类型：固定时间 和 动态时间
            private String type;
            private String value;

            public Object convertToValueDto() {
                if (Objects.equals(type, IncreaseTimeType.FIXED_TIME)) {
                    return JsonUtil.parse(value, FixedTimeDto.class);
                } else if (Objects.equals(type, IncreaseTimeType.DYNAMIC_TIME)) {
                    return JsonUtil.parse(value, DynamicValueDto.class);
                }
                return null;
            }
        }

        @JSONField(serialize = false)
        public IncreaseAmtParamDto getParamDto(){
            IncreaseAmtParamDto increaseAmtParamDto = new IncreaseAmtParamDto();
            increaseAmtParamDto.setAmount(increaseAmt.multiply(new BigDecimal("100")).intValue());
            
            if (Objects.equals(increaseTimeConfig.getType(), IncreaseTimeType.FIXED_TIME)) {
                FixedTimeDto fixedTimeDto = (FixedTimeDto)increaseTimeConfig.convertToValueDto();
                increaseAmtParamDto.setStartTime(DateFormatUtils.format(fixedTimeDto.getStart(), "yyyy-MM-dd 00:00:00"));
                increaseAmtParamDto.setEndTime(DateFormatUtils.format(fixedTimeDto.getEnd(), "yyyy-MM-dd 00:00:00"));
            }else if (Objects.equals(increaseTimeConfig.getType(), IncreaseTimeType.DYNAMIC_TIME)){
                DynamicValueDto dynamicValueDto = (DynamicValueDto)increaseTimeConfig.convertToValueDto();
                if(Objects.equals(IncreaseTimeValueType.IMMEDIATELY, dynamicValueDto.getStart().getType())) {
                    increaseAmtParamDto.setStartTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
                    increaseAmtParamDto.setEndTime(DateFormatUtils.format(DateUtil.addDays(new Date(), dynamicValueDto.getEnd().getDelayDay() + 1),
                            "yyyy-MM-dd 00:00:00"));
                }else if (Objects.equals(IncreaseTimeValueType.DELAY, dynamicValueDto.getStart().getType())) {
                    increaseAmtParamDto.setStartTime(DateFormatUtils.format(DateUtil.addDays(new Date(), dynamicValueDto.getStart().getDelayDay()), "yyyy-MM-dd 00:00:00"));
                    increaseAmtParamDto.setEndTime(DateFormatUtils.format(DateUtil.addDays(new Date(), dynamicValueDto.getStart().getDelayDay() + dynamicValueDto.getEnd().getDelayDay() + 1),
                            "yyyy-MM-dd 00:00:00"));
                }
            }
            return increaseAmtParamDto;
        }
    }

    @Data
    public static class FixedTimeDto {
        private Date start;
        private Date end;

        @JsonIgnore
        @JSONField(serialize = false)
        public boolean isValid(){
            return start != null && end != null;
        }
    }

    @Data
    public static class DynamicValueDto {
        private StartTimeConfigDto start;
        private EndTimeConfigDelayDto end;

        @JsonIgnore
        @JSONField(serialize = false)
        public boolean isValid(){
            if (start == null || end == null){
                return false;
            }
            return start.isValid() && end.isValid();
        }
    }

    @Data
    public static class StartTimeConfigDto {
        private String type;
        private Integer delayDay;

        public boolean isValid(){
            if (IncreaseTimeValueType.DELAY
                    .equals(type)){
                if (delayDay < 0){
                    return false;
                }
            }else if (IncreaseTimeValueType.IMMEDIATELY
                    .equals(type)){
            }
            return true;
        }
    }

    @Data
    public static class EndTimeConfigDelayDto {
        private Integer delayDay;
        public boolean isValid(){
            if (delayDay < 0){
                return false;
            }
            return true;
        }
    }

    public static class IncreaseType {
        // 临时额度-新客
        public static final String TEMP_CREDIT = "TEMP_CREDIT";
        // 固定额度
        public static final String FIXED_CREDIT = "FIXED_CREDIT";
        // api首登调额
        public static final String PERSONAL_API_FST_LOGIN_TEMP = "PERSONAL_API_FST_LOGIN_TEMP";
        // 向上补整调额
        public static final String LOAN_UPTO_FULLAMT = "LOAN_UPTO_FULLAMT";
        // 营销临额-老客
        public static final String PERSONAL_MARKETING_RELOAN_TEMP = "PERSONAL_MARKETING_RELOAN_TEMP";

        public static final List<String> increaseTypes = Arrays.asList(TEMP_CREDIT, FIXED_CREDIT, PERSONAL_API_FST_LOGIN_TEMP, LOAN_UPTO_FULLAMT, PERSONAL_MARKETING_RELOAN_TEMP);
    }

    public static class IncreaseTimeType {
        // 立刻
        public static final String FIXED_TIME = "FIXED_TIME";
        // 延迟
        public static final String DYNAMIC_TIME = "DYNAMIC_TIME";

        public static final List<String> increaseTimeTypes
                = Arrays.asList(FIXED_TIME, DYNAMIC_TIME);
    }

    public static class IncreaseTimeValueType {
        // 立刻
        public static final String IMMEDIATELY = "IMMEDIATELY";
        // 延迟
        public static final String DELAY = "DELAY";

        public static final List<String> increaseTimeValueTypes
                = Arrays.asList(IMMEDIATELY, DELAY);
    }
}