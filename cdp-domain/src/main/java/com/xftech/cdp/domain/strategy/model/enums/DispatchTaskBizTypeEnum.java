package com.xftech.cdp.domain.strategy.model.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public enum DispatchTaskBizTypeEnum {

    STRATEGY(1, "离线周期策略"),
    // 离线引擎
    STRATEGY_OFF_ENGINE(2, "离线引擎策略"),
    FLOW(100, "画布"),
    OFFLINE_STRATEGY(101, "普通离线策略"), //DB中的存储类型是tinyint，不能超过127

    ;
    private final int code;
    private final String description;

    DispatchTaskBizTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static DispatchTaskBizTypeEnum getByCode(int code) {
        return Arrays.stream(values()).filter(x -> Objects.equals(code, x.code))
                .findFirst().orElse(null);
    }

    public static boolean isFlow(Byte code){
        return Objects.equals(FLOW.getCode(), code.intValue());
    }

    public static boolean isOffEngine(Byte code) {
        return Objects.equals(STRATEGY_OFF_ENGINE.getCode(), code.intValue());
    }
}
