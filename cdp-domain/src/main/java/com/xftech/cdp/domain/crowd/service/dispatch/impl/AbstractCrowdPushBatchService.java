package com.xftech.cdp.domain.crowd.service.dispatch.impl;

import com.xftech.cdp.infra.repository.Bo;
import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2023-03-11
 */
@Slf4j
public abstract class AbstractCrowdPushBatchService {

    protected AbstractCrowdPushBatchService() {
    }

    /**
     * 分页操作（正常分页）
     *
     * @param invokePage 分页参数
     * @param supplier   分页查询
     * @param consumer   按页消费
     */
    protected <S extends Bo> void invokePagination(InvokePage invokePage, Supplier<List<S>> supplier, Consumer<List<S>> consumer) {
        while (true) {
            List<S> bos = supplier.get();
            if (CollectionUtils.isEmpty(bos)) {
                break;
            }
            consumer.accept(bos);
            if (bos.size() < invokePage.getPageSize()) {
                break;
            }
            invokePage.setPage(++invokePage.page);
        }
    }

    /**
     * 分页操作（根据ID分页）
     *
     * @param invokePageById 分页参数
     * @param supplier       分页查询
     * @param consumer       按页消费
     */
    protected <S extends Do> void invokePaginationById(InvokePageById invokePageById, Supplier<List<S>> supplier, Consumer<List<S>> consumer) {
        while (true) {
            List<S> bos = supplier.get();
            if (CollectionUtils.isEmpty(bos)) {
                break;
            }
            consumer.accept(bos);
            if (bos.size() < invokePageById.getPageSize()) {
                break;
            }
            invokePageById.setId(bos.stream().max(Comparator.comparing(S::getId)).map(S::getId).orElse(0L));
        }
    }

    @Data
    @NoArgsConstructor
    public static class InvokePage {

        private long page = 1;

        private long pageSize = 1000;

        public InvokePage(long pageSize) {
            this.pageSize = pageSize;
        }
    }

    @Data
    @NoArgsConstructor
    public static class InvokePageById {

        private long id = 0;

        private long pageSize = 1000;

        public InvokePageById(long pageSize) {
            this.pageSize = pageSize;
        }
    }
}
