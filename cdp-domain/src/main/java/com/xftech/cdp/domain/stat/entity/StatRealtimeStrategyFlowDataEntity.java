package com.xftech.cdp.domain.stat.entity;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 实时策略流控数据统计表
 */
@Data
public class StatRealtimeStrategyFlowDataEntity {

    /**
     * 自增id
     */
    private Long id;
    /**
     * 统计日期
     */
    private LocalDate bizDate;
    /**
     * 策略ID
     */
    private Long strategyId;
    /**
     * 事件总数量
     */
    private Integer eventSum;
    /**
     * 事件用户总数量
     */
    private Integer userSum;
    /**
     * 事件条件过滤用户数
     */
    private Integer filterEventNum;
    /**
     * 注册时间过滤用户数
     */
    private Integer filterRegTimNum;
    /**
     * 离线人群过滤用户数
     */
    private Integer filterCrowdNum;
    /**
     * 实时标签过滤用户数
     */
    private Integer filterLabelNum;
    /**
     * 实时排除项过滤用户数
     */
    private Integer filterExcludeNum;
    /**
     * 复筛通过用户数
     */
    private Integer passNum;
    /**
     * 流控用户数
     */
    private Integer flowControlNum;
    /**
     * 应发用户数
     */
    private Integer dispatchNum;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
