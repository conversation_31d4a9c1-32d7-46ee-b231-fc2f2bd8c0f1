package com.xftech.cdp.domain.cache.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.cache.CacheCrowdExecLogService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class CacheCacheCrowdExecLogServiceImpl implements CacheCrowdExecLogService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;

    @Override
    public List<Map<String, Object>> selectCrowdMaxExecLogIds(List<Long> crowdIds) {
        List<Map<String, Object>> maxExecLogIdMapList = new ArrayList<>();
        crowdIds.forEach(crowdId -> {
            Map<String, Object> execLogIdMap = redisUtils.get(RedisKeyUtils.genMaxCrowdExecLogIdKey(crowdId), Map.class);
            if (execLogIdMap == null) {
                execLogIdMap = crowdExecLogRepository.selectCrowdMaxExecLogIdMap(crowdId);
                redisUtils.set(RedisKeyUtils.genMaxCrowdExecLogIdKey(crowdId), execLogIdMap);
            }
            if (execLogIdMap != null) {
                maxExecLogIdMapList.add(execLogIdMap);
            }
        });
        return maxExecLogIdMapList;
    }

    @Override
    public boolean updateById(CrowdExecLogDo crowdExecLog) {
        boolean isUpdate = crowdExecLogRepository.updateById(crowdExecLog);
        if(isUpdate) {
            Map<String, Object> maxExecLogIdMap = crowdExecLogRepository.selectCrowdMaxExecLogIdMap(crowdExecLog.getCrowdId());
            redisUtils.set(RedisKeyUtils.genMaxCrowdExecLogIdKey(crowdExecLog.getCrowdId()), maxExecLogIdMap);
        }
        return isUpdate;
    }

    @Override
    public void saveBatchCrowdExeLog(List<CrowdPackDo> res, LocalDateTime finishExecTime) {
        for (CrowdPackDo re : res) {
            CrowdExecLogDo crowdExecLogDo = new CrowdExecLogDo();
            crowdExecLogDo.setCrowdId(re.getId());
            crowdExecLogDo.execStatus(CrowdStatusEnum.getInstance(re.getStatus()));
            crowdExecLogDo.setExecType(1);
            LocalDateTime execTime = re.getLatestRefreshTime() == null ? LocalDateTimeUtil.beginOfDay(LocalDateTime.now()) : re.getLatestRefreshTime();
            crowdExecLogDo.setExecTime(execTime);
            crowdExecLogDo.setFinishExecTime(finishExecTime);
            crowdExecLogDo.setCrowdPersonNum(re.getCrowdPersonNum());
            if (crowdExecLogRepository.existLogByCrowdAndExecTime(crowdExecLogDo)) {
                crowdExecLogRepository.updateByCrowdAndExecTime(crowdExecLogDo);
            } else {
                crowdExecLogRepository.insert(crowdExecLogDo);
            }
        }
    }

    @Override
    public List<Map<String, Object>> selectCrowdMaxExecLogTime(List<Long> crowdIds) {
        List<Map<String, Object>> maxExecLogIdMapList = new ArrayList<>();
        crowdIds.forEach(crowdId -> {
            Map<String, Object> execLogIdMap = redisUtils.get(RedisKeyUtils.genMaxCrowdExecTimeKey(crowdId), Map.class);
            if (execLogIdMap == null) {
                execLogIdMap = crowdExecLogRepository.selectCrowdMaxExecLogTimeMap(crowdId);
                if(Objects.isNull(execLogIdMap) || execLogIdMap.isEmpty()){
                    execLogIdMap = new HashMap<>();
                    execLogIdMap.put("crowd_id",crowdId);
                }
                redisUtils.set(RedisKeyUtils.genMaxCrowdExecTimeKey(crowdId), execLogIdMap, 60 * 10);
            }
            maxExecLogIdMapList.add(execLogIdMap);
        });
        return maxExecLogIdMapList;
    }
}
