package com.xftech.cdp.domain.ads.service.impl;

import brave.Tracing;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.TimeInterval;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelQueryService;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity;
import com.xftech.cdp.domain.stat.repository.OfflineDecisionRecordDao;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.service.StrategyMarketEventConditionService;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.rabbitmq.vo.HitResult;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import com.xftech.cdp.infra.utils.AviatorUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/20 9:24
 */
@Slf4j
@Service
public class AdsStrategyLabelQueryServiceImpl implements AdsStrategyLabelQueryService {

    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private OfflineDecisionRecordDao offlineDecisionRecordDao;
    @Autowired
    private AdsStrategyLabelService adsStrategyLabelService;
    @Autowired
    private StrategyMarketEventConditionService strategyMarketEventConditionService;

    /**
     * 标签查询
     *
     * @param strategyId      策略ID
     * @param marketChannelId 渠道ID
     * @param marketChannel   渠道类型
     * @param app             app
     * @param list            当前批人群明细
     * @return 当前批人群明细
     */
    @Override
    public List<CrowdDetailDo> queryLabelHandler(Long strategyId, Long marketChannelId, Integer marketChannel, String app, List<CrowdDetailDo> list) {
        final TimeInterval timer = new TimeInterval();
        try {
            if (strategyConfig.getLabelQuerySwitch() != 1) {
                log.warn("离线策略标签查询未开启...");
                return list;
            }

            int totalCount = list.size();
            // 查询策略配置的实时标签相关配置
            Map<String, List<StrategyMarketEventConditionDo>> eventConditionMap = strategyMarketEventConditionService.getStringToListByStrategyId(strategyId);
            if (CollectionUtils.isEmpty(eventConditionMap) || CollectionUtils.isEmpty(list)) {
                log.warn("不存在需要查询的标签或人群明细为空");
                return list;
            }
            log.info("处理eventConditionMap:{}",JsonUtil.toJson(eventConditionMap));
            // 截止本次营销前
            String startTime = this.getStartTime(eventConditionMap.values().stream().flatMap(List::stream).filter(item -> Objects.nonNull(item.getTimeType())).findFirst().orElse(null));
            log.info("处理startTime:{}",startTime);
            // 请求数仓
            BatchAdsLabelVO adsLabelVO = new BatchAdsLabelVO();
            adsLabelVO.setStrategyId(strategyId);
            adsLabelVO.setApp(app);
            adsLabelVO.setStartTime(startTime);
            adsLabelVO.setUserInfoList(list.stream().map(item -> {
                BatchAdsLabelVO.UserInfo userInfo = new BatchAdsLabelVO.UserInfo();
                userInfo.setMobile(item.getMobile());
                userInfo.setAppUserId(item.getUserId());
                userInfo.setRegisterTime(item.getRegisterTime());
                userInfo.setFlowBatchNo(item.getFlowBatchNo());
                return userInfo;
            }).collect(Collectors.toList()));
            log.info("查标签adsLabelVO:{}", JsonUtil.toJson(adsLabelVO));
            Map<Long, Map<String, Object>> labelValueMap = adsStrategyLabelService.queryBatch(adsLabelVO, eventConditionMap.keySet(), StrategyInstantLabelTypeEnum.LABEL, StrategyTypeEnum.OFFLINE_STRATEGY);
            // 根据标签类型分组
            Map<Integer, List<StrategyMarketEventConditionDo>> optionalMap = eventConditionMap.values().stream().flatMap(List::stream).collect(Collectors.groupingBy(item -> item.getOptional() == 1 ? 1 : 2));
            List<OfflineDecisionRecordEntity> recordList = new ArrayList<>();
            String tableNameNo = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMM");
            // 依次检查 实时标签、排序项标签
            for (Map.Entry<Integer, List<StrategyMarketEventConditionDo>> entry : new TreeMap<>(optionalMap).entrySet()) {
                Integer type = entry.getKey();
                list = this.labelCheck(list, entry.getValue(), labelValueMap, type, recordList);
                log.info("未通过{}标签判断的总人数：{}", type == 1 ? "实时" : "排除", totalCount - list.size());
            }
            if (!CollectionUtils.isEmpty(recordList)) {
                recordList.forEach(item -> {
                    item.setStrategyId(strategyId);
                    item.setMarketChannel(marketChannel);
                    item.setMarketChannelId(marketChannelId);
                    item.setTableNameNo(tableNameNo);
                });
                offlineDecisionRecordDao.saveOfflineDecisionRecord(recordList);
            }
            log.info("实时标签/排除标签后，当前批次人数：{}，耗时：{}ms", list.size(), timer.interval());
            return list;
        } catch (Exception e) {
            log.warn("离线策略标签查询异常，策略ID：{}，耗时：{}ms", strategyId, timer.interval(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 截止本次营销前
     *
     * @param eventCondition 标签配置
     * @return startTime，格式：yyyy-MM-dd HH:mm:ss
     */
    private String getStartTime(StrategyMarketEventConditionDo eventCondition) {
        LocalDateTime startTime = LocalDateTime.now();
        if (Objects.nonNull(eventCondition)) {
            switch (Optional.ofNullable(eventCondition.getTimeType()).orElse(3)) {
                case 1:
                    startTime = startTime.plusMinutes(-eventCondition.getTimeValue());
                    break;
                case 2:
                    startTime = startTime.plusHours(-eventCondition.getTimeValue());
                    break;
                default:
                    startTime = LocalDate.now().atStartOfDay();
            }
        }
        return LocalDateTimeUtil.format(startTime, TimeFormat.DATE_TIME);
    }

    /**
     * 标签判断
     *
     * @param crowdDetailList 用户明细集合
     * @param conditionList   标签集合
     * @param labelValueMap   标签值
     * @param type            标签类型
     */
    private List<CrowdDetailDo> labelCheck(List<CrowdDetailDo> crowdDetailList, List<StrategyMarketEventConditionDo> conditionList,
                                           Map<Long, Map<String, Object>> labelValueMap, Integer type, List<OfflineDecisionRecordEntity> recordVoList) {
        if (CollectionUtils.isEmpty(conditionList)) {
            log.warn("标签判断，不存在该类型标签。标签类型：{}", type == 1 ? "实时" : "排除");
            return crowdDetailList;
        }

        // 拼装表达式
        String expression = conditionList.stream().map(StrategyMarketEventConditionDo::getExpression).collect(Collectors.joining(" && "));

        // 表达式判断，返回值校验通过的用户
        return crowdDetailList.stream().filter(item -> {
            Boolean hit = AviatorUtil.compute(expression, labelValueMap.get(item.getUserId()));

            OfflineDecisionRecordEntity recordVo = contain(recordVoList, item.getUserId());
            List<HitResult> hitResultList = null;
            if (recordVo == null) {
                recordVo = new OfflineDecisionRecordEntity();
                recordVo.setApp(item.getApp());
                recordVo.setInnerApp(item.getInnerApp());
                recordVo.setMobile(item.getMobile());
                recordVo.setAppUserId(item.getUserId());
                String traceId = "";
                if (Tracing.current().currentTraceContext() != null && Tracing.current().currentTraceContext().get() != null) {
                    traceId = Tracing.current().currentTraceContext().get().traceIdString();
                }
                recordVo.setTraceId(traceId);
                hitResultList = new ArrayList<>();
                recordVo.setHitResultList(hitResultList);
                recordVoList.add(recordVo);
            } else {
                hitResultList = recordVo.getHitResultList();
            }
            HitResult hitResult = new HitResult();
            hitResult.setExpression(expression);
            hitResult.setExpParam(labelValueMap.get(item.getUserId()).toString());
            hitResult.setExecuteTime(LocalDateTime.now());
            hitResult.setHit(hit);
            recordVo.setDecisionResult(hit ? 1 : 0);
            hitResultList.add(hitResult);
            recordVo.setFailCode(DecisionResultEnum.NONE.getFailCode());
            if (!hit) {
                if (type == 1) {
                    recordVo.setFailCode(DecisionResultEnum.INSTANT_LABEL_FILTER_FAIL.getFailCode());
                    recordVo.setFailReason(DecisionResultEnum.INSTANT_LABEL_FILTER_FAIL.getFailReason());
                } else {
                    recordVo.setFailCode(DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL.getFailCode());
                    recordVo.setFailReason(DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL.getFailReason());
                }
            }
            return hit;
        }).collect(Collectors.toList());
    }

    private OfflineDecisionRecordEntity contain(List<OfflineDecisionRecordEntity> recordVoList, Long appUserId) {
        for (OfflineDecisionRecordEntity vo : recordVoList) {
            if (vo.getAppUserId().equals(appUserId)) {
                return vo;
            }
        }
        return null;
    }
}
