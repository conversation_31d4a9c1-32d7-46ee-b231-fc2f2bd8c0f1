package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelEnum;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/18
 */
@Component
public class CrowdLabelDictFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<CrowdLabelEnum> enums = Arrays.stream(CrowdLabelEnum.values()).collect(Collectors.toList());
        for (CrowdLabelEnum value : enums) {
            result.add(Dict.builder().dictCode(String.valueOf(value.getCode())).dictValue(value.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.CROWD_LABEL_GROUP_TYPE;
    }

    @Override
    public String getDescription() {
        return "人群圈选";
    }
}
