package com.xftech.cdp.domain.strategy.service.impl;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyInstantLabelRepository;
import com.xftech.cdp.domain.strategy.service.StrategyInstantLabelService;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @<NAME_EMAIL>
 */
@Service
public class StrategyInstantLabelServiceImpl implements StrategyInstantLabelService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyInstantLabelRepository strategyInstantLabelRepository;

    @Override
    public StrategyInstantLabelDo getByLabelNameAndLabelType(String labelName, StrategyInstantLabelTypeEnum queryType, Integer strategyType, Integer optional) {
        String key;
        if (StrategyInstantLabelTypeEnum.LABEL.getType().equals(queryType.getType())) {
            key = String.format(RedisKeyConstants.STRATEGY_INSTANT_LABEL_STRATEGY, strategyType, labelName, optional);
        } else {
            key = String.format(RedisKeyConstants.STRATEGY_INSTANT_LABEL_MSG, labelName);
        }

        if (redisUtils.hasKey(key)) {
            String instantLabelJson = redisUtils.get(key);
            if (StringUtils.isNotBlank(instantLabelJson)) {
                return JSON.parseObject(instantLabelJson, StrategyInstantLabelDo.class);
            }
        }

        StrategyInstantLabelDo instantLabelDo = strategyInstantLabelRepository.getByLabelNameAndLabelType(labelName, queryType.getType(), strategyType, optional);
        redisUtils.set(key, instantLabelDo, RedisUtils.DEFAULT_EXPIRE_HOUR);
        return instantLabelDo;
    }
}
