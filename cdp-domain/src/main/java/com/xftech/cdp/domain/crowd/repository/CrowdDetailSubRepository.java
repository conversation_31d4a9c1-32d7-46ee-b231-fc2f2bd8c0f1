package com.xftech.cdp.domain.crowd.repository;

import cn.hutool.core.bean.BeanUtil;
import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.constant.TransConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * crowd_detail 分表 dao
 * <AUTHOR>
 * @since 2023-03-27
 */

@Component
public class CrowdDetailSubRepository {
//    /**
//     * 批量保存人群用户明细到对应分表
//     * @param tableNo         表序号
//     * @param crowdDetailDoList 人群用户明细对象列表
//     */
//    public void saveBatch(int tableNo, List<CrowdDetailDo> crowdDetailDoList) {
//        Map<String, Object> param = new HashMap<>();
//        param.put("tableName", TransConstants.crowdDetailTableName(tableNo));
//        param.put("crowdDetailList", crowdDetailDoList);
//        DBUtil.insert("crowdDetailSub.insertBatch", param);
//    }

    public void batchSaveCrowdDetailSub(int tableNo, List<CrowdDetailDo> crowdDetailDoList) {
        for (CrowdDetailDo item : crowdDetailDoList) {
            item.setTableName(TransConstants.crowdDetailTableName(tableNo));
        }
        DBUtil.insertBatch("crowdDetailSub.saveCrowdDetailSub", crowdDetailDoList);
    }

    /**
     * 查询分表最新一条记录
     * @param tableNo
     * @return
     */
    public CrowdDetailDo selectTableLastRecord(int tableNo) {
        return DBUtil.selectOne("crowdDetailSub.selectTableLastRecord", TransConstants.crowdDetailTableName(tableNo));
    }

    /**
     * 根据分表名统计记录数
     * @param tableNo 表序号
     * @return 该表下的总记录数
     */
    public Integer countCrowdDetail(int tableNo) {
        return DBUtil.selectOne("crowdDetailSub.countCrowdDetail", TransConstants.crowdDetailTableName(tableNo));
    }

    /**
     * 清空 crowd_detail 分表
     * @param tableNo
     * @return
     */
    public boolean truncateTable(int tableNo) {
        return DBUtil.update("crowdDetailSub.truncateTable", TransConstants.crowdDetailTableName(tableNo)) > 0;
    }
}
