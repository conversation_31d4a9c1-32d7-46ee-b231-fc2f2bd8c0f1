package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;

import java.util.List;

public interface CacheStrategyMarketEventService {

//    /**
//     * 根据主键id查询
//     *
//     * @param id id
//     * @return id对应的记录
//     */
//    StrategyMarketEventDo selectById(Long id);

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    boolean insert(StrategyMarketEventDo param);

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    boolean updateById(StrategyMarketEventDo param);

    /**
     * 根据事件类型查询对接记录
     *
     * @param bizEventType 事件类型
     * @return 策略事件
     */
    List<StrategyMarketEventDo> getByEventName(String bizEventType);

    List<StrategyMarketEventDo> getByStrategyId(Long strategyId);

//    /**
//     * 根据策略id查询
//     *
//     * @param strategyId 策略id
//     * @return strategyId对应的记录
//     */
//    StrategyMarketEventDo selectByStrategyId(Long strategyId);

    void refreshStrategyMarketEventCache(String eventName);

    void refreshStrategyMarketEventCache(String eventName, List<StrategyMarketEventDo> strategyMarketEventDoList);

}
