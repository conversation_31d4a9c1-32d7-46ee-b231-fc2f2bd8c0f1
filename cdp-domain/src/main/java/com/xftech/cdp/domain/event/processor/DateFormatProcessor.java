package com.xftech.cdp.domain.event.processor;

import cn.hutool.core.date.DateUtil;
import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import com.xftech.cdp.domain.event.model.config.DateFormatConfig;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 日期转换数据处理器
 * <AUTHOR>
 * @version $ DateFormatProcessor, v 0.1 2024/11/18 11:42 snail Exp $
 */
@Slf4j
@Component
@ProcessorRule(processor = DataProcessEnum.FORMAT)
public class DateFormatProcessor extends AbstractDataProcessor {
    @Override
    public Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String, Object> values) {
        if(!preCheckPass(detail,values)){
            return null;
        }
        DateFormatConfig dataFormatConfig = (DateFormatConfig) fieldConfig;
        Date curDate = getDate(detail,values);
        String dataFormat = Objects.isNull(curDate) ? null: DateUtil.format(curDate,dataFormatConfig.getFormat());

        return new Field(detail.getTargetField(),dataFormat);
    }

    private Date getDate(FieldDetail detail, Map<String, Object> values){
        Object value = values.get(detail.getTargetField());
        if(Objects.isNull(value)){
            return null;
        }

        if(value instanceof Date){
            return (Date) value;
        }else if(value instanceof Number){
            return new Date(((Number) value).longValue());
        }

        return null;
    }

    private boolean preCheckPass(FieldDetail detail, Map<String, Object> values){
        if(CollectionUtils.isEmpty(values)){
            return false;
        }
        Object value = values.get(detail.getTargetField());
        if(Objects.isNull(value)){
            return false;
        }

        return true;
    }
}
