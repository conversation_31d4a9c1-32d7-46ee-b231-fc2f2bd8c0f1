package com.xftech.cdp.domain.crowd.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.base.database.Page;
import com.xftech.base.util.XxlJobUtil;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.label.AssociatedCrowdsReq;
import com.xftech.cdp.api.dto.resp.CrowdExecSqlResp;
import com.xftech.cdp.api.dto.resp.CrowdListResp;
import com.xftech.cdp.api.dto.resp.CrowdOneResp;
import com.xftech.cdp.api.dto.resp.CrowdParseResp;
import com.xftech.cdp.api.dto.resp.CrowdUploadResp;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.domain.cache.CacheCrowdExecLogService;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.cache.CacheSplitTableService;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.factory.AbsCrowdOptService;
import com.xftech.cdp.domain.crowd.factory.CrowdOptFactory;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdConstant;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.crowd.model.dispatch.MinMaxId;
import com.xftech.cdp.domain.crowd.model.enums.*;
import com.xftech.cdp.domain.crowd.model.label.crowd.DataSegment;
import com.xftech.cdp.domain.crowd.model.label.crowd.NewRandom;
import com.xftech.cdp.domain.crowd.repository.*;
import com.xftech.cdp.domain.crowd.service.*;
import com.xftech.cdp.domain.crowd.service.dispatch.impl.CrowdDispatchStartRockServiceImpl;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.model.enums.ReportDailyTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyCrowdPackRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.ReportDailyTaskService;
import com.xftech.cdp.domain.strategy.service.impl.StrategyCommonService;
import com.xftech.cdp.domain.subtable.repository.SplitTableRouteRepository;
import com.xftech.cdp.infra.assembler.CrowdAssembler;
import com.xftech.cdp.infra.client.ads.config.AdsConfig;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.oss.OssUtil;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.constant.TransConstants;
import com.xftech.cdp.infra.excel.UploadCrowdModel;
import com.xftech.cdp.infra.excel.UploadCrowdPageReadListen;
import com.xftech.cdp.infra.repository.cdp.crowd.po.*;
import com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyTaskDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyCrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.subtable.po.SplitTableRoute;
import com.xftech.cdp.infra.utils.*;
import com.xftech.xxljob.XxlJobAdminClient;
import com.xyf.cis.query.facade.dto.standard.response.MobileDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Slf4j
@Service
public class CrowdPackServiceImpl implements CrowdPackService {
    @Autowired
    public CrowdPackRepository crowdPackRepository;
    @Autowired
    public CrowdLabelRepository crowdLabelRepository;
    @Autowired
    public CrowdLabelPrimaryRepository crowdLabelPrimaryRepository;
    @Autowired
    public CrowdDetailRepository crowdDetailRepository;
    @Autowired
    public CrowdUploadLogRepository crowdUploadLogRepository;
    @Autowired
    public OssUtil ossUtil;
    @Autowired
    private LabelRepository labelRepository;
    @Autowired
    private Config config;
    @Autowired
    private XxlJobAdminClient xxlJobAdminClient;
    @Autowired
    private AdsLabelMonitorDfRepository adsLabelMonitorDfMysqlRepository;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;
    @Autowired
    private StrategyCommonService strategyService;
    @Autowired
    private CrowdWereHouseSnapshotRepository crowdWereHouseSnapshotRepository;
    @Autowired
    private AdsUserLabelDetailInfoDfRepository adsUserLabelDetailInfoDfRepository;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SplitTableRouteRepository splitTableRouteRepository;
    @Autowired
    private CrowdConfig crowdConfig;
    @Autowired
    private CrowdDetailSubRepository crowdDetailSubRepository;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private CacheSplitTableService cacheSplitTableService;
    @Autowired
    private CrowdLabelSubRepository crowdLabelSubRepository;
    @Autowired
    private CacheCrowdExecLogService cacheCrowdExecLogService;
    @Autowired
    private CacheCrowdPackService cacheCrowdPackService;
    @Autowired
    private CrowdExecSnapshotRepository crowdExecSnapshotRepository;
    @Autowired
    private StrategyCrowdPackRepository strategyCrowdPackRepository;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private CrowdPushBatchService crowdPushBatchService;
    @Autowired
    private CrowdOptFactory crowdOptFactory;
    @Autowired
    private AdsConfig adsConfig;
    @Autowired
    private CrowdDispatchStartRockServiceImpl crowdDispatchStartRockService;
    @Autowired
    private ReportDailyCrowdService reportDailyCrowdService;
    @Autowired
    private ReportDailyTaskService reportDailyTaskService;
    // 获取文件扩展名
    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private CisService cisService;
    @Autowired
    private CrowdLabelService crowdLabelService;
    @Autowired
    private ChangeService changeService;

    public final String ADS_USER_LABEL="ads_user_label_detail_info_df";
    public final String ADS_USER_LABEL_VIEW="ads_user_label_detail_info_df_view";
    public final String ADS_SQL_IN="AND NOT EXISTS (SELECT 1 FROM ads_user_label_detail_info_df_view AS b WHERE  a.app_user_id = b.app_user_id  AND ( ";
    public final String ADS_SQL="SELECT a.app_user_id, a.app, a.inner_app, a.ab_num, a.app_user_id_last2, a.mobile, a.register_time, a.mobile_utm_source, a.utm_source_list, a.last_loan_success_utm_source FROM ads_user_label_detail_info_df_view AS a WHERE a.app_user_id IN (SELECT app_user_id FROM ads_user_label_detail_info_df_view WHERE";


    private static String getExtension(String fileName) {
        return fileName.substring(fileName.lastIndexOf('.') + 1);
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public boolean createByCondition(CrowdCreateReq crowdCreateReq) {
        this.verifyLabel(crowdCreateReq.getFilterMethod(), crowdCreateReq.getCrowdLabelPrimaries());

        CrowdPackDo record = crowdPackRepository.selectByNameAndBusinessType(crowdCreateReq.getCrowdName(), crowdCreateReq.getBusinessType());
        if (record != null) {
            throw new CrowdException("人群包名称[" + crowdCreateReq.getCrowdName() + "]已存在");
        }

        CrowdPackDo crowdPackDo = CrowdAssembler.assemblerPackToDo(crowdCreateReq);
        crowdPackDo.InitDefaultValue();
        cacheCrowdPackService.insert(crowdPackDo);

        if (crowdPackDo.getId() == 0) {
            throw new CrowdException("人群包写入失败，请稍后再试");
        }

        // 写入 crowd_label_primary
        for (CrowdCreateReq.CrowdLabelPrimary crowdLabelPrimary : crowdCreateReq.getCrowdLabelPrimaries()) {
            CrowdLabelPrimaryDo crowdLabelPrimaryDo = CrowdAssembler.assemblerLabelPrimaryToDo(crowdLabelPrimary);

            crowdLabelPrimaryDo.setCrowdId(crowdPackDo.getId());
            crowdLabelPrimaryRepository.insert(crowdLabelPrimaryDo);
            if (crowdLabelPrimaryDo.getId() == 0) {
                throw new CrowdException("人群包写入失败，请检查配置");
            }
            // 写入 crowd_label
            for (CrowdCreateReq.CrowdLabel crowdLabel : crowdLabelPrimary.getCrowdLabels()) {
                CrowdLabelDo crowdLabelDo = CrowdAssembler.assemblerLabelToDo(crowdLabel);
                crowdLabelDo.verify();
                crowdLabelDo.setCrowdId(crowdPackDo.getId());
                crowdLabelDo.setCrowdLabelPrimaryId(crowdLabelPrimaryDo.getId());
                crowdLabelRepository.insert(crowdLabelDo);
                if (crowdLabelDo.getId() == 0) {
                    throw new CrowdException("人群包写入失败，请检查配置：" + crowdLabelDo.getLabelValue());
                }
                for (CrowdCreateReq.SubCrowdLabel subCrowdLabel : crowdLabel.getSubCrowdLabels()) {
                    CrowdLabelSubDo subCrowdLabelDo = CrowdAssembler.assemblerLabelSubToDo(subCrowdLabel);
                    subCrowdLabelDo.verify();
                    subCrowdLabelDo.setCrowdId(crowdPackDo.getId());
                    subCrowdLabelDo.setCrowdLabelId(crowdLabelDo.getId());
                    crowdLabelSubRepository.insert(subCrowdLabelDo);
                }
            }
        }

        //生成sql
        setSql(crowdPackDo);
        // 删除XXL-Job新增逻辑
        StrategyCreateReq.SendFrequency sendFrequency = new StrategyCreateReq.SendFrequency();
        sendFrequency.setType(0);
        if (crowdCreateReq.getRefreshType() == CrowdRefreshTypeEnum.MANUAL.getCode()) {
            crowdCreateReq.setRefreshTime(LocalDateTime.now().toLocalTime());
        } else {
            if (crowdCreateReq.getRefreshTime() == null) {
                throw new CrowdException("刷新时间不能为空");
            }
        }
        String cron = strategyService.convertToCron(sendFrequency, crowdCreateReq.getRefreshTime()).asString();
        crowdPackDo.setCron(cron);
        cacheCrowdPackService.updateById(crowdPackDo);

        log.info("testOperateLogObjectId setsValue:{}", crowdPackDo.getId());
        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(crowdPackDo.getId()));

        changeService.asyncSubmitCreateCrowdChange(crowdPackDo.getId()+ "", crowdCreateReq, crowdPackDo);
        return true;
    }

    public void setSql(CrowdPackDo crowdPack) {
        StringBuilder sql= new StringBuilder();
        List<CrowdLabelDo> crowdLabelList = crowdLabelRepository.selectListByCrowdId(crowdPack.getId());
        List<CrowdLabelSubDo> crowdLabeSublDoList = crowdLabelSubRepository.selectSubListByCrowdId(crowdPack.getId());
        List<Long> labelIds = ListUtils.distinctMap(crowdLabelList, CrowdLabelDo::getLabelId);
        labelIds.addAll(ListUtils.distinctMap(crowdLabeSublDoList, CrowdLabelSubDo::getLabelId));
        List<CrowdLabelPrimaryDo> crowdLabelPrimaryList = crowdLabelPrimaryRepository.selectListByCrowdId(crowdPack.getId());
        List<LabelDo> labelDos = labelRepository.selectBatchIds(labelIds);
        CrowdContext crowdContext = CrowdContext.init(false,crowdPack, crowdLabelPrimaryList, crowdLabelList, labelDos, crowdLabeSublDoList);
        AbsCrowdOptService crowdOpt = crowdOptFactory.createOpt(CrowdPullTypeEnum.USER_LABLE.getCode());
        crowdOpt.organizeSql(crowdContext);
        StringBuilder right = crowdContext.getLabelSqlPair().getRight();
        StringBuilder left = crowdContext.getLabelSqlPair().getLeft();
        if(StringUtils.isNotBlank(left)){
            left.replace(left.indexOf(ADS_USER_LABEL), left.indexOf(ADS_USER_LABEL) + ADS_USER_LABEL.length(),ADS_USER_LABEL_VIEW);
            left.append(" )");
            sql.append(left);
            crowdPack.setIncludeSql(left.toString());
        }
        if(StringUtils.isNotBlank(right)){
            right.replace(right.indexOf(ADS_USER_LABEL), right.indexOf(ADS_USER_LABEL) + ADS_USER_LABEL.length(),ADS_USER_LABEL_VIEW);
            right.append(" )");
            crowdPack.setExcludeSql(right.toString());
        }else {
            crowdPack.setCrowdSql(sql.toString());
            return;
        }
        int whereIndex = right.indexOf("where");
        int leftIndex = left.indexOf("where");
        if(StringUtils.isNotBlank(left) && whereIndex != -1 && leftIndex != -1){
            sql.setLength(0);
            sql.append(ADS_SQL);
            sql.append(left.substring(leftIndex + 5).trim()).append(")");
            sql.append(ADS_SQL_IN);
            sql.append(right.substring(whereIndex + 5).trim());
            sql.append("))");
        }else if(StringUtils.isBlank(left) && whereIndex != -1) {
            sql.append(right.insert(whereIndex + 5, " not"));
        }
        crowdPack.setCrowdSql(sql.toString());
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public boolean updateByCondition(CrowdUpdateReq crowdUpdateReq) {
        this.verifyLabel(crowdUpdateReq.getFilterMethod(), crowdUpdateReq.getCrowdLabelPrimaries());

        CrowdPackDo crowdPackDo = crowdPackRepository.selectById(crowdUpdateReq.getId().longValue());
        if (crowdPackDo == null || crowdPackDo.getId() == 0) {
            throw new CrowdException("人群包不存在，请检参数");
        }

        if (crowdPackDo.getStatus() == CrowdStatusEnum.EXECUTING.getCode() || crowdPackDo.getStatus() == CrowdStatusEnum.ENDED.getCode()) {
            throw new CrowdException("[刷新中]、[已完成] 状态不支持编辑，仅支持查看");
        }

        CrowdPackDo record = crowdPackRepository.selectByNameAndBusinessType(crowdUpdateReq.getCrowdName(), crowdPackDo.getBusinessType());
        if (record != null && !Objects.equals(record.getId(), crowdPackDo.getId())) {
            throw new CrowdException("人群包名称[" + crowdUpdateReq.getCrowdName() + "]已存在");
        }

        crowdPackDo.setCrowdName(crowdUpdateReq.getCrowdName());
        crowdPackDo.setGroupType(crowdUpdateReq.getGroupType());
        crowdPackDo.setFilterMethod(crowdUpdateReq.getFilterMethod());
        crowdPackDo.setRefreshType(crowdUpdateReq.getRefreshType());
        crowdPackDo.setValidityBegin(crowdUpdateReq.getValidityBegin());
        crowdPackDo.setValidityEnd(crowdUpdateReq.getValidityEnd());
        crowdPackDo.setRefreshTime(crowdUpdateReq.getRefreshTime());
        crowdPackDo.setH5Option(crowdUpdateReq.getH5Option());
        crowdPackDo.setUpdatedOp(SsoUtil.get().getName());
        crowdPackDo.setUpdatedTime(LocalDateTime.now());
        crowdPackDo.setUpdatedOpMobile(SsoUtil.get().getMobile());
        cacheCrowdPackService.updateById(crowdPackDo);

        // 删除 crowd_label_primary & crowd_label
        crowdLabelPrimaryRepository.deleteByCrowdId(crowdUpdateReq.getId().longValue());
        crowdLabelRepository.deleteByCrowdId(crowdUpdateReq.getId().longValue());
        crowdLabelSubRepository.deleteByCrowdId(crowdUpdateReq.getId().longValue());

        // 写入 crowd_label_primary
        for (CrowdCreateReq.CrowdLabelPrimary crowdLabelPrimary : crowdUpdateReq.getCrowdLabelPrimaries()) {
            CrowdLabelPrimaryDo crowdLabelPrimaryDo = CrowdAssembler.assemblerLabelPrimaryToDo(crowdLabelPrimary);

            crowdLabelPrimaryDo.setCrowdId(crowdPackDo.getId());
            crowdLabelPrimaryRepository.insert(crowdLabelPrimaryDo);
            if (crowdLabelPrimaryDo.getId() == 0) {
                throw new CrowdException("人群包写入失败，请检查配置");
            }

            // 写入 crowd_label
            for (CrowdCreateReq.CrowdLabel crowdLabel : crowdLabelPrimary.getCrowdLabels()) {
                CrowdLabelDo crowdLabelDo = CrowdAssembler.assemblerLabelToDo(crowdLabel);
                crowdLabelDo.verify();
                crowdLabelDo.setCrowdId(crowdPackDo.getId());
                crowdLabelDo.setCrowdLabelPrimaryId(crowdLabelPrimaryDo.getId());
                crowdLabelRepository.insert(crowdLabelDo);
                if (crowdLabelDo.getId() == 0) {
                    throw new CrowdException("人群包写入失败，请检查配置：" + crowdLabelDo.getLabelValue());
                }
                for (CrowdCreateReq.SubCrowdLabel subCrowdLabel : crowdLabel.getSubCrowdLabels()) {
                    CrowdLabelSubDo subCrowdLabelDo = CrowdAssembler.assemblerLabelSubToDo(subCrowdLabel);
                    subCrowdLabelDo.verify();
                    subCrowdLabelDo.setCrowdId(crowdPackDo.getId());
                    subCrowdLabelDo.setCrowdLabelId(crowdLabelDo.getId());
                    crowdLabelSubRepository.insert(subCrowdLabelDo);
                }
            }
        }

        //生成sql
        setSql(crowdPackDo);

        // 删除XXL-Job逻辑
        StrategyCreateReq.SendFrequency sendFrequency = new StrategyCreateReq.SendFrequency();
        sendFrequency.setType(0);

        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(crowdPackDo.getId()));

        // 手动刷新
        if (crowdUpdateReq.getRefreshType() == CrowdRefreshTypeEnum.MANUAL.getCode()) {
            crowdUpdateReq.setRefreshTime(LocalDateTime.now().toLocalTime());
        }

        String cron = strategyService.convertToCron(sendFrequency, crowdUpdateReq.getRefreshTime()).asString();
        crowdPackDo.setCron(cron);
        cacheCrowdPackService.updateById(crowdPackDo);

        // 暂停状态，例行刷新->手动刷新，保存后触发刷新一次
        if (crowdPackDo.getStatus() == CrowdStatusEnum.PAUSING.getCode()
                && crowdUpdateReq.getRefreshType() == CrowdRefreshTypeEnum.MANUAL.getCode()) {
            // 刷新Job逻辑移除;
        }

        changeService.asyncSubmitUpdateCrowdChange(crowdUpdateReq, crowdPackDo);
        return true;
    }

    private void verifyLabel(Integer filterMethod, List<CrowdCreateReq.CrowdLabelPrimary> crowdLabelPrimaries) {
        if (CrowdFilterMethodEnum.UPLOAD.getCode() == filterMethod) {
            return;
        }
        // 先遍历验证是否至少选择一个标签
        boolean isNotChoose = true;
        List<Long> labelIdList = new ArrayList<>();

        for (CrowdCreateReq.CrowdLabelPrimary crowdLabelPrimary : crowdLabelPrimaries) {
            if (CrowdLabelEnum.INCLUDE_LABEL.getCode() == (crowdLabelPrimary.getLabelGroupType())
                    && !CollectionUtils.isEmpty(crowdLabelPrimary.getCrowdLabels())) {
                List<CrowdCreateReq.CrowdLabel> includedLabelList = crowdLabelPrimary.getCrowdLabels();
                labelIdList.addAll(includedLabelList.stream().map(CrowdCreateReq.CrowdLabel::getLabelId).collect(Collectors.toList()));
                for (CrowdCreateReq.CrowdLabel crowdLabel : includedLabelList) {
                    labelIdList.addAll(crowdLabel.getSubCrowdLabels().stream().map(CrowdCreateReq.SubCrowdLabel::getLabelId).collect(Collectors.toList()));
                }
            }
        }
        List<LabelDo> labelDo = labelRepository.selectBatchIds(labelIdList);
        boolean noOnlyNewRandom = labelDo.stream().anyMatch(item -> Objects.equals(0,item.getLabelType()));
        if (noOnlyNewRandom) {
            isNotChoose = false;
        }
        if (isNotChoose) {
            throw new CrowdException("人群包写入失败，请至少选择一项除新随机数外的圈选标签)！");
        }
    }

    @Override
    public CrowdOneResp getOne(Long crowdId) {

        CrowdOneResp crowdOneResp = new CrowdOneResp();
        CrowdPackDo crowdPackDo = crowdPackRepository.selectById(crowdId);

        if (crowdPackDo == null) {
            return crowdOneResp;
        }

        if (crowdPackDo.getFilterMethod() == CrowdFilterMethodEnum.UPLOAD.getCode()) {
            CrowdUploadLogDo crowdUploadLogDo = crowdUploadLogRepository.selectByCrowdId(crowdId);
            crowdOneResp.setFileName(crowdUploadLogDo.getFileName());
            crowdOneResp.setUploadLogId(crowdUploadLogDo.getId());
        }
        crowdOneResp.setId(crowdPackDo.getId());
        crowdOneResp.setCrowdName(crowdPackDo.getCrowdName());
        crowdOneResp.setFilterMethod(crowdPackDo.getFilterMethod());
        crowdOneResp.setRefreshType(crowdPackDo.getRefreshType());
        crowdOneResp.setStatus(crowdPackDo.getStatus());
        crowdOneResp.setGroupType(crowdPackDo.getGroupType());
        crowdOneResp.setValidityBegin(crowdPackDo.getValidityBegin());
        crowdOneResp.setValidityEnd(crowdPackDo.getValidityEnd());
        crowdOneResp.setRefreshTime(crowdPackDo.getRefreshTime());
        crowdOneResp.setH5Option(crowdPackDo.getH5Option());
        crowdOneResp.setCrowdLabelPrimaries(new ArrayList<>());

        // primary label
        List<CrowdLabelPrimaryDo> primaryLabels = crowdLabelPrimaryRepository.selectListByCrowdId(crowdPackDo.getId());

        if (CollectionUtils.isEmpty(primaryLabels)) {
            return crowdOneResp;
        }

        // label kv
        List<CrowdLabelDo> crowdLabels = crowdLabelRepository.selectListByCrowdId(Long.parseLong(String.valueOf(crowdId)));
        List<CrowdLabelSubDo> subCrowdLabels = crowdLabelSubRepository.selectSubListByCrowdId(Long.parseLong(String.valueOf(crowdId)));

        List<CrowdOneResp.CrowdLabelPrimary> crowdLabelPrimaryArrayList = new ArrayList<>();
        List<LabelDo> labelBoList = labelRepository.getAllWithoutLimit();
        Map<Long, LabelDo> labelDoMap = MapUtils.listToMap(labelBoList, LabelDo::getId);
        for (CrowdLabelPrimaryDo primaryLabel : primaryLabels) {
            CrowdOneResp.CrowdLabelPrimary crowdLabelPrimary = new CrowdOneResp.CrowdLabelPrimary();
            crowdLabelPrimary.setLabelGroupType(primaryLabel.getLabelGroupType());
            crowdLabelPrimary.setPrimaryLabel(primaryLabel.getPrimaryLabel());
            crowdLabelPrimary.setExecIndex(primaryLabel.getExecIndex());
            crowdLabelPrimary.setPrimaryLabelRelation(primaryLabel.getPrimaryLabelRelation());
            crowdLabelPrimary.setCrowdLabels(new ArrayList<>());

            for (CrowdLabelDo crowdLabelDo : crowdLabels) {
                if (Objects.equals(crowdLabelDo.getCrowdLabelPrimaryId(), primaryLabel.getId())) {
                    CrowdOneResp.CrowdLabel crowdLabel = new CrowdOneResp.CrowdLabel();
                    crowdLabel.setLabelId(crowdLabelDo.getLabelId().intValue());
                    crowdLabel.setLabelRelation(crowdLabelDo.getLabelRelation());
                    crowdLabel.setLabelValue(crowdLabelDo.getLabelValue());
                    crowdLabel.setExecIndex(crowdLabelDo.getExecIndex());
                    crowdLabel.setConfigurationOption(JSON.parseObject(labelDoMap.get(crowdLabelDo.getLabelId()).getConfigurationOption()));
                    crowdLabel.setLabelName(labelDoMap.get(crowdLabelDo.getLabelId()).getLabelName());
                    crowdLabel.setLabelOptionType(labelDoMap.get(crowdLabelDo.getLabelId()).getConfigurationOptionType());
                    crowdLabel.setSubCrowdLabels(new ArrayList<>());
                    if ("new_random".equals(labelDoMap.get(crowdLabelDo.getLabelId()).getDataWarehouseField())) {
                        crowdLabel.setRandomItem(this.getNewRandomInfo(crowdLabelDo.getLabelValue()));
                    }
                    for (CrowdLabelSubDo subCrowdLabel : subCrowdLabels) {
                        if (Objects.equals(crowdLabelDo.getId(), subCrowdLabel.getCrowdLabelId())) {
                            CrowdOneResp.SubCrowdLabel subCrowdLabelResp = new CrowdOneResp.SubCrowdLabel();
                            subCrowdLabelResp.setLabelId(subCrowdLabel.getLabelId().intValue());
                            subCrowdLabelResp.setLabelRelation(subCrowdLabel.getLabelRelation());
                            subCrowdLabelResp.setLabelValue(subCrowdLabel.getLabelValue());
                            subCrowdLabelResp.setExecIndex(subCrowdLabel.getExecIndex());
                            subCrowdLabelResp.setConfigurationOption(JSON.parseObject(labelDoMap.get(subCrowdLabel.getLabelId()).getConfigurationOption()));
                            subCrowdLabelResp.setLabelName(labelDoMap.get(subCrowdLabel.getLabelId()).getLabelName());
                            subCrowdLabelResp.setLabelOptionType(labelDoMap.get(subCrowdLabel.getLabelId()).getConfigurationOptionType());
                            if ("new_random".equals(labelDoMap.get(subCrowdLabel.getLabelId()).getDataWarehouseField())) {
                                subCrowdLabelResp.setRandomItem(this.getNewRandomInfo(crowdLabelDo.getLabelValue()));
                            }
                            crowdLabel.getSubCrowdLabels().add(subCrowdLabelResp);
                        }
                    }
                    crowdLabelPrimary.getCrowdLabels().add(crowdLabel);
                }
            }
            crowdLabelPrimaryArrayList.add(crowdLabelPrimary);
        }

        crowdOneResp.setCrowdLabelPrimaries(crowdLabelPrimaryArrayList);

        return crowdOneResp;
    }


    @Override
    public PageResultResponse queryList(CrowdListReq crowdListReq) {

        Page<CrowdPackDo> records = this.getCrowdPackRecord(crowdListReq);
        List<CrowdPackDo> crowdPackDoList = records.getList();

        if (CollectionUtils.isEmpty(crowdPackDoList)) {
            return null;
        }

        List<StrategyDo> strategyDoList = strategyRepository.getAllStrategyStatusAndCrowdPack();

        List<CrowdListResp> crowdListResponse = new ArrayList<>();
        for (CrowdPackDo crowdPackDo : crowdPackDoList) {
            CrowdListResp crowdListResp = new CrowdListResp();
            crowdListResp.setId(crowdPackDo.getId().intValue());
            crowdListResp.setCrowdName(crowdPackDo.getCrowdName());
            crowdListResp.setCrowdPersonNum(crowdPackDo.getCrowdPersonNum());
            crowdListResp.setFilterMethod(crowdPackDo.getFilterMethod());
            crowdListResp.setRefreshType(crowdPackDo.getRefreshType());
            crowdListResp.setUpdateOp(crowdPackDo.getUpdatedOp());
            crowdListResp.setStatus(crowdPackDo.getStatus());
            crowdListResp.setGroupType(crowdPackDo.getGroupType());
            crowdListResp.setRefreshTime(crowdPackDo.getRefreshTime());
            crowdListResp.setLatestRefreshTime(crowdPackDo.getLatestRefreshTime());
            crowdListResp.setStrategyIds(getCrowdStrategyList(strategyDoList, crowdPackDo.getId()));
            crowdListResp.setUpdatedTime(crowdPackDo.getUpdatedTime());
            crowdListResp.setCreatedTime(crowdPackDo.getCreatedTime());
            if(appConfigService.whetherGrayscaleCrowd(crowdPackDo.getId())){
                crowdListResp.setExecSql(getCrowSql(crowdPackDo));
            }else {
                crowdListResp.setExecSql(getExecSql(crowdPackDo.getId()));
            }
            crowdListResponse.add(crowdListResp);
        }

        return new PageResultResponse(crowdListResponse, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    private CrowdExecSqlResp getCrowSql(CrowdPackDo crowdPackDo) {
        CrowdExecSqlResp ces = new CrowdExecSqlResp();
        ces.setExcludeSql(crowdPackDo.getExcludeSql());
        ces.setIncludeSql(crowdPackDo.getIncludeSql());
        ces.setRefreshTime(crowdPackDo.getLatestRefreshTime());
        return ces;
    }

    List<CrowdListResp.StrategyResp> getCrowdStrategyList(List<StrategyDo> strategyDoList, Long crowdPackId) {
        return strategyDoList.stream().filter(item -> StringUtils.isNotBlank(item.getCrowdPackId()) && Arrays.asList(item.getCrowdPackId().split(";")).contains(String.valueOf(crowdPackId))).map(strategy -> {
            CrowdListResp.StrategyResp strategyResp = new CrowdListResp.StrategyResp();
            strategyResp.setStrategyId(strategy.getId());
            strategyResp.setStatus(strategy.getStatus());
            return strategyResp;
        }).sorted(Comparator.comparing(CrowdListResp.StrategyResp::getStatus)).collect(Collectors.toList());
    }

    Page<CrowdPackDo> getCrowdPackRecord(CrowdListReq crowdListReq) {
        if (Objects.isNull(crowdListReq.getStrategyIds())) {
            return crowdPackRepository.selectPage(crowdListReq);
        } else if (crowdListReq.getStrategyIds() == -1) {
            return crowdPackRepository.selectNoStrategyIdCrowdPack(crowdListReq);
        } else {
            List<Long> crowdPackIds = strategyCrowdPackRepository.selectCrowdPackIdsByStrategyId(crowdListReq.getStrategyIds());
            crowdListReq.setCrowdPackIds(crowdPackIds);
            return crowdPackRepository.selectPage(crowdListReq);
        }

    }

    @Override
    public boolean createCrowdByExcel(CrowdUploadReq crowdUploadReq) {
        CrowdUploadLogDo crowdUploadLogDo = crowdUploadLogRepository.selectById(crowdUploadReq.getUploadLogId());
        CrowdPackDo crowdPackDo = crowdPackRepository.selectByIdAndNotFlag(crowdUploadLogDo.getCrowdId());
        if (crowdPackDo == null) {
            throw new CrowdException("文件未解析，不能生成人群包");
        }
        CrowdPackDo sameNameRecord = crowdPackRepository.selectByNameAndBusinessType(crowdUploadReq.getCrowdName(), crowdUploadReq.getBusinessType());
        if (sameNameRecord != null && !sameNameRecord.getId().equals(crowdPackDo.getId())) {
            throw new CrowdException("人群名称已存在");
        }
        if (crowdPackDo.getDFlag() != 1) {
            throw new CrowdException("人群状态错误");
        }
        crowdPackDo.setCrowdName(crowdUploadReq.getCrowdName());
        crowdPackDo.setDFlag(0);
        crowdPackDo.setBusinessType(crowdUploadReq.getBusinessType());
        Long maxExecLogId = crowdExecLogRepository.selectCrowdMaxExecLogId(crowdPackDo.getId());
        //入库
        return TransactionUtil.transactional(() -> {
            cacheCrowdPackService.updateById(crowdPackDo);
            crowdDetailRepository.updateStatusByCrowdIdAndExecLogId(crowdPackDo.getId(), 0, 1, maxExecLogId);
            changeService.asyncSubmitCrowdCreateByExcelChange(crowdUploadReq, crowdPackDo);
        });
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public boolean duplicate(Long crowdId) {

        Long longCrowdId = (long) crowdId;
        CrowdPackDo crowdPackDo = crowdPackRepository.selectById(crowdId);
        if (crowdPackDo == null) {
            throw new CrowdException("人群不存在");
        }

        crowdPackDo.setId(0L);
        crowdPackDo.setStatus(CrowdStatusEnum.INIT.getCode());
        crowdPackDo.setCreatedOp(SsoUtil.get().getName());
        crowdPackDo.setUpdatedOp(SsoUtil.get().getName());
        crowdPackDo.setCrowdName(crowdPackDo.getCrowdName() + "_copy");
        if (crowdPackDo.getCrowdName().length() > 20) {
            throw new CrowdException("人群包名称过长，无法拷贝，请修改名称后再试");
        }
        cacheCrowdPackService.insert(crowdPackDo);

        List<CrowdLabelPrimaryDo> crowdLabelPrimaryDoList = crowdLabelPrimaryRepository.selectListByCrowdId(longCrowdId);

        // 一级标签
        if (crowdLabelPrimaryDoList.isEmpty()) {
            return true;
        }

        crowdLabelPrimaryDoList.forEach(crowdLabelPrimaryBo -> {
            Long crowdLabelPrimaryId = crowdLabelPrimaryBo.getId();
            crowdLabelPrimaryBo.setId(0L);
            crowdLabelPrimaryBo.setCrowdId(crowdPackDo.getId());
            crowdLabelPrimaryRepository.insert(crowdLabelPrimaryBo);

            // 二级标签
            List<CrowdLabelDo> crowdLabelDoList = crowdLabelRepository.selectListByLabelPrimaryId(crowdLabelPrimaryId);
            crowdLabelDoList.forEach(crowdLabelDo -> {
                crowdLabelDo.setId(0L);
                crowdLabelDo.setCrowdId(crowdPackDo.getId());
                crowdLabelDo.setCrowdLabelPrimaryId(crowdLabelPrimaryBo.getId());
                crowdLabelRepository.insert(crowdLabelDo);
            });
        });

        int triggerStatus = crowdPackDo.getRefreshType() == CrowdRefreshTypeEnum.MANUAL.getCode() ? 0 : 1;

        // 移除xxl-job逻辑
        crowdPackDo.setXxlJobId(null);
        cacheCrowdPackService.updateById(crowdPackDo);
        return true;
    }

    @Override
    public boolean operate(CrowdOperateReq crowdOperateReq) {

        CrowdPackDo crowdPackDo = crowdPackRepository.selectById(crowdOperateReq.getCrowdId());

        if (crowdPackDo == null) {
            throw new CrowdException("人群包不存在");
        }

        if (crowdOperateReq.getRunType() == CrowdStatusEnum.PAUSING.getCode() &&
                (crowdPackDo.getStatus() == CrowdStatusEnum.EXECUTING.getCode() || crowdPackDo.getStatus() == CrowdStatusEnum.ENDED.getCode())) {
            throw new CrowdException("[刷新中]、[已完成]状态不支持暂停操作");
        }

        // 开启
        if (crowdOperateReq.getRunType() == CrowdOperateEnum.ENABLE.getCode()) {
            //xxlJobAdminClient.startJob(crowdPackDo.getXxlJobId());
            if (crowdPackDo.getStatus() == CrowdStatusEnum.INIT.getCode()) {
            } else {
                crowdPackDo.setStatus(CrowdStatusEnum.INIT.getCode());
                cacheCrowdPackService.updateById(crowdPackDo);
            }
        } else if (crowdOperateReq.getRunType() == CrowdOperateEnum.PAUSE.getCode()) {
            try {
            } catch (Exception e) {
                log.warn("人群包编号{}：暂停异常", crowdOperateReq.getCrowdId(), e);
                return false;
            }
            crowdPackDo.setStatus(CrowdStatusEnum.PAUSING.getCode());
            cacheCrowdPackService.updateById(crowdPackDo);
        } else if (crowdOperateReq.getRunType() == CrowdOperateEnum.REFRESH.getCode()) {
            CrowdFilterMethodEnum filterMethodEnum = CrowdFilterMethodEnum.getInstance(crowdPackDo.getFilterMethod());
            if (filterMethodEnum == CrowdFilterMethodEnum.UPLOAD) {
                throw new CrowdException("文件上传模式的人群包不支持刷新");
            }

            Boolean isStop = appConfigService.whetherGrayscaleCrowd(crowdPackDo.getId());
            if(isStop){
                //新人群包执行
                setSql(crowdPackDo);
                crowdPushBatchService.pushCrowdSqlToAds(crowdPackDo);
            }else {
                throw new CrowdException("不再支持人群包原始版本的刷新方式");
            }

        } else if (crowdOperateReq.getRunType() == CrowdOperateEnum.DELETE.getCode()) {
            if (!SsoUtil.get().getIfAdmin() && !SsoUtil.get().getName().equals(crowdPackDo.getUpdatedOp())){
                throw new CrowdException("您没有权限删除该条记录");
            }
            //人群包状态校验
            if (CrowdStatusEnum.EXECUTING.getCode() == crowdPackDo.getStatus()) {
                throw new CrowdException(String.format("人群包状态为:%s,不可删除", CrowdStatusEnum.EXECUTING.getDescription()));
            }
            //人群包是否关联策略
            List<StrategyCrowdPackDo> strategyCrowdPackDos = strategyCrowdPackRepository.selectByCrowdPackId(crowdPackDo.getId());
            if (!CollectionUtils.isEmpty(strategyCrowdPackDos)) {
                List<Long> strategyIds = strategyCrowdPackDos.stream().map(StrategyCrowdPackDo::getStrategyId).distinct().collect(Collectors.toList());
                List<StrategyDo> strategyDoList = strategyRepository.getByIds(strategyIds);
                List<Long> noDeprecatedIds = strategyDoList.stream().filter(item -> StrategyStatusEnum.DEPRECATED.getCode() != item.getStatus())
                        .map(StrategyDo::getId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(noDeprecatedIds)) {
                    throw new CrowdException(String.format("人群包存在关联未删除的策略id:%s,不可删除", noDeprecatedIds));
                }
            }
            //删除的人群包需要停止job
            crowdPackDo.setStatus(CrowdStatusEnum.DEPRECATED.getCode());
            crowdPackDo.setUpdatedOp(SsoUtil.get().getName());
            crowdPackDo.setUpdatedTime(LocalDateTime.now());
            cacheCrowdPackService.updateById(crowdPackDo);
            log.info("人群包删除操作,人群包id:{},操作人:{}", crowdPackDo.getId(), crowdPackDo.getUpdatedOp());
        } else if (crowdOperateReq.getRunType() == CrowdOperateEnum.RECOVER.getCode()) {
            if (CrowdFilterMethodEnum.LABEL.getCode() == crowdPackDo.getFilterMethod()) {
                if (crowdPackDo.getValidityBegin().isAfter(LocalDateTime.now())) {
                    crowdPackDo.setStatus(CrowdStatusEnum.INIT.getCode());
                } else if (crowdPackDo.getValidityEnd().isAfter(LocalDateTime.now())) {
                    if (CrowdRefreshTypeEnum.MANUAL.getCode() == crowdPackDo.getRefreshType()) {
                        crowdPackDo.setStatus(CrowdStatusEnum.SUCCESS.getCode());
                    } else {
                        crowdPackDo.setStatus(CrowdStatusEnum.PAUSING.getCode());
                    }
                } else {
                    crowdPackDo.setStatus(CrowdStatusEnum.ENDED.getCode());
                }
            } else {
                crowdPackDo.setStatus(CrowdStatusEnum.ENDED.getCode());
            }
            crowdPackDo.setUpdatedOp(SsoUtil.get().getName());
            crowdPackDo.setUpdatedTime(LocalDateTime.now());
            cacheCrowdPackService.updateById(crowdPackDo);
            log.info("人群包恢复操作,人群包id:{},操作人:{}", crowdPackDo.getId(), crowdPackDo.getUpdatedOp());
        }

        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(crowdPackDo.getId()));

        changeService.asyncSubmitOperateCrowdChange(crowdOperateReq, crowdPackDo);
        return true;
    }

    @Override
    public String batchDelete(CrowdBatchDeleteReq crowdBatchDeleteReq) {
        List<Long> crowdIds = crowdBatchDeleteReq.getCrowdIds();
        if (CollectionUtils.isEmpty(crowdIds)) {
            throw new CrowdException("未选择待操作人群包");
        }
        String[] ids = crowdIds.stream().map(String::valueOf).toArray(String[]::new);
        List<CrowdPackDo> crowdPackDos = crowdPackRepository.selectByIds(ids);
        if (!SsoUtil.get().getIfAdmin()) {
            List<Long> noOwnId = crowdPackDos.stream().filter(crowdPackDo -> !SsoUtil.get().getName().equals(crowdPackDo.getUpdatedOp()))
                    .map(CrowdPackDo::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noOwnId)) {
                throw new CrowdException(String.format("存在无权限操作人群,不可删除，人群编号:%s", noOwnId));
            }
        }
        //人群包状态校验校验
        List<Long> executingCrowdIds = crowdPackDos.stream().filter(item -> CrowdStatusEnum.EXECUTING.getCode() == item.getStatus()).map(CrowdPackDo::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(executingCrowdIds)) {
            throw new CrowdException(String.format("存在执行中人群,不可删除，人群编号:%s", executingCrowdIds));
        }
        //人群包是否关联策略
        List<StrategyCrowdPackDo> strategyCrowdPackDoList = strategyCrowdPackRepository.selectByCrowdPackIds(crowdIds);
        if (!CollectionUtils.isEmpty(strategyCrowdPackDoList)) {
            //筛选人群关联策略id
            List<Long> strategyIds = strategyCrowdPackDoList.stream().map(StrategyCrowdPackDo::getStrategyId).distinct().collect(Collectors.toList());
            List<StrategyDo> strategyDoList = strategyRepository.getByIds(strategyIds);
            //筛选策略状态为未删除的策略id
            List<Long> noDeprecatedStrategyIds = strategyDoList.stream().filter(item -> StrategyStatusEnum.DEPRECATED.getCode() != item.getStatus())
                    .map(StrategyDo::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noDeprecatedStrategyIds)) {
                //筛选未删除的策略id对应的人群id
                List<Long> canNotDeprecatedCrowdIds = strategyCrowdPackDoList.stream().filter(item -> noDeprecatedStrategyIds.contains(item.getStrategyId())).map(StrategyCrowdPackDo::getCrowdPackId).distinct().collect(Collectors.toList());
                throw new CrowdException(String.format("存在关联未删除策略，不可删除，人群编号:%s", canNotDeprecatedCrowdIds));
            }
        }
        crowdPackDos.forEach(item -> {
            item.setXxlJobId(null);
            item.setStatus(CrowdStatusEnum.DEPRECATED.getCode());
            item.setUpdatedOp(SsoUtil.get().getName());
            item.setUpdatedTime(LocalDateTime.now());
        });

        crowdPackRepository.updateBatchById(crowdPackDos);
        //收集操作对象Id
        OperateLogObjectIdUtils.set(crowdIds);
        changeService.asyncSubmitBatchDeleteCrowdChange(crowdBatchDeleteReq, crowdPackDos);
        return "操作成功";
    }

    @Override
    public void crowdReset() {
        //xxlJobAdminClient.startJob(crowdConfig.getAlarmJobId());
        // 重置生效人群包为初始化
        List<CrowdPackDo> crowdPackDos = crowdPackRepository.queryAllEffectiveCrowdsByTime(LocalDateTime.now());
        if (CollectionUtils.isEmpty(crowdPackDos)) {
            return;
        }

        String finishKey = String.format("crowdReset:fininsh:%s", com.xftech.cdp.infra.utils.DateUtil.dayOfInt(new Date()));
        if (!StringUtils.isEmpty(redisUtils.get(finishKey))) {
            log.info("今日已经存在完成标记缓存,并且在有效期内,不在执行");
            return;
        }

        for (CrowdPackDo crowdPackDo : crowdPackDos) {
            crowdPackDo.setStatus(CrowdStatusEnum.INIT.getCode());
            crowdPackDo.setUpdatedTime(null);
        }
        cacheCrowdPackService.updateBatchById(crowdPackDos);
        // 停止所有生效人群包任务
        for (CrowdPackDo crowdPackDo : crowdPackDos) {
            try {
                if (crowdConfig.isCrowdJobStop()) {
                    // 直接退出，人群包的Job后期统一删除
                    break;
                }
                if (crowdPackDo.getXxlJobId() != null && crowdPackDo.getXxlJobId() > 0){
                    continue;
                }
                xxlJobAdminClient.stopJob(crowdPackDo.getXxlJobId());
            }catch (Exception exception){
                log.error("crowdReset", exception);
            }
        }

        // 核心逻辑暂且设置一个完成标记, 允许Job执行多次补偿，内部判定这个标记;
        long expire = 60 * 60 * 2L; // 设置2个小时
        redisUtils.set(finishKey, "1", expire);

        // 重置过期人群包为已完成
        List<CrowdPackDo> expiredCrowdPackDos = crowdPackRepository.queryAllExpireCrowdsByTime(LocalDateTime.now());
        if (CollectionUtils.isEmpty(expiredCrowdPackDos)) {
            return;
        }
        for (CrowdPackDo crowdPackDo : expiredCrowdPackDos) {
            crowdPackDo.setStatus(CrowdStatusEnum.ENDED.getCode());
            crowdPackDo.setUpdatedTime(null);
        }
        cacheCrowdPackService.updateBatchById(expiredCrowdPackDos);
    }

    @Override
    public void clearHistoryCrowdDetail() {
        MinMaxId minMaxId = crowdDetailRepository.selectMinId();
        if (minMaxId != null) {
            long curMinId = minMaxId.getMinId();
            long curMaxId = minMaxId.getMinId() + 10000;
            crowdDetailRepository.clearData(curMinId, curMaxId);
//            return;
        }

        LocalDateTime expireTime = LocalDateTimeUtil.beginOfDay(LocalDateTime.now().plus(-2, ChronoUnit.DAYS));
        // 获取分表路由表执行时间（exec_date）超过2天的记录
        List<SplitTableRoute> splitTableRouteList = splitTableRouteRepository.selectExpireRecord(Constants.CROWD_DETAIL_STR, expireTime.toLocalDate());
        if (CollectionUtils.isEmpty(splitTableRouteList)) {
            return;
        }
        for (SplitTableRoute splitTableRoute : splitTableRouteList) {
            int tableNo = splitTableRoute.getTableNo();
            CrowdDetailDo crowdDetailDo = crowdDetailSubRepository.selectTableLastRecord(tableNo);
            if (crowdDetailDo == null) {
                // Redis分表记录数置0
                redisTemplate.opsForValue().set(TransConstants.crowdDetailTableKey(tableNo), 0);
                // 更新路由表删除标志
                splitTableRouteRepository.updateDflagByTableNo(tableNo);
                return;
            }
            // 判断分表最新一条记录创建时间（create_time）是否超过2天，是则 truncate 清空该表
            if (crowdDetailDo.getCreatedTime().isBefore(expireTime)) {
                MinMaxId minMaxIdSub = crowdDetailRepository.selectSubMinId(TransConstants.crowdDetailTableName(tableNo));
                if (minMaxIdSub != null) {
                    long curMinId = minMaxIdSub.getMinId();
                    long curMaxId = minMaxIdSub.getMinId() + 10000;
                    crowdDetailRepository.clearSubData(curMinId, curMaxId, TransConstants.crowdDetailTableName(tableNo));
                    return;
                }
            }
        }
    }

    @Override
    public void clearHistoryCrowdDetailByTruncate() {
        LocalDateTime expireTime = LocalDateTimeUtil.beginOfDay(LocalDateTime.now().plus(-2, ChronoUnit.DAYS));
        int truncateNo = -1;
        // 获取分表路由表执行时间（exec_date）超过2天的记录
        List<SplitTableRoute> splitTableRouteList = splitTableRouteRepository.selectExpireRecord(Constants.CROWD_DETAIL_STR, expireTime.toLocalDate());
        if (CollectionUtils.isEmpty(splitTableRouteList)) {
            return;
        }
        for (SplitTableRoute splitTableRoute : splitTableRouteList) {
            int tableNo = splitTableRoute.getTableNo();
            // 表已清空，跳过
            if (truncateNo == tableNo) {
                continue;
            }
            CrowdDetailDo crowdDetailDo = crowdDetailSubRepository.selectTableLastRecord(tableNo);
            // 判断分表最新一条记录创建时间（create_time）是否超过2天，是则 truncate 清空该表
            if (crowdDetailDo.getCreatedTime().isBefore(expireTime)) {
                // truncate 清空分表，自增id重置
                crowdDetailSubRepository.truncateTable(tableNo);
                // Redis分表记录数置0
                redisTemplate.opsForValue().set(TransConstants.crowdDetailTableKey(tableNo), 0);
                // 更新路由表删除标志
                splitTableRouteRepository.updateDflagByTableNo(tableNo);
                // 清空表序号
                truncateNo = tableNo;
            }
        }
    }

    @Override
    public void crowdWareHouseAlarm() {
        LocalDateTime now = LocalDateTime.now();
        Boolean exist = adsLabelMonitorDfMysqlRepository.selectExistByDataDate(LocalDateTimeUtil.beginOfDay(now), CrowdConstant.MONITOR);
        if (exist) {
            //关闭当前任务
            CrowdWereHouseSnapshotDo crowdWereHouseSnapshotDo = crowdWereHouseSnapshotRepository.getTodayCrowd();
            if (crowdWereHouseSnapshotDo == null) {
                Long maxId = adsUserLabelDetailInfoDfRepository.selectMaxAppUserId();
                Long minId = adsUserLabelDetailInfoDfRepository.selectMinAppUserId();
                crowdWereHouseSnapshotDo = new CrowdWereHouseSnapshotDo();
                crowdWereHouseSnapshotDo.setBizDate(LocalDateTime.now());
                crowdWereHouseSnapshotDo.setMaxId(maxId);
                crowdWereHouseSnapshotDo.setMinId(minId);
                crowdWereHouseSnapshotRepository.insert(crowdWereHouseSnapshotDo);
            }
            // 操作人群包定时任务
            this.crowdPackExec(CrowdPullTypeEnum.USER_LABLE.getCode());
            xxlJobAdminClient.stopJob(Integer.parseInt(XxlJobUtil.getCurrentJobId()));
        } else {
            // 用户标签人群包报警
            CrowdPackDo crowdPackDo = crowdPackRepository.selectMinExecTimeBo(now, CrowdPullTypeEnum.USER_LABLE.getCode());
            if (!ObjectUtils.isEmpty(crowdPackDo) && CrowdStatusEnum.DEPRECATED.getCode() != crowdPackDo.getStatus() && now.toLocalTime().isBefore(crowdPackDo.getRefreshTime())) {
                List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
                atMobileList.add(crowdPackDo.getUpdatedOpMobile());
                crowdPackDo.alarmDingTalk(crowdConfig.getAlarmUrl(), atMobileList, "");
            }
        }
        // 加贷电销人群包告警
        this.autoMessAlarm();
    }

    private void autoMessAlarm() {
        // 用户标签刷新告警
        Boolean existAutoMess = adsLabelMonitorDfMysqlRepository.selectExistByDataDate(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), CrowdConstant.AUTO_MESSAGE);
        if (existAutoMess) {
            this.crowdPackExec(CrowdPullTypeEnum.AUTO_MESSAGE.getCode());
            return;
        }
        xxlJobAdminClient.startJob(crowdConfig.getAlarmJobId());
        CrowdPackDo crowdPackDo = crowdPackRepository.selectMinExecTimeBo(LocalDateTime.now(), CrowdPullTypeEnum.AUTO_MESSAGE.getCode());
        if (!ObjectUtils.isEmpty(crowdPackDo) && CrowdStatusEnum.DEPRECATED.getCode() != crowdPackDo.getStatus()) {
            List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
            atMobileList.add(crowdPackDo.getUpdatedOpMobile());
            crowdPackDo.alarmDingTalk(crowdConfig.getAlarmUrl(), atMobileList,"");
        }
    }

    private void crowdPackExec(Integer pullType) {
        int retryTimes = 5;
        LocalDateTime now = LocalDateTime.now();
        //重跑所有未执行的任务
        List<CrowdPackDo> refreshTimeCrowdPacks = crowdPackRepository.queryLeRefreshTime(now, pullType);
        for (CrowdPackDo packBo : refreshTimeCrowdPacks) {
            retryTimes = 5;
            while (true) {
                retryTimes--;
                try {
                    xxlJobAdminClient.triggerJob(packBo.getXxlJobId(), JSON.toJSONString(packBo.toXxlJobParam()));
                    break;
                } catch (Exception ex) {
                    if (retryTimes <= 0) {
                        throw ex;
                    }
                    log.error("crowdPackExec, error", ex);
                }
            }
        }

        //开启所有人群包任务
        List<CrowdPackDo> crowdPackDos = crowdPackRepository.startCrowdPackJob(now, pullType);
        for (CrowdPackDo packBo : crowdPackDos) {
            retryTimes = 5;
            while (true) {
                retryTimes--;
                try {
                    xxlJobAdminClient.startJob(packBo.getXxlJobId());
                    break;
                } catch (Exception ex) {
                    if (retryTimes <= 0) {
                        throw ex;
                    }
                    log.error("crowdPackExec, error", ex);
                }
            }

        }
    }

    @Override
    public List<Long> selectEffectiveCrowd(@NonNull List<Long> crowdIds) {
        return crowdPackRepository.selectEffectiveCrowd(crowdIds);
    }

    @Override
    public List<Map<String, Long>> selectCrowdMaxExecLogIds(@NonNull List<Long> crowdIds) {
        return crowdExecLogRepository.selectCrowdMaxExecLogIds(crowdIds);
    }

    public boolean getCrowdFilterMethod(Long crowdId,Map<Long, CrowdContext> crowdContent){
        try {
            Integer filterMethod = crowdContent.get(crowdId).getCrowdPack().getFilterMethod();
            return Objects.equals(filterMethod, CrowdFilterMethodEnum.LABEL.getCode());
        }catch (Exception e){
            log.error("getCrowdFilterMethod error",e);
        }
        return false;
    }

    /**
     * 获取所有执行ID对应的表名
     *
     * @param crowdIds 人群包ID集合
     * @return 所有执行ID对应的表名
     */
    @Override
    public List<Triple<Long, Long, List<String>>> getExecLogIdAndTablePairList(List<Long> crowdIds, Map<Long, CrowdContext> crowdContent) {
        List<Long> filteredCrowdIds = crowdIds.stream()
                .filter(id -> appConfigService.whetherGrayscaleCrowd(id) && getCrowdFilterMethod(id, crowdContent))
                .collect(Collectors.toList());
        List<Map<String, Object>> mapList = cacheCrowdExecLogService.selectCrowdMaxExecLogIds(
                crowdIds.stream()
                        .filter(id -> !filteredCrowdIds.contains(id))
                        .collect(Collectors.toList())
        );
        List<Triple<Long, Long, List<String>>> tripleList = mapList.stream()
                .map(map -> {
                    Long crowdId = Long.valueOf(map.get("crowd_id").toString());
                    Long crowdExceLogId = Long.valueOf(map.get("id").toString());
                    return Triple.of(crowdId, crowdExceLogId, cacheSplitTableService.getTableNameByLogId(crowdExceLogId));
                })
                .collect(Collectors.toList());

        List<Triple<Long, Long, List<String>>> grayscaleCrowds = cacheCrowdExecLogService.selectCrowdMaxExecLogTime(filteredCrowdIds)
                .stream()
                .map(m -> {
                    Long crowdId = Long.valueOf(m.get("crowd_id").toString());
                    Long time = getFinishTime(m.get("finish_exec_time"));
                    return Triple.of(crowdId, time, Collections.singletonList(adsConfig.getAdsTable()));
                })
                .collect(Collectors.toList());

        // 合并结果
        tripleList.addAll(grayscaleCrowds);
        log.info("getExecLogIdAndTablePairList 获取所有执行ID对应的表名:{}", JSON.toJSONString(tripleList));
        return tripleList;
    }

    private Long getFinishTime(Object finishExecTimeObj) {
        LocalDateTime finishExecTimeDate = parseFinishTime(finishExecTimeObj);
        return (finishExecTimeDate != null) ? com.xftech.cdp.infra.utils.DateUtil.getPreviousDayMillis(finishExecTimeDate) : com.xftech.cdp.infra.utils.DateUtil.getDefaultFinishExecTimeMillis();
    }

    private LocalDateTime parseFinishTime(Object finishExecTimeObj) {
        try {
            if (finishExecTimeObj instanceof LocalDateTime) {
                return (LocalDateTime) finishExecTimeObj;
            } else if (finishExecTimeObj instanceof String) {
                return LocalDateTime.parse((String) finishExecTimeObj);
            }
        }catch (Exception e){
            log.error("parseFinishTime error",e);
        }
       return null;
    }

    @Override
    public Integer countCrowdPackUserNum(String crowdPackNoStrs) {
        String[] crowdPackNos = crowdPackNoStrs.split(";");
        int packNum = crowdPackRepository.countCrowdPackNum(crowdPackNos);
        if (packNum < crowdPackNos.length) {
            throw new CrowdException("人群包编号有误");
        }
        return crowdPackRepository.countCrowdPackUserNum(crowdPackNos);
    }

    @Override
    public CrowdUploadResp uploadExcel(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.length() == 0) {
            throw new CrowdException("文件格式有误，请检查后再试");
        }
        String extension = getExtension(fileName);
        if (!"xls".equals(extension) && !"xlsx".equals(extension)) {
            throw new CrowdException("文件格式有误，请检查后再试");
        }
        if (file.isEmpty()) {
            throw new CrowdException("上传文件为空");
        }
        if (file.getSize() / (1024 * 1024) > 20) {
            throw new CrowdException("上传文件不能超过20M");
        }
        CrowdUploadLogDo crowdUploadLogDo = new CrowdUploadLogDo();

        try (InputStream inputStream = file.getInputStream()) {
            String dateDir = DateUtil.format(new Date(), "yyyy-MM-dd") + "/";
            String ossFileName = DigestUtils.md5DigestAsHex((LocalDateTimeUtil.now() + fileName).getBytes()) + "." + extension;
            String key = config.getUploadFilePath() + "/" + dateDir + ossFileName;
            ossUtil.upload(key, inputStream);

            crowdUploadLogDo.setFileName(fileName);
            crowdUploadLogDo.setOssFileName(ossFileName);
            crowdUploadLogDo.setOssUrl(key);
            crowdUploadLogDo.setCreatedOp(SsoUtil.get().getName());
            crowdUploadLogDo.setUpdatedOp(SsoUtil.get().getName());
            crowdUploadLogRepository.insert(crowdUploadLogDo);

        } catch (IOException e) {
            throw new CrowdException("uploadFile error");
        }
        CrowdUploadResp crowdUploadResp = new CrowdUploadResp();
        crowdUploadResp.setUploadLogId(crowdUploadLogDo.getId());
        return crowdUploadResp;
    }

    @Override
    public CrowdParseResp parseExcel(CrowdDownLoadReq crowdDownLoadReq) {
        CrowdUploadLogDo crowdUploadLogDo = crowdUploadLogRepository.selectById(crowdDownLoadReq.getUploadLogId());
        if (crowdUploadLogDo == null) {
            throw new CrowdException("不存在该文件上传记录");
        }
        CrowdPackDo crowdPackDo;
        if (crowdUploadLogDo.getCrowdId() == null) {
            crowdPackDo = new CrowdPackDo();
            crowdPackDo.setRefreshType(CrowdRefreshTypeEnum.NO_REFRESH.getCode());
            crowdPackDo.setFilterMethod(CrowdStatusEnum.INIT.getCode());
            crowdPackDo.setStatus(CrowdStatusEnum.EXECUTING.getCode());
            crowdPackDo.setUpdatedOpMobile(SsoUtil.get().getMobile());
            crowdPackDo.setCreatedOp(crowdUploadLogDo.getCreatedOp());
            crowdPackDo.setUpdatedOp(crowdUploadLogDo.getCreatedOp());
            crowdPackDo.setDFlag(1);
            crowdPackDo.setLatestRefreshTime(LocalDateTime.now());
            cacheCrowdPackService.insert(crowdPackDo);
        } else {
            crowdPackDo = crowdPackRepository.selectByIdAndNotFlag(crowdUploadLogDo.getCrowdId());
            if (crowdPackDo == null) {
                throw new CrowdException("不存在该人群包");
            }
            if (crowdPackDo.getStatus() == CrowdStatusEnum.EXECUTING.getCode()) {
                throw new CrowdException("解析中，请勿重复提交");
            }
            crowdPackDo.setStatus(CrowdStatusEnum.EXECUTING.getCode());
        }

        //更新上传记录表人群包id
        crowdUploadLogDo.setCrowdId(crowdPackDo.getId());
        crowdUploadLogRepository.updateById(crowdUploadLogDo);

        int successCount = 0;
        // 添加人群包执行日志记录
        CrowdExecLogDo execLog = new CrowdExecLogDo();
        execLog.setCrowdId(crowdPackDo.getId());
        execLog.setExecTime(LocalDateTime.now());
        execLog.setExecResult(CrowdExecResultEnum.EXECUTING.getCode());
        execLog.setExecMan(SsoUtil.get().getName());
        execLog.setCrowdPersonNum(successCount);
        crowdExecLogRepository.insert(execLog);

        try (InputStream fileStream = ossUtil.getInputStream(crowdUploadLogDo.getOssUrl())) {
            Collection<Future<Long>> futures = new ArrayList<>();
            EasyExcelFactory.read(fileStream, UploadCrowdModel.class, new UploadCrowdPageReadListen<UploadCrowdModel>(crowdConfig.getBatchSaveSize(), dataList -> {
                List<Long> userIdList = dataList.stream().map(item -> Long.parseLong(item.getUserId())).collect(Collectors.toList());
                //异步批量查询
                futures.add(asyncService.getValidUserNum(userIdList, execLog.getId(), crowdPackDo.getId()));
            })).sheet().headRowNumber(0).doRead();

            //等待异步线程执行完毕
            for (Future<Long> future : futures) {
                Long counts = future.get();
                successCount += counts;
            }
        } catch (InterruptedException e) {
            log.warn("error", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.warn("文件格式有误，请检查后再试", e);
        }

        // 更新执行日志状态，人数
        execLog.setExecResult(successCount > 0 ? CrowdExecResultEnum.SUCCESS.getCode() : CrowdExecResultEnum.FAIL.getCode());
        execLog.setCrowdPersonNum(successCount);
        cacheCrowdExecLogService.updateById(execLog);

        //更新人群包状态
        crowdPackDo.setCrowdPersonNum(successCount);
        crowdPackDo.setStatus(successCount > 0 ? CrowdStatusEnum.ENDED.getCode() : CrowdStatusEnum.FAILED.getCode());
        cacheCrowdPackService.updateById(crowdPackDo);

        CrowdParseResp crowdParseResp = new CrowdParseResp();
        crowdParseResp.setTotal(successCount);
        return crowdParseResp;
    }


    @Override
    public void downLoadFile(Long uploadLogId, HttpServletResponse response) {
        CrowdUploadLogDo crowdUploadLogDo = crowdUploadLogRepository.selectById(uploadLogId);
        if (crowdUploadLogDo != null) {
            ossUtil.downloadFile(response, crowdUploadLogDo.getOssFileName(), crowdUploadLogDo.getOssUrl());
        }
    }

    @Override
    public CrowdExecSqlResp getExecSql(Long crowdId) {
        CrowdExecSqlResp crowdExecSqlResp = new CrowdExecSqlResp();
        String snapshot = crowdExecSnapshotRepository.selectSnapshotByCrowdId(crowdId);
        if (StringUtils.isNotBlank(snapshot)) {
            JSONObject data = JSONObject.parseObject(snapshot);
            CrowdPackDo crowdPackDo = JSONObject.parseObject(data.getString("crowdPack"), CrowdPackDo.class);
            if (crowdPackDo != null) {
                crowdExecSqlResp.setRefreshTime(crowdPackDo.getLatestRefreshTime());
            }
            if (StringUtils.isNotBlank(data.getString("labelSqlPair"))) {
                for (Map.Entry<String, Object> labelSqlPair : data.getJSONObject("labelSqlPair").entrySet()) {
                    if (StringUtils.isNotBlank(labelSqlPair.getKey())) {
                        crowdExecSqlResp.setIncludeSql(labelSqlPair.getKey() + ");");
                    }
                    if (StringUtils.isNotBlank(data.getJSONObject("labelSqlPair").getString(labelSqlPair.getKey()))) {
                        crowdExecSqlResp.setExcludeSql(data.getJSONObject("labelSqlPair").getString(labelSqlPair.getKey()) + ");");
                    }
                }
            }
        }
        return crowdExecSqlResp;
    }

    @Override
    public List<CrowdPackDo> selectTodayByStatus(CrowdStatusEnum... statusEnum) {
        return crowdPackRepository.selectTodayByStatus(statusEnum);
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public String operateSql() {
        List<CrowdPackDo> crowdPackAll = crowdPackRepository.getCrowdPackAll(CrowdPullTypeEnum.USER_LABLE.getCode());
        int operate =0;
        for(CrowdPackDo t:crowdPackAll) {
            try {
                setSql(t);
                cacheCrowdPackService.updateById(t);
                operate++;
            } catch (Exception e) {
                log.error("operateSql error,crowId={}",t.getId(), e);
            }
        }
        return "total="+crowdPackAll.size()+"==operate="+operate;
    }

    @Override
    public ImmutableTriple<List<CrowdDetailDo>, Integer, Long> getCrowdDetailList(Long crowdId,String tableName, Long crowdMaxExecLogId, Long crowdDetailId, Long pageSize, CrowdContext crowdContent) {
        if (adsConfig.getAdsTable().equals(tableName)) {
            List<CrowdWereHouse> crowdWereHouses = adsUserLabelDetailInfoDfRepository.selectBatchStarrocks(tableName, LocalDateTime.now().minusDays(1),
                    crowdDetailId, pageSize, crowdId);
            Collections.sort(crowdWereHouses, Comparator.comparingLong(CrowdWereHouse::getCno));
            Integer total = crowdWereHouses.size();
            Long lastIndex = total== 0 ? 0:crowdWereHouses.get(total - 1).getCno();
            List<CrowdDetailDo> res = filterAndConvert(crowdWereHouses, crowdId, crowdContent);
            return ImmutableTriple.of(res, total, lastIndex);
        }
        List<CrowdDetailDo> rest = crowdDetailRepository.selectBatchByCrowdMaxExecLogId(tableName, crowdMaxExecLogId, crowdDetailId, pageSize);
        // todo 查询用户手机号
        if (!CollectionUtils.isEmpty(rest)) {
            int batchSize = crowdConfig.getBatcQueryUserMobileSize();
            com.google.common.collect.Lists.partition(rest, batchSize).forEach(items -> {
                List<Long> userNos = items.stream().map(CrowdDetailDo::getUserId).distinct().collect(Collectors.toList());
                List<MobileDTO> mobileDTOList = cisService.batchQueryMobileByUserNos(userNos);
                Map<Long, String> userMaps = mobileDTOList.stream().collect(Collectors.toMap(MobileDTO::getUserNo,
                        MobileDTO::getMobile, (k1, k2) -> k2));
                items.forEach(u -> {
                    if (StringUtils.isEmpty(u.getMobile())) {
                        u.setMobile(userMaps.getOrDefault(u.getUserId(), null));
                    }
                });
            });
        }
        return ImmutableTriple.of(rest, rest.size(), rest.size() == 0 ? 0:rest.get(rest.size() - 1).getId());
    }

    @Override
    public Optional<CrowdDetailDo> hasUserRecord(Long crowdId,Long userId, Long crowdExecLogId, String tableName) {
        if(adsConfig.getAdsTable().equals(tableName)){
            LocalDateTime finishTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(crowdExecLogId), ZoneId.systemDefault());
            return adsUserLabelDetailInfoDfRepository.hasUserRecord(tableName,userId,crowdId,finishTime)
                    .map(crowdWereHouse -> fromCrowdWereHouse(crowdId, crowdWereHouse));
        }
        return crowdDetailRepository.hasUserRecord(userId, crowdExecLogId, tableName);
    }

    @Override
    public CrowdDetailDo hasUserRecordByIn(Set<Long> crowdIds, Long userId, Long crowdExecLogId, String tableName) {
        LocalDateTime finishTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(crowdExecLogId), ZoneId.systemDefault());
        List<CrowdWereHouse> crowdWereHouses = adsUserLabelDetailInfoDfRepository.hasUserRecordByIn(tableName, userId, crowdIds, finishTime);
        if (CollectionUtils.isEmpty(crowdWereHouses)) {
            return null;
        } else {
            CrowdWereHouse crowdWereHouse = crowdWereHouses.get(0);
            return fromCrowdWereHouse(crowdWereHouse.getCrowdId(), crowdWereHouse);
        }
    }

    private List<CrowdDetailDo> filterAndConvert(List<CrowdWereHouse> crowdWereHouses, Long crowdMaxExecLogId, CrowdContext crowdContent) {
        List<CrowdDetailDo> crowdDetailDos = Lists.newArrayList();
        try {
            crowdDispatchStartRockService.filter(crowdContent, crowdWereHouses);
            for (CrowdWereHouse crowdWereHouse : crowdWereHouses) {
                crowdDetailDos.add(fromCrowdWereHouse(crowdMaxExecLogId, crowdWereHouse));
            }
        } catch (Exception e) {
            log.error("filterAndConvert error,req={}", JsonUtil.toJson(crowdWereHouses), e);
        }
        return crowdDetailDos;
    }

    CrowdDetailDo fromCrowdWereHouse(Long crowdMaxExecLogId,CrowdWereHouse crowdWereHouse){
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(crowdMaxExecLogId);
        crowdDetailDo.setCrowdExecLogId(-1l);
        crowdDetailDo.setUserId(crowdWereHouse.getAppUserId());
        crowdDetailDo.setApp(crowdWereHouse.getApp());
        crowdDetailDo.setInnerApp(crowdWereHouse.getInnerApp());
        crowdDetailDo.setMobile(AesUtil.mobileDecrypt(crowdWereHouse.getMobile(), config.getAdsMobileAesKey()));
        crowdDetailDo.setAppUserIdLast2(crowdWereHouse.getAppUserIdLast2());
        crowdDetailDo.setAbNum(crowdWereHouse.getAbNum());
        crowdDetailDo.setRegisterTime(crowdWereHouse.getRegisterTime());
        crowdDetailDo.setId(crowdWereHouse.getCno());
        return crowdDetailDo;
    }


    public List<CrowdExecLogDo> selectTodayExecLogByCrowdIds(List<Long> crowdIds) {
        return crowdExecLogRepository.selectExecLogByCrowdIdsAndTime(crowdIds,
                LocalDateTime.of(LocalDate.now(), LocalTime.MIN),
                LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
    }

    public List<CrowdExecLogDo> selectYesterdayExecLogByCrowdIds(List<Long> crowdIds) {
        return crowdExecLogRepository.selectExecLogByCrowdIdsAndTime(crowdIds,
                LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MIN),
                LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MAX));
    }

    @Override
    public void reportDailyCrowd() {
        //查出当日需要统计的人群
        List<CrowdPackDo> crowdPackDos = crowdPackRepository.selectTodayReportCrowd();
        List<Long> crowdIds = crowdPackDos.stream().map(CrowdPackDo::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crowdIds)) {
            log.info("未查询到存在当日执行的人群包");
            return;
        }
        //查出当日人群执行日志
        List<CrowdExecLogDo> crowdExecLogDos = selectTodayExecLogByCrowdIds(crowdIds);

        List<CrowdExecLogDo> crowdExecLogDoList = new ArrayList<>();
        //同一人群包如果有多条取最新一条
        crowdExecLogDos.stream()
                .collect(Collectors.toMap(CrowdExecLogDo::getCrowdId, Function.identity(), (t1, t2) -> t1.getId() > t2.getId() ? t1 : t2))
                .forEach((k, v) -> crowdExecLogDoList.add(v));

        List<ReportDailyCrowdDo> reportDailyCrowdDos = transformReportDailyCrowd(crowdExecLogDoList, crowdPackDos);

        //保存或修改 按照日期 + crowdId的维度
        reportDailyCrowdService.saveOrUpdateBatch(reportDailyCrowdDos);
        //统计完之后，记录统计记录
        reportDailyTaskService.saveOrUpdate(ReportDailyTypeEnum.CROWD.getCode());
    }

    private List<ReportDailyCrowdDo> transformReportDailyCrowd(List<CrowdExecLogDo> todayCrowdExecLogDoList, List<CrowdPackDo> crowdPackDos) {
        List<Long> crowdIds = crowdPackDos.stream().map(CrowdPackDo::getId).collect(Collectors.toList());
        //查询昨日人群执行人数
        List<CrowdExecLogDo> yesterdayExecLogDos = selectYesterdayExecLogByCrowdIds(crowdIds);
        List<CrowdExecLogDo> yesterdayExecLogDoList = new ArrayList<>();

        yesterdayExecLogDos.stream()
                .collect(Collectors.toMap(CrowdExecLogDo::getCrowdId, Function.identity(), (t1, t2) -> t1.getId() > t2.getId() ? t1 : t2))
                .forEach((k, v) -> yesterdayExecLogDoList.add(v));

        //转成 crowdId -> name
        Map<Long, String> crowdIdToName = crowdPackDos.stream().collect(Collectors.toMap(CrowdPackDo::getId, CrowdPackDo::getCrowdName));
        // crowdId -> crowdPersonNum
        Map<Long, Integer> crowdIdToYesterdayPersonNum = yesterdayExecLogDoList.stream().collect(HashMap::new, (map, item) -> map.put(item.getCrowdId(), item.getCrowdPersonNum()), HashMap::putAll);

        List<ReportDailyCrowdDo> reportDailyCrowdDoList = new ArrayList<>();
        for (CrowdExecLogDo crowdExecLogDo : todayCrowdExecLogDoList) {
            ReportDailyCrowdDo reportDailyCrowdDo = new ReportDailyCrowdDo();
            reportDailyCrowdDo.setDate(new Date());
            reportDailyCrowdDo.setCrowdId(crowdExecLogDo.getCrowdId());
            reportDailyCrowdDo.setCrowdName(crowdIdToName.get(crowdExecLogDo.getCrowdId()));
            reportDailyCrowdDo.setExecStartTime(crowdExecLogDo.getExecTime());
            reportDailyCrowdDo.setExecEndTime(crowdExecLogDo.getFinishExecTime() == null ? crowdExecLogDo.getUpdatedTime() : crowdExecLogDo.getFinishExecTime());
            reportDailyCrowdDo.setExecStatus(crowdExecLogDo.getExecResult());
            reportDailyCrowdDo.setFailReason(crowdExecLogDo.getFailReason());
            reportDailyCrowdDo.setCrowdTodaySum(crowdExecLogDo.getCrowdPersonNum());
            reportDailyCrowdDo.setCrowdYesterdaySum(crowdIdToYesterdayPersonNum.get(crowdExecLogDo.getCrowdId()));

            reportDailyCrowdDoList.add(reportDailyCrowdDo);
        }
        return reportDailyCrowdDoList;
    }

    @Override
    public CrowdReportDailyResp queryReportDailyList() {
        CrowdReportDailyResp crowdReportDailyResp = new CrowdReportDailyResp();
        //查询需要统计的人群包
        List<CrowdPackDo> crowdPackDos = crowdPackRepository.selectTodayReportCrowd();
        crowdReportDailyResp.setCrowdCount(crowdPackDos.size());

        ReportDailyTaskDo taskDo = reportDailyTaskService.selectTodayRecord(ReportDailyTypeEnum.CROWD.getCode());
        if (taskDo == null){
            return null;
        }
        crowdReportDailyResp.setRefreshTime(taskDo.getRefreshTime());

        //查询当天统计数据
        List<ReportDailyCrowdDo> reportDailyCrowdDoList =  reportDailyCrowdService.selectToday();

        int successCrowdNum = (int) reportDailyCrowdDoList.stream().filter(item -> Objects.equals(CrowdExecResultEnum.SUCCESS.getCode(), item.getExecStatus())).count();
        int failCrowdNum = (int) reportDailyCrowdDoList.stream().filter(item -> Objects.equals(CrowdExecResultEnum.FAIL.getCode(), item.getExecStatus())).count();
        crowdReportDailyResp.setSuccessCount(successCrowdNum);
        crowdReportDailyResp.setFailCount(failCrowdNum);
        long personSum = reportDailyCrowdDoList.stream().filter(item -> item.getCrowdTodaySum() != null).mapToLong(ReportDailyCrowdDo::getCrowdTodaySum).sum();
        crowdReportDailyResp.setPersonSum(personSum);

        List<ReportDailyCrowdDo> sortedReport =  reportDailyCrowdDoList.stream().sorted(Comparator.comparingInt(ReportDailyCrowdDo::getStatusSort)).collect(Collectors.toList());

        crowdReportDailyResp.setDetailList(transformResp(sortedReport));

        return crowdReportDailyResp;
    }

    @Override
    public PageResultResponse<CrowdRefreshInfoResp> crowdRefreshInfo(CrowdRefreshInfoReq crowdRefreshInfo) {
        Page<ReportDailyCrowdDo> records = reportDailyCrowdService.queryPageByCrowdId(crowdRefreshInfo);
        List<CrowdRefreshInfoResp> crowdRefreshInfoRespList = new ArrayList<>();
        records.getList().forEach(item -> {
            crowdRefreshInfoRespList.add(new CrowdRefreshInfoResp(item.getCrowdId(), LocalDateTimeUtil.format(item.getExecStartTime(), "yyyy-MM-dd HH:mm:ss"), item.getCrowdTodaySum()));
        });
        return new PageResultResponse<>(crowdRefreshInfoRespList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    @Override
    public boolean verifyCrowdPackByNewRandom(Long userId, Collection<CrowdPackDo> crowdPackCol) {

        for (CrowdPackDo crowdPackDo : crowdPackCol) {
            List<List<String>> newRandomLabels = crowdLabelService.getNewRandomLabels(crowdPackDo);
            List<String> includeLabels = newRandomLabels.get(0);
            List<String> excludeLabels = newRandomLabels.get(1);
            if ( !CollectionUtils.isEmpty(includeLabels)) {
                for (String label : includeLabels) {
                    NewRandom newRandom = JSON.parseObject(label, NewRandom.class);
                    DataSegment dataSegment = JSON.parseObject(newRandom.getCrowdLabelOption(), DataSegment.class);
                    Long randomNumber = randomNumService.generateRandomNumber(newRandom.getBizKey(), userId, 2);
                    for (DataSegment.Segment segment : dataSegment.getSegments()) {
                        if (segment.getMin() > randomNumber || segment.getMax() < randomNumber) {
                            log.info("[实时策略]人群包[新随机数][include]校验失败,userId:{},新随机数:{},人群包ID:{}", userId, randomNumber, crowdPackDo.getId());
                            return false;
                        }
                    }
                }
            }
            if ( !CollectionUtils.isEmpty(excludeLabels)) {
                for (String label : excludeLabels) {
                    NewRandom newRandom = JSON.parseObject(label, NewRandom.class);
                    DataSegment dataSegment = JSON.parseObject(newRandom.getCrowdLabelOption(), DataSegment.class);
                    Long randomNumber = randomNumService.generateRandomNumber(newRandom.getBizKey(), userId, 2);
                    for (DataSegment.Segment segment : dataSegment.getSegments()) {
                        if (segment.getMin() <= randomNumber && randomNumber <= segment.getMax()) {
                            log.info("[实时策略]人群包[新随机数][exclude]校验失败,userId:{},新随机数:{},人群包ID:{}", userId, randomNumber, crowdPackDo.getId());
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    private List<CrowdReportDailyDetailResp> transformResp(List<ReportDailyCrowdDo> sortedReport) {
        List<CrowdReportDailyDetailResp> result = new ArrayList<>();
        for (ReportDailyCrowdDo reportDailyCrowdDo : sortedReport) {
            CrowdReportDailyDetailResp crowdReportDailyDetailResp = new CrowdReportDailyDetailResp();
            BeanUtils.copyProperties(reportDailyCrowdDo,crowdReportDailyDetailResp);
            if (Objects.equals(CrowdExecResultEnum.SUCCESS.getCode(), reportDailyCrowdDo.getExecStatus())){
                CrowdExecResultEnum statusEnum = CrowdExecResultEnum.getInstance(reportDailyCrowdDo.getExecStatus());
                crowdReportDailyDetailResp.setBetweenTime(Duration.between(reportDailyCrowdDo.getExecStartTime(),reportDailyCrowdDo.getExecEndTime()).toMinutes());
                crowdReportDailyDetailResp.setExecStatus(statusEnum!=null?statusEnum.getDescription():"未知");
                if (reportDailyCrowdDo.getCrowdTodaySum()!=null && reportDailyCrowdDo.getCrowdYesterdaySum() !=null && reportDailyCrowdDo.getCrowdYesterdaySum() != 0 ){
                    String growthRate = new BigDecimal(reportDailyCrowdDo.getCrowdTodaySum()-reportDailyCrowdDo.getCrowdYesterdaySum()).multiply(new BigDecimal(100)).divide(new BigDecimal(reportDailyCrowdDo.getCrowdYesterdaySum()), 2, RoundingMode.HALF_UP) + "%";
                    crowdReportDailyDetailResp.setGrowthRate(growthRate);
                }
            }
            result.add(crowdReportDailyDetailResp);
        }
        return result;
    }

    String getNewRandomInfo(String labelValue) {
        NewRandom newRandom = JSON.parseObject(labelValue, NewRandom.class);
        return randomNumService.getRandomItem(newRandom.getBizKey());
    }

    public Page<CrowdPackDo> queryPageByLabelId(AssociatedCrowdsReq associatedCrowdsReq) {
        return crowdPackRepository.queryPageByLabelId(associatedCrowdsReq);
    }

    public Integer querySizeByLabelId(Long labelId) {
        return crowdPackRepository.querySizeByLabelId(labelId);
    }

    @Override
    public Set<Long> resetBigDataErrorResultCrowdPacks() {
        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        List<CrowdPackDo> crowdPackDos = Optional.ofNullable(crowdPackRepository.selectBigDataErrorResultCrowdPacks(date)).orElse(Lists.newArrayList());
        Set<Long> crowdIds = crowdPackDos.stream().map(CrowdPackDo::getId).collect(Collectors.toSet());
        int effectRecords = 0;
        if (!CollectionUtils.isEmpty(crowdIds)) {
            effectRecords = crowdPackRepository.updateBigDataErrorResultCrowdPacks(crowdIds);
        }
        log.info("CrowdPackServiceImpl resetBigDataErrorResultCrowdPacks date={} crowdIds={} effectRecords={}", date, JSON.toJSONString(crowdIds), effectRecords);
        return crowdIds;
    }

}
