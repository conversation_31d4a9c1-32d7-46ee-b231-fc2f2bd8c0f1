package com.xftech.cdp.domain.cache.impl;

import com.alibaba.fastjson.JSONArray;
import com.xftech.cdp.domain.cache.CacheStrategyMarketChannelService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.cache.CacheFlowCtrlSerivce;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
@Slf4j
public class CacheFlowCtrlServiceImpl implements CacheFlowCtrlSerivce {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private FlowCtrlRepository flowCtrlRepository;
    @Autowired
    private CacheStrategyMarketChannelService cacheStrategyMarketChannelService;
    @Autowired
    private CacheStrategyService cacheStrategyService;

    /**
     * 插入
     *
     * @param flowCtrlDo 对象
     * @return 是否插入成功标识
     */
    public boolean insert(FlowCtrlDo flowCtrlDo) {
        boolean isInsert = flowCtrlRepository.insert(flowCtrlDo);
        if (isInsert) {
            this.updateFlowCtrlRuleCache(flowCtrlDo.getEffectiveStrategy());
        }
        return isInsert;
    }

    /**
     * 根据主键id更新
     *
     * @param flowCtrlDo 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(FlowCtrlDo flowCtrlDo) {
        boolean isUpdate = flowCtrlRepository.updateById(flowCtrlDo);
        if (isUpdate) {
            this.updateFlowCtrlRuleCache(flowCtrlDo.getEffectiveStrategy());
        }
        return isUpdate;
    }

    public boolean updateStatusById(FlowCtrlDo flowCtrlDo) {
        boolean isUpdate = flowCtrlRepository.updateStatusById(flowCtrlDo);
        if (isUpdate) {
            this.updateFlowCtrlRuleCache(flowCtrlDo.getEffectiveStrategy());
        }
        return isUpdate;
    }

    public void closeByStrategyId(Long strategyId) {
        flowCtrlRepository.closeByStrategyId(strategyId);
        this.updateFlowCtrlRuleCache(String.valueOf(strategyId));
    }

    @Override
    public List<FlowCtrlDo> getFlowCtrlConfig(Integer marketChannel, Long strategyId, String bizType) {

        if (WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlRuleSwitch")) {
            log.info("getNewFlowCtrlConfig 新版流控规则生效 marketChannel:{},strategyId:{},bizType:{}", marketChannel, strategyId, bizType);
            String newRedisKey = RedisKeyUtils.genNewFcListByStrategyIdKey(strategyId, marketChannel);
            String data = redisUtils.get(newRedisKey);
            List<FlowCtrlDo> newFlowCtrlDoList = JSONArray.parseArray(data, FlowCtrlDo.class);
            if (CollectionUtils.isEmpty(newFlowCtrlDoList)) {
                newFlowCtrlDoList = flowCtrlRepository.getNewFlowCtrlConfig(marketChannel, strategyId, bizType);
                redisUtils.set(newRedisKey, newFlowCtrlDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
            return newFlowCtrlDoList;
        } else {
            String redisKey = RedisKeyUtils.genFcListByStrategyIdKey(strategyId, marketChannel);
            String data = redisUtils.get(redisKey);
            List<FlowCtrlDo> flowCtrlDoList = JSONArray.parseArray(data, FlowCtrlDo.class);
            if (CollectionUtils.isEmpty(flowCtrlDoList)) {
                flowCtrlDoList = flowCtrlRepository.getFlowCtrlConfig(marketChannel, strategyId);
                redisUtils.set(redisKey, flowCtrlDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
            return flowCtrlDoList;
        }
    }

    /**
     *
     * @param effectiveStrategy 生效策略 0-全部，多个逗号分隔
     */
    @Override
    public void updateFlowCtrlRuleCache(String effectiveStrategy) {
        if (StringUtils.isBlank(effectiveStrategy)) {
            return;
        }
        if ("0".equals(effectiveStrategy)) {
            return;
        }
        String[] strategyIdArr = effectiveStrategy.split(",");
        for (String strategyIdStr : strategyIdArr) {
            if (StringUtils.isBlank(strategyIdStr)) {
                return;
            }
            Long strategyId = Long.parseLong(strategyIdStr);
            List<StrategyMarketChannelDo> strategyMarketChannelDoList = cacheStrategyMarketChannelService.selectByStrategyId(strategyId);
            if (!CollectionUtils.isEmpty(strategyMarketChannelDoList)) {
                for (StrategyMarketChannelDo strategyMarketChannelDo : strategyMarketChannelDoList) {
                    String redisKey = RedisKeyUtils.genFcListByStrategyIdKey(strategyId, strategyMarketChannelDo.getMarketChannel());
                    List<FlowCtrlDo> flowCtrlConfig = flowCtrlRepository.getFlowCtrlConfig(strategyMarketChannelDo.getMarketChannel(), strategyId);
                    redisUtils.set(redisKey, flowCtrlConfig, RedisUtils.DEFAULT_EXPIRE_SECONDS);
                }
            }
        }
//        List<StrategyDo> strategyDoList = strategyRepository.listT0ExecutingStrategy();
//        if (!CollectionUtils.isEmpty(strategyDoList)) {
//            for (StrategyDo strategyDo : strategyDoList) {
//                List<StrategyMarketChannelDo> strategyMarketChannelDoList = cacheStrategyMarketChannelService.selectByStrategyId(Long.valueOf(strategyDo.getId()));
//                if (!CollectionUtils.isEmpty(strategyMarketChannelDoList)) {
//                    for (StrategyMarketChannelDo strategyMarketChannelDo : strategyMarketChannelDoList) {
//                        String redisKey = RedisKeyUtils.genFcListByStrategyIdKey(strategyDo.getId(), strategyMarketChannelDo.getMarketChannel());
//                        List<FlowCtrlDo> flowCtrlConfig = flowCtrlRepository.getFlowCtrlConfig(strategyMarketChannelDo.getMarketChannel(), strategyDo.getId());
//                        redisUtils.set(redisKey, flowCtrlConfig, RedisUtils.DEFAULT_EXPIRE_SECONDS);
//                    }
//                }
//            }
//        }
    }

    @Override
    public List<FlowCtrlDo> getFlowCtrlConfigs(Integer strategyType, Long strategyId) {
        return flowCtrlRepository.getFlowCtrlConfigs(strategyType, strategyId);
    }
}
