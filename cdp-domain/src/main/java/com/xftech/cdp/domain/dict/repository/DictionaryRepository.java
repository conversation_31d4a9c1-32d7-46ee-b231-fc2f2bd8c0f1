package com.xftech.cdp.domain.dict.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.dictionary.po.DictionaryDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @<NAME_EMAIL>
 */
@Component
public class DictionaryRepository {
    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public DictionaryDo selectById(Long id) {
        return DBUtil.selectOne("dictionaryMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(DictionaryDo param) {
        return DBUtil.insert("dictionaryMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(DictionaryDo param) {
        return DBUtil.update("dictionaryMapper.updateByPrimaryKeySelective", param) > 0;
    }

    /**
     * 根据字典类型列表查询
     *
     * @param dictTypeList dictTypeList
     * @return dictTypeList对应的所有字典
     */
    public List<DictionaryDo> selectByDictTypeListAndBusinessType(List<String> dictTypeList,String businessType) {
        Map<String, Object> param = new HashMap<>();
        param.put("dictTypeList",dictTypeList);
        param.put("businessType",businessType);
        return DBUtil.selectList("dictionaryMapper.selectByDictTypeListAndBusinessType",param);
    }

    public List<DictionaryDo> selectAllByBusinessType(String businessType) {
        return DBUtil.selectList("dictionaryMapper.selectAllByBusinessType",businessType);
    }

    public String selectAllBusinessType(){
        return DBUtil.selectOne("dictionaryMapper.selectAllBusinessType",null);
    }
}
