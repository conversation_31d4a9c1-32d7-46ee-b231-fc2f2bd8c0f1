package com.xftech.cdp.domain.flowctrl.service;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlCreateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlDetailReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlEffectiveContentReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlListReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlOperateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlUpdateReq;
import com.xftech.cdp.api.dto.resp.flowctrl.EffectiveContentListResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlDetailResp;
import com.xftech.cdp.api.dto.resp.flowctrl.FlowCtrlListResp;
import com.xftech.cdp.domain.flowctrl.model.enums.EffectiveContentTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;

import java.util.List;
import java.util.Optional;

/**
 * 流控前后端交互
 *
 * @<NAME_EMAIL>
 * @date 2023/3/24 17:06
 */
public interface FlowCtrlService {
    /**
     * 新建流控规则
     *
     * @param flowCtrlCreateReq 请求参数
     * @return 是否新建成功
     */
    boolean insert(FlowCtrlCreateReq flowCtrlCreateReq);

    /**
     * 更新流控规则
     *
     * @param flowCtrlUpdateReq 请求参数
     * @return 是否更新成功
     */
    boolean update(FlowCtrlUpdateReq flowCtrlUpdateReq);

    /**
     * 分页获取流控规则列表
     *
     * @param flowCtrlListReq 请求参数
     * @return 当前页流控规则列表
     */
    PageResultResponse<FlowCtrlListResp> list(FlowCtrlListReq flowCtrlListReq);

    /**
     * 获取流控规则详情
     *
     * @param flowCtrlDetailReq 请求参数
     * @return 流控规则详情
     */
    FlowCtrlDetailResp getDetail(FlowCtrlDetailReq flowCtrlDetailReq);

    /**
     * 操作流控规则
     *
     * @param flowCtrlOperateReq 请求参数
     * @return 是否操作成功
     */
    boolean operate(FlowCtrlOperateReq flowCtrlOperateReq);

    /**
     * 获取生效内容列表
     *
     * @param flowCtrlEffectiveContentReq 请求参数
     * @return 生效内容列表
     */
    List<EffectiveContentListResp> getEffectiveContentList(FlowCtrlEffectiveContentReq flowCtrlEffectiveContentReq);

    /**
     * 获取符合当前执行渠道的流控规则
     *
     * @param flowCtrlTypeEnum  流控类型
     * @param effectiveTypeEnum 生效类型
     * @param strategyId        策略ID
     * @param channel           渠道
     * @return 流控规则
     */
    Optional<FlowCtrlDo> getByType(FlowCtrlTypeEnum flowCtrlTypeEnum, EffectiveContentTypeEnum effectiveTypeEnum, Long strategyId, Integer channel);
}
