package com.xftech.cdp.domain.flowctrl.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlListReq;
import com.xftech.cdp.domain.flowctrl.model.enums.EffectiveContentTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/27 9:48
 */
@Component
public class FlowCtrlRepository {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public FlowCtrlDo selectById(Long id) {
        return DBUtil.selectOne("flowCtrlMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param flowCtrlDo 对象
     * @return 是否插入成功标识
     */
    public boolean insert(FlowCtrlDo flowCtrlDo) {
        return DBUtil.insert("flowCtrlMapper.insertSelective", flowCtrlDo) > 0;
    }

    /**
     * 根据主键id删除
     *
     * @param id 主键id
     * @return 是否删除成功标识
     */
    public boolean delete(Long id) {
        return DBUtil.delete("flowCtrlMapper.deleteByPrimaryKey", id) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param flowCtrlDo 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(FlowCtrlDo flowCtrlDo) {
        return DBUtil.update("flowCtrlMapper.updateByPrimaryKeySelective", flowCtrlDo) > 0;
    }

    /**
     * 获取流控规则配置
     *
     * @param marketChannel 渠道
     * @param strategyId    策略ID
     * @return 流控规则配置
     */
    public List<FlowCtrlDo> getFlowCtrlConfig(Integer marketChannel, Long strategyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("marketChannel", marketChannel);
        params.put("strategyId", strategyId);
        return DBUtil.selectList("flowCtrlMapper.getFlowCtrlConfig", params);
    }

    public List<FlowCtrlDo> getNewFlowCtrlConfig(Integer marketChannel, Long strategyId, String bizType) {
        Map<String, Object> params = new HashMap<>();
        params.put("marketChannel", marketChannel);
        params.put("strategyId", strategyId);
        params.put("bizType", bizType);
        return DBUtil.selectList("flowCtrlMapper.getFlowCtrlConfigNew", params);
    }

    /**
     * @return
     */
    public List<FlowCtrlDo> selectAllEffectContentByType(Integer type, Long flowCtrlId, String bizType) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", type);
        params.put("flowCtrlId", flowCtrlId);
        if (!"all".equals(bizType)) {
            params.put("bizType", bizType);
        }
        return DBUtil.selectList("flowCtrlMapper.selectAllEffectContentByType", params);
    }

    public FlowCtrlDo selectByName(String name) {
        return DBUtil.selectOne("flowCtrlMapper.selectByRuleName", name);
    }

    public Page<FlowCtrlDo> selectByPage(FlowCtrlListReq flowCtrlListReq) {
        return DBUtil.selectPage("flowCtrlMapper.selectAllRule", flowCtrlListReq, flowCtrlListReq.getBeginNum(), flowCtrlListReq.getSize());
    }

    public boolean updateStatusById(FlowCtrlDo flowCtrlDo) {
        return DBUtil.update("flowCtrlMapper.updateStatusByPrimaryKey", flowCtrlDo) > 0;
    }

    public FlowCtrlDo getByType(FlowCtrlTypeEnum flowCtrlTypeEnum, EffectiveContentTypeEnum effectiveTypeEnum, Long strategyId, Integer channel) {
        Map<String, Object> params = new HashMap<>();
        params.put("type", flowCtrlTypeEnum.getType());
        params.put("effective", getEffectiveContent(flowCtrlTypeEnum, effectiveTypeEnum, strategyId, channel));
        return DBUtil.selectOne("flowCtrlMapper.getByType", params);
    }

    private Object getEffectiveContent(FlowCtrlTypeEnum flowCtrlTypeEnum, EffectiveContentTypeEnum effectiveTypeEnum, Long strategyId, Integer channel) {
        if (flowCtrlTypeEnum == FlowCtrlTypeEnum.CHANNEL) {
            return effectiveTypeEnum == EffectiveContentTypeEnum.SPECIAL ? channel : 0;
        } else {
            return effectiveTypeEnum == EffectiveContentTypeEnum.SPECIAL ? strategyId : 0;
        }
    }

    public FlowCtrlDo selectInstantStrategyRule(Integer strategyType, String effectiveStrategy) {
        Map<String, Object> params = new HashMap<>();
        params.put("strategyType", strategyType);
        params.put("effectiveStrategy", effectiveStrategy);
        return DBUtil.selectOne("flowCtrlMapper.selectInstantStrategyRule", params);
    }

    public void closeByStrategyId(Long strategyId) {
        DBUtil.update("flowCtrlMapper.closeByStrategyId", String.valueOf(strategyId));
    }

    public List<FlowCtrlDo> getFlowCtrlConfigs(Integer strategyType, Long strategyId) {
        Map<String, Object> params = new HashMap<>();
        params.put("strategyType", strategyType);
        params.put("effectiveStrategy", strategyId);
        return DBUtil.selectList("flowCtrlMapper.selectInstantStrategyRulesOnline", params);
    }
}
