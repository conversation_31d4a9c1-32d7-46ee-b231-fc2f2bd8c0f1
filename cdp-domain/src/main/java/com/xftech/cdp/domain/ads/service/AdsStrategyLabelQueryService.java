package com.xftech.cdp.domain.ads.service;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/20 9:24
 */
public interface AdsStrategyLabelQueryService {

    /**
     * 标签查询
     *
     * @param strategyId      策略ID
     * @param marketChannelId 渠道ID
     * @param marketChannel   渠道类型
     * @param app             app
     * @param list            当前批人群明细
     * @return 当前批人群明细
     */
    List<CrowdDetailDo> queryLabelHandler(Long strategyId, Long marketChannelId, Integer marketChannel, String app, List<CrowdDetailDo> list);

}
