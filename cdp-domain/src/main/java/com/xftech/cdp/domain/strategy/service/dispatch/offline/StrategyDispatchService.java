package com.xftech.cdp.domain.strategy.service.dispatch.offline;

import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo;
import org.springframework.lang.NonNull;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
public interface StrategyDispatchService {
    /**
     * 执行策略
     *
     * @param marketChannelId 触达渠道ID
     */
    void execute(@NonNull Long marketChannelId, DispatchTaskDo dispatchTaskDo);

    /**
     * 策略失败批次重试
     *
     * @param strategyExecLogId 策略执行日志ID
     */
    void retry(@NonNull Long strategyExecLogId, DispatchTaskDo dispatchTaskDo);
}
