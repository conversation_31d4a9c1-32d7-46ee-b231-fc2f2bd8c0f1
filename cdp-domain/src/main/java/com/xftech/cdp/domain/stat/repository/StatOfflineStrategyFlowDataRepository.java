package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.stat.entity.StatOfflineStrategyFlowDataEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/27 14:08
 */
@Component
public class StatOfflineStrategyFlowDataRepository {
    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public StatOfflineStrategyFlowDataEntity selectById(Long id) {
        return DBUtil.selectOne("statOfflineStrategyFlowDataMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StatOfflineStrategyFlowDataEntity param) {
        return DBUtil.insert("statOfflineStrategyFlowDataMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StatOfflineStrategyFlowDataEntity param) {
        return DBUtil.update("statOfflineStrategyFlowDataMapper.updateByPrimaryKeySelective", param) > 0;
    }

    public Page<StatOfflineStrategyFlowDataEntity> selectByStrategyIdAndPage(Long strategyId, Integer pageNum, Integer pageSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        return DBUtil.selectPage("statOfflineStrategyFlowDataMapper.selectByStrategyId", param, pageNum, pageSize);
    }


    public List<StatOfflineStrategyFlowDataEntity> selectByStrategyId(Long strategyId) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        return DBUtil.selectList("statOfflineStrategyFlowDataMapper.selectByStrategyId", param);
    }


    public StatOfflineStrategyFlowDataEntity getByStrategyIdAndBizDate(Long strategyId, LocalDateTime bizDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("bizDate", bizDate);
        return DBUtil.selectOne("statOfflineStrategyFlowDataMapper.getByStrategyIdAndBizDate", param);
    }
}
