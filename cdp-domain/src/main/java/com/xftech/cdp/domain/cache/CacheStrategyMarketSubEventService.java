package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;

import java.util.List;

public interface CacheStrategyMarketSubEventService {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
//    StrategyMarketSubEventDo selectById(Long id);

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    boolean insert(StrategyMarketSubEventDo param);

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    boolean updateById(StrategyMarketSubEventDo param);

    /**
     * 根据事件ID查询
     *
     * @param eventId 事件ID
     * @return 子事件集合
     */
    List<StrategyMarketSubEventDo> getByEventId(Long eventId);

    List<StrategyMarketSubEventDo> getByStrategyId(Long strategyId);

    /**
     * 插入
     *
     * @param strategyMarketSubEventList 对象
     * @return 是否插入成功标识
     */
    boolean insertBatch(List<StrategyMarketSubEventDo> strategyMarketSubEventList );


    /**
     * 插入
     *
     * @param delSubEventIdList 对象
     */
    void deleteBatch(List<Long> delSubEventIdList);

    void refreshCacheListByEventId(Long eventId);

}
