package com.xftech.cdp.domain.mq.impl;

import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.domain.mq.RabbitMqService;
import com.xftech.cdp.infra.rabbitmq.factory.impl.BizEventMqService;
import com.xftech.cdp.infra.rabbitmq.factory.impl.CouponMqService;
import com.xftech.cdp.infra.rabbitmq.factory.impl.SmsMqService;
import com.xftech.cdp.infra.rabbitmq.vo.MqSwitchXxlJobParam;
import com.xftech.rabbitmq.UdpMqConsumerManager;
import com.xftech.rabbitmq.core.UdpMqConsumer;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collection;
import java.util.concurrent.TimeoutException;

@Service
public class RabbitMqServiceImpl implements RabbitMqService {

    private static final IUdpLogger logger = LogUtil.getLogger(RabbitMqServiceImpl.class);

    public static final String BZ_CONSUMER_CONN_01 = "BZ_CONSUMER_CONN_01";
    public static final String BZ_DELAY_CONSUMER_CONN_01 = "BZ_DELAY_CONSUMER_CONN_01";
    public static final String BZ_DISPATCH_CONSUMER_CONN_01 = "BZ_DISPATCH_CONSUMER_CONN_01";
    public static final String BZ_DECISION_CONSUMER_CONN_01 = "BZ_DECISION_CONSUMER_CONN_01";

    @Autowired
    private SmsMqService smsMqService;
    @Autowired
    private CouponMqService couponMqService;
    @Autowired
    private BizEventMqService bizEventMqService;

    @Override
    public void switchRabbitMq(MqSwitchXxlJobParam mqSwitchXxlJobParam) throws IOException, TimeoutException {
        if ("info".equals(mqSwitchXxlJobParam.getSwitchType())) {
            Collection<String> consumerInfo = UdpMqConsumerManager.getInstance().getConsumerInfo(mqSwitchXxlJobParam.getConnectId());
            logger.info("mq connectId：{}，info：{}", mqSwitchXxlJobParam.getConnectId(), consumerInfo);
            XxlJobLogger.log("mq connectId：{}，info：{}", mqSwitchXxlJobParam.getConnectId(), consumerInfo);
            return;
        }
        if (mqSwitchXxlJobParam.getStatus() == Constants.FAIL_STATUS) {
            if (mqSwitchXxlJobParam.getConnectId() != null) {
                this.closeConnection(mqSwitchXxlJobParam.getConnectId());
            }
            return;
        }
        if (mqSwitchXxlJobParam.getStatus() == Constants.SUCCESS_STATUS) {
            UdpMqConsumer udpMqConsumer = UdpMqConsumerManager.getInstance().getUdpMqConsumer(mqSwitchXxlJobParam.getConnectId());
            if ("sms".equals(mqSwitchXxlJobParam.getType())) {
                if (mqSwitchXxlJobParam.getConnectId() == null) {
                    smsMqService.init();
                    return;
                }
                smsMqService.createConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
            }
            if ("coupon".equals(mqSwitchXxlJobParam.getType())) {
                if (mqSwitchXxlJobParam.getConnectId() == null) {
                    couponMqService.init();
                    return;
                }
                couponMqService.createConsumer(UdpMqConsumerManager.getInstance().getUdpMqConsumer(mqSwitchXxlJobParam.getConnectId()), mqSwitchXxlJobParam.getChannelId());
                couponMqService.createDeadLetterConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
            }
            if ("event".equals(mqSwitchXxlJobParam.getType())) {
                // channelId为空，初始化连接、消费者
                if (mqSwitchXxlJobParam.getChannelId() == null) {
                    if (BZ_CONSUMER_CONN_01.equals(mqSwitchXxlJobParam.getConnectId())) {
                        bizEventMqService.initBizConsumer();
                    } else if (BZ_DELAY_CONSUMER_CONN_01.equals(mqSwitchXxlJobParam.getConnectId())) {
                        bizEventMqService.initDelayConsumer();
                    } else if (BZ_DISPATCH_CONSUMER_CONN_01.equals(mqSwitchXxlJobParam.getConnectId())) {
                        bizEventMqService.initDispatchConsumer();
                    } else if (BZ_DECISION_CONSUMER_CONN_01.equals(mqSwitchXxlJobParam.getConnectId())) {
                        bizEventMqService.initDecisionConsumer();
                    }
                    return;
                }
                if ("hlBiz".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createMqHighLevelConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("mlBiz".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createMqMiddleLevelConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("hlDelay".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createHighLevelDelayConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("mlDelay".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createMiddleLevelDelayConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("llDelay".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createLowLevelDelayConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("smsDispatch".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createSmsDispatchConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("increaseAmtDispatch".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createIncreaseAmtDispatchConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("lifeRightsDispatch".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createLifeRightsDispatchConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("xDayInterestFreeDispatch".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createXDayInterestFreeDispatchConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("aiDispatch".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createAiDispatchConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("teleDispatch".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createTeleDispatchConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("couponDispatch".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createCouponDispatchConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("noMarket".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createNoMarketConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
                if ("decisionResult".equals(mqSwitchXxlJobParam.getQueueType())) {
                    bizEventMqService.createDecisionResultConsumer(udpMqConsumer, mqSwitchXxlJobParam.getChannelId());
                }
            }
        }
    }

    public void closeConnection(String connectionId) {
        try {
            UdpMqConsumerManager.getInstance().closeUdpMqConsumer(connectionId);
        } catch (Exception e) {
            logger.warn("mq connection close fail.", e);
            XxlJobLogger.log(e);
        }
        logger.info("mq connection close finish.");
        XxlJobLogger.log("mq connection close finish");
    }
}
