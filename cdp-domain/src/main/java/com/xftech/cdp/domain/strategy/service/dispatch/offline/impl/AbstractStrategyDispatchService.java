package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xftech.cdp.api.dto.req.DispatchConfig;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.strategy.StrategyXxlJobParam;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoVersionRepository;
import com.xftech.cdp.distribute.crowd.repository.CrowdSliceRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.distribute.offline.engine.DistributeOfflineEngineDispatchServiceImpl;
import com.xftech.cdp.distribute.offline.repository.StrategySliceExecLogRepository;
import com.xftech.cdp.distribute.oss.StrategyOssReaderService;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelQueryService;
import com.xftech.cdp.domain.ads.service.impl.AbstractAdsStrategyLabelService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.factory.AbsCrowdOptService;
import com.xftech.cdp.domain.crowd.factory.CrowdOptFactory;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushBatchStatusEnum;
import com.xftech.cdp.domain.crowd.model.enums.SliceExecLogEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPushBatchRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.impl.CrowdPushBatchServiceImpl;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlCoreService;
import com.xftech.cdp.domain.param.service.TemplateParamQueryService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.domain.strategy.model.dto.StrategyCycleDayConfig;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.model.strategy.DispatchTaskExtBO;
import com.xftech.cdp.domain.strategy.repository.StrategyCrowdPackRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.repository.StrategySnapshotRepository;
import com.xftech.cdp.domain.strategy.service.*;
import com.xftech.cdp.domain.strategy.service.flow.*;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.randomnum.RandomNumClient;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdExecLogDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import com.xftech.cdp.infra.utils.*;
import com.xftech.xxljob.XxlJobAdminClient;
import com.xftech.xxljob.model.XxlJobDto;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.util.Json;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.roaringbitmap.longlong.Roaring64Bitmap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK;
import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_OSS_CROWD_PACK_WHITELIST;
import static java.util.stream.Collectors.groupingBy;

/**
 * 策略执行核心逻辑
 */
@Slf4j
@Getter
public abstract class AbstractStrategyDispatchService {

    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private StrategyService strategyService;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private XxlJobAdminClient xxlJobAdminClient;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private FlowCtrlCoreService flowCtrlCoreService;
    @Autowired
    private StrategyGroupService strategyGroupService;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private CrowdPushBatchRepository crowdPushBatchRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private UserDispatchDetailService userDispatchDetailService;
    @Autowired
    private StrategySnapshotRepository strategySnapshotRepository;
    @Autowired
    private StrategyCrowdPackRepository strategyCrowdPackRepository;
    @Autowired
    private UserBlankGroupDetailService userBlankGroupDetailService;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private TemplateParamQueryService templateParamQueryService;
    @Autowired
    private AdsStrategyLabelQueryService adsStrategyLabelQueryService;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private StrategyExecCycleCounterService strategyExecCycleCounterService;
    @Autowired
    private StrategyExecCycleService strategyExecCycleService;
    @Autowired
    private RandomNumClient randomNumClient;
    @Autowired
    private DispatchTaskService dispatchTaskService;
    @Autowired
    private CrowdOptFactory crowdOptFactory;
    @Autowired
    private AbstractAdsStrategyLabelService abstractAdsStrategyLabelService;
    @Autowired
    private StrategyFlowService strategyFlowService;
    @Autowired
    private StrategyFlowNodeService flowNodeService;
    @Autowired
    private StrategyFlowBatchService flowBatchService;
    @Autowired
    private DispatchCrowdService dispatchCrowdService;
    @Autowired
    private StrategyOssReaderService ossReaderService;
    @Autowired
    private CrowdInfoVersionRepository crowdInfoVersionRepository;
    @Autowired
    private CrowdSliceRepository crowdSliceRepository;
    @Autowired
    private StrategySliceExecLogRepository strategySliceExecLogRepository;
    @Autowired
    private CrowdInfoRepository crowdInfoRepository;
    @Autowired
    private DistributeOfflineEngineDispatchServiceImpl strategyOfflineEngineDispatchService;

    /**
     * 策略执行入口 非离线引擎策略
     * 周期策略 普通离线策略
     *
     * @param marketChannelId 渠道ID
     */
    public void execute(@NonNull Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
        StrategyContext strategyContext = initContext(marketChannelId, dispatchTaskDo);
        try {
            beginExecute(strategyContext);
            preHandler(strategyContext);
            coreLogicExecute(strategyContext);
            successExecute(strategyContext);
            dispatchTaskService.updateTaskFinish(dispatchTaskDo, "SUCCEED");
        } catch (Exception e) {
            dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, e.getMessage());

            StrategyDo strategyDo = strategyContext.getStrategyDo();
            StrategyMarketChannelDo marketChannelDo = strategyContext.getStrategyMarketChannelDo();
            StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(marketChannelDo.getMarketChannel());
            failedExecute(strategyContext);
            List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
            atMobileList.add(strategyDo.getUpdatedOpMobile());
            strategyDo.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobileList, e);
            log.warn("策略执行异常, 策略ID:{}, 渠道类型:{}, 渠道ID:{}", strategyDo.getId(), channelEnum.getDescription(), marketChannelDo.getId(), e);
        } finally {
            isEnded(strategyContext);
        }
    }

    /**
     * 初始化数据
     */
    protected StrategyContext initContext(@NonNull Long channelId, DispatchTaskDo dispatchTaskDo) {
        LocalDateTime now = LocalDateTime.now();
        // 画布判定
        boolean isFlow = dispatchTaskDo != null && DispatchTaskBizTypeEnum.isFlow(dispatchTaskDo.getBizType());
        DispatchTaskExtBO.FlowExt flowExt = null;
        if (isFlow) {
            flowExt = JsonUtil.parse(dispatchTaskDo.getExtDetail(), DispatchTaskExtBO.FlowExt.class);
        }

        StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(channelId);
        if (strategyMarketChannelDo == null) {
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "不存在该触达渠道");
            throw new StrategyException(String.format("不存在该触达渠道,触达渠道id:%s", channelId));
        }

        StrategyGroupDo strategyGroupDo = strategyGroupRepository.selectById(strategyMarketChannelDo.getStrategyGroupId());
        if (strategyGroupDo == null) {
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "不存在该策略分组");
            throw new StrategyException(String.format("不存在该策略分组,策略分组id:%s", strategyMarketChannelDo.getStrategyGroupId()));
        }

        StrategyDo strategyDo = strategyRepository.selectById(strategyMarketChannelDo.getStrategyId());
        if (strategyDo == null) {
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "不存在该策略");
            throw new StrategyException(String.format("不存在该策略,策略id:%s", strategyMarketChannelDo.getStrategyId()));
        }
        // 画布数据验证
        if (isFlow) {
            assert flowExt != null;
            if (!StringUtils.equalsIgnoreCase(strategyDo.getFlowNo(),
                    flowExt.getFlowNo())) {
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, "画布编号验证失败");
                throw new StrategyException(String.format("画布编号验证失败, 派发任务id:%s", dispatchTaskDo.getId()));
            }
            StrategyFlowDo strategyFlowDo = strategyFlowService.select(flowExt.getFlowNo());
            if (strategyFlowDo == null) {
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, "画布不存在");
                throw new StrategyException(String.format("画布不存在, 派发任务id:%s", dispatchTaskDo.getId()));
            }
            if (!StrategyFlowStatusEnum.isRunning(strategyFlowDo.getStatus())) {
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, "画布不在可执行状态");
                throw new StrategyException(String.format("画布不在可执行状态, 派发任务id:%s", dispatchTaskDo.getId()));
            }
            if (now.isAfter(DateUtil.convert(strategyFlowDo.getValidityEnd()))
                    || now.isBefore(DateUtil.convert(strategyFlowDo.getValidityBegin()))) {
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, "画布执行时间不在有效期");
                throw new StrategyException(String.format("画布执行时间不在有效期, 派发任务id:%s", dispatchTaskDo.getId()));
            }
            boolean isRootNode = flowNodeService.select(flowExt.getFlowNo()).stream()
                    .anyMatch(x -> Objects.equals(strategyDo.getId(), x.getStrategyId())
                            && StringUtils.isEmpty(x.getParentId()));
            if (!Objects.equals(isRootNode, flowExt.isRootNode())) {
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, "画布-策略节点类型验证失败(是否是根节点类型)");
                throw new StrategyException(String.format("画布-策略节点类型验证失败(是否是根节点类型), 派发任务id:%s", dispatchTaskDo.getId()));
            }
        }

        if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "策略状态暂停或者结束不营销");
            throw new StrategyException(String.format("策略状态暂停或者结束不营销,策略id:%s", strategyMarketChannelDo.getStrategyId()));
        }

        if (!isFlow && now.isBefore(strategyDo.getValidityBegin())) {
            dispatchTaskService.updateDispatchTaskReset(dispatchTaskDo, "策略未到执行时间");
            throw new StrategyException(String.format("该策略未到执行时间，策略id：%s", strategyDo.getId()));
        }

        StrategyRulerEnum rulerEnum = StrategyRulerEnum.getInstance(strategyDo.getSendRuler());
        if (!isFlow) {
            if ((StrategyRulerEnum.getCycleCodes().contains(rulerEnum.getCode()) &&
                    now.isAfter(strategyDo.getValidityEnd())) || (rulerEnum == StrategyRulerEnum.ONCE &&
                    now.isAfter(strategyDo.getValidityEnd().plusDays(1)))) {
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, "该策略已结束");
                this.strategyFinish(strategyDo, strategyMarketChannelDo);
                throw new StrategyException(String.format("该策略已结束，策略id：%s", strategyDo.getId()));
            }
        }

        List<StrategyExecLogDo> strategyExecLogDos = getExecLogSuccessRecord(strategyDo, strategyMarketChannelDo.getId());
        if (!CollectionUtils.isEmpty(strategyExecLogDos)) {
            dispatchTaskService.updateTaskFinish(dispatchTaskDo, "该策略当天已经执行过");
            throw new StrategyException(String.format("该策略触达渠道，当天已经执行过，策略ID：%s，触达渠道ID：%s", strategyDo.getId(), strategyMarketChannelDo.getId()));
        }

        strategyExecLogDos = getExecLogExecutingRecord(strategyDo, strategyMarketChannelDo.getId());
        if (!CollectionUtils.isEmpty(strategyExecLogDos)) {
            dispatchTaskService.updateDispatchTaskReset(dispatchTaskDo, "存在执行中的记录请勿重复执行");
            throw new StrategyException(String.format("该策略触达渠道，存在执行中的记录，请勿重复执行，策略ID：%s，触达渠道ID：%s", strategyDo.getId(), strategyMarketChannelDo.getId()));
        }

        List<StrategyCrowdPackDo> strategyCrowdPackDos = strategyCrowdPackRepository.selectByStrategyId(strategyDo.getId());
        if ((!isFlow || flowExt.isRootNode()) && CollectionUtils.isEmpty(strategyCrowdPackDos)) {
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "该策略不存在人群包");
            throw new StrategyException("该策略不存在人群包！");
        }
//        List<CrowdSliceDo> crowdSliceList = null;
//        if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyDo.getId(), "offlineStrategyReadOssSwitch")) {
//            crowdSliceList = queryCrowdSliceList(strategyDo.getCrowdPackId());
//        }
        StrategyContext.FlowContext flowContext = null;
        Map<Long, CrowdContext> crowdContent = getCrowdContent(ListUtils.distinctMap(strategyCrowdPackDos, StrategyCrowdPackDo::getCrowdPackId));
        if (isFlow) {
            // 生成批次号
            List<StrategyFlowNodeDo> flowNodeDos = flowNodeService.select(flowExt.getFlowNo());
            StrategyFlowNodeDo currentNode = flowNodeDos.stream()
                    .filter(x -> Objects.equals(x.getStrategyId(), strategyDo.getId()))
                    .findFirst().orElse(null);
            if (currentNode == null) {
                dispatchTaskService.updateTaskFailed(dispatchTaskDo, "当前策略不在画布中");
                throw new StrategyException("当前策略不在画布中！");
            }
            Long nextStrategyId = null;
            StrategyFlowNodeDo nextNode = flowNodeDos.stream()
                    .filter(x -> Objects.equals(x.getParentId(), currentNode.getNodeId()))
                    .findFirst().orElse(null);
            if (nextNode != null) {
                nextStrategyId = nextNode.getStrategyId();
            }
            String batchNo = null;
            int dateValue = DateUtil.dayOfInt(DateUtil.convert(now));
            if (flowExt.isRootNode()) {
                StrategyFlowBatchDo batchDo = new StrategyFlowBatchDo();
                batchDo.setDateValue(dateValue);
                batchDo.setBatchNo(String.format("%s_%s", flowExt.getFlowNo(), dateValue));
                batchDo.setFlowNo(flowExt.getFlowNo());
                flowBatchService.insert(batchDo);

                batchDo = flowBatchService.selectByDateValue(flowExt.getFlowNo(), dateValue);
                batchNo = batchDo.getBatchNo();
            }
            DispatchConfig.FlowDispatchConfig flowDispatchConfig = null;
            DispatchConfig dispatchConfig = JsonUtil.parse(strategyDo.getDispatchConfig(), DispatchConfig.class);
            if (dispatchConfig != null) {
                flowDispatchConfig = dispatchConfig.getFlowDispatchConfig();
            }
            flowContext = new StrategyContext.FlowContext(true, flowExt.isRootNode(),
                    flowExt.getFlowNo(), Optional.ofNullable(nextStrategyId).orElse(0L), batchNo, dateValue, flowDispatchConfig);
        }

        StrategyContext.CycleStrategy cycleStrategy = (!isFlow || flowExt.isRootNode()) ? loadCycleStrategy(dispatchTaskDo, strategyDo) : null;
        StrategyContext strategyContext = StrategyContext.initContext(
                strategyDo,
                strategyGroupDo,
                strategyMarketChannelDo,
                getFlowCtrlRule(strategyDo.getId(), strategyMarketChannelDo.getMarketChannel(), strategyDo.getBusinessType()),
                ListUtils.distinctMap(strategyCrowdPackDos, StrategyCrowdPackDo::getCrowdPackId),
                cycleStrategy,
                crowdContent,
                flowContext
        );
        log.info("初始化数据加载完成, 策略ID:{}, 触达渠道ID:{}, 画布信息:{}", strategyContext.getStrategyDo().getId(),
                strategyContext.getStrategyMarketChannelDo().getId(), JsonUtil.toJson(flowContext));
        return strategyContext;
    }

    public List<CrowdSliceDo> queryCrowdSliceList(String crowdIds) {
        if (StringUtils.isBlank(crowdIds)) {
            throw new StrategyException("策略没有配置人群包分片");
        }
        List<CrowdSliceDo> allSlices = new ArrayList<>();
        String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
        String[] crowdIdsArr = crowdIds.split(";");
        for (String crowdId : crowdIdsArr) {
            // 判断导入类型人群包，不需要当日刷新
            CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(Long.parseLong(crowdId));
            if(crowdInfoDo == null) {
                throw new StrategyException("策略人群包没有准备好" + crowdId);
            }

            CrowdInfoVersionDo crowdInfoVersionDo = null;
            if(crowdInfoDo.getCrowdCreateType() == 1) { // 规则创建
                // 当天最新版本，且已分片，
                // 如果人群包没有改动，重跑可复用已经生成的exelog，如果已经有新版，则生成新版任务，幂等会保证不会重复执行
                crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxCrowdVersion(crowdInfoDo.getCrowdId());

            } else if(crowdInfoDo.getCrowdCreateType() == 2) { // 文件上传
                crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxSliceVersion(crowdInfoDo.getCrowdId());
            }

            List<CrowdSliceDo> sliceDoList = crowdSliceRepository.selectByCrowdVersion(crowdInfoDo.getCrowdId(), crowdInfoVersionDo.getCrowdVersion());
            if (CollectionUtils.isEmpty(sliceDoList)) {
                throw new StrategyException("策略没有配置人群包分片");
            }
            allSlices.addAll(sliceDoList);
        }
        return allSlices;
    }

    private void validCrowdInfo(CrowdInfoDo crowdInfoDo) {
        Date now = new Date();
        if(crowdInfoDo.getCrowdStatus() == null || crowdInfoDo.getCrowdStatus() != 4) {
            throw new StrategyException("人群包状态不满足" + crowdInfoDo.getCrowdId());
        }
        if(crowdInfoDo.getIsPermanent() != null && crowdInfoDo.getIsPermanent() == 0) {
            if (crowdInfoDo.getValidityBegin() == null || now.before(crowdInfoDo.getValidityBegin())) {
                throw new StrategyException("策略人群包有效期未开始" + crowdInfoDo.getCrowdId());
            }
            if (crowdInfoDo.getValidityEnd() == null || now.after(crowdInfoDo.getValidityEnd())) {
                throw new StrategyException("策略人群包已失效" + crowdInfoDo.getCrowdId());
            }
        }
    }

    public Map<Long, CrowdContext> getCrowdContent(List<Long> ids) {
        Map<Long, CrowdContext> res = new HashMap<>(ids.size());
        try {
            for (Long cid : ids) {
                CrowdPackDo crowdPack = crowdPackRepository.selectById(cid);
                AbsCrowdOptService crowdOpt = crowdOptFactory.createOpt(crowdPack.getPullType());
                CrowdContext crowdContext = crowdOpt.initContext(false, crowdPack, crowdOpt);
                CrowdExecLogDo cd = new CrowdExecLogDo();
                cd.setId(-1l); //新人群包无execlogid，用-1
                crowdContext.setCrowdExecLog(cd);
                res.put(cid, crowdContext);
            }
        } catch (Exception e) {
            log.error("getCrowdContent error", e);
        }
        return res;
    }

    public StrategyContext.CycleStrategy loadCycleStrategy(DispatchTaskDo dispatchTaskDo, StrategyDo strategyDo) {
        StrategyContext.CycleStrategy cycleStrategy = null;
        if (dispatchTaskDo != null && strategyDo.getSendRuler() ==
                StrategyRulerEnum.CYCLE_DAY.getCode()) {
            StrategyCreateReq.SendFrequency sendFrequency = JsonUtil.parse(strategyDo.getSendFrequency(),
                    StrategyCreateReq.SendFrequency.class);
            if (sendFrequency.getType() != 3) {
                throw new StrategyException("周期策略数据有问题！");
            }
            cycleStrategy = new StrategyContext.CycleStrategy();
            StrategyCycleDayConfig cycleDayConfig = appConfigService.getStrategyCycleDayConfig();
            if (cycleDayConfig == null || StringUtils.isEmpty(cycleDayConfig.getBizKey())) {
                throw new StrategyException("周期策略不存在！");
            }
            cycleStrategy.setCycleNum(sendFrequency.getValue().get(0));
            cycleStrategy.setCycleDayConfig(cycleDayConfig);
            StrategyExecCycleCounterDo strategyExecCycleCounterDo = strategyExecCycleCounterService
                    .selectStrategyCycleCounter(strategyDo.getId());
            if (strategyExecCycleCounterDo == null) {
                // 不存在则进行插入
                strategyExecCycleCounterDo = new StrategyExecCycleCounterDo();
                strategyExecCycleCounterDo.setStrategyId(strategyDo.getId());
                strategyExecCycleCounterDo.setSumVal(1);
                strategyExecCycleCounterDo.setDateValue(dispatchTaskDo.getDateValue());
                strategyExecCycleCounterService.insert(strategyExecCycleCounterDo);
                int current = (1 % cycleStrategy.getCycleNum()) == 0 ? cycleStrategy.getCycleNum()
                        : 1 % cycleStrategy.getCycleNum();
                cycleStrategy.setCurrCycleNum(current);
            } else {
                // 存在则更新计数值
                strategyExecCycleCounterService
                        .updateStrategyCycleCounter(strategyDo.getId(), dispatchTaskDo.getDateValue());
                strategyExecCycleCounterDo = strategyExecCycleCounterService
                        .selectStrategyCycleCounter(strategyDo.getId(), dispatchTaskDo.getDateValue());
                int current = (strategyExecCycleCounterDo.getSumVal() % cycleStrategy.getCycleNum()) == 0 ? cycleStrategy.getCycleNum()
                        : strategyExecCycleCounterDo.getSumVal() % cycleStrategy.getCycleNum();
                cycleStrategy.setCurrCycleNum(current);
            }
            StrategyExecCycleDo strategyExecCycleDo = strategyExecCycleService.selectStrategyCycle(strategyDo.getId(), dispatchTaskDo.getDateValue());
            if (strategyExecCycleDo == null) {
                strategyExecCycleDo = new StrategyExecCycleDo();
                strategyExecCycleDo.setStrategyId(strategyDo.getId());
                strategyExecCycleDo.setDateValue(dispatchTaskDo.getDateValue());
                strategyExecCycleDo.setTotalVal(cycleStrategy.getCycleNum());
                strategyExecCycleDo.setCurVal(cycleStrategy.getCurrCycleNum());
                strategyExecCycleService.insert(strategyExecCycleDo);
            }
        }
        return cycleStrategy;
    }

    /**
     * 获取流控规则
     *
     * @param strategyId 策略ID
     * @param channel    渠道
     * @return 流控规则
     */
    private List<FlowCtrlDo> getFlowCtrlRule(Long strategyId, Integer channel, String bizType) {
        return flowCtrlCoreService.getFlowCtrlRule(strategyId, channel, bizType);
    }

    /**
     * 查询当天是否已成功执行
     */
    public List<StrategyExecLogDo> getExecLogSuccessRecord(StrategyDo strategyDo, Long marketChannelId) {
        StrategyRulerEnum rulerEnum = StrategyRulerEnum.getInstance(strategyDo.getSendRuler());
        return strategyExecLogRepository.selectByStrategyIdAndChannelAndExecStatus(
                strategyDo.getId(), marketChannelId, StrategyRulerEnum.getCycleCodeEnums().contains(rulerEnum) ? LocalDate.now() : null, StrategyExecStatusEnum.SUCCESS
        );
    }

    /**
     * 查询当天是否存在执行中的记录
     */
    public List<StrategyExecLogDo> getExecLogExecutingRecord(StrategyDo strategyDo, Long marketChannelId) {
        StrategyRulerEnum rulerEnum = StrategyRulerEnum.getInstance(strategyDo.getSendRuler());
        return strategyExecLogRepository.selectByStrategyIdAndChannelAndExecStatus(
                strategyDo.getId(), marketChannelId, StrategyRulerEnum.getCycleCodeEnums().contains(rulerEnum) ? LocalDate.now() : null, StrategyExecStatusEnum.EXECUTING
        );
    }

    /**
     * 预处理
     */
    protected void preHandler(@NonNull StrategyContext strategyContext) {
        // 如果是画布，非根节点的不需要验证人群
        if (strategyContext.isFlow() && !strategyContext.isFlowRootNode()){
            return;
        }

        for(Long crowdId : strategyContext.getCrowdIds()) {

            if (ApolloUtil.isInTheWhitelist(INSIGHT_OSS_CROWD_PACK_WHITELIST, crowdId)) {
                // 校验人群包新版本
                validNewCrowdInfo(strategyContext.getStrategyDo().getId(), crowdId);

            } else {

                if (crowdPackRepository.existExpiredCrowd(strategyContext.getCrowdIds())) {
                    strategyContext.setFailReason("人群包已过期，策略执行失败。");
                    throw new StrategyException(99999999, "人群包已过期，策略执行失败，请尽快排查并操作重试！");
                }

                List<Long> effectiveCrowdIds = crowdPackService.selectEffectiveCrowd(strategyContext.getCrowdIds());
                if (CollectionUtils.isEmpty(effectiveCrowdIds)) {
                    String message = String.format("该策略所有人群包未初始化完成！策略ID：%s，人群包ID:%s", strategyContext.getStrategyDo().getId(),
                            strategyContext.getCrowdIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
                    strategyContext.setFailReason(message);
                    throw new StrategyException(message);
                }

                if (strategyContext.getCrowdIds().size() != effectiveCrowdIds.size()) {
                    String filterIds = strategyContext.getCrowdIds().stream().filter(effectiveCrowdIds::contains).map(String::valueOf).collect(Collectors.joining(","));
                    String message = String.format("该策略有人群包未初始化完成！策略ID：%s，人群包id:%s", strategyContext.getStrategyDo().getId(), filterIds);
                    strategyContext.setFailReason(message);
                    throw new StrategyException(message);
                }
            }
        }
    }

    private void validNewCrowdInfo(Long strategyId, Long crowdId) {
        // 判断导入类型人群包，不需要当日刷新
        CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(crowdId);
        if(crowdInfoDo == null) {
            String message = String.format("策略人群包没有准备好 策略ID:%d 人群包ID:%d", strategyId, crowdId);
            throw new StrategyException(message);
        }
        // 有效期校验
        validCrowdInfo(crowdInfoDo);
        CrowdInfoVersionDo crowdInfoVersionDo = null;
        if(crowdInfoDo.getCrowdCreateType() == 1) { // 规则创建
            // 当天最新版本，且已分片，
            // 如果人群包没有改动，重跑可复用已经生成的exelog，如果已经有新版，则生成新版任务，幂等会保证不会重复执行
            crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxCrowdVersion(crowdInfoDo.getCrowdId());
            if(crowdInfoVersionDo == null) {
                String message = String.format("未找到人群包最新版本 策略ID:%d 人群包ID:%d", strategyId, crowdId);
                throw new StrategyException(message);
            }
            long version = crowdInfoVersionDo.getCrowdVersion()/100L;
            long todayNum = DateUtil.dayOfInt(new Date());
            if(version != todayNum) {
                String message = String.format("版本日期不匹配 策略ID:%d 人群包ID:%d", strategyId, crowdId);
                throw new StrategyException(message);
            }
            if (crowdInfoVersionDo.getSliceStatus() != 2) {
                String message = String.format("最新人群包版本没有分片 策略ID:%d 人群包ID:%d", strategyId, crowdId);
                throw new StrategyException(message);
            }

        } else if(crowdInfoDo.getCrowdCreateType() == 2) { // 文件上传
            crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxSliceVersion(crowdInfoDo.getCrowdId());
            if(crowdInfoVersionDo == null) {
                String message = String.format("未找到导入人群包的已分片版本 策略ID:%d 人群包ID:%d", strategyId, crowdId);
                throw new StrategyException(message);
            }
        } else {
            String message = String.format("策略人群包创建类型不支持 策略ID:%d 人群包ID:%d", strategyId, crowdId);
            throw new StrategyException(message);
        }
    }

    /**
     * 开始执行
     */
    protected void beginExecute(StrategyContext strategyContext) {
        TransactionUtil.transactional(() -> {
            strategyContext.getStrategyDo().executing();
            cacheStrategyService.updateById(strategyContext.getStrategyDo());

            StrategyExecLogDo strategyExecLogDo = StrategyExecLogDo.beginExecute(strategyContext.getStrategyGroupDo(), strategyContext.getStrategyMarketChannelDo());
            strategyExecLogDo.setExtDetail(JsonUtil.toJson(strategyContext.getExtDataMap()));
            strategyExecLogRepository.insert(strategyExecLogDo);

            strategyContext.setStrategyExecLogDo(strategyExecLogDo);
            StrategySnapshotDo strategySnapshotDo = StrategySnapshotDo.beginExecute(strategyContext);
            strategySnapshotRepository.insert(strategySnapshotDo);

            log.info("开始执行, 策略ID:{}, 触达渠道ID:{}", strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId());
        }, e -> {
            throw new StrategyException("beginExecute()执行异常", e);
        });
    }

    /**
     * 匹配分组规则
     */
    protected void coreLogicExecute(StrategyContext strategyContext) {
        StrategyDo strategyDo = strategyContext.getStrategyDo();
        StrategyAbTestEnum abTestEnum = StrategyAbTestEnum.getInstance(strategyDo.getAbTest());

        BiPredicate<String, Integer> matchFunction = null;
        List<StrategyGroupDo> blankGroup = Lists.newArrayList();
        if (abTestEnum == StrategyAbTestEnum.YES) {
            // 获取分组匹配规则
            matchFunction = strategyContext.getStrategyGroupDo().match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType()));
            log.info("获取分组配置,策略ID:{},触达渠道ID:{}", strategyDo.getId(), strategyContext.getStrategyMarketChannelDo().getId());
        }

        // 分页查询+按分组规则过滤+下发
        this.queryAndGroupAndSend(strategyContext, Triple.of(strategyDo.getBizKey(), matchFunction, blankGroup));
    }

    /**
     * 查询过滤下发
     */
    protected void queryAndGroupAndSend(StrategyContext context, Triple<String, BiPredicate<String, Integer>, List<StrategyGroupDo>> funPair) {
        long start = Instant.now().toEpochMilli();
        Roaring64Bitmap container = new Roaring64Bitmap();
        AtomicInteger groupCount = new AtomicInteger(0);
        AtomicInteger totalCount = new AtomicInteger(0);
        AtomicInteger sendCount = new AtomicInteger(0);
        String flowBatchNo;
        if (context.isFlow()) {
            flowBatchNo = context.getFlowContext().getBatchNo();
        } else {
            flowBatchNo = null;
        }
        try {
            Long strategyId = context.getStrategyDo().getId();
            if (context.isFlow() && !context.isFlowRootNode()) {
                flowDispatch(context, funPair, container, groupCount, totalCount, sendCount, strategyId);
                return;
            }
            // 优化为按人群包处理读取逻辑
            for(Long crowdId : context.getCrowdIds()) {

                if (ApolloUtil.isInTheWhitelist(INSIGHT_OSS_CROWD_PACK_WHITELIST, crowdId)) {

//                if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "offlineStrategyReadOssSwitch")) {
                    // 获取 crowdIds
                    List<CrowdSliceDo> crowdList = queryCrowdSliceList("" + crowdId);
                    for (CrowdSliceDo crowdSliceDo : crowdList) {
                        StrategySliceExecLogDo execLogDo = buildSliceExecLog(crowdSliceDo, strategyId);
                        PageUtil.PaginationParam paginationParam = new PageUtil.PaginationParam(setBatchSize());

                        // 分片查询从OSS获取数据
                        List<CrowdDetailDo> crowdDetailList = ossReaderService.readBySliceExecLog(execLogDo);
                        if (crowdDetailList == null || crowdDetailList.size() == 0) {
                            return;
                        }

                        log.info("OfflineDispatch batchDispatch:{} crowdDetailList size:{} firstCrowdDetail:{}", strategyId, crowdDetailList.size(), crowdDetailList.get(0));
                        ossReaderService.filterNewRandom(context.getCrowdContent().get(crowdSliceDo.getCrowdId()), crowdDetailList);

                        int fullPageNum = crowdDetailList.size() / (int) paginationParam.getPageSize();
                        int lastPage = crowdDetailList.size() % (int) paginationParam.getPageSize() == 0 ? 0 : 1;
                        fullPageNum = fullPageNum + lastPage;
                        for (int i = 1; i <= fullPageNum; i++) {
                            List<CrowdDetailDo> list = subCrowdDetailList(crowdDetailList, i, (int) paginationParam.getPageSize());

                            log.info("OfflineDispatch batchDispatch:{} pageNum:{} pageSize:{}", list.size(), i, paginationParam.getPageSize());
                            list.stream().collect(groupingBy(CrowdDetailDo::getApp))
                                    .forEach((app, detailList) -> {
                                                detailList.forEach(z -> {
                                                    z.setFlowBatchNo(flowBatchNo);
                                                    z.setPreStrategyId(0);
                                                    z.setStrategyId(strategyId);
                                                    if (context.isFlow()) {
                                                        z.setNextStrategyId(context.getFlowContext().getNextStrategyId());
                                                        z.setFlowNo(context.getFlowContext().getFlowNo());
                                                    }
                                                });
                                                batchFilterAndDispatch(context, funPair, container, groupCount, totalCount, sendCount, app, detailList);
                                            }
                                    );
                            }
                    }

                } else {
                    List<Long> crowdIds = Arrays.asList(crowdId);
                    Map<Long, CrowdContext> oneCrowdContent = new HashMap<>();
                    oneCrowdContent.put(crowdId,context.getCrowdContent().get(crowdId));
                    // crowid, execlogid, tables
                    List<Triple<Long, Long, List<String>>> crowdList = crowdPackService.getExecLogIdAndTablePairList(crowdIds, oneCrowdContent);
                    for (Triple<Long, Long, List<String>> tableTriple : crowdList) {
                        for (String tableName : tableTriple.getRight()) {
                            PageUtil.PaginationParam paginationParam = new PageUtil.PaginationParam(setBatchSize());
                            log.info("tableName:{}, tableTriple.getMiddle():{},paginationParam.getPageSize():{},context.getCrowdContent().get(tableTriple.getLeft()):{}", tableName,
                                    tableTriple.getMiddle(), paginationParam.getPageSize(), context.getCrowdContent().get(tableTriple.getLeft()));

                            PageUtil.invokePaginationNew(PageUtil.PaginationTypeEnum.ID_PAGING, paginationParam,
                                    () -> crowdPackService.getCrowdDetailList(tableTriple.getLeft(), tableName, tableTriple.getMiddle(),
                                            paginationParam.getId(), paginationParam.getPageSize(), context.getCrowdContent().get(tableTriple.getLeft())),
                                    list -> list.stream().collect(groupingBy(CrowdDetailDo::getApp)).forEach((app, detailList) -> {
                                        detailList.forEach(z -> {
                                            z.setFlowBatchNo(flowBatchNo);
                                            z.setPreStrategyId(0);
                                            z.setStrategyId(strategyId);
                                            if (context.isFlow()) {
                                                z.setNextStrategyId(context.getFlowContext().getNextStrategyId());
                                                z.setFlowNo(context.getFlowContext().getFlowNo());
                                            }
                                        });
                                        batchFilterAndDispatch(context, funPair, container, groupCount, totalCount, sendCount, app, detailList);
                                    })
                            );
                        }
                    }
                }
            }
        } finally {
            context.setCompleteDispatch(sendCount.get() > 0);
            context.setCountTriple(ImmutableTriple.of(groupCount.get(), totalCount.get(), sendCount.get()));
            context.getStrategyExecLogDo().setGroupCount(groupCount.get());
            context.getStrategyExecLogDo().setExecCount(totalCount.get());
            context.getStrategyExecLogDo().setSendCount(sendCount.get());
            log.info("分组匹配人数:{}, 发送成功人数:{}, 耗时:{}ms", totalCount.get(), sendCount.get(), Instant.now().toEpochMilli() - start);
        }
    }

    private StrategySliceExecLogDo buildSliceExecLog(CrowdSliceDo sliceDo, Long strategyId) {
        StrategySliceExecLogDo strategySliceExecLogDo = new StrategySliceExecLogDo();
        strategySliceExecLogDo.setStrategyId(strategyId);
        strategySliceExecLogDo.setCrowdSliceId(sliceDo.getId());
        strategySliceExecLogDo.setCrowdVersion(sliceDo.getCrowdVersion());
        strategySliceExecLogDo.setStatus(SliceExecLogEnum.INIT.getCode());
        strategySliceExecLogDo.setCrowdId(sliceDo.getCrowdId());
        strategySliceExecLogDo.setRetryTimes(0);
        return strategySliceExecLogDo;
    }

    private List<CrowdDetailDo> subCrowdDetailList(List<CrowdDetailDo> crowdDetailList, int pageNum, int pageSize) {

        if(crowdDetailList.size() > (pageNum-1) * pageSize && crowdDetailList.size() >= pageNum * pageSize) {
            return crowdDetailList.subList(((pageNum-1) * pageSize), (pageNum * pageSize));
        } else if (crowdDetailList.size() > (pageNum-1) * pageSize && crowdDetailList.size() < pageNum * pageSize){
            return crowdDetailList.subList(((pageNum-1) * pageSize), crowdDetailList.size());
        } else {
            return org.apache.commons.compress.utils.Lists.newArrayList();
        }
    }

    private void flowDispatch(StrategyContext context, Triple<String, BiPredicate<String, Integer>, List<StrategyGroupDo>> funPair,
                              Roaring64Bitmap container, AtomicInteger groupCount, AtomicInteger totalCount, AtomicInteger sendCount,
                              Long strategyId) {
        Date localNow = new Date();
        StrategyContext.FlowContext flowContext = context.getFlowContext();
        int delayDay = context.getFlowContext().getFlowDispatchConfig()
                .getDelay().getTimeValue();
        int dateValue = DateUtil.dayOfInt(DateUtil.addDays(localNow, delayDay * (-1)));
        int crowdCount = dispatchCrowdService.selectCount(strategyId, flowContext.getFlowNo(), dateValue);
        if (crowdCount <= 0) {
            // 没有人群
            context.setNotCheckFlowUserCount(true);
            return;
        }
        long startIndex = 0;
        long pageSize = setBatchSize();
        while (true) {
            List<DispatchCrowdDo> dispatchCrowdDos = dispatchCrowdService
                    .pullCrowds(startIndex, pageSize, strategyId, flowContext.getFlowNo(), dateValue);
            List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
            dispatchCrowdDos.forEach(dispatchCrowdDo -> {
                CrowdDetailDo crowdDetailDo = JsonUtil.parse(dispatchCrowdDo.getUserInfo(), CrowdDetailDo.class);
                if (crowdDetailDo != null) {
                    crowdDetailDo.setUserId(dispatchCrowdDo.getUserId());
                    crowdDetailDo.setPreStrategyId(dispatchCrowdDo.getStrategyId());
                    crowdDetailDo.setFlowBatchNo(dispatchCrowdDo.getBatchNo());
                    crowdDetailDo.setNextStrategyId(flowContext.getNextStrategyId());
                    crowdDetailDo.setStrategyId(strategyId);
                    crowdDetailDo.setFlowNo(flowContext.getFlowNo());
                    crowdDetailDoList.add(crowdDetailDo);
                }
            });
            crowdDetailDoList.stream().collect(groupingBy(CrowdDetailDo::getApp)).forEach((app, detailList) -> {
                batchFilterAndDispatch(context, funPair, container, groupCount, totalCount, sendCount, app, detailList);
            });
            if (CollectionUtils.isEmpty(dispatchCrowdDos) || dispatchCrowdDos.size() < pageSize) {
                break;
            }
            startIndex = dispatchCrowdDos.get(dispatchCrowdDos.size() - 1).getId();
        }
    }

    protected void batchFilterAndDispatch(StrategyContext context, Triple<String, BiPredicate<String, Integer>, List<StrategyGroupDo>> funPair,
                                        Roaring64Bitmap container, AtomicInteger groupCount, AtomicInteger totalCount, AtomicInteger sendCount,
                                        String app, List<CrowdDetailDo> detailList) {
        int size = detailList.size();
        detailList = context.cycleFilter(detailList, strategyService, randomNumClient);
        if (CollectionUtils.isEmpty(detailList)) {
            log.info("周期策略筛选, 本批次无人满足条件, 策略id:{}, 渠道id:{}", context.getStrategyDo().getId(),
                    context.getStrategyMarketChannelDo().getId());
            return;
        }
        log.info("初始筛选后, 策略id:{}, 渠道id:{}, 过滤前大小:{}, 本批次剩余人数:{}", context.getStrategyDo().getId(),
                context.getStrategyMarketChannelDo().getId(), size, detailList.size());
        // 获取随机数
        detailList = randomNumService.randomNum(context, detailList);
        // 1.匹配分组规则
        detailList = strategyGroupService.matchGroupRule(funPair.getLeft(), funPair.getMiddle(), detailList);
        //1.1 麻雀-fxk老客转xyf01下发 ,AB分组之后，触达下发前，流控判断前
        detailList = abstractAdsStrategyLabelService.convertCrowdList(detailList, context.getStrategyDo().getUserConvert());
        // 2.根据用户ID全局去重
        detailList = detailList.stream().filter(item -> !container.contains(item.getUserId())).collect(Collectors.toList());
        // 3.去重后的用户ID加入到容器中，参与下次去重
        detailList.stream().map(CrowdDetailDo::getUserId).forEachOrdered(container::add);
        // 4.根据手机号去重
        detailList = detailList.stream().filter(ListUtils.distinctByKey(CrowdDetailDo::getMobile)).collect(Collectors.toList());
        // 分组人数统计
        this.groupCount(context, groupCount, totalCount, sendCount, detailList.size());
        // 5.标签和模板参数查询
        AtomicReference<List<CrowdDetailDo>> availableDetails = new AtomicReference<>();
        availableDetails.set(new ArrayList<>());
        Pair<List<CrowdDetailDo>, List<Object>> pair = this.labelAndTempParamQuery(context, app, detailList, availableDetails);
        // 6. 保存营销记录
        if (context.isFlow()) {
            StrategyContext.FlowContext flowContext = context.getFlowContext();
            dispatchCrowdService.saveCrowdDetail(availableDetails.get(), flowContext.getDayValue());
        }
        log.info("下发前筛选后, 策略id:{}, 渠道id:{}, 过滤前大小:{}, 本批次剩余人数:{}", context.getStrategyDo().getId(),
                context.getStrategyMarketChannelDo().getId(), size, detailList.size());
        // 7.执行下发
        dispatchHandler(context, groupCount, totalCount, sendCount, app, pair.getLeft(), pair.getRight());
    }

    /**
     * 分组人数统计
     *
     * @param context    初始化数据
     * @param groupCount 分组人数
     * @param totalCount 应发总数
     * @param sendCount  发送总数
     * @param count      当前批次人群总数
     */
    public void groupCount(StrategyContext context, AtomicInteger groupCount, AtomicInteger totalCount, AtomicInteger sendCount, Integer count) {
        groupCount.updateAndGet(v -> v + count);
        this.refreshExecLogCount(context, groupCount.get(), totalCount.get(), sendCount.get());
    }

    /**
     * 标签查询和短信模板查询
     *
     * @param strategyContext   初始化数据
     * @param app               当前批APP
     * @param crowdDetailDoList 当前批人群明细
     * @return 通过便签验证和短信参数验证的人群以及对应的参数
     */
    public Pair<List<CrowdDetailDo>, List<Object>> labelAndTempParamQuery(StrategyContext strategyContext, String app, List<CrowdDetailDo> crowdDetailDoList, AtomicReference<List<CrowdDetailDo>> availableDetails) {
        List<Object> finalTemplateParamList = Lists.newArrayList();
        List<CrowdDetailDo> finalCrowdDetailList = Lists.newArrayList();
        for (List<CrowdDetailDo> partition : Lists.partition(crowdDetailDoList, strategyConfig.getLabelQueryBatchSize())) {
            // 1.标签查询
            List<CrowdDetailDo> queryLabelList = adsStrategyLabelQueryService.queryLabelHandler(
                    strategyContext.getStrategyDo().getId(),
                    strategyContext.getStrategyMarketChannelDo().getId(),
                    strategyContext.getStrategyMarketChannelDo().getMarketChannel(),
                    app,
                    partition
            );
            availableDetails.get().addAll(queryLabelList);
            log.info("满足标签的用户数量:{}", queryLabelList.size());
            // 2.模板参数查询
            if (!CollectionUtils.isEmpty(queryLabelList)) {
                Pair<List<CrowdDetailDo>, List<?>> paramQueryResult = this.templateParamQuery(strategyContext, app, queryLabelList);
                finalCrowdDetailList.addAll(paramQueryResult.getLeft());
                finalTemplateParamList.addAll(paramQueryResult.getRight());
            }
        }
        return Pair.of(finalCrowdDetailList, finalTemplateParamList);
    }

    /**
     * 执行下发
     *
     * @param context    初始化数据
     * @param totalCount 圈选总数
     * @param sendCount  发送成功总数
     * @param app        当前批APP
     * @param list       当前批人群明细
     */
    protected void dispatchHandler(StrategyContext context, AtomicInteger groupCount, AtomicInteger totalCount, AtomicInteger sendCount, String app, List<CrowdDetailDo> list, List<Object> params) {
        Pair<Integer, Integer> respPair = Pair.of(list.size(), 0);
        try {
            // 1.执行下发逻辑
            respPair = this.executeDispatch(context, app, app, list, params);
        } catch (StrategyException e) {
            // 2.下发异常
            throw new StrategyException(e.getCode(), e.getMessage());
        } finally {
            if (context.getStrategyMarketChannelDo() != null &&
                    context.getStrategyMarketChannelDo().getMarketChannel() == StrategyMarketChannelEnum.NONE.getCode()) {
                log.info("[dispatchHandler统计数据] [更新前] 策略id：{}, 渠道id：{}, groupCount: {}, totalCount: {}, sendCount: {}, respPair: {}",
                        context.getStrategyMarketChannelDo().getStrategyId(),
                        context.getStrategyMarketChannelDo().getId(),
                        groupCount.get(),
                        totalCount.get(),
                        sendCount.get(),
                        JsonUtil.toJson(respPair));
            }
            // 3.统计数量
            this.count(totalCount, sendCount, respPair);
            // 4.刷新执行日志
            this.refreshExecLogCount(context, groupCount.get(), totalCount.get(), sendCount.get());

            if (context.getStrategyMarketChannelDo() != null &&
                    context.getStrategyMarketChannelDo().getMarketChannel() == StrategyMarketChannelEnum.NONE.getCode()) {
                log.info("[dispatchHandler统计数据] [更新后] 策略id：{}, 渠道id：{}, groupCount: {}, totalCount: {}, sendCount: {}, respPair: {}",
                        context.getStrategyMarketChannelDo().getStrategyId(),
                        context.getStrategyMarketChannelDo().getId(),
                        groupCount.get(),
                        totalCount.get(),
                        sendCount.get(),
                        JsonUtil.toJson(respPair));
            }
        }
    }

    /**
     * 模板参数查询
     *
     * @param context         策略执行初始化内容
     * @param app             app
     * @param crowdDetailList 需要执行下发的用户
     * @return left-需要执行下发的用户，right-需要执行下发的用户的模板参数
     */
    private Pair<List<CrowdDetailDo>, List<?>> templateParamQuery(StrategyContext context, String app, List<CrowdDetailDo> crowdDetailList) {
        final TimeInterval timer = new TimeInterval();
        Pair<List<CrowdDetailDo>, List<?>> result = Pair.of(crowdDetailList, Collections.emptyList());
        StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(context.getStrategyMarketChannelDo().getMarketChannel());
        switch (marketChannelEnum) {
            case SMS:
                Triple<Boolean, List<CrowdDetailDo>, List<SmsBatchWithParamArgs.Sms>> triple = templateParamQueryService.smsParamBatchQuery(convertToDispatchDto(context), app, crowdDetailList);
                result = Pair.of(triple.getMiddle(), triple.getRight());
                break;
            case PUSH:
                Triple<Boolean, List<CrowdDetailDo>, List<PushUserData>> push = templateParamQueryService.pushParamBatchQuery(convertToDispatchDto(context), app, crowdDetailList);
                result = Pair.of(push.getMiddle(), push.getRight());
                break;
            case AI_PRONTO:
                Triple<Boolean, List<CrowdDetailDo>, List<AiUserData>> ai = templateParamQueryService.aiParamBatchQuery(convertToDispatchDto(context), app, crowdDetailList);
                result = Pair.of(ai.getMiddle(), ai.getRight());
                break;
            default:
                log.info("当前渠道[{}]不涉及模板参数查询,继续执行下发逻辑", marketChannelEnum.getDescription());
                result = Pair.of(crowdDetailList, Collections.emptyList());
        }
        log.info("模板参数查询完成, 需要查询用户数:{}, 最终查询成功用户数:{},耗时:{}ms", crowdDetailList.size(), result.getLeft().size(), timer.interval());
        return result;
    }

    /**
     * 执行下发
     *
     * @param strategyContext 策略执行初始化内容
     * @param k1              APP
     * @param k2              InnerApp
     * @param v2              需要执行下发的用户
     */
    private <T> ImmutablePair<Integer, Integer> executeDispatch(StrategyContext strategyContext, String k1, String k2, List<CrowdDetailDo> v2, List<T> templateParam) {
        if (CollectionUtils.isEmpty(v2)) {
            log.warn("当前批次没有需要下发的用户,策略ID:{},渠道ID:{}", strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId());
            return ImmutablePair.of(0, 0);
        }

        ImmutablePair<Integer, CrowdPushBatchDo> pair = this.dispatchHandler(strategyContext, k1, k2, v2, templateParam);
        return ImmutablePair.of(pair.getLeft(), pair.getLeft());
    }

    /**
     * 计数
     *
     * @param totalCount 执行总人数
     * @param sendCount  发送总数
     * @param respPair   当前批次人数
     */
    private void count(AtomicInteger totalCount, AtomicInteger sendCount, Pair<Integer, Integer> respPair) {
        totalCount.updateAndGet(v -> v + respPair.getLeft());
        sendCount.updateAndGet(v -> v + respPair.getRight());
    }

    /**
     * 刷新执行日志总数/发送成功数
     */
    private void refreshExecLogCount(StrategyContext strategyContext, Integer groupCount, Integer totalCount, Integer sendTotalCount) {
        strategyContext.getStrategyExecLogDo().setGroupCount(groupCount);
        strategyContext.getStrategyExecLogDo().setExecCount(totalCount);
        strategyContext.getStrategyExecLogDo().setSendCount(sendTotalCount);
        strategyExecLogRepository.updateById(strategyContext.getStrategyExecLogDo());
        log.info("当前策略执行总人数:{},调用下发接口成功总人数:{}", totalCount, sendTotalCount);
    }

    /**
     * 执行成功
     */
    protected void successExecute(StrategyContext strategyContext) {
        this.checkFinishExecUserNum(strategyContext);
        TransactionUtil.transactional(() -> {
            strategyContext.getStrategyDo().execSuccess();
            strategyContext.getStrategyExecLogDo().execSuccess();
            strategyExecLogRepository.updateById(strategyContext.getStrategyExecLogDo());
            cacheStrategyService.updateById(strategyContext.getStrategyDo());
            log.info("策略成功,策略ID:{},触达渠道ID:{}", strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId());
        });
    }

    /**
     * 检查最终执行人数
     */
    private void checkFinishExecUserNum(StrategyContext strategyContext) {
        if (strategyContext.isFlow() && strategyContext.isNotCheckFlowUserCount()) {
            return;
        }

        Integer crowdPackUserNum = 0;
        if (!CollectionUtils.isEmpty(strategyContext.getCrowdIds())) {
            List<Long> crowdIdList = strategyContext.getCrowdIds();
            for(Long crowdId : crowdIdList) {

                if (ApolloUtil.isInTheWhitelist(INSIGHT_OSS_CROWD_PACK_WHITELIST, crowdId)) {
                    CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(crowdId);
                    if(crowdInfoDo == null) {
                        throw new StrategyException("执行完成后查询不到人群包 人群包ID:" + crowdId);
                    }

                    CrowdInfoVersionDo crowdInfoVersionDo = null;
                    if(crowdInfoDo.getCrowdCreateType() == 1) { // 规则创建
                        crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxCrowdVersion(crowdInfoDo.getCrowdId());
                    } else if(crowdInfoDo.getCrowdCreateType() == 2) { // 文件上传
                        crowdInfoVersionDo = crowdInfoVersionRepository.selectMaxSliceVersion(crowdInfoDo.getCrowdId());
                    }
                    if(crowdInfoVersionDo != null && crowdInfoVersionDo.getCrowdSize() != null) {
                        crowdPackUserNum += crowdInfoVersionDo.getCrowdSize().intValue();
                    } else {
                        throw new StrategyException(String.format("离线策略执行完成后查询不到人群包version或size信息为空，人群包ID:%s ", crowdId));
                    }

                } else {
                    String crowdIds = "" + crowdId;
//                    String crowdIds = strategyContext.getCrowdIds().stream().map(Convert::toStr).collect(Collectors.joining(";"));
                    crowdPackUserNum += crowdPackService.countCrowdPackUserNum(crowdIds);
                    if (Objects.equals(crowdPackUserNum, 0)) {
                        strategyContext.setFailReason(StrategyExecFailReasonEnum.CROWD_PACKAGE_COUNT_IS_0.getReason());
                        throw new StrategyException(String.format("人群包用户数为0，人群包ID：%s", crowdIds));
                    }
                }
            }
        }

        ImmutableTriple<Integer, Integer, Integer> countTriple = strategyContext.getCountTriple();

        if (!Objects.equals(countTriple.getLeft(), 0) && Objects.equals(countTriple.getMiddle(), 0)) {
            throw new StrategyException(String.format("人群包总人数：%s，分组人数：%s，应发人数：0", crowdPackUserNum, countTriple.getLeft()));
        }

        if (!Objects.equals(countTriple.getLeft(), 0) && Objects.equals(countTriple.getRight(), 0)) {
            throw new StrategyException(String.format("人群包总人数：%s，分组人数：%s，应发人数：%s，实际发送人数：0", crowdPackUserNum, countTriple.getLeft(), countTriple.getMiddle()));
        }

        if (!Objects.equals(countTriple.getMiddle(), countTriple.getRight())) {
            throw new StrategyException(String.format("人群包总人数：%s，分组人数：%s，应发人数：%s，实际发送人数：%s", crowdPackUserNum, countTriple.getLeft(), countTriple.getMiddle(), countTriple.getRight()));
        }
    }

    /**
     * 执行失败
     */
    protected void failedExecute(StrategyContext strategyContext) {
        TransactionUtil.transactional(() -> {
            StrategyDo strategyDo = strategyContext.getStrategyDo();
            strategyDo.execFailure();
            cacheStrategyService.updateById(strategyDo);

            StrategyExecLogDo strategyExecLogDo = strategyContext.getStrategyExecLogDo();
            strategyExecLogDo.execFailure(strategyContext.getFailReason());
            strategyExecLogRepository.updateById(strategyExecLogDo);

            log.info("策略{},策略ID:{},触达渠道ID:{}", StrategyStatusEnum.getInstance(strategyContext.getStrategyDo().getStatus()).getDescription(), strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId());
        }, e -> {
            throw new StrategyException("执行失败,更新策略/执行日志状态记录异常", e);
        });
    }

    /**
     * 检查策略执行成功/执行失败状态
     * (目前以最后执行的渠道状态为准，暂不使用改逻辑)
     */
    protected void isFinished(StrategyContext strategyContext) {
        StrategyDo strategyDo = strategyContext.getStrategyDo();

        LocalDateTime min = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime max = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

        List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectByStrategyId(strategyDo.getId());
        List<StrategyExecLogDo> strategyExecLogDos = strategyExecLogRepository.selectByStrategyIdAndExecTime(strategyDo.getId(), min, max, StrategyExecStatusEnum.SUCCESS);
        if (strategyExecLogDos.size() >= strategyMarketChannelDos.size()) {
            strategyDo.execSuccess();
        } else {
            strategyDo.execFailure();
        }
        cacheStrategyService.updateById(strategyContext.getStrategyDo());
    }

    /**
     * 检查策略是否已完成状态
     */
    protected void isEnded(StrategyContext strategyContext) {
        StrategyDo strategyDo = strategyContext.getStrategyDo();
        StrategyRulerEnum rulerEnum = StrategyRulerEnum.getInstance(strategyDo.getSendRuler());
        StrategyStatusEnum strategyStatusEnum = StrategyStatusEnum.getInstance(strategyDo.getStatus());

        if (LocalDate.now().compareTo(strategyDo.getValidityEnd().toLocalDate()) < 0) {
            this.verifyStatusAndFailRetry(strategyContext);
            return;
        }

        List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectByStrategyId(strategyDo.getId()).stream()
                .filter(item -> StrategyMarketChannelEnum.APP_BANNER != StrategyMarketChannelEnum.getInstance(item.getMarketChannel()))
                .collect(Collectors.toList());

        List<StrategyExecLogDo> strategyExecLogDos = strategyExecLogRepository.selectByStrategyIdAndExecTime(
                strategyDo.getId(),
                LocalDateTime.of(strategyDo.getValidityEnd().toLocalDate(), LocalTime.of(0, 0, 0)),
                LocalDateTime.of(strategyDo.getValidityEnd().toLocalDate(), LocalTime.of(23, 59, 59)),
                rulerEnum == StrategyRulerEnum.ONCE ? StrategyExecStatusEnum.SUCCESS : null
        );

        if (StrategyStatusEnum.SUCCESS != strategyStatusEnum || strategyExecLogDos.size() < strategyMarketChannelDos.size()) {
            this.verifyStatusAndFailRetry(strategyContext);
            return;
        }
        if (strategyContext.isFlow()){
            return;
        }
        this.strategyFinish(strategyDo, strategyContext.getStrategyMarketChannelDo());
        XxlJobLogger.log(String.format("该策略已完成,策略ID:%s，最后一次执行的触达渠道ID:%s", strategyDo.getId(), strategyContext.getStrategyMarketChannelDo().getId()));
    }

    /**
     * 策略结束-关闭所有渠道任务
     */
    private void strategyFinish(StrategyDo strategyBo, StrategyMarketChannelDo strategyMarketChannelBo) {
        try {
            List<StrategyMarketChannelDo> strategyMarketChannelBoList = strategyMarketChannelRepository.selectByStrategyId(strategyMarketChannelBo.getStrategyId());
            strategyMarketChannelBoList.stream().map(StrategyMarketChannelDo::getXxlJobId).filter(Objects::nonNull).forEach(xxlJobAdminClient::stopJob);
            log.info("该策略已结束，策略所有渠道任务关闭成功！！！，策略ID：{}", strategyBo.getId());
        } catch (Exception e) {
            log.warn("该策略已结束，关闭任务异常，策略ID：{}，触达渠道ID：{}", strategyMarketChannelBo.getStrategyId(), strategyMarketChannelBo.getId());
        } finally {
            strategyBo.execEnded();
            cacheStrategyService.updateById(strategyBo);
            strategyService.strategyEndAlarm(strategyBo);
        }
    }

    /**
     * 失败重试
     */
    protected void verifyStatusAndFailRetry(StrategyContext strategyContext) {
        if (!strategyContext.isCompleteDispatch()) {
            if (strategyContext.isFlow() && strategyContext.isNotCheckFlowUserCount()){
                return;
            }
            log.warn("未完成下发,策略ID:{},触达渠道ID:{}", strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId());
            return;
        }

        if (StrategyStatusEnum.FAIL != StrategyStatusEnum.getInstance(strategyContext.getStrategyDo().getStatus())) {
            log.warn("当前触达渠道非失败状态,策略ID:{},触达渠道ID:{}", strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId());
            return;
        }

        if (strategyContext.isFlow()){
            return;
        }
        if (strategyContext.isCycleStrategy()) {
            // 周期策略不建立任务XXL-Job
            return;
        }
        if (strategyContext.getStrategyMarketChannelDo().getMarketChannel() == StrategyMarketChannelEnum.NONE.getCode()) {
            //“不营销”组不建立XXL-Job
            return;
        }

        XxlJobDto xxlJobDto = new XxlJobDto();
        StrategyXxlJobParam strategyXxlJobParam = new StrategyXxlJobParam(strategyContext.getStrategyExecLogDo().getId());
        xxlJobDto.setExecutorHandler(XxlJobConstants.STRATEGY_DISPATCH_RETRY);
        xxlJobDto.setJobCron(xxlJobAdminClient.timeCron(LocalDateTime.now().plus(5, ChronoUnit.MINUTES)).asString());
        xxlJobDto.setJobDesc(this.genRetryJobName(strategyContext));
        xxlJobDto.setAddTime(new Date());
        xxlJobDto.setExecutorParam(JSON.toJSONString(strategyXxlJobParam));
        xxlJobDto.setTriggerStatus(0);
        xxlJobDto.setAuthor(strategyContext.getStrategyDo().getUpdatedOp());
        int jobId = xxlJobAdminClient.addJob(xxlJobDto);

        strategyContext.getStrategyExecLogDo().setRetryId(Convert.toLong(jobId));
        strategyExecLogRepository.updateById(strategyContext.getStrategyExecLogDo());
        log.info("新建重试任务成功,策略ID:{},触达渠道ID:{},重试任务ID:{}", strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId(), jobId);
    }

    /**
     * 生成重试任务名称
     *
     * @param strategyContext 策略执行初始化内容
     * @return 重试任务名称
     */
    private String genRetryJobName(StrategyContext strategyContext) {
        StrategyDo strategyBo = strategyContext.getStrategyDo();
        StrategyGroupDo strategyGroupDo = strategyContext.getStrategyGroupDo();
        StrategyMarketChannelDo strategyMarketChannelDo = strategyContext.getStrategyMarketChannelDo();
        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(strategyMarketChannelDo.getMarketChannel());
        String jobName = "策略组:" + strategyBo.getName() + "_" + strategyGroupDo.getName() + "_" + strategyMarketChannelEnum.getDescription() + "_重试";
        log.info("新建重试任务名称:{},策略ID:{},触达渠道ID:{}", jobName, strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId());
        return jobName;
    }

    public void retry(@NonNull Long strategyExecLogId, DispatchTaskDo dispatchTaskDo) {
        LocalDateTime now = LocalDateTime.now();
        StrategyExecLogDo strategyExecLogDo = strategyExecLogRepository.selectById(strategyExecLogId);
        if (strategyExecLogDo == null) {
            dispatchTaskService.updateTaskFinish(dispatchTaskDo, "不存在该重试策略执行日志记录");
            throw new StrategyException(String.format("不存在该重试策略执行日志记录，执行日志id：%s", strategyExecLogId));
        }

        StrategyContext strategyContext = initContext(strategyExecLogDo.getStrategyMarketChannelId(), dispatchTaskDo);
        strategyContext.setStrategyExecLogDo(strategyExecLogDo);

        StrategyDo strategyBo = strategyContext.getStrategyDo();
        StrategyRulerEnum rulerEnum = StrategyRulerEnum.getInstance(strategyBo.getSendRuler());
        if (StrategyRulerEnum.getCycleCodeEnums().contains(rulerEnum)
                && now.compareTo(strategyBo.getValidityEnd()) > 0 || rulerEnum == StrategyRulerEnum.ONCE && now.compareTo(strategyBo.getValidityEnd().plusDays(1)) > 0) {
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "该策略已结束");
            this.strategyFinish(strategyBo, strategyContext.getStrategyMarketChannelDo());
            throw new StrategyException(String.format("该策略已结束，策略id：%s", strategyBo.getId()));
        }

        TransactionUtil.transactional(() -> {
            Integer totalNum = crowdPushBatchRepository.selectTotalByExecLogId(strategyExecLogId);
            if (totalNum <= 0) {
                log.info("不存在触达渠道下发记录，重试执行策略任务");
                StrategyMarketChannelDo strategyMarketChannelDo = strategyContext.getStrategyMarketChannelDo();
                if (strategyContext.isCycleStrategy()) {
                    log.info("重试策略是周期策略, 渠道id:{}", strategyMarketChannelDo.getId());
                    execute(strategyMarketChannelDo.getId(), dispatchTaskDo);
                    return;
                }
                StrategyXxlJobParam strategyXxlJobParam = new StrategyXxlJobParam(strategyMarketChannelDo.getId());
                if (strategyMarketChannelDo.getXxlJobId() == null
                        || strategyMarketChannelDo.getXxlJobId() <= 0){
                    return;
                }
                xxlJobAdminClient.triggerJob(strategyMarketChannelDo.getXxlJobId(), JSON.toJSONString(strategyXxlJobParam));
            } else {
                List<CrowdPushBatchDo> crowdPushBatchDoList = crowdPushBatchRepository.selectFailBatchByStrategyExecLogId(strategyExecLogId);
                if (CollectionUtils.isEmpty(crowdPushBatchDoList)) {
                    dispatchTaskService.updateTaskFailed(dispatchTaskDo,"该策略执行不存在推送失败批次");
                    throw new StrategyException(String.format("该策略执行不存在推送失败批次，重试批次的执行日志id：%s", strategyExecLogId));
                }
                // 重试处理
                this.retryExecute(crowdPushBatchDoList, strategyContext);
                dispatchTaskService.updateTaskFinish(dispatchTaskDo, "重试成功");
            }
        }, e -> {
            // 更新策略 状态
            dispatchTaskService.updateTaskFailed(dispatchTaskDo,"该策略执行重试异常");
            StrategyDo updateStrategy = new StrategyDo();
            updateStrategy.setId(strategyExecLogDo.getStrategyId());
            updateStrategy.execFailure();
            cacheStrategyService.updateById(updateStrategy);
            throw new StrategyException(String.format("该策略执行重试异常，执行日志id：%s", strategyExecLogId), e);
        }, () -> {
            log.info("该策略重试完成！！！");
            isEnded(strategyContext);
        });
    }

    /**
     * 重试下发更新相关策略
     */
    private void retryExecute(List<CrowdPushBatchDo> crowdPushBatchDoList, StrategyContext strategyContext) {
        String tableNameNo = strategyContext.getDetailTableNo();
        Integer marketChannel = strategyContext.getStrategyMarketChannelDo().getMarketChannel();
        // 所有批次总数和
        AtomicInteger batchSize = new AtomicInteger();
        // 所有批次下发成功数
        AtomicInteger sendSize = new AtomicInteger();
        crowdPushBatchDoList.forEach(crowdPushBatchBo -> {
            // 更新批次状态：可重试->已完成
            CrowdPushBatchDo updateCrowdPushBatch = new CrowdPushBatchDo();
            updateCrowdPushBatch.setId(crowdPushBatchBo.getId());
            updateCrowdPushBatch.setBatchStatus(CrowdPushBatchStatusEnum.FINISH.getCode());
            crowdPushBatchRepository.updateById(updateCrowdPushBatch);
            // 批次记录+
            CrowdPushBatchServiceImpl.BatchItem<?> batchItem = JSON.parseObject(crowdPushBatchBo.getMobileBatch(), CrowdPushBatchServiceImpl.BatchItem.class);
            // 查询用户明细
            List<CrowdDetailDo> crowdDetailList = userDispatchDetailService.selectListByBatchNo(tableNameNo, marketChannel, crowdPushBatchBo.getBatchNum());
            // 模板参数查询
            List<CrowdDetailDo> finalCrowdDetailList = new ArrayList<>();
            List<Object> finalTemplateParamList = new ArrayList<>();
            for (List<CrowdDetailDo> partition : Lists.partition(crowdDetailList, strategyConfig.getLabelQueryBatchSize())) {
                Pair<List<CrowdDetailDo>, List<?>> paramQueryResult = this.templateParamQuery(strategyContext, batchItem.getApp(), partition);
                finalCrowdDetailList.addAll(paramQueryResult.getLeft());
                finalTemplateParamList.addAll(paramQueryResult.getRight());
            }
            // 执行下发
            ImmutablePair<Integer, CrowdPushBatchDo> pair = this.retryDispatchHandler(strategyContext, crowdPushBatchBo.getDetailTableNo(), batchItem.getApp(), batchItem.getInnerApp(), finalCrowdDetailList, finalTemplateParamList);

            sendSize.set(sendSize.get() + (Objects.equals(pair.getLeft(), -1) ? 0 : pair.getLeft()));
            batchSize.set(batchSize.get() + crowdPushBatchBo.getBatchTotal());
        });

        // 更新策略执行日志 执行状态
        StrategyExecLogDo updateStrategyExecLog = new StrategyExecLogDo();
        updateStrategyExecLog.setId(strategyContext.getStrategyExecLogDo().getId());
        updateStrategyExecLog.setTemplateId(strategyContext.getStrategyMarketChannelDo().getTemplateId());
        updateStrategyExecLog.setSendCount(strategyContext.getStrategyExecLogDo().getSendCount() + sendSize.get());
        // 更新策略 状态
        StrategyDo updateStrategy = new StrategyDo();
        updateStrategy.setId(strategyContext.getStrategyDo().getId());
        // 下发总数 == 重试总数 ? 成功 : 失败
        if (sendSize.get() == batchSize.get()) {
            updateStrategyExecLog.execSuccess();
            updateStrategy.execSuccess();
        } else {
            updateStrategyExecLog.execFailure("重试失败");
            updateStrategy.execFailure();
        }
        strategyExecLogRepository.updateById(updateStrategyExecLog);
        cacheStrategyService.updateById(updateStrategy);

//        // 留白组
//        userBlankGroupDetailService.blankGroupAsync(strategyContext);

        // 下发总数 == 重试总数，关停重试任务
        if (sendSize.get() == batchSize.get()) {
            if (strategyContext.getStrategyExecLogDo().getRetryId() != null &&
            strategyContext.getStrategyExecLogDo().getRetryId() > 0) {
                xxlJobAdminClient.stopJob(strategyContext.getStrategyExecLogDo().getRetryId().intValue());
            }
        }
    }

    protected DispatchDto convertToDispatchDto(StrategyContext context) {
        DispatchDto reach = new DispatchDto();
        reach.setDetailTableNo(context.getDetailTableNo());
        reach.setStrategyId(context.getStrategyDo().getId());
        reach.setBizType(context.getStrategyDo().getBusinessType()); // 250401新增业务线字段
        reach.setStrategyGroupId(context.getStrategyGroupDo().getId());
        reach.setStrategyChannelId(context.getStrategyMarketChannelDo().getId());
        reach.setStrategyChannelXxlJobId(context.getStrategyMarketChannelDo().getXxlJobId());
        reach.setStrategyChannel(context.getStrategyMarketChannelDo().getMarketChannel());
        reach.setStrategyMarketChannelTemplateId(context.getStrategyMarketChannelDo().getTemplateId());
        reach.setNameTypeId(context.getStrategyMarketChannelDo().getTemplateId());
        reach.setStrategyExecLogId(context.getStrategyExecLogDo().getId());
        reach.setStrategyExecLogRetryId(context.getStrategyExecLogDo().getRetryId());
        reach.setStrategyRulerEnum(StrategyRulerEnum.getInstance(context.getStrategyDo().getSendRuler()));
        reach.setStrategyMarketChannelDo(context.getStrategyMarketChannelDo());
        reach.setFlowCtrlList(context.getFlowCtrlList());

        reach.setStrategyGroupName(StringUtils.replace(Optional.ofNullable(context.getStrategyGroupDo()
                .getName()).orElse(""), "组", ""));
        if (!Objects.isNull(context.getStrategyMarketChannelDo().getExtInfo())) {
            Map extInfo = JSONObject.parseObject(context.getStrategyMarketChannelDo().getExtInfo(), Map.class);
            if (!Objects.isNull(extInfo) && extInfo.containsKey("policyId")) {
                String policyId = String.valueOf(extInfo.get("policyId"));
                reach.setStrategyMarketChannelTemplateId(policyId);
            }
            if (!Objects.isNull(extInfo) && extInfo.containsKey("activityId")) {
                String activityId = String.valueOf(extInfo.get("activityId"));
                reach.setActivityId(activityId);
            }
            if (!Objects.isNull(extInfo) && extInfo.containsKey("signatureKey")) {
                String signature = String.valueOf(extInfo.get("signatureKey"));
                reach.setSignatureKey(signature);
            }
        }
        // 提额策略
        if (context.getStrategyMarketChannelDo().getMarketChannel() ==
                StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode()) {
            IncreaseAmtDto increaseAmtDto = JsonUtil.parse(context.getStrategyMarketChannelDo().getExtInfo(),
                    IncreaseAmtDto.class);
            if (increaseAmtDto == null) {
                log.error("离线策略提额配置信息异常, 策略id:{}", reach.getStrategyId());
                throw new StrategyException("离线策略提额配置信息异常");
            }
            try {
                IncreaseAmtParamDto increaseAmtParamDto = increaseAmtDto.getIncreaseAmtConfig().getParamDto();
                if (!increaseAmtParamDto.isValid()) {
                    throw new StrategyException("离线策略提额配置信息异常");
                }
                reach.setIncreaseAmtParamDto(increaseAmtParamDto);
            } catch (Exception ex) {
                log.error("离线策略提额配置信息异常, 策略id:{}, error:{}", reach.getStrategyId(), ex.getMessage(), ex);
                throw new StrategyException("离线策略提额配置信息异常");
            }
        }
        //ai
        if (context.getStrategyMarketChannelDo().getMarketChannel() ==
                StrategyMarketChannelEnum.AI_PRONTO.getCode()) {
            AiProntoChannelDto aiProntoChannelDto = JsonUtil.parse(context.getStrategyMarketChannelDo().getExtInfo(),
                    AiProntoChannelDto.class);
            if (aiProntoChannelDto == null || StringUtils.isBlank(aiProntoChannelDto.getNameTypeId())) {
                log.error("离线策略ai_pronto配置信息异常, 策略id:{}", reach.getStrategyId());
                throw new StrategyException("离线策略ai_pronto配置信息异常");
            }
            reach.setAiProntoChannelDto(aiProntoChannelDto);
        }
        return reach;
    }

    /**
     * 设置批次大小
     *
     * @return 批次大小
     */
    protected abstract Integer setBatchSize();

    /**
     * 下发
     *
     * @param strategyContext 策略执行初始化内容
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           按分组配置过滤出来的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    protected abstract <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<T> templateParam);

    /**
     * 重试下发
     *
     * @param context     策略执行初始化内容
     * @param tableNameNo 下发明细表序号
     * @param app         app
     * @param innerApp    innerApp
     * @param detailList  下发明细
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    protected abstract <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext context, String tableNameNo, String app, String innerApp, List<CrowdDetailDo> detailList, List<T> templateParam);

}
