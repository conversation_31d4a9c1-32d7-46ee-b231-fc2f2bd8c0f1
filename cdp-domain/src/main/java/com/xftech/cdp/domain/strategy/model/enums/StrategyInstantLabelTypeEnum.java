package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/27 10:55
 */
@Getter
@AllArgsConstructor
public enum StrategyInstantLabelTypeEnum {
    /**
     * 标签查询
     */
    LABEL(1),
    /**
     * 短信参数查询
     */
    SMS_PARAM(2);


    private final Integer type;


    public static StrategyInstantLabelTypeEnum getInstance(Integer type) {
        for (StrategyInstantLabelTypeEnum labelTypeEnum : StrategyInstantLabelTypeEnum.values()) {
            if (Objects.equals(labelTypeEnum.getType(), type)) {
                return labelTypeEnum;
            }
        }
        throw new StrategyException(String.format("标签类型异常，类型：%s", type));
    }

}
