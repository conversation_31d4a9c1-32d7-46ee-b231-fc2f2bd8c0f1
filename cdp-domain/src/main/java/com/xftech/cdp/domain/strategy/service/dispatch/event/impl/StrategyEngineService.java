package com.xftech.cdp.domain.strategy.service.dispatch.event.impl;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.xinfei.enginebase.util.apollo.ApolloUtil;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.repository.StrategyEngineRepository;
import com.xftech.cdp.feign.model.response.FeatureQueryResponse;
import com.xftech.cdp.feign.service.DataFeatureService;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo;
import com.xftech.cdp.infra.utils.DateUtil;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/18
 * @description StrategyEngineService
 */
@Slf4j
@Service
public class StrategyEngineService {

    @Resource
    private StrategyEngineRepository strategyEngineRepository;

    @Resource
    private DataFeatureService dataFeatureService;

    // 用户触发某个事件次数
    @Value("${feature_strategy_initiate_cnt:strategy_initiate_cnt}")
    private String STRATEGY_INITIATE_CNT;

    // 用户触发某个事件最近一次的时间
    @Value("${feature_last_strategy_initiate_time:last_strategy_initiate_time}")
    private String LAST_STRATEGY_INITIATE_TIME;

    @Value("${feature_last_strategy_initiate_time_default:-9999}")
    private String LAST_STRATEGY_INITIATE_TIME_DEFAULT;

    /**
     * 检查推入引擎次数限制
     *
     * @param strategyId
     * @return true:通过; false:不通过
     */
    public boolean checkEngineRateLimit(Long userId, Long strategyId) {
        try {
            if (!ApolloUtil.containsInJSONStrList("ReDecisionWhiteList", String.valueOf(strategyId))) {
                return true;
            }
            if (userId == null || strategyId == null) {
                return true;
            }

            StrategyEngineRateLimitDo strategyEngineRateLimitDo = strategyEngineRepository.selectByStrategyIdCache(strategyId);
            if (strategyEngineRateLimitDo == null) {
                return true;
            }
            Integer days = strategyEngineRateLimitDo.getDays();
            Integer quota = strategyEngineRateLimitDo.getQuota();
            Integer coolDownPeriod = strategyEngineRateLimitDo.getCoolDownPeriod();
            if (days == null || quota == null || coolDownPeriod == null) {
                return true;
            }

            // 查询"推入引擎次数"特征
            List<String> featureList = Lists.newArrayList(STRATEGY_INITIATE_CNT, LAST_STRATEGY_INITIATE_TIME);
            Map<String, Object> inputs = Maps.newConcurrentMap();
            inputs.put("app_user_id", userId);
            inputs.put("user_no", userId);
            inputs.put("strategy_id", strategyId);
            inputs.put("start_date", DateUtil.getRelativeZeroTime(-(days - 1)));
            inputs.put("end_date", DateUtil.convertStr(new Date()));
            FeatureQueryResponse featureQueryResponse = dataFeatureService.getDateFeatureResponse(inputs, featureList);
            LogUtil.logDebug("StrategyEngineService checkEngineRateLimit inputs={} featureLis{} featureQueryResponse={}", JSON.toJSONString(inputs), JSON.toJSONString(featureList), JSON.toJSONString(featureQueryResponse));
            if (featureQueryResponse == null) {
                return true;
            }
            Map<String, FeatureQueryResponse.FeatureValueModel> featureValues = featureQueryResponse.getFeatureValues();
            FeatureQueryResponse.FeatureValueModel featureCount = featureValues.get(STRATEGY_INITIATE_CNT);
            FeatureQueryResponse.FeatureValueModel featureTime = featureValues.get(LAST_STRATEGY_INITIATE_TIME);
            if (featureCount == null || featureCount.getObj() == null || featureTime == null || featureTime.getObj() == null) {
                return true;
            }

            // 对比
            int fCount = Integer.parseInt(featureCount.getObj().toString());
            if (fCount >= quota) {
                LogUtil.logDebug("StrategyEngineService 校验不通过 fCount={} > quota={}. user_no={}, strategy_id={}", fCount, quota, userId, strategyId);
                return false;
            }
            String featureTimeString = featureTime.getObj().toString();
            if (StringUtils.equals(featureTimeString, LAST_STRATEGY_INITIATE_TIME_DEFAULT)) {
                return true;
            }
            LocalDateTime fTime = DateUtil.parseToLocalDateTime(featureTimeString);
            if (DateUtil.calculateMinutesBetween(fTime, LocalDateTime.now()) <= coolDownPeriod) {
                LogUtil.logDebug("StrategyEngineService 校验不通过 fTime={} + coolDownPeriod={} <= 当前时间. user_no={}, strategy_id={}", fTime, coolDownPeriod, userId, strategyId);
                return false;
            }
        } catch (Exception e) {
            log.error("StrategyEngineService checkEngineRateLimit error", e);
        }
        return true;
    }

    public void checkEngineRateLimitThrowException(Long userId, Long strategyId) {
        if (!checkEngineRateLimit(userId, strategyId)) {
            throw new StrategyException(String.format("营销节点限制条件验证不通过,userId=%s,strategyId=%s", userId, strategyId));
        }
    }

}
