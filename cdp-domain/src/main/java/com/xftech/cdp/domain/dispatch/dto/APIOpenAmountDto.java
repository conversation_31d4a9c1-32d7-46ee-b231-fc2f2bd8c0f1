package com.xftech.cdp.domain.dispatch.dto;

import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Data
public class APIOpenAmountDto {
    private String innerApps;
    private Integer afterDays;
}

