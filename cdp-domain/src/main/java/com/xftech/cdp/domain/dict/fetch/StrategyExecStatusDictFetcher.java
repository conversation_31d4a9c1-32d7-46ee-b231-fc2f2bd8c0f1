package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import com.xftech.cdp.domain.strategy.model.enums.StrategyExecStatusEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@Component
public class StrategyExecStatusDictFetcher implements DictFetcher {

    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<StrategyExecStatusEnum> strategyStatusEnums = Arrays.stream(StrategyExecStatusEnum.values()).collect(Collectors.toList());
        for (StrategyExecStatusEnum strategyStatusEnum : strategyStatusEnums) {
            result.add(Dict.builder().dictCode(String.valueOf(strategyStatusEnum.getCode())).dictValue(strategyStatusEnum.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.STRATEGY_EXEC_STATUS;
    }

    @Override
    public String getDescription() {
        return "策略执行状态";
    }
}
