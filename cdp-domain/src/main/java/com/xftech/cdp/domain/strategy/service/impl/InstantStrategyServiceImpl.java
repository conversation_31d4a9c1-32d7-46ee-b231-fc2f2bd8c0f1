package com.xftech.cdp.domain.strategy.service.impl;

import com.alibaba.fastjson.JSON;

import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.resp.EngineReInputReportResp;
import com.xftech.cdp.api.dto.resp.InstantStrategyDetailResp;
import com.xftech.cdp.domain.cache.*;
import com.xftech.cdp.domain.crowd.service.ChangeService;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.sms.SmsService;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyFlowDataEntity;
import com.xftech.cdp.domain.stat.repository.StatRealtimeStrategyFlowDataRepository;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.repository.*;
import com.xftech.cdp.domain.strategy.service.EventMetaDataService;
import com.xftech.cdp.domain.strategy.service.InstantStrategyService;
import com.xftech.cdp.domain.strategy.service.StrategyInstantLabelService;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.model.enums.MarketCrowdTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelOptionEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyTimeTypeEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyCrowdPackRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventConditionRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketSubEventRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.*;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventCatchService;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItem;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * @<NAME_EMAIL>
 */

@Slf4j
@Service
public class InstantStrategyServiceImpl implements InstantStrategyService {

    private static final String YYYY_MM_DD = "yyyy-MM-dd";

    @Autowired
    private CacheStrategyMarketEventService cacheStrategyMarketEventService;
    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;
    @Autowired
    private StrategyMarketSubEventRepository strategyMarketSubEventRepository;
    @Autowired
    private StrategyMarketEventConditionRepository strategyMarketEventConditionRepository;
    @Autowired
    private CacheStrategyMarketEventConditionService cacheStrategyMarketEventConditionService;
    @Autowired
    private CacheStrategyMarketSubEventService cacheStrategyMarketSubEventService;
    @Autowired
    private StrategyInstantLabelService strategyInstantLabelService;
    @Autowired
    private EventMetaDataService eventMetaDataService;
    @Autowired
    private FlowCtrlRepository flowCtrlRepository;
    @Autowired
    private CacheFlowCtrlSerivce cacheFlowCtrlSerivce;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private StrategyCommonService strategyCommonService;
    @Autowired
    private StrategyCrowdPackRepository strategyCrowdPackRepository;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private CacheStrategyGroupService cacheStrategyGroupService;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private CacheStrategyMarketChannelService cacheStrategyMarketChannelService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private StrategyEventCatchService strategyEventCatchService;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private StatRealtimeStrategyFlowDataRepository statRealtimeStrategyFlowDataRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyService strategyService;
    @Autowired
    private TelePushService telePushService;
    @Resource
    private StrategyEngineRepository strategyEngineRepository;
    @Resource
    private EngineReDecisionDelayRepository engineReDecisionDelayRepository;
    @Autowired
    private ChangeService changeService;


    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public boolean createInstantStrategy(InstantStrategyCreateReq strategyCreateReq) {

        StrategyDo strategy = strategyRepository.getByNameAndBusinessType(strategyCreateReq.getName(), strategyCreateReq.getBusinessType());
        if (strategy != null) {
            throw new StrategyException("策略[" + strategyCreateReq.getName() + "]已存在");
        }
        //校验策略结束时间
        strategyCommonService.verifyValidEndTime(strategyCreateReq.getValidityEnd());
        //校验延迟时间
        this.verifyDelayTime(strategyCreateReq.getEventSet());
        //校验分组配置
        strategyCommonService.verifyGroups(strategyCreateReq.getStrategyGroups(), strategyCreateReq.getAbTest(), strategyCreateReq.getAbType(), strategyCreateReq.getBizKey());
        //校验渠道
        this.verifyChannel(strategyCreateReq.getStrategyGroups(), strategyCreateReq.getType());
        if (!Objects.equals("NOTIFY", strategyCreateReq.getDispatchType()) &&
                !strategyCreateReq.getMarketType().equals(2)) {
            strategyCommonService.verifyRuleConfig(strategyCreateReq.getLimitDays(), strategyCreateReq.getLimitTimes());
        }

        if (!strategyCreateReq.isEngineCodeValid()) {
            throw new StrategyException("未填写策略引擎code");
        }

        if (strategyCreateReq.getDispatchConfig() != null) {
            InstantStrategyCreateReq.DispatchTimeConfig dispatchTimeConfig = strategyCreateReq.getDispatchConfig().getDispatchTimeConfig();
            if (dispatchTimeConfig.getBegin() == null || dispatchTimeConfig.getEnd()
                    .isAfter(strategyCreateReq.getValidityEnd())) {
                throw new StrategyException("触达下发时间不能超过执行时间");
            }
        }
        // 策略主表
        StrategyDo strategyDo = new StrategyDo();
        strategyDo.setUserConvert(strategyCreateReq.getUserConvert());
        strategyDo.setName(strategyCreateReq.getName());
        strategyDo.setDetailDescription(strategyCreateReq.getDetailDescription());
        strategyDo.setAbTest(strategyCreateReq.getAbTest());
        strategyDo.setAbType(strategyCreateReq.getAbType());
        strategyDo.setSendRuler(StrategyRulerEnum.EVENT.getCode());
        strategyDo.setValidityBegin(strategyCreateReq.getValidityBegin());
        strategyDo.setValidityEnd(strategyCreateReq.getValidityEnd());
        strategyDo.setStatus(strategyCreateReq.getPublishType());
        strategyDo.setUpdatedOp(SsoUtil.get().getName());
        strategyDo.setUpdatedTime(LocalDateTime.now());
        strategyDo.setUpdatedOpMobile(SsoUtil.get().getMobile());
        strategyDo.setBusinessType(strategyCreateReq.getBusinessType());
        strategyDo.setCrowdPackId(strategyCreateReq.getCrowdPackIds());
        if ( Objects.nonNull(strategyCreateReq.getCallingSource())) {
            Integer callingSource = strategyCreateReq.getCallingSource();
            if (callingSource.equals(CallingSourceEnum.ApiHold.getCode())) {
                strategyDo.setMarketChannel(String.valueOf(StrategyMarketChannelEnum.API_HOLD.getCode()));
            } else if (callingSource.equals(CallingSourceEnum.Overloan.getCode())){
                strategyDo.setMarketChannel(String.valueOf(StrategyMarketChannelEnum.LOAN_OVERLOAD.getCode()));
            } else {
                strategyDo.setMarketChannel(StringUtils.EMPTY);
            }
        } else {
            strategyDo.setMarketChannel(strategyCommonService.getMarketChannelStr(strategyCreateReq.getStrategyGroups()));
        }
        strategyDo.setGroupType(strategyCreateReq.getStrategyGroupType());
        strategyDo.setMarketCrowdType(MarketCrowdTypeEnum.getInstance(strategyCreateReq.getMarketCrowdType()).getCode());
        strategyDo.setBizKey(strategyCommonService.getBizKey(strategyDo.getAbType(), strategyCreateReq.getBizKey()));
        strategyDo.setType(strategyCreateReq.getType());
        strategyDo.setEngineCode(strategyCreateReq.getEngineCode());
        strategyDo.setDispatchConfig(JsonUtil.toJson(strategyCreateReq.getDispatchConfig()));
        strategyDo.setDispatchType(strategyCreateReq.getDispatchType());

        strategyDo.setType(strategyCreateReq.getType());
        strategyDo.setEngineCode(strategyCreateReq.getEngineCode());

        strategyDo.setMarketType(strategyCreateReq.getMarketType());
        strategyDo.setCallingSource(strategyCreateReq.getCallingSource());

        LocalDateTime now = LocalDateTime.now();
        if (strategyDo.getStatus().equals(StrategyStatusEnum.INIT.getCode()) && now.compareTo(strategyDo.getValidityBegin()) >= 0 && now.compareTo(strategyDo.getValidityEnd()) <= 0) {
            strategyDo.setStatus(StrategyStatusEnum.EXECUTING.getCode());
        }
        cacheStrategyService.insert(strategyDo);

        if (strategyDo.getStatus().equals(StrategyStatusEnum.EXECUTING.getCode())) {
            if (Objects.nonNull(strategyCreateReq.getEventSet())) {
                strategyCreateReq.getEventSet().forEach(s -> {
                    strategyEventCatchService.resetEventTriggerTime(s.getEventName(), strategyDo);
                    strategyEventCatchService.removeHasEventButNoMatchFlag(s.getEventName(), strategyDo.getId());
                });
            }
        }
        //保存人数包
        this.saveCrowdPack(strategyCreateReq.getMarketCrowdType(), strategyCreateReq.getCrowdPackIds(), strategyCreateReq.getBusinessType(), strategyDo.getId());
        //保存营销节点
        this.saveMarketNodeInfo(strategyDo, strategyCreateReq);
        //保存流控规则
        strategyCommonService.createFlowCtrlRule(strategyCreateReq.getBusinessTypeName(), strategyDo, strategyCreateReq.getLimitDays(), strategyCreateReq.getLimitTimes(), StrategyTypeEnum.REALTIME_STRATEGY);
        // 分组配置
        strategyCreateReq.getStrategyGroups().forEach(strategyGroup -> {
            StrategyGroupDo strategyGroupDo = new StrategyGroupDo();
            strategyGroupDo.setIsExecutable(Objects.nonNull(strategyGroup.getIsExecutable()) ? strategyGroup.getIsExecutable() : 1);
            if ( Objects.nonNull(strategyCreateReq.getCallingSource()) &&
                    strategyCreateReq.getCallingSource().equals(CallingSourceEnum.ApiHold.getCode())) {
                Map<String, Object> m = new HashMap<>();
                m = JsonUtil.parse(strategyGroup.getExtInfo(), m.getClass());
                if ( Objects.nonNull(m) && m.containsKey("hold_duration")) {
                    Object object = m.get("hold_duration");
                    int duration = -1;
                    if (object instanceof String) {
                        duration = Integer.parseInt((String) object);
                    } else if (object instanceof Integer) {
                        duration = (Integer)(object);
                    }
                    if ( duration != -1 &&
                            (duration < 1 || duration > 20)) {
                        throw new BizException("api 卡单时长不在[1,20]范围内");
                    }
                }
            }
            strategyGroupDo.setExtInfo(strategyGroup.getExtInfo());
            strategyGroupDo.setStrategyId(strategyDo.getId());
            strategyGroupDo.setGroupConfig(JSON.toJSONString(strategyGroup.getGroupConfig()));
            strategyGroupDo.setName(strategyGroup.getName());
            cacheStrategyGroupService.insert(strategyGroupDo);
            if (CollectionUtils.isEmpty(strategyGroup.getStrategyMarketChannels())){
                return;
            }
            // 触达渠道
            strategyGroup.getStrategyMarketChannels().forEach(strategyMarketChannel -> {
                String extInfo = strategyMarketChannel.getExtInfo();
                if (Objects.equals(StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode(), strategyMarketChannel.getMarketChannel())) {
                    if (!strategyCommonService.checkIncreaseAmtChannnel(extInfo)) {
                        throw new StrategyException("提额渠道配置信息验证错误");
                    }
                }
                if (Objects.equals(StrategyMarketChannelEnum.AI_PRONTO.getCode(), strategyMarketChannel.getMarketChannel())) {
                    if (!strategyCommonService.checkAiProntoChannel(extInfo)) {
                        throw new StrategyException("AI-即时触达配置信息验证错误");
                    }
                }
                StrategyMarketChannelDo strategyMarketChannelDo = new StrategyMarketChannelDo();
                strategyMarketChannelDo.setStrategyId(strategyDo.getId());
                strategyMarketChannelDo.setStrategyGroupId(strategyGroupDo.getId());
                strategyMarketChannelDo.setMarketChannel(strategyMarketChannel.getMarketChannel());
                strategyMarketChannelDo.setTemplateId(strategyMarketChannel.getTemplateId());
                strategyMarketChannelDo.setApp(strategyMarketChannel.getApp());
                strategyMarketChannelDo.setDispatchApp(strategyMarketChannel.getDispatchApp());
                if (!StringUtils.isEmpty(extInfo)){
                    strategyMarketChannelDo.setExtInfo(extInfo);
                }
                cacheStrategyMarketChannelService.insert(strategyMarketChannelDo);
            });
        });

        // T0引擎策略-推入引擎次数限制
        InstantStrategyCreateReq.EngineRateLimit engineRateLimit = strategyCreateReq.getEngineRateLimit();
        if (engineRateLimit != null && isT0EngineStrategy(strategyCreateReq.getType())) {
            if (Objects.isNull(engineRateLimit.getDays()) || Objects.isNull(engineRateLimit.getQuota()) || Objects.isNull(engineRateLimit.getCoolDownPeriod())) {
                throw new StrategyException("营销节点-推入引擎次数限制, 配置错误");
            }
            strategyEngineRepository.insert(new StrategyEngineRateLimitDo(strategyDo.getId(), engineRateLimit.getDays(), engineRateLimit.getQuota(), engineRateLimit.getCoolDownPeriod()));
        }

        changeService.asyncSubmitInsertT0StrategyChange(strategyDo.getId() + "", strategyCreateReq, strategyDo);
        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(strategyDo.getId()));
        return true;
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public boolean updateInstantStrategy(InstantStrategyUpdateReq strategyUpdateReq) {
        StrategyDo strategyDo = strategyRepository.selectById(strategyUpdateReq.getId());
        strategyCommonService.verifyStrategyName(strategyDo, strategyUpdateReq.getName(), strategyUpdateReq.getId());

        if (StrategyStatusEnum.ENDED.getCode() == strategyDo.getStatus()) {
            throw new StrategyException("策略状态为已结束不支持更新");
        }

        if (!strategyUpdateReq.isEngineCodeValid()) {
            throw new StrategyException("未填写策略引擎code");
        }

        strategyCommonService.verifyValidEndTime(strategyUpdateReq.getValidityEnd());
        //校验延迟时间
        this.verifyDelayTime(strategyUpdateReq.getEventSet());
        //校验分组配置
        strategyCommonService.verifyGroups(strategyUpdateReq.getStrategyGroups(), strategyUpdateReq.getAbTest(), strategyUpdateReq.getAbType(), strategyUpdateReq.getBizKey());
        //校验渠道
        this.verifyChannel(strategyUpdateReq.getStrategyGroups(), strategyUpdateReq.getType());
        if (!Objects.equals("NOTIFY", strategyUpdateReq.getDispatchType()) &&
                !strategyUpdateReq.getMarketType().equals(2)) {
            strategyCommonService.verifyRuleConfig(strategyUpdateReq.getLimitDays(), strategyUpdateReq.getLimitTimes());
        }
        if (strategyUpdateReq.getDispatchConfig() != null) {
            DispatchConfig.DispatchTimeConfig dispatchTimeConfig = strategyUpdateReq.getDispatchConfig().getDispatchTimeConfig();
            if (dispatchTimeConfig.getBegin() == null || dispatchTimeConfig.getEnd()
                    .isAfter(strategyUpdateReq.getValidityEnd())) {
                throw new StrategyException("触达下发时间不能超过执行时间");
            }
        }
        // T0引擎策略-推入引擎次数限制
        InstantStrategyCreateReq.EngineRateLimit engineRateLimitRequest = strategyUpdateReq.getEngineRateLimit();
        if (engineRateLimitRequest != null && isT0EngineStrategy(strategyUpdateReq.getType())) {
            if (Objects.isNull(engineRateLimitRequest.getDays()) || Objects.isNull(engineRateLimitRequest.getQuota()) || Objects.isNull(engineRateLimitRequest.getCoolDownPeriod())) {
                throw new StrategyException("营销节点-推入引擎次数限制, 配置错误");
            }
        }

        // 策略主表
        strategyDo.setName(strategyUpdateReq.getName());
        strategyDo.setUserConvert(strategyUpdateReq.getUserConvert());
        strategyDo.setDetailDescription(strategyUpdateReq.getDetailDescription());
        strategyDo.setAbTest(strategyUpdateReq.getAbTest());
        strategyDo.setAbType(strategyUpdateReq.getAbType());
        //   strategyDo.setStatus(strategyUpdateReq.getPublishType());
        strategyDo.setValidityBegin(strategyUpdateReq.getValidityBegin());
        strategyDo.setValidityEnd(strategyUpdateReq.getValidityEnd());
        strategyDo.setUpdatedOp(SsoUtil.get().getName());
        strategyDo.setUpdatedTime(LocalDateTime.now());
        strategyDo.setUpdatedOpMobile(SsoUtil.get().getMobile());
        strategyDo.setBusinessType(strategyUpdateReq.getBusinessType());
        strategyDo.setCrowdPackId(MarketCrowdTypeEnum.CROWD_PACK.getCode().equals(strategyUpdateReq.getMarketCrowdType()) ? strategyUpdateReq.getCrowdPackIds() : "");
        strategyDo.setMarketChannel(strategyCommonService.getMarketChannelStr(strategyUpdateReq.getStrategyGroups()));
        strategyDo.setGroupType(strategyUpdateReq.getStrategyGroupType());
        strategyDo.setMarketCrowdType(strategyUpdateReq.getMarketCrowdType());
        strategyDo.setBizKey(strategyCommonService.getBizKey(strategyDo.getAbType(), strategyUpdateReq.getBizKey()));
        //strategyCommonService.eventStrategyExecuting(strategyUpdateReq.getEventName(), strategyDo);
        strategyCommonService.eventStrategyExecutingList(strategyUpdateReq.getEventSet(),strategyDo);
        strategyDo.setType(strategyUpdateReq.getType());
        strategyDo.setEngineCode(strategyUpdateReq.getEngineCode());
        strategyDo.setDispatchConfig(JsonUtil.toJson(strategyUpdateReq.getDispatchConfig()));
        strategyDo.setDispatchType(strategyUpdateReq.getDispatchType());
        strategyDo.setMarketType(strategyUpdateReq.getMarketType());
        strategyDo.setCallingSource(strategyUpdateReq.getCallingSource());

        this.saveCrowdPack(strategyUpdateReq.getMarketCrowdType(), strategyUpdateReq.getCrowdPackIds(), strategyUpdateReq.getBusinessType(), strategyDo.getId());

        List<Long> strategyGroupIdList = new ArrayList<>();
        List<Long> strategyChannelIdList = new ArrayList<>();
        List<Long> strategyMarketSubEventIdList = new ArrayList<>();
        List<Long> strategyMarketConditionIdList = new ArrayList<>();

        // 更新策略组
        strategyUpdateReq.getStrategyGroups().forEach(strategyGroup -> {
            //更新策略组
            StrategyGroupDo strategyGroupDo = strategyCommonService.updateStrategyGroup(strategyGroup, strategyDo.getId());
            strategyGroupIdList.add(strategyGroupDo.getId());

            // 更新触达渠道
            Optional.ofNullable(strategyGroup.getStrategyMarketChannels()).orElse(new ArrayList<>(0)).forEach(strategyMarketChannel -> {
                StrategyMarketChannelDo strategyMarketChannelDo = strategyCommonService. updateStrategyMarketChannels(strategyMarketChannel, strategyDo, strategyGroupDo, null, null);
                strategyChannelIdList.add(strategyMarketChannelDo.getId());
            });
            //更新缓存
            List<StrategyMarketChannelDo> marketChannelList = strategyMarketChannelRepository.selectByStrategyGroupId((strategyGroupDo.getId()));
            if (marketChannelList != null) {
                redisUtils.set(RedisKeyUtils.genListByStrategyGroupIdKey(strategyGroupDo.getId()), marketChannelList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
            }
        });

        // 更新策略
        cacheStrategyService.updateById(strategyDo);

        // 更新策略人群包
        strategyCrowdPackRepository.deleteByStrategyId(strategyUpdateReq.getId());

        this.saveCrowdPack(strategyUpdateReq.getMarketCrowdType(), strategyUpdateReq.getCrowdPackIds(), strategyUpdateReq.getBusinessType(), strategyDo.getId());

        List<StrategyMarketEventDo> strategyMarketEventDo = strategyMarketEventRepository.selectByStrategyId(strategyUpdateReq.getId());

        //更新事件策略
        this.updateStrategyEvent(strategyUpdateReq, strategyMarketEventDo);

        // 更新策略子事件
        // 更新触达渠道
       /* strategyUpdateReq.getEventCondition().forEach(eventCondition -> {
            StrategyMarketSubEventDo strategyMarketSubEventDo = this.updateStrategyMarketSubEvent(eventCondition, strategyMarketEventDo.getId(), strategyDo.getId());
            strategyMarketSubEventIdList.add(strategyMarketSubEventDo.getId());
        });*/

        // 更新策略营销规则
        // 更新触达渠道
        strategyUpdateReq.getMarketCondition().forEach(marketCondition -> {
            StrategyMarketEventConditionDo strategyMarketEventConditionDo = strategyCommonService.updateMarketConditionInfo(marketCondition, strategyDo.getId(), StrategyTypeEnum.REALTIME_STRATEGY);
            strategyMarketConditionIdList.add(strategyMarketEventConditionDo.getId());
        });

        // 更新排除项
        strategyCommonService.updateExcludeLabel(strategyDo.getId(), strategyUpdateReq.getExcludeType(), strategyDo.getBusinessType(), StrategyTypeEnum.REALTIME_STRATEGY);

        //更新流控规则
        this.updateRuleConfig(strategyUpdateReq, strategyDo);

        // 删除 被删除组
        List<StrategyGroupDo> strategyGroupDoList = strategyGroupRepository.selectListByStrategyId(strategyUpdateReq.getId());
        List<Long> delGroupIdList = strategyGroupDoList.stream().map(StrategyGroupDo::getId).filter(id -> !strategyGroupIdList.contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delGroupIdList)) {
            cacheStrategyGroupService.deleteBatch(delGroupIdList);
        }

        // 删除 被删除渠道
        List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectByStrategyId(strategyUpdateReq.getId());
        List<StrategyMarketChannelDo> delChannelIdList = strategyMarketChannelDos.stream().filter(item -> !strategyChannelIdList.contains(item.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delChannelIdList)) {
            List<Long> delIdList = delChannelIdList.stream().map(StrategyMarketChannelDo::getId).collect(Collectors.toList());
            cacheStrategyMarketChannelService.deleteByIdBatch(delIdList);
        }

        // 删除 被删除营销条件(默认排除项除外)
        List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList = strategyMarketEventConditionRepository.getByStrategyIdAndOption(strategyUpdateReq.getId());
        List<Long> delEventConditionIdList = strategyMarketEventConditionDoList.stream().filter(t -> StrategyInstantLabelOptionEnum.INPUT_OPTIONAL.getCode() == t.getOptional()).map(StrategyMarketEventConditionDo::getId).filter(id -> !strategyMarketConditionIdList.contains(id)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(delEventConditionIdList)) {
            cacheStrategyMarketEventConditionService.deleteBatch(delEventConditionIdList);
        }

        // T0引擎策略-推入引擎次数限制
        if (isT0EngineStrategy(strategyUpdateReq.getType())) {
            StrategyEngineRateLimitDo strategyEngineRateLimitDo = strategyEngineRepository.selectByStrategyId(strategyDo.getId());
            if (engineRateLimitRequest != null) {
                if (strategyEngineRateLimitDo != null) { // 已有 -> 更新
                    strategyEngineRateLimitDo.setDays(engineRateLimitRequest.getDays());
                    strategyEngineRateLimitDo.setQuota(engineRateLimitRequest.getQuota());
                    strategyEngineRateLimitDo.setCoolDownPeriod(engineRateLimitRequest.getCoolDownPeriod());
                    strategyEngineRateLimitDo.setUpdatedTime(new Date());
                    strategyEngineRepository.updateByPrimaryKeySelective(strategyEngineRateLimitDo);
                } else { // 没有 -> 新增
                    strategyEngineRepository.insert(new StrategyEngineRateLimitDo(strategyDo.getId(), engineRateLimitRequest.getDays(), engineRateLimitRequest.getQuota(), engineRateLimitRequest.getCoolDownPeriod()));
                }
            } else {
                if (strategyEngineRateLimitDo != null) { // 已有 -> 删除
                    strategyEngineRepository.deleteById(strategyEngineRateLimitDo.getId());
                }
            }
        }


        changeService.asyncSubmitUpdateT0StrategyChange(strategyUpdateReq, strategyDo);

        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(strategyDo.getId()));
        return true;
    }

    @Override
    public InstantStrategyDetailResp getInstantStrategyDetail(Long strategyId) {
        StrategyDo strategyDo = strategyRepository.selectById(strategyId);
        if (strategyDo == null) {
            throw new StrategyException("策略:" + strategyId + "不存在");
        }

        List<StrategyMarketEventDo> strategyMarketEventDo = strategyMarketEventRepository.selectByStrategyId(strategyId);

        InstantStrategyDetailResp strategyDetailResp = new InstantStrategyDetailResp();
        strategyDetailResp.setName(strategyDo.getName());
        strategyDetailResp.setUserConvert(strategyDo.getUserConvert());
        strategyDetailResp.setDetailDescription(strategyDo.getDetailDescription());
        strategyDetailResp.setAbTest(strategyDo.getAbTest());
        strategyDetailResp.setAbType(strategyDo.getAbType());
        strategyDetailResp.setValidityBegin(strategyDo.getValidityBegin());
        strategyDetailResp.setValidityEnd(strategyDo.getValidityEnd());
        strategyDetailResp.setMarketCrowdType(strategyDo.getMarketCrowdType());
        strategyDetailResp.setStrategyGroupType(strategyDo.getGroupType());
        strategyDetailResp.setCrowdPackIds(strategyDo.getCrowdPackId());
        strategyDetailResp.setMarketType(strategyDo.getMarketType());
        strategyDetailResp.setCallingSource(strategyDo.getCallingSource());
        strategyDetailResp.setDispatchConfig(JsonUtil.parse(strategyDo.getDispatchConfig(),
                DispatchConfig.class));
        strategyDetailResp.setDispatchType(strategyDo.getDispatchType());
        if (strategyDo.getAbType() == StrategyGroupTypeEnum.NEW_RANDOM.getCode()) {
            strategyDetailResp.setRandomItem(strategyCommonService.getRandomItem(strategyDo.getBizKey()));
        }
        if(!CollectionUtils.isEmpty(strategyMarketEventDo)){
            List<InstantStrategyCreateReq.EventSet> eventSet = Lists.newArrayList();
            strategyMarketEventDo.forEach(t->{
                InstantStrategyCreateReq.EventSet e = new  InstantStrategyCreateReq.EventSet();
                e.setId(t.getId());
                e.setDelay(new InstantStrategyCreateReq.EventSet.Delay(t.getTimeType(),t.getTimeUnit(),t.getTimeValue()));
                e.setEventName(t.getEventName());
                e.setEventCondition(getEventConditionList(t.getId()));
                eventSet.add(e);
            });
            strategyDetailResp.setEventSet(eventSet);
            strategyDetailResp.setDispatchMinUserNum(strategyMarketEventDo.get(0).getDispatchMinUserNum());
            strategyDetailResp.setDispatchMaxUserNum(strategyMarketEventDo.get(0).getDispatchMaxUserNum());
        }


        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(strategyDo.getFlowCtrlId());
        if (flowCtrlDo != null) {
            strategyDetailResp.setLimitDays(flowCtrlDo.getLimitDays());
            strategyDetailResp.setLimitTimes(flowCtrlDo.getLimitTimes());
        }

        strategyDetailResp.setType(strategyDo.getType());
        strategyDetailResp.setEngineCode(strategyDo.getEngineCode());

        List<StrategyGroupDo> strategyGroupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
        List<StrategyCreateReq.StrategyGroup> strategyGroupList = new ArrayList<>();

        for (StrategyGroupDo strategyGroupDo : strategyGroupDoList) {
            StrategyCreateReq.StrategyGroup strategyGroup = new StrategyCreateReq.StrategyGroup();
            strategyGroup.setIsExecutable(Objects.nonNull(strategyGroupDo.getIsExecutable()) ? strategyGroupDo.getIsExecutable() : 1);
            strategyGroup.setName(strategyGroupDo.getName());
            strategyGroup.setGroupConfig(JSON.parseObject(strategyGroupDo.getGroupConfig(), StrategyCreateReq.GroupConfig.class));
            strategyGroup.setGroupId(String.valueOf(strategyGroupDo.getId()));
            strategyGroup.setExtInfo(strategyGroupDo.getExtInfo());

            List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupDo.getId());
            List<StrategyCreateReq.StrategyMarketChannel> strategyMarketChannelList = strategyMarketChannelDoList.stream().map(item -> {
                StrategyCreateReq.StrategyMarketChannel strategyMarketChannel = new StrategyCreateReq.StrategyMarketChannel();
                strategyMarketChannel.setChannelId(String.valueOf(item.getId()));
                if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler()) && Objects.equals(StrategyMarketChannelEnum.SMS.getCode(), item.getMarketChannel())) {
                    SmsItem smsItem = smsService.queryTemplateDetail(item.getTemplateId(), item.getApp());
                    strategyMarketChannel.setTemplate(Objects.nonNull(smsItem) ? smsItem.getTemplate() : null);
                } else {
                    strategyMarketChannel.setTemplate(strategyCommonService.getTemplate(item));
                }
                strategyMarketChannel.setMarketChannel(item.getMarketChannel());
                strategyMarketChannel.setApp(item.getApp());
                strategyMarketChannel.setTemplateId(item.getTemplateId());
                strategyMarketChannel.setDispatchApp(item.getDispatchApp());
                if(!StringUtils.isEmpty(item.getExtInfo())){
                    strategyMarketChannel.setExtInfo(item.getExtInfo());
                }
                return strategyMarketChannel;
            }).collect(Collectors.toList());

            strategyGroup.setStrategyMarketChannels(strategyMarketChannelList);
            strategyGroupList.add(strategyGroup);
        }
        strategyDetailResp.setStrategyGroups(strategyGroupList);

        Pair<List<InstantStrategyDetailResp.MarketCondition>, List<Integer>> pair = strategyCommonService.getMarketConditionList(strategyId, StrategyTypeEnum.REALTIME_STRATEGY);
        strategyDetailResp.setMarketCondition(pair.getLeft());
        strategyDetailResp.setExcludeType(pair.getRight());

        // T0引擎策略-推入引擎次数限制
        StrategyEngineRateLimitDo strategyEngineRateLimitDo = strategyEngineRepository.selectByStrategyId(strategyDo.getId());
        if (strategyEngineRateLimitDo != null) {
            strategyDetailResp.setEngineRateLimit(new InstantStrategyCreateReq.EngineRateLimit(strategyEngineRateLimitDo.getDays(), strategyEngineRateLimitDo.getQuota(), strategyEngineRateLimitDo.getCoolDownPeriod()));
        }
        LogUtil.logDebug("InstantStrategyServiceImpl getInstantStrategyDetail strategyDo={} strategyEngineRateLimitDo={} strategyDetailResp={}", JSON.toJSONString(strategyDo), JSON.toJSONString(strategyEngineRateLimitDo), JSON.toJSONString(strategyDetailResp));
        return strategyDetailResp;
    }

    private List<InstantStrategyCreateReq.EventCondition> getEventConditionList(Long id) {
        List<StrategyMarketSubEventDo> strategyMarketSubEventList = strategyMarketSubEventRepository.getByEventId(id);

        if (strategyMarketSubEventList == null) {
            return Collections.emptyList();
        }

        return strategyMarketSubEventList.stream()
                .map(item -> {
                    InstantStrategyCreateReq.EventCondition eventCondition = new InstantStrategyCreateReq.EventCondition();
                    eventCondition.setSubEventId(item.getId());
                    eventCondition.setSubEventName(item.getEventName());
                    eventCondition.setEventValue(item.getEventValue());
                    eventCondition.setOperateType(item.getOperateType());
                    eventCondition.setEventType(item.getEventType());
                    return eventCondition;
                })
                .collect(Collectors.toList());
    }

    void saveCrowdPack(Integer marketCrowdType, String crowdPackIds, String businessType, Long strategyId) {
        boolean needVerifyFlag = Objects.equals(MarketCrowdTypeEnum.CROWD_PACK.getCode(), marketCrowdType);
        if (needVerifyFlag) {
            strategyCommonService.verifyCrowIds(crowdPackIds, businessType);
            String[] crowdIds = crowdPackIds.split(";");
            //人群包
            List<StrategyCrowdPackDo> strategyCrowdPackDoList = new ArrayList<>();
            for (String crowdPackId : crowdIds) {
                if (!StringUtils.isNumeric(crowdPackId)) {
                    throw new StrategyException("人群包id[" + crowdPackId + "]有误");
                }
                StrategyCrowdPackDo strategyCrowdPackDo = new StrategyCrowdPackDo();
                strategyCrowdPackDo.setStrategyId(strategyId);
                strategyCrowdPackDo.setCrowdPackId(Long.valueOf(crowdPackId));
                strategyCrowdPackDoList.add(strategyCrowdPackDo);
            }
            strategyCrowdPackRepository.saveBatch(strategyCrowdPackDoList);
        }
    }

    public void updateStrategyEvent(InstantStrategyUpdateReq strategyUpdateReq, List<StrategyMarketEventDo> strategyMarketEventDo) {
        List<InstantStrategyCreateReq.EventSet> eventSet = strategyUpdateReq.getEventSet();
        Map<Long, StrategyMarketEventDo> eventMap = Optional.ofNullable(strategyMarketEventDo)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(StrategyMarketEventDo::getId, Function.identity()));
        Set<Long> existEventIds = new HashSet<>();

        for (InstantStrategyCreateReq.EventSet event : eventSet) {
            Long eventId = event.getId();
            StrategyMarketEventDo eventDo = eventMap.get(eventId);
            Set<Long> reqIds = new HashSet<>();

            if (eventId == null) {
                // 新增子项
                StrategyMarketEventDo createDo = createStrategyMarketEventDo(strategyUpdateReq, event);
                cacheStrategyMarketEventService.insert(createDo);
                existEventIds.add(createDo.getId());
                updateStrategyMarketSubEvents(event.getEventCondition(), createDo.getId(), strategyUpdateReq.getId(), reqIds);
            } else if (eventDo != null) {
                // 更新子项
                List<StrategyMarketSubEventDo> strategyMarketSubEventDoList = strategyMarketSubEventRepository.getByEventId(eventId);
                updateStrategyMarketEventDo(eventDo, strategyUpdateReq, event);
                cacheStrategyMarketEventService.updateById(eventDo);
                existEventIds.add(eventDo.getId());
                updateStrategyMarketSubEvents(event.getEventCondition(), eventDo.getId(), strategyUpdateReq.getId(), reqIds);
                deleteUnusedStrategyMarketSubEvents(strategyMarketSubEventDoList, reqIds);
            } else {
                throw new StrategyException("Data has an exception, please check!!");
            }
        }

        updateUnusedStrategyMarketEvents(strategyMarketEventDo, existEventIds);
    }

    private void updateStrategyMarketSubEvents(List<InstantStrategyCreateReq.EventCondition> eventConditions, Long eventId, Long strategyId, Set<Long> reqIds) {
        if (!CollectionUtils.isEmpty(eventConditions)) {
            eventConditions.forEach(condition -> {
                StrategyMarketSubEventDo subEvent = updateStrategyMarketSubEvent(condition, eventId, strategyId);
                reqIds.add(subEvent.getId());
            });
        }
    }

    private void deleteUnusedStrategyMarketSubEvents(List<StrategyMarketSubEventDo> subEventList, Set<Long> reqIds) {
        if (!CollectionUtils.isEmpty(subEventList)) {
            List<Long> delSubEventIdList = subEventList.stream()
                    .map(StrategyMarketSubEventDo::getId)
                    .filter(id -> !reqIds.contains(id))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(delSubEventIdList)) {
                cacheStrategyMarketSubEventService.deleteBatch(delSubEventIdList);
            }
        }
    }

    private void updateUnusedStrategyMarketEvents(List<StrategyMarketEventDo> strategyMarketEventDo, Set<Long> existEventIds) {
        Optional.ofNullable(strategyMarketEventDo)
                .ifPresent(events -> events.stream()
                        .filter(event -> !existEventIds.contains(event.getId()))
                        .forEach(event -> {
                            event.setUpdatedOp(SsoUtil.get().getName());
                            event.setDFlag(1);
                            cacheStrategyMarketEventService.updateById(event);
                        }));
    }


    private StrategyMarketEventDo createStrategyMarketEventDo(InstantStrategyUpdateReq strategyUpdateReq, InstantStrategyCreateReq.EventSet event) {
        StrategyMarketEventDo createDo = new StrategyMarketEventDo(
                strategyUpdateReq.getId(), event.getEventName(),
                event.getDelay().getTimeType(), event.getDelay().getTimeValue(), event.getDelay().getTimeUnit(),
                strategyUpdateReq.getDispatchMinUserNum(), strategyUpdateReq.getDispatchMaxUserNum(),""
        );
        createDo.setCreatedOp(SsoUtil.get().getName());
        createDo.setUpdatedOp(SsoUtil.get().getName());
        return createDo;
    }

    private void updateStrategyMarketEventDo(StrategyMarketEventDo eventDo, InstantStrategyUpdateReq strategyUpdateReq, InstantStrategyCreateReq.EventSet event) {
        eventDo.setEventName(event.getEventName());
        eventDo.setTimeType(event.getDelay().getTimeType());
        eventDo.setTimeValue(event.getDelay().getTimeValue());
        eventDo.setTimeUnit(event.getDelay().getTimeUnit());
        eventDo.setDispatchMinUserNum(strategyUpdateReq.getDispatchMinUserNum());
        eventDo.setDispatchMaxUserNum(strategyUpdateReq.getDispatchMaxUserNum());
        eventDo.setUpdatedOp(SsoUtil.get().getName());
    }


    StrategyMarketSubEventDo updateStrategyMarketSubEvent(InstantStrategyCreateReq.EventCondition eventCondition, Long marketEventId, Long strategyId) {
        StrategyMarketSubEventDo strategyMarketSubEventDo;
        if (eventCondition.getSubEventId() != null) {
            strategyMarketSubEventDo = strategyMarketSubEventRepository.selectById(eventCondition.getSubEventId());
            if (strategyMarketSubEventDo == null) {
                throw new StrategyException(eventCondition.getSubEventId() + "子事件不存在");
            }
        } else {
            strategyMarketSubEventDo = new StrategyMarketSubEventDo();
        }
        strategyMarketSubEventDo.setStrategyId(strategyId);
        strategyMarketSubEventDo.setMarketEventId(marketEventId);
        strategyMarketSubEventDo.setEventName(eventCondition.getSubEventName());
        strategyMarketSubEventDo.setEventValue(eventCondition.getEventValue());
        strategyMarketSubEventDo.setOperateType(eventCondition.getOperateType());

        EventMetaDataDo eventMetaDataDo = eventMetaDataService.selectByEventName(eventCondition.getSubEventName());

        String expression = strategyCommonService.convertToExpression(eventCondition.getSubEventName(), eventCondition.getOperateType(), eventCondition.getEventValue(), eventMetaDataDo.getLabelValueType());
        strategyMarketSubEventDo.setExpression(expression);
        strategyMarketSubEventDo.setRelationship(1);
        strategyMarketSubEventDo.setUpdatedOp(SsoUtil.get().getName());
        strategyMarketSubEventDo.setEventType(eventMetaDataDo.getEventType());
        strategyMarketSubEventDo.setUpdatedTime(LocalDateTime.now());
        if (eventCondition.getSubEventId() != null) {
            cacheStrategyMarketSubEventService.updateById(strategyMarketSubEventDo);
        } else {
            cacheStrategyMarketSubEventService.insert(strategyMarketSubEventDo);
        }
        return strategyMarketSubEventDo;
    }


    public void updateRuleConfig(InstantStrategyUpdateReq strategyUpdateReq, StrategyDo strategyDo) {
        if (StringUtils.equalsIgnoreCase("NOTIFY", strategyDo.getDispatchType())){
            return;
        }
        if (strategyDo.getFlowCtrlId() == null || strategyDo.getFlowCtrlId() < 0){
            return;
        }
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(strategyDo.getFlowCtrlId());
        flowCtrlDo.setName(strategyUpdateReq.getBusinessTypeName() + "-" + strategyDo.getName() + "-" + strategyDo.getValidityBegin().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        flowCtrlDo.setLimitDays(strategyUpdateReq.getLimitDays());
        flowCtrlDo.setLimitTimes(strategyUpdateReq.getLimitTimes());
        flowCtrlDo.setUpdatedOp(SsoUtil.get().getName());
        cacheFlowCtrlSerivce.updateById(flowCtrlDo);
    }


    void saveMarketNodeInfo(StrategyDo strategyDo, InstantStrategyCreateReq strategyCreateReq) {
        strategyCommonService.saveMarketConditionInfo(strategyDo, strategyCreateReq.getMarketCondition(), strategyCreateReq.getExcludeType(), strategyCreateReq.getBusinessType(), StrategyTypeEnum.REALTIME_STRATEGY);

        if (Objects.isNull(strategyCreateReq.getEventSet())) {
            return;
        }

        strategyCreateReq.getEventSet().forEach(s->{
            StrategyMarketEventDo strategyMarketEventDo = new StrategyMarketEventDo();
            strategyMarketEventDo.setStrategyId(strategyDo.getId());
            strategyMarketEventDo.setEventName(s.getEventName());
            if(Objects.nonNull(s.getDelay())){
                strategyMarketEventDo.setTimeType(s.getDelay().getTimeType());
                strategyMarketEventDo.setTimeUnit(s.getDelay().getTimeUnit());
                strategyMarketEventDo.setTimeValue(s.getDelay().getTimeValue());
            }
            strategyMarketEventDo.setDispatchMinUserNum(strategyCreateReq.getDispatchMinUserNum());
            strategyMarketEventDo.setDispatchMaxUserNum(strategyCreateReq.getDispatchMaxUserNum());
            strategyMarketEventDo.setCreatedOp(strategyDo.getCreatedOp());
            strategyMarketEventDo.setUpdatedOp(strategyDo.getUpdatedOp());
            cacheStrategyMarketEventService.insert(strategyMarketEventDo);

            List<InstantStrategyCreateReq.EventCondition> eventConditions = s.getEventCondition();
            if ( !CollectionUtils.isEmpty(eventConditions)) {
                List<StrategyMarketSubEventDo> subEventDoList = eventConditions.stream().map(item -> {
                    StrategyMarketSubEventDo strategyMarketSubEventDo = new StrategyMarketSubEventDo();
                    strategyMarketSubEventDo.setStrategyId(strategyDo.getId());
                    strategyMarketSubEventDo.setMarketEventId(strategyMarketEventDo.getId());
                    strategyMarketSubEventDo.setEventName(item.getSubEventName());
                    strategyMarketSubEventDo.setEventValue(item.getEventValue());
                    strategyMarketSubEventDo.setOperateType(item.getOperateType());

                    EventMetaDataDo eventMetaDataDo = eventMetaDataService.selectByEventName(item.getSubEventName());
                    if (Objects.isNull(eventMetaDataDo)) {
                        log.warn("[{}] 没有EventMetaData", item.getSubEventName());
                        return null;
                    }

                    String expression = strategyCommonService.convertToExpression(item.getSubEventName(), item.getOperateType(), item.getEventValue(),
                            eventMetaDataDo.getLabelValueType());
                    strategyMarketSubEventDo.setExpression(expression);
                    strategyMarketSubEventDo.setRelationship(1);
                    strategyMarketSubEventDo.setCreatedOp(strategyDo.getCreatedOp());
                    strategyMarketSubEventDo.setUpdatedOp(strategyDo.getUpdatedOp());
                    strategyMarketSubEventDo.setEventType(eventMetaDataDo.getEventType());
                    return strategyMarketSubEventDo;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                cacheStrategyMarketSubEventService.insertBatch(subEventDoList);
            }

        });
        }


    void verifyRuleConfig(String dayCount, String weekCount, String monthCount) {
        if (StringUtils.isEmpty(dayCount) && StringUtils.isEmpty(weekCount) && StringUtils.isEmpty(monthCount)) {
            throw new BizException("流控触达次数需至少配置一个");
        }
    }

    void verifyDelayTime(Integer timeType, Integer timeValue) {
        if (Objects.equals(StrategyTimeTypeEnum.DELAY.getCode(), timeType) && timeValue <= 0) {
            throw new BizException("延迟时间须大于0");
        }
    }

    void verifyDelayTime(List<InstantStrategyCreateReq.EventSet> eventSet) {
        if ( !CollectionUtils.isEmpty(eventSet)) {
            eventSet.stream()
                    .map(InstantStrategyCreateReq.EventSet::getDelay)
                    .filter(Objects::nonNull)
                    .forEach(delay -> verifyDelayTime(delay.getTimeType(), delay.getTimeValue()));
        }
    }

    void verifyChannel(List<StrategyCreateReq.StrategyGroup> strategyGroups, Integer type) {
        if (!CollectionUtils.isEmpty(strategyGroups)) {
            boolean flag = false;
            if (!Objects.equals(type, 1)) {
                // 非引擎策略
                flag = strategyGroups.stream()
                        .anyMatch(item -> item.getStrategyMarketChannels().stream()
                                .anyMatch(t -> StrategyMarketChannelEnum.hasTemplateId().contains(t.getMarketChannel()) && StringUtils.isBlank(t.getTemplateId())));
            }
            if (flag) {
                throw new StrategyException("模板id不能为空");
            }
        }
    }

    @Override
    public void strategyEventRefreshStatus() {
        List<StrategyDo> eventStrategy = strategyRepository.getEventStrategy();
        Map<Integer, List<StrategyDo>> strategyMap = eventStrategy.stream().collect(Collectors.groupingBy(StrategyDo::getStatus));
        strategyMap.forEach((k, v) -> TransactionUtil.transactional(() -> {
            StrategyStatusEnum statusEnum = StrategyStatusEnum.getInstance(k);
            switch (statusEnum) {
                case INIT:
                    v.stream().filter(strategyDo -> LocalDateTime.now().compareTo(strategyDo.getValidityBegin()) >= 0).forEach(item -> {
                        item.setStatus(StrategyStatusEnum.EXECUTING.getCode());
                        cacheStrategyService.updateById(item);

                        cacheStrategyService.refreshStrategy(item.getId(), item);

                        List<StrategyMarketEventDo> marketEventDo = strategyMarketEventRepository.selectByStrategyId(item.getId());
                        if (!CollectionUtils.isEmpty(marketEventDo)) {
                            marketEventDo.forEach(t->{
                                strategyEventCatchService.resetEventTriggerTime(t.getEventName(), item);
                                cacheStrategyMarketEventService.refreshStrategyMarketEventCache(t.getEventName());

                            });
                            }

                    });
                    break;
                case PAUSING:
                case EXECUTING:
                    // 事件14天内将结束预警，只预警不改状态
                    v.stream().filter(item -> LocalDateTime.now().compareTo(item.getValidityEnd().minusDays(14)) > 0
                            && LocalDateTime.now().compareTo(item.getValidityEnd()) < 0).forEach(item ->{
                        strategyService.strategy14DayEndAlarm(item);
                    });

                    v.stream().filter(item -> LocalDateTime.now().compareTo(item.getValidityEnd()) > 0).forEach(item -> {
                        item.setStatus(StrategyStatusEnum.ENDED.getCode());
                        cacheFlowCtrlSerivce.closeByStrategyId(item.getId());

                        cacheStrategyService.updateById(item);

                        cacheStrategyService.delCacheStrategy(item.getId());

                        List<StrategyMarketEventDo> marketEventDo = strategyMarketEventRepository.selectByStrategyId(item.getId());
                        if (!CollectionUtils.isEmpty(marketEventDo)) {
                            marketEventDo.forEach(t->{
                                cacheStrategyMarketEventService.refreshStrategyMarketEventCache(t.getEventName());
                            });
                        }

                        strategyService.strategyEndAlarm(item);
                    });
                    break;
                default:

            }
        }));
    }

    /**
     * T0策略昨天触达人数告警
     */
    @Override
    public void t0StrategyDispatchUserNumAlarm() {
        LocalDate now = LocalDate.now();
        LocalDate yesterday = now.plusDays(-1);
        LocalDateTime startTime = LocalDateTime.of(yesterday, LocalTime.of(0, 0, 0));
        LocalDateTime endTime = LocalDateTime.of(yesterday, LocalTime.of(23, 59, 59));

        List<StrategyDo> eventStrategy = strategyRepository.getEventStrategyByValidity(startTime, endTime);
        eventStrategy.forEach(strategyDo -> {
            StrategyStatusEnum strategyStatusEnum = StrategyStatusEnum.getInstance(strategyDo.getStatus());
            if (StrategyStatusEnum.PAUSING == strategyStatusEnum || StrategyStatusEnum.DEPRECATED == strategyStatusEnum) {
                log.info("策略[{}]暂停/已删除状态，不需要告警", strategyDo.getId());
                return;
            }

            if (StrategyStatusEnum.ENDED == strategyStatusEnum && LocalDate.now().compareTo(strategyDo.getValidityEnd().toLocalDate().plusDays(1)) > 0) {
                log.info("策略[{}]已超过有效期结束时间一天以上，不需要告警", strategyDo.getId());
                return;
            }

            List<StrategyMarketEventDo> marketEventDo = strategyMarketEventRepository.selectByStrategyId(strategyDo.getId());
            Integer dispatchMinUserNum = null;
            Integer dispatchMaxUserNum = null;
            if(!CollectionUtils.isEmpty(marketEventDo)){
                 dispatchMinUserNum = marketEventDo.get(0).getDispatchMinUserNum();
                 dispatchMaxUserNum = marketEventDo.get(0).getDispatchMaxUserNum();
            }
            if (Objects.isNull(dispatchMinUserNum) || Objects.isNull(dispatchMaxUserNum)) {
                log.info("策略[{}]预期执行人数区间配置异常，dispatchMinUserNum：{}，dispatchMaxUserNum：{}", strategyDo.getId(), dispatchMinUserNum, dispatchMaxUserNum);
                return;
            }

            StatRealtimeStrategyFlowDataEntity flowDataEntity = statRealtimeStrategyFlowDataRepository.selectByStrategyIdAndBizDate(strategyDo.getId(), yesterday);
            int count = Optional.ofNullable(flowDataEntity).map(StatRealtimeStrategyFlowDataEntity::getDispatchNum).orElse(0);
            if (dispatchMinUserNum <= count && count <= dispatchMaxUserNum) {
                log.info("策略ID：{}，策略名称：{}，{}应发用户数{}人，在预期区间！！！", strategyDo.getId(), strategyDo.getName(), yesterday, count);
                return;
            }

            strategyDo.alarmDingTalk(
                    dingTalkConfig.getAlarmUrl(),
                    Collections.singletonList(strategyDo.getUpdatedOpMobile()),
                    String.format("%s应发用户数%s人，不在预期区间", yesterday, count),
                    null
            );
        });
    }

    private boolean isT0EngineStrategy(Integer type) {
        return Objects.equals(type, 1);
    }

    /**
     * 引擎延迟决策-监控报表
     * @param strategyId
     * @param queryDate
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageResultResponse<EngineReInputReportResp> monitorEngineReInput(Long strategyId, Integer queryDate, Integer pageNum, Integer pageSize) {
        Page<EngineReDecisionDelayReportDo> records = engineReDecisionDelayRepository.selectPageByStrategyIdAndDate(strategyId, queryDate, pageNum, pageSize);
        if (CollectionUtils.isEmpty(records.getList())) {
            return null;
        }
        List<EngineReInputReportResp> respList = covertToEngineReInputReportResp(records.getList());
        return new PageResultResponse<>(respList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    private List<EngineReInputReportResp> covertToEngineReInputReportResp(List<EngineReDecisionDelayReportDo> records) {
        List<EngineReInputReportResp> respList = new ArrayList<>();
        records.forEach(item -> {
            EngineReInputReportResp resp = new EngineReInputReportResp();
            resp.setGroupId(item.getGroupName());
            resp.setReinputCount(item.getReInputCount());

            resp.setNumberOfDelayed(item.getTotalRecords());
            resp.setNumberOfPeopleDelayed(item.getDistinctUserCount());
            resp.setNumberOfEntryEngine(item.getTotalStatus1Records());
            resp.setNumberOfPeopleEntryEngine(item.getDistinctUserCountStatus1());
            resp.setNumberOfMarketing(item.getTotalReInputResult3Records());
            resp.setNumberOfPeopleMarketing(item.getDistinctUserReInputResult3());
            resp.setNumberOfPeopleExcludedByTagFiltering(item.getDistinctUserReInputResult1());
            resp.setNumberOfDistribution(item.getTotalReInputResult3Records());
            resp.setNumberOfPeopleDistribution(item.getDistinctUserReInputResult3());
            resp.setNumberOfFail(item.getTotalStatus2Records());
            resp.setNumberOfPeopleFail(item.getDistinctUserCountStatus2());

            respList.add(resp);
        });
        return respList;
    }

}
