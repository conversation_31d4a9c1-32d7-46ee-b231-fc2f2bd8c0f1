/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ StrategyExecCycleCounterRepository, v 0.1 2023/11/3 13:48 yye.xu Exp $
 */

@Component
public class StrategyExecCycleCounterRepository {

    public void insert(StrategyExecCycleCounterDo strategyExecCycleCounterDo){
        DBUtil.insert("strategyExecCycleCounterMapper.insertSelective", strategyExecCycleCounterDo);
    }

    public StrategyExecCycleCounterDo selectStrategyCycleCounter(Long strategyId) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        return DBUtil.selectOne("strategyExecCycleCounterMapper.selectStrategyCycleCounterByStrategyId", param);
    }

    public StrategyExecCycleCounterDo selectStrategyCycleCounter(Long strategyId, int dateValue) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("dateValue", dateValue);
        return DBUtil.selectOne("strategyExecCycleCounterMapper.selectStrategyCycleCounter", param);
    }

    public int updateStrategyCycleCounter(Long strategyId, int dateValue) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("dateValue", dateValue);
        return DBUtil.update("strategyExecCycleCounterMapper.updateStrategyCycleCounter", param);
    }
}