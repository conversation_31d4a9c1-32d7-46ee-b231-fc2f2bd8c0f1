package com.xftech.cdp.domain.crowd.model.label.crowd;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.label.LabelConfigurationOptionRadio;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2023/2/13 10:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FixedRadioOption extends CrowdLabelOption {

    private Boolean result;

    /**
     * column is null/column = 0
     */
    @Override
    public StringBuilder condition(String column, String configOptionReflect) {
        LabelConfigurationOptionRadio labelConfigurationOptionRadio = JSON.parseObject(configOptionReflect, LabelConfigurationOptionRadio.class);
        return super.condition(column, configOptionReflect).append(result ? labelConfigurationOptionRadio.getYes() : labelConfigurationOptionRadio.getNo());
    }

    @Override
    public void verify() {
        if (ObjectUtils.isEmpty(result)) {
            throw new CrowdException("单选项数据错误！");
        }
    }
}
