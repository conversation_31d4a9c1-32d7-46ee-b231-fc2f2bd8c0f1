/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ EventFlcConfig, v 0.1 2023/10/25 14:33 yye.xu Exp $
 */

@Data
public class EventFlcConfig {
    // 配置值， key:事件名, value: 多少秒1次;
    private Map<String, Integer> eventFlcMap;


    @JsonIgnore
    @JSONField(serialize = false)
    public Integer getLimitSeconds(String eventName) {
        if (eventFlcMap != null && eventFlcMap.containsKey(eventName)) {
            return eventFlcMap.get(eventName);
        }
        return null;
    }
}