/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.factory;

import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ MonitorListGroupResp, v 0.1 2023/12/14 15:20 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MonitorListGroupResp extends AbsMonitorListResp {

    @ApiModelProperty(value = "下发日期")
    private LocalDateTime bizDate;

    @ApiModelProperty(value = "是否进入引擎")
    private Boolean ifEngine;

    @ApiModelProperty(value = "groupId")
    protected String groupId;

    @ApiModelProperty(value = "引擎分组标识")
    protected String groupSource;

    @ApiModelProperty(value = "发送渠道")
    protected String sendChannel;

    @ApiModelProperty(value = "模板编号")
    protected String templateId;

    @ApiModelProperty(value = "状态")
    protected String status;

    @ApiModelProperty(value = "分组人数")
    protected Integer execNum;

    @ApiModelProperty(value = "应发人数")
    protected Integer triggerNum;

    @ApiModelProperty(value = "麻雀推送人数")
    protected Integer pushNum;

    @ApiModelProperty(value = "渠道接收人数")
    protected Integer receiveNum;

    @ApiModelProperty(value = "麻雀发送率")
    protected String sendRate;

    @ApiModelProperty(value = "渠道成功人数")
    protected Integer channelSuccNum;

    @ApiModelProperty(value = "渠道触达成功率")
    protected String channelSuccRate;

    @ApiModelProperty(value = "营销触达成功率")
    protected String reachSuccRate;


}