/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.log.QueryOperateLogReq;
import com.xftech.cdp.domain.stat.entity.StatStrategyGroupDataEntity;
import com.xftech.cdp.infra.repository.cdp.strategy.po.OperateLogDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ OperateLogRepository, v 0.1 2024/4/11 16:03 lingang.han Exp $
 */

@Component
public class OperateLogRepository {
    public StatStrategyGroupDataEntity selectById(Long id) {
        return DBUtil.selectOne("operateLog.selectByPrimaryKey", id);
    }

    public boolean insert(OperateLogDo entity) {
        return DBUtil.insert("operateLog.insertSelective", entity) > 0;
    }

    public boolean updateById(OperateLogDo entity) {
        return DBUtil.update("operateLog.updateByPrimaryKeySelective", entity) > 0;
    }

    public Page<OperateLogDo> selectPage(QueryOperateLogReq request) {
        return DBUtil.selectPage("operateLog.selectPage", request, request.getBeginNum(), request.getSize());
    }

    public boolean saveBatch(List<OperateLogDo> operateLogDoList) {
         return DBUtil.insertBatch("operateLog.insertSelective",operateLogDoList) > 0;
    }
}