package com.xftech.cdp.domain.flowctrl.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.domain.flowctrl.model.dto.FlowCtrlDto;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlInterceptionLogRepository;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlInterceptionLogService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlInterceptionLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/29 16:46
 */
@Slf4j
@Service
public class FlowCtrlInterceptionLogServiceImpl implements FlowCtrlInterceptionLogService {

    @Autowired
    private FlowCtrlInterceptionLogRepository flowCtrlInterceptionLogRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 保存拦截日志
     */
    @Override
    public void saveInterceptionLog(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList) {
        log.info("流控拦截用户数量：{}", flowCtrlRefuseList.size());
        if (CollectionUtils.isEmpty(flowCtrlRefuseList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        List<FlowCtrlInterceptionLogDo> list = flowCtrlRefuseList.stream().map(item -> {
            FlowCtrlInterceptionLogDo interceptionLogDo = new FlowCtrlInterceptionLogDo();
            interceptionLogDo.setUserId(item.getRight());
            interceptionLogDo.setFlowCtrlId(item.getLeft());
            interceptionLogDo.setStrategyId(flowCtrlDto.getMarketChannelDo().getStrategyId());
            interceptionLogDo.setStrategyChannelId(flowCtrlDto.getMarketChannelDo().getId());
            interceptionLogDo.setInterceptionTime(now);
            interceptionLogDo.setMessageId(flowCtrlDto.getMessageId());
            interceptionLogDo.setTriggerDatetime(Objects.nonNull(flowCtrlDto.getTriggerDatetime()) ? flowCtrlDto.getTriggerDatetime() : now);
            interceptionLogDo.setCreatedTime(now);
            interceptionLogDo.setUpdatedTime(now);
            interceptionLogDo.setMarketChannel(flowCtrlDto.getMarketChannelDo().getMarketChannel());
            interceptionLogDo.setBizEventType(flowCtrlDto.getBizEventType());
            return interceptionLogDo;
        }).collect(Collectors.toList());
        statFlowCtrl(list, flowCtrlDto.getStrategyRulerEnum());
        flowCtrlInterceptionLogRepository.saveBatch(list);
    }

    @Override
    public void saveInterceptionLogNew(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList) {
        log.info("流控拦截用户数量：{}", flowCtrlRefuseList.size());
        if (CollectionUtils.isEmpty(flowCtrlRefuseList)) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        List<FlowCtrlInterceptionLogDo> list = flowCtrlRefuseList.stream().map(item -> {
            FlowCtrlInterceptionLogDo interceptionLogDo = new FlowCtrlInterceptionLogDo();
            interceptionLogDo.setUserId(item.getRight());
            interceptionLogDo.setFlowCtrlId(item.getLeft());
            interceptionLogDo.setStrategyId(flowCtrlDto.getMarketChannelDo().getStrategyId());
            interceptionLogDo.setStrategyChannelId(flowCtrlDto.getMarketChannelDo().getId());
            interceptionLogDo.setInterceptionTime(now);
            interceptionLogDo.setMessageId(flowCtrlDto.getMessageId());
            interceptionLogDo.setTriggerDatetime(Objects.nonNull(flowCtrlDto.getTriggerDatetime()) ? flowCtrlDto.getTriggerDatetime() : now);
            interceptionLogDo.setCreatedTime(now);
            interceptionLogDo.setUpdatedTime(now);
            interceptionLogDo.setMarketChannel(flowCtrlDto.getMarketChannelDo().getMarketChannel());
            interceptionLogDo.setBizEventType(flowCtrlDto.getBizEventType());
            interceptionLogDo.setNewFlag(1);
            return interceptionLogDo;
        }).collect(Collectors.toList());
        statFlowCtrl(list, flowCtrlDto.getStrategyRulerEnum());
        flowCtrlInterceptionLogRepository.saveBatch(list);
    }

    @Override
    public void saveInterceptionLog(FlowCtrlInterceptionLogDo flowCtrlInterceptionLogDo) {
        flowCtrlInterceptionLogRepository.saveBatch(Arrays.asList(flowCtrlInterceptionLogDo));
    }

    private void statFlowCtrl(List<FlowCtrlInterceptionLogDo> list, StrategyRulerEnum strategyRulerEnum) {
        if (StrategyRulerEnum.EVENT == strategyRulerEnum) {
            for (FlowCtrlInterceptionLogDo item : list) {
                String curDate = LocalDateTimeUtil.format(item.getTriggerDatetime(), "yyyyMMdd");
                if (item.getStrategyChannelId() == null || item.getStrategyChannelId() < 1){
                    redisUtils.increment(RedisKeyUtils.genStatDecnFlowSum(curDate, item.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
                    return;
                }
                StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.cacheSelectById(item.getStrategyChannelId());
                if (strategyMarketChannelDo == null){
                    return;
                }
                redisUtils.increment(RedisKeyUtils.genStatDecnFlowSum(curDate, item.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
                String key = RedisKeyUtils.genStatDecnUserFlow(curDate, item.getStrategyId(), strategyMarketChannelDo.getStrategyGroupId());
                redisUtils.pfAddTwoDay(key, item.getUserId());
            }
        }
    }
}
