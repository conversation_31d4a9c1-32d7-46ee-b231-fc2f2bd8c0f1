/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.strategy.repository.ReportDailyStrategyRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo;
import groovy.util.logging.Slf4j;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ ReportDailyStrategyService, v 0.1 2023/12/1 16:04 lingang.han Exp $
 */

@Service
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class ReportDailyStrategyService {

    @Autowired
    private ReportDailyStrategyRepository repository;

    public ReportDailyStrategyDo queryById(Long id) {
        return repository.selectById(id);
    }

    public boolean save(ReportDailyStrategyDo reportDailyStrategyDo) {
        return repository.insert(reportDailyStrategyDo);
    }

    public boolean updateById(ReportDailyStrategyDo reportDailyStrategyDo) {
        return repository.updateById(reportDailyStrategyDo);
    }

    public void saveOrUpdateBatch(List<ReportDailyStrategyDo> reportDailyStrategyDoList) {
        for (ReportDailyStrategyDo reportDailyStrategyDo : reportDailyStrategyDoList) {
            if (repository.existReportDailyStrategy(reportDailyStrategyDo)) {
                repository.updateByDateAndChannelId(reportDailyStrategyDo);
            } else {
                save(reportDailyStrategyDo);
            }
        }
    }

    public List<ReportDailyStrategyDo> selectTodayFail() {
        return repository.selectTodayFail();
    }

    public List<ReportDailyStrategyDo> selectTodayList() {
        return repository.selectToday();
    }
}