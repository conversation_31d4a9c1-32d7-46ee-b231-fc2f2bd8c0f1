package com.xftech.cdp.domain.flowctrl.service;

import com.xftech.cdp.domain.flowctrl.model.dto.FlowCtrlDto;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;

import java.util.List;

/**
 * 流控核心逻辑
 *
 * @<NAME_EMAIL>
 * @date 2023/3/24 16:44
 */
public interface FlowCtrlCoreService {
    /**
     * 获取当前策略渠道需要用到的流控规则
     *
     * @param strategyId 策略ID
     * @param channel    渠道
     * @return 当前策略渠道需要用到的流控规则
     */
    List<FlowCtrlDo> getFlowCtrlRule(Long strategyId, Integer channel, String bizType);

    /**
     * 根据配置的流控规则过滤用户
     *
     * @param flowCtrlDto 流控参数
     * @return 根据流控规则过滤后的用户集合
     */
    List<CrowdDetailDo> flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList);


    List<FlowCtrlDo> getFlowCtrlRulesOnline(Integer strategyType, Long strategyId);
}
