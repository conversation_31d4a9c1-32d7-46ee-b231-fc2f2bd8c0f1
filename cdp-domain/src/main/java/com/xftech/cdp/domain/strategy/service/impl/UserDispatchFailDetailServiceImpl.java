package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchFailDetailDto;
import com.xftech.cdp.domain.strategy.repository.UserDispatchFailDetailRepository;
import com.xftech.cdp.domain.strategy.service.UserDispatchFailDetailService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.param.po.UserDispatchFailDetailDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserDispatchFailDetailServiceImpl implements UserDispatchFailDetailService {

    @Autowired
    private UserDispatchFailDetailRepository userDispatchFailDetailRepository;

    /**
     * 批量保存用户下发失败明细
     *
     * @param dispatchFailDetail 失败明细记录
     * @return 保存成功数量
     */
    @Override
    public int batchSave(UserDispatchFailDetailDto dispatchFailDetail) {
        List<UserDispatchFailDetailDo> failDetailList = dispatchFailDetail.getList().stream().map(userInfo -> {
            UserDispatchFailDetailDo failDetail = new UserDispatchFailDetailDo();
            failDetail.setAppUserId(userInfo.getUserId());
            failDetail.setMobile(userInfo.getMobile());
            failDetail.setApp(userInfo.getApp());
            failDetail.setStrategyId(dispatchFailDetail.getStrategyId());
            failDetail.setMarketChannel(dispatchFailDetail.getMarketChannel());
            failDetail.setTemplateType(dispatchFailDetail.getMarketChannel());
            failDetail.setTemplateNo(dispatchFailDetail.getTemplateNo());
            failDetail.setStatus(0);
            failDetail.setDispatchTime(LocalDateTime.now());
            failDetail.setFailReason(dispatchFailDetail.getFailReason());
            failDetail.setGroupName(dispatchFailDetail.getGroupName());
            failDetail.setBizEventType(dispatchFailDetail.getBizEventType());
            return failDetail;
        }).collect(Collectors.toList());
        return userDispatchFailDetailRepository.batchSave(failDetailList);
    }

    @Override
    public void saveFailRecords(List<CrowdDetailDo> crowdDetails, DispatchDto dispatchDto, String failReason, String groupName) {
        if (CollectionUtils.isEmpty(crowdDetails)) {
            return;
        }
        try {
            UserDispatchFailDetailDto dispatchFailDetail = new UserDispatchFailDetailDto();
            dispatchFailDetail.setStrategyId(dispatchDto.getStrategyId());
            dispatchFailDetail.setMarketChannel(dispatchDto.getStrategyChannel());
            dispatchFailDetail.setTemplateNo(dispatchDto.getStrategyMarketChannelTemplateId());
            dispatchFailDetail.setGroupName(groupName);
            dispatchFailDetail.setFailReason(failReason);
            dispatchFailDetail.setList(crowdDetails.stream().map(crowdDetailDo -> {
                UserDispatchFailDetailDto.UserInfo userInfo = new UserDispatchFailDetailDto.UserInfo();
                userInfo.setUserId(crowdDetailDo.getUserId());
                userInfo.setApp(crowdDetailDo.getApp());
                userInfo.setMobile(crowdDetailDo.getMobile());
                return userInfo;
            }).collect(Collectors.toList()));
            dispatchFailDetail.setBizEventType(dispatchDto.getBizEventType());
            if (StringUtils.isNotEmpty(dispatchDto.getDispatchType())) {
                dispatchFailDetail.setDispatchType(dispatchDto.getDispatchType());
            }
            batchSave(dispatchFailDetail);
        } catch (Exception ex) {
            log.error("saveFailRecord error", ex);
        }
    }
}
