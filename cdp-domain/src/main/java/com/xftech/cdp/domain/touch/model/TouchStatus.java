package com.xftech.cdp.domain.touch.model;

/**
 * 触达状态枚举
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public enum TouchStatus {
    
    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),
    
    /**
     * 失败
     */
    FAILED("FAILED", "失败"),
    
    /**
     * 流控拦截
     */
    FLOW_CONTROLLED("FLOW_CONTROLLED", "流控拦截"),
    
    /**
     * 处理中
     */
    PENDING("PENDING", "处理中"),
    
    /**
     * 部分成功（批量场景）
     */
    PARTIAL_SUCCESS("PARTIAL_SUCCESS", "部分成功");
    
    private final String code;
    private final String name;
    
    TouchStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static TouchStatus fromCode(String code) {
        for (TouchStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的触达状态代码: " + code);
    }
    
    /**
     * 判断是否为成功状态
     */
    public boolean isSuccessStatus() {
        return this == SUCCESS || this == PARTIAL_SUCCESS;
    }
    
    /**
     * 判断是否为失败状态
     */
    public boolean isFailedStatus() {
        return this == FAILED;
    }
    
    /**
     * 判断是否为流控状态
     */
    public boolean isFlowControlledStatus() {
        return this == FLOW_CONTROLLED;
    }
    
    /**
     * 判断是否为处理中状态
     */
    public boolean isPendingStatus() {
        return this == PENDING;
    }
    
    /**
     * 判断是否为最终状态（非处理中）
     */
    public boolean isFinalStatus() {
        return this != PENDING;
    }
}
