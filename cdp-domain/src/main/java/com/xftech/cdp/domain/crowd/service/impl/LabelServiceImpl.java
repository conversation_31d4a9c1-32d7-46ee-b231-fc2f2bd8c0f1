package com.xftech.cdp.domain.crowd.service.impl;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.req.LabelReq;
import com.xftech.cdp.api.dto.req.label.LabelRemarkReq;
import com.xftech.cdp.api.dto.resp.CrowdLabelsResp;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.crowd.repository.LabelRepository;
import com.xftech.cdp.domain.crowd.service.LabelService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.impl.StrategyDispatchForSmsServiceImpl;
import com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo;
import com.xftech.cdp.infra.utils.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
@Service
public class LabelServiceImpl implements LabelService {

    @Autowired
    public LabelRepository labelRepository;

    @Autowired
    @Lazy
    private StrategyDispatchForSmsServiceImpl strategyDispatchForSmsService;


    @Override
    public List<CrowdLabelsResp> getAll(String businessType) {
        List<CrowdLabelsResp> result = new ArrayList<>();
        List<LabelDo> labelDoList = labelRepository.getAll(businessType);
        for (LabelDo label : labelDoList) {
            CrowdLabelsResp crowdLabelsResp = new CrowdLabelsResp();
            crowdLabelsResp.setId(label.getId().intValue());
            crowdLabelsResp.setPrimaryLabel(label.getPrimaryLabel());
            crowdLabelsResp.setSecondaryLabel(label.getSecondaryLabel());
            crowdLabelsResp.setLabelName(label.getLabelName());
            crowdLabelsResp.setDataWarehouseField(label.getDataWarehouseField());
            crowdLabelsResp.setConfigurationOptionType(label.getConfigurationOptionType());
            if (crowdLabelsResp.getConfigurationOptionType() == LabelEnum.LabelOptionTypeEnum.FIXED_CHECK.getCode()) {
                crowdLabelsResp.setConfigurationOption(JSON.parseObject(label.getConfigurationOption()));
            }
            crowdLabelsResp.setDescription(label.getDescription());
            result.add(crowdLabelsResp);
        }
        return result;
    }

    @Override
    public List<CrowdLabelsResp> getTree(LabelReq labelReq) {

        List<LabelDo> labelDoList = labelRepository.getAll(labelReq.getBusinessType());
        List<CrowdLabelsResp> crowdLabelsRespList = new ArrayList<>();

        // 父子级关系
        Map<Integer, Integer> primarySecondaryRealMap = new HashMap<>();
        labelDoList.forEach(label -> {
            CrowdLabelsResp crowdLabelsResp = new CrowdLabelsResp();
            crowdLabelsResp.setId(label.getId().intValue());
            crowdLabelsResp.setPrimaryLabel(label.getPrimaryLabel());
            crowdLabelsResp.setSecondaryLabel(label.getSecondaryLabel());
            crowdLabelsResp.setLabelName(label.getLabelName());
            crowdLabelsResp.setDataWarehouseField(label.getDataWarehouseField());
            crowdLabelsResp.setConfigurationOptionType(label.getConfigurationOptionType());
            if (crowdLabelsResp.getConfigurationOptionType() == LabelEnum.LabelOptionTypeEnum.FIXED_CHECK.getCode()
                    || crowdLabelsResp.getConfigurationOptionType() == LabelEnum.LabelOptionTypeEnum.TIME_LIMIT.getCode()
                    || crowdLabelsResp.getConfigurationOptionType() == LabelEnum.LabelOptionTypeEnum.VALUE_RANGE.getCode()) {
                crowdLabelsResp.setConfigurationOption(JSON.parseObject(label.getConfigurationOption()));
            }
            crowdLabelsResp.setDescription(label.getDescription());
            crowdLabelsRespList.add(crowdLabelsResp);
            primarySecondaryRealMap.put(label.getSecondaryLabel(), label.getPrimaryLabel());
        });

        List<CrowdLabelsResp> primaryList = new ArrayList<>();
        for (LabelEnum.PrimaryLabelEnum primaryLabelEnum : LabelEnum.PrimaryLabelEnum.values()) {
            CrowdLabelsResp primary = new CrowdLabelsResp();
            primary.setPrimaryLabel(primaryLabelEnum.getCode());
            primary.setLabelName(primaryLabelEnum.getDescription());
            primary.setList(new ArrayList<>());
            primaryList.add(primary);
        }

        List<CrowdLabelsResp> secondaryList = new ArrayList<>();
        for (LabelEnum.SecondaryLabelEnum secondaryLabelEnum : LabelEnum.SecondaryLabelEnum.values()) {
            CrowdLabelsResp secondary = new CrowdLabelsResp();
            secondary.setSecondaryLabel(secondaryLabelEnum.getCode());
            secondary.setLabelName(secondaryLabelEnum.getDescription());
            secondary.setList(new ArrayList<>());
            secondaryList.add(secondary);
        }

        Map<Integer, CrowdLabelsResp> primaryLabels = MapUtils.listToMap(primaryList, CrowdLabelsResp::getPrimaryLabel);
        Map<Integer, CrowdLabelsResp> secondaryLabels = MapUtils.listToMap(secondaryList, CrowdLabelsResp::getSecondaryLabel);

        crowdLabelsRespList.forEach(item -> {
            secondaryLabels.get(item.getSecondaryLabel()).getList().add(item);
        });

        secondaryLabels.forEach((idx, item) -> {
            Integer pk = primarySecondaryRealMap.get(item.getSecondaryLabel());// 对应的pk
            if (primaryLabels.get(pk) != null) {
                primaryLabels.get(pk).getList().add(item);
            }
        });

        // strategyDispatchForSmsService.printPageSize();

        return MapUtils.mapToValueList(primaryLabels);
    }

    public List<LabelDo> getByLabelCode(String labelCode) {
        return labelRepository.getByLabelCode(labelCode);
    }

    public Boolean updateDescription(LabelRemarkReq labelRemarkReq) {
        LabelDo labelDo = new LabelDo();
        labelDo.setId(labelRemarkReq.getLabelId());
        labelDo.setDescription(labelRemarkReq.getDescription());
        return labelRepository.updateById(labelDo);
    }

    public Boolean labelDiscard(Long labelId) {
        LabelDo labelDo = new LabelDo();
        labelDo.setId(labelId);
        labelDo.setDFlag(1);
        return labelRepository.updateById(labelDo);
    }

    public Boolean saveOrUpdate(LabelDo labelDo) {
        if (labelDo.getId() == null) {
            return labelRepository.insert(labelDo);
        }
        return labelRepository.updateById(labelDo);
    }

    public LabelDo getById(Long labelId) {
        return labelRepository.selectById(labelId);
    }

    public Boolean existByLabelCode(String dataWarehouseField) {
        return !CollectionUtils.isEmpty(labelRepository.getByLabelCode(dataWarehouseField));
    }
}
