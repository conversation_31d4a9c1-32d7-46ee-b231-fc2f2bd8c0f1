package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Component
public class StrategyMarketEventRepository {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public StrategyMarketEventDo selectById(Long id) {
        return DBUtil.selectOne("strategyMarketEventMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyMarketEventDo param) {
        return DBUtil.insert("strategyMarketEventMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyMarketEventDo param) {
        return DBUtil.update("strategyMarketEventMapper.updateByPrimaryKeySelective", param) > 0;
    }

    /**
     * 根据事件类型查询对接记录
     *
     * @param bizEventType 事件类型
     * @return 策略事件
     */
    public List<StrategyMarketEventDo> getByEventName(String bizEventType) {
        return DBUtil.selectList("strategyMarketEventMapper.getByEventName", bizEventType);
    }

    public List<StrategyMarketEventDo> getByStrategyId(Long strategyId) {
        return DBUtil.selectList("strategyMarketEventMapper.getByStrategyId", strategyId);
    }

    /**
     * 根据策略id查询
     *   2023-9-27 变更：一个策略id对应多个事件
     * @param strategyId 策略id
     * @return strategyId对应的记录
     */
    public List<StrategyMarketEventDo> selectByStrategyId(Long strategyId) {
        return DBUtil.selectList("strategyMarketEventMapper.selectByStrategyId", strategyId);
    }

}
