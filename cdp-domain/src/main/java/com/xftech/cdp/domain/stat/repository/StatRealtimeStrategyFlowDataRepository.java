package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyFlowDataEntity;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实时策略流程数据
 */
@Component
public class StatRealtimeStrategyFlowDataRepository {

    /**
     * 保存实时策略流程数据
     * @param entity
     */
    public void saveStatRealtimeStrategyFlowData(StatRealtimeStrategyFlowDataEntity entity) {
        DBUtil.insert("statRealtimeStrategyFlowData.saveStatRealtimeStrategyFlowData", entity);
    }

    /**
     * 更新实时策略流程数据
     * @param entity
     */
    public void updateStatRealtimeStrategyFlowData(StatRealtimeStrategyFlowDataEntity entity) {
        DBUtil.update("statRealtimeStrategyFlowData.updateStatRealtimeStrategyFlowData", entity);
    }

    /**
     * 是否存在实时策略流程数据
     * @param entity
     */
    public Boolean existRealtimeStrategyFlowData(StatRealtimeStrategyFlowDataEntity entity) {
        Integer num = DBUtil.selectOne("statRealtimeStrategyFlowData.existRealtimeStrategyFlowData", entity);
        return num > 0;
    }

    /**
     * 根据策略Id分页查询流程数据
     * @param strategyId 策略ID
     * @param pageNum 页码，从0开始
     * @param pageSize 每页大小
     * @return
     */
    public Page<StatRealtimeStrategyFlowDataEntity> listByStrategyId(Long strategyId, Integer pageNum, Integer pageSize) {
        Map<String,Object> param = new HashMap<>();
        param.put("strategyId",strategyId);
        return DBUtil.selectPage("statRealtimeStrategyFlowData.listByStrategyId", param, pageNum, pageSize);
    }

    public List<StatRealtimeStrategyFlowDataEntity> selectByStrategyId(Long strategyId) {
        return DBUtil.selectList("statRealtimeStrategyFlowData.listByStrategyId", strategyId);
    }

    public StatRealtimeStrategyFlowDataEntity selectByStrategyIdAndBizDate(Long strategyId, LocalDate bizDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("strategyId", strategyId);
        param.put("bizDate", bizDate);
        return DBUtil.selectOne("statRealtimeStrategyFlowData.selectByStrategyIdAndBizDate", param);
    }
}
