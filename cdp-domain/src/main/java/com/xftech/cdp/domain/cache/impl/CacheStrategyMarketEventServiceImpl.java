package com.xftech.cdp.domain.cache.impl;

import com.alibaba.fastjson.JSONArray;
import com.xftech.base.common.util.LogUtil;
import com.xftech.base.log.IUdpLogger;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventRepository;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class CacheStrategyMarketEventServiceImpl implements CacheStrategyMarketEventService {

    private static final IUdpLogger logger = LogUtil.getLogger(CacheStrategyMarketEventService.class);

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
//    public StrategyMarketEventDo selectById(Long id) {
//        String redisKey = String.format(RedisKeyConstants.SME_ONE_ID, id);
//        StrategyMarketEventDo strategyMarketEventConditionDo = redisUtils.get(redisKey, StrategyMarketEventDo.class);
//        if (strategyMarketEventConditionDo == null) {
//            strategyMarketEventConditionDo = strategyMarketEventRepository.selectById(id);
//            redisUtils.set(redisKey, strategyMarketEventConditionDo, RedisUtils.NOT_EXPIRE);
//        }
//        return strategyMarketEventConditionDo;
//    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyMarketEventDo param) {
        boolean isInsert = strategyMarketEventRepository.insert(param);
        if (isInsert) {
//            this.delListKey(param.getEventName());
            this.refreshStrategyMarketEventCache(param.getEventName());
        }
        return isInsert;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyMarketEventDo param) {
        boolean isUpdate = strategyMarketEventRepository.updateById(param);
        if (isUpdate) {
            this.refreshStrategyMarketEventCache(param.getEventName());

//            String redisKey1 = String.format(RedisKeyConstants.SME_ONE_ID, param.getId());
//            redisUtils.set(redisKey1, param, RedisUtils.NOT_EXPIRE);
//            String redisKey2 = String.format(RedisKeyConstants.SME_ONE_STRATEGYID, param.getStrategyId());
//            redisUtils.set(redisKey2, param, RedisUtils.NOT_EXPIRE);
//            this.delListKey(param.getStrategyId());
        }
        return isUpdate;
    }

    /**
     * 根据事件类型查询对接记录
     *
     * @param bizEventType 事件类型
     * @return 策略事件
     */
    public List<StrategyMarketEventDo> getByEventName(String bizEventType) {
        String redisKey = RedisKeyUtils.genEventNameKey(bizEventType);
        String data = redisUtils.get(redisKey);
        List<StrategyMarketEventDo> strategyMarketEventDoList = JSONArray.parseArray(data, StrategyMarketEventDo.class);
        if (strategyMarketEventDoList == null) {
            strategyMarketEventDoList = strategyMarketEventRepository.getByEventName(bizEventType);
            redisUtils.set(redisKey, strategyMarketEventDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyMarketEventDoList;
    }

    public List<StrategyMarketEventDo> getByStrategyId(Long strategyId) {
        String redisKey = RedisKeyUtils.genSMEStrategyIdKey(strategyId);
        String data = redisUtils.get(redisKey);
        List<StrategyMarketEventDo> strategyMarketEventDoList = JSONArray.parseArray(data, StrategyMarketEventDo.class);
        if (strategyMarketEventDoList == null) {
            strategyMarketEventDoList = strategyMarketEventRepository.getByStrategyId(strategyId);
            redisUtils.set(redisKey, strategyMarketEventDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyMarketEventDoList;
    }
    
//    /**
//     * 根据策略id查询
//     *
//     * @param strategyId 策略id
//     * @return strategyId对应的记录
//     */
//    public StrategyMarketEventDo selectByStrategyId(Long strategyId) {
//        String redisKey = String.format(RedisKeyConstants.SME_ONE_STRATEGYID, strategyId);
//        StrategyMarketEventDo strategyMarketEventConditionDo = redisUtils.get(redisKey, StrategyMarketEventDo.class);
//        if (strategyMarketEventConditionDo == null) {
//            strategyMarketEventConditionDo = strategyMarketEventRepository.selectByStrategyId(strategyId);
//            redisUtils.set(redisKey, strategyMarketEventConditionDo, RedisUtils.NOT_EXPIRE);
//        }
//        return strategyMarketEventConditionDo;
//    }

//    private void delListKey(Long strategyId) {
//        String redisKey = String.format(RedisKeyConstants.SME_LIST, strategyId);
//        if (redisUtils.hasKey(redisKey)) {
//            redisUtils.delete(redisKey);
//        }
//    }

    public void refreshStrategyMarketEventCache(String eventName) {
        if (StringUtils.isBlank(eventName)) {
            return;
        }
        List<StrategyMarketEventDo> strategyMarketEventDoList = strategyMarketEventRepository.getByEventName(eventName);
        refreshStrategyMarketEventCache(eventName, strategyMarketEventDoList);
    }

    public void refreshStrategyMarketEventCache(String eventName, List<StrategyMarketEventDo> strategyMarketEventDoList) {
        String redisKey = RedisKeyUtils.genEventNameKey(eventName);
        redisUtils.set(redisKey, strategyMarketEventDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
    }




}
