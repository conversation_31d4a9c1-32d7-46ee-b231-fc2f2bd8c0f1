package com.xftech.cdp.domain.dispatch;

import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.feign.model.SendPushInfo;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.infra.client.appcore.request.BalanceOptReq;
import com.xftech.cdp.infra.client.coupon.model.req.CouponSendBatchReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.sms.model.SmsSingleSendRequester;
import com.xftech.cdp.infra.client.sms.model.resp.SmsSingleSendResp;
import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleSaveBatchRequest;
import com.xftech.cdp.infra.client.telemarketing.model.resp.AiSendResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TelePushResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleSaveBatchResp;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo;
import com.xftech.cdp.infra.rocketmq.dto.AiSendArgs;
import org.apache.commons.lang3.tuple.ImmutableTriple;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/23 14:41
 */
public interface EventDispatchService {

    /**
     * 单条短信下发 （带参）
     *
     * @param reach       批次参数
     * @param app         app
     * @param innerApp    innerApp
     * @param crowdDetail 用户信息
     * @return 下发数量
     */
    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendSmsEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail, BizEventVO bizEvent, String extInfo);

    /**
     * 调用电销保存接口
     *
     * @param reach       策略执行初始化内容
     * @param app         app
     * @param innerApp    innerApp
     * @param crowdDetail 需要下发的用户ID集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendTeleEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail);


    /**
     * 调用优惠券批量下发接口
     *
     * @param reach       策略执行初始化内容
     * @param app         app
     * @param innerApp    innerApp
     * @param crowdDetail 需要下发的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendCouponEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail);

    SmsSingleSendResp sendSms(SmsSingleSendRequester request);

    TeleSaveBatchResp sendTele(TeleSaveBatchRequest request);

    TelePushResp sendTeleNew(TelePushArgs request);

    boolean increaseBalance(BalanceOptReq req, String orderNo);

    AiSendResp sendAiPronto(AiSendArgs aiSendArgs);

    BaseCouponResponse<Object> sendCoupon(CouponSendBatchReq request);

    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendNewTeleEvent(DispatchDto dispatchDto, String app, String innerApp, CrowdDetailDo crowdDetail);

    PushResponse<SendPushInfo> sendPush(PushBaseRequest<SendPushRequest> request);

    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendPushEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail, BizEventVO bizEvent);

    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendIncreaseAmtEvent(DispatchDto reach, String app, String innerApp, CrowdDetailDo crowdDetail, Long creditUserId, String deviceId, String ip);

    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendLifeRightsEvent(DispatchDto dispatchDto, String app, String innerApp, CrowdDetailDo crowdDetail);

    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendXDayInterestFreeEvent(DispatchDto dispatchDto, String app, String innerApp, CrowdDetailDo crowdDetail);

    ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendAiProntoEvent(DispatchDto dispatchDto, String app, String innerApp, CrowdDetailDo crowdDetail);
}
