package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.base.common.util.UdpUtil;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelQueryService;
import com.xftech.cdp.domain.ads.service.impl.AbstractAdsStrategyLabelService;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushBatchStatusEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushExecTypeEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdPushQueryStatusEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.model.enums.UserDispatchDetailStatusEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.UserBlankGroupDetailRepository;
import com.xftech.cdp.domain.strategy.service.StrategyGroupService;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.UserBlankGroupDetailService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.impl.AbstractStrategyDispatchService;
import com.xftech.cdp.domain.strategy.service.flow.DispatchCrowdService;
import com.xftech.cdp.infra.client.randomnum.RandomNumClient;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchCrowdDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserBlankGroupDetailDo;
import com.xftech.cdp.infra.thread.DispatchCrowdExecutor;
import com.xftech.cdp.infra.utils.*;
import com.xftech.zipkin.brave.ThreadTraceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.roaringbitmap.longlong.Roaring64Bitmap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/19 15:16
 */
@Slf4j
@Service(StrategyDispatchConstants.NONE_SERVICE)
public class UserBlankGroupDetailServiceImpl extends AbstractStrategyDispatchService implements UserBlankGroupDetailService, StrategyDispatchService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private StrategyGroupService strategyGroupService;
    @Autowired
    private CrowdDetailRepository crowdDetailRepository;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private UserBlankGroupDetailRepository userBlankGroupDetailRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private AdsStrategyLabelQueryService adsStrategyLabelQueryService;
    @Autowired
    private StrategyService strategyService;
    @Autowired
    private RandomNumClient randomNumClient;
    @Autowired
    private AbstractAdsStrategyLabelService abstractAdsStrategyLabelService;
    @Autowired
    private DispatchCrowdService dispatchCrowdService;

    @Override
    public void add(UserBlankGroupDetailDo blankGroupDetail, StrategyRulerEnum strategyRulerEnum) {
        statUserBlank(blankGroupDetail, strategyRulerEnum);
        userBlankGroupDetailRepository.saveBatch(LocalDateTimeUtil.format(blankGroupDetail.getTriggerDatetime(), "yyyyMM"), Collections.singletonList(blankGroupDetail));
    }

    /**
     * 重试-保存留白组
     *
     * @param context 初始化参数
     */
    @Override
    public int blankGroupAsync(StrategyContext context) {
        AtomicInteger totalCount = new AtomicInteger();
        Long strategyId = context.getStrategyDo().getId();

        List<StrategyGroupDo> blankGroupList = Collections.singletonList(context.getStrategyGroupDo());
        log.info("blankGroupAsync,策略ID:{},blankGroupList:{}", context.getStrategyDo().getId(), blankGroupList);

        String flowBatchNo;
        if (context.isFlow()) {
            flowBatchNo = context.getFlowContext().getBatchNo();
        } else {
            flowBatchNo = null;
        }

//        UdpUtil.getInstance().getBean("strategyBlankTaskExecutor", ExecutorService.class).execute(ThreadTraceUtil.wrap(() -> {
            long startTime = Instant.now().toEpochMilli();
            log.info("保存留白组明细, 策略ID:{}", context.getStrategyDo().getId());
            final Roaring64Bitmap container = new Roaring64Bitmap();
            try {
                if (context.isFlow() && !context.isFlowRootNode()) {
                    totalCount.set(flowDispatchBlank(context, strategyId, blankGroupList, container));
                } else {
                    for (Triple<Long, Long, List<String>> tableTriple : crowdPackService.getExecLogIdAndTablePairList(context.getCrowdIds(), context.getCrowdContent())) {
                        for (String tableName : tableTriple.getRight()) {
                            PageUtil.PaginationParam param = new PageUtil.PaginationParam(strategyConfig.getBlankBatchSize());
                            PageUtil.invokePaginationNew(PageUtil.PaginationTypeEnum.ID_PAGING, param,
                                    () -> crowdPackService.getCrowdDetailList(tableTriple.getLeft(),tableName, tableTriple.getMiddle(), param.getId(), param.getPageSize(), context.getCrowdContent().get(tableTriple.getLeft())),
                                    list -> list.stream().collect(groupingBy(CrowdDetailDo::getApp)).forEach((app, detailList) -> {
                                        detailList.forEach(z -> {
                                            z.setFlowBatchNo(flowBatchNo);
                                            z.setPreStrategyId(0);
                                            z.setStrategyId(strategyId);
                                            if (context.isFlow() && context.getFlowContext() != null) {
                                                z.setFlowNo(context.getFlowContext().getFlowNo());
                                                z.setNextStrategyId(context.getFlowContext().getNextStrategyId());
                                            }
                                        });
                                        totalCount.addAndGet(dispatchBlank(context, blankGroupList, container, app, detailList));
                                    })
                            );
                        }
                    }
                }
                this.setSuccStatus(context.getDetailTableNo(), context.getStrategyDo().getId());
                log.info("blankGroupAsync 执行成功, 策略id:{}", context.getStrategyDo().getId());
            } catch (Exception e) {
                log.warn("blankGroupAsync,保存留白组明细异常, 策略ID:{}", context.getStrategyDo().getId(), e);
                this.setFailStatus(context.getDetailTableNo(), context.getStrategyDo().getId());
            }
            log.info("blankGroupAsync,保存留白组明细,策略ID:{},耗时:{}ms", context.getStrategyDo().getId(), Instant.now().toEpochMilli() - startTime);
//        }));

        return totalCount.get();
    }

    private int flowDispatchBlank(StrategyContext context, Long strategyId, List<StrategyGroupDo> blankGroupList, Roaring64Bitmap container) {
        Date localNow = new Date();
        StrategyContext.FlowContext flowContext = context.getFlowContext();
        int delayDay = context.getFlowContext().getFlowDispatchConfig()
                .getDelay().getTimeValue();
        int dateValue = DateUtil.dayOfInt(DateUtil.addDays(localNow, delayDay * (-1)));
        int crowdCount = dispatchCrowdService.selectCount(strategyId, flowContext.getFlowNo(), dateValue);
        if (crowdCount <= 0) {
            // 没有人群
            return 0;
        }
        long startIndex = 0;
        long pageSize = strategyConfig.getBlankBatchSize();
        AtomicInteger totalCount = new AtomicInteger(0);
        while (true) {
            List<DispatchCrowdDo> dispatchCrowdDos = dispatchCrowdService
                    .pullCrowds(startIndex, pageSize, strategyId, flowContext.getFlowNo(), dateValue);
            dispatchCrowdDos.stream().collect(groupingBy(DispatchCrowdDo::getBatchNo)).forEach((batchNo, list) -> {
                List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
                for (DispatchCrowdDo dispatchCrowdDo : list) {
                    CrowdDetailDo crowdDetailDo = JsonUtil.parse(dispatchCrowdDo.getUserInfo(), CrowdDetailDo.class);
                    if (crowdDetailDo != null) {
                        crowdDetailDo.setUserId(dispatchCrowdDo.getUserId());
                        crowdDetailDo.setPreStrategyId(dispatchCrowdDo.getStrategyId());
                        crowdDetailDo.setFlowBatchNo(dispatchCrowdDo.getBatchNo());
                        crowdDetailDo.setNextStrategyId(flowContext.getNextStrategyId());
                        crowdDetailDo.setStrategyId(strategyId);
                        crowdDetailDo.setFlowNo(flowContext.getFlowNo());
                        crowdDetailDoList.add(crowdDetailDo);
                    }
                }
                crowdDetailDoList.stream().collect(groupingBy(CrowdDetailDo::getApp)).forEach((app, detailList) -> {
                    totalCount.addAndGet(dispatchBlank(context, blankGroupList, container, app, detailList));
                });
            });
            if (CollectionUtils.isEmpty(dispatchCrowdDos) || dispatchCrowdDos.size() < pageSize) {
                break;
            }
            startIndex = dispatchCrowdDos.get(dispatchCrowdDos.size() - 1).getId();
        }
        return totalCount.get();
    }

    private int dispatchBlank(StrategyContext context, List<StrategyGroupDo> blankGroupList,
                               Roaring64Bitmap container, String app, List<CrowdDetailDo> detailList) {
        int totalCount = 0;
        for (StrategyGroupDo strategyGroup : blankGroupList) {
            int count = 0;
            log.info("开始保存留白组明细,策略ID:{},分组ID:{}", context.getStrategyDo().getId(), strategyGroup.getId());
            Optional<StrategyMarketChannelDo> first = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroup.getId()).stream().findFirst();
            if (first.isPresent()) {
                StrategyMarketChannelDo strategyMarketChannelDo = first.get();
                detailList = context.cycleFilter(detailList, strategyService, randomNumClient);
                if (CollectionUtils.isEmpty(detailList)) {
                    log.info("本批次无人满足条件,策略id:{},渠道id:{}", context.getStrategyDo().getId(),
                            context.getStrategyMarketChannelDo().getId());
                    return totalCount;
                }
                // 获取随机数
                detailList = randomNumService.randomNum(context, detailList);
                // 分组过滤
                StrategyGroupTypeEnum groupTypeEnum = StrategyGroupTypeEnum.getInstance(context.getStrategyDo().getAbType());
                BiPredicate<String, Integer> matchFunction = strategyGroup.match(groupTypeEnum);
                detailList = strategyGroupService.matchGroupRule(context.getStrategyDo().getBizKey(), matchFunction, detailList);
                //1.1 麻雀-fxk老客转xyf01下发 ,AB分组之后，触达下发前，流控判断前
                detailList = abstractAdsStrategyLabelService.convertCrowdList(detailList, context.getStrategyDo().getUserConvert());
                // 根据用户ID全局去重
                detailList = detailList.stream().filter(item -> !container.contains(item.getUserId())).collect(Collectors.toList());
                // 去重后的用户ID加入到容器中，参与下次去重
                detailList.stream().map(CrowdDetailDo::getUserId).forEachOrdered(container::add);
                // 根据手机号去重
                detailList = detailList.stream().filter(ListUtils.distinctByKey(CrowdDetailDo::getMobile)).collect(Collectors.toList());
                // 标签查询
                detailList = adsStrategyLabelQueryService.queryLabelHandler(
                        context.getStrategyDo().getId(),
                        strategyMarketChannelDo.getId(),
                        strategyMarketChannelDo.getMarketChannel(),
                        app,
                        detailList
                );
                final List<CrowdDetailDo> availableDetails = detailList;
                if (context.isFlow()) {
                    StrategyContext.FlowContext flowContext = context.getFlowContext();
                    dispatchCrowdService.saveCrowdDetail(availableDetails,
                            flowContext.getDayValue());
                }
                // 保存入库
                count = this.saveBatch(context, strategyGroup.getId(), detailList);
                totalCount += count;
            }
            log.info("blankGroupAsync,结束保存留白组明细,策略ID:{},分组ID:{},数量:{}", context.getStrategyDo().getId(), strategyGroup.getId(), totalCount);
        }
        return totalCount;
    }

    /**
     * Redis 不存在执行标识或存在失败标识，需设置执行标识，并返回当前策略的【不营销】组配置信息
     *
     * @param strategyId 策略ID
     * @return 【不营销】组配置信息
     */
    private List<StrategyGroupDo> getBlankGroupList(Long strategyId) {
        Triple<String, String, String> key = getKey(strategyId);
        if (redisUtils.hasKey(key.getMiddle()) || (!redisUtils.hasKey(key.getLeft()) && !redisUtils.hasKey(key.getRight()))) {
            if (redisUtils.lock(key.getLeft(), strategyId, getSeconds())) {
                return strategyGroupRepository.selectBlankGroup(strategyId);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 中途异常，删除执行标识，设置失败标识，并把已保存的数据更新为失败
     *
     * @param tableNameNo 表序号
     * @param strategyId  策略ID
     */
    private void setFailStatus(String tableNameNo, Long strategyId) {
        try {
            Triple<String, String, String> key = getKey(strategyId);
            redisUtils.delete(key.getLeft());
            redisUtils.set(key.getMiddle(), strategyId, getSeconds());
            updateStatus(tableNameNo, strategyId, UserDispatchDetailStatusEnum.FAIL);
        } catch (Exception e) {
            log.warn("策略失败，留白组置为失败异常", e);
        }
    }

    /**
     * 策略渠道执行成功，删除失败标识，设置成功标识
     *
     * @param strategyId 策略ID
     */
    private void setSuccStatus(String tableNameNo, Long strategyId) {
        Triple<String, String, String> key = getKey(strategyId);
        redisUtils.delete(key.getMiddle());
        redisUtils.delete(key.getLeft());
        redisUtils.set(key.getRight(), strategyId, getSeconds());
        updateStatus(tableNameNo, strategyId, UserDispatchDetailStatusEnum.SUCCESS);
    }

    private Integer saveBatch(StrategyContext context, Long strategyGroupId, List<CrowdDetailDo> detailList) {
        LocalDateTime now = LocalDateTime.now();
        StrategyMarketChannelDo marketChannel = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupId).get(0);
        List<UserBlankGroupDetailDo> blankGroupDetailList = detailList.stream().map(crowdDetailDo -> {
            UserBlankGroupDetailDo blankGroupDetail = new UserBlankGroupDetailDo();
            blankGroupDetail.setUserId(crowdDetailDo.getUserId());
            blankGroupDetail.setMobile(crowdDetailDo.getMobile());
            blankGroupDetail.setBatchNum(serialNumberUtil.batchNum());
            blankGroupDetail.setCrowdPackId(crowdDetailDo.getCrowdId());
            blankGroupDetail.setStrategyId(context.getStrategyDo().getId());
            blankGroupDetail.setStrategyChannelId(marketChannel.getId());
            blankGroupDetail.setMarketChannel(marketChannel.getMarketChannel());
            blankGroupDetail.setStrategyExecId(LocalDateTimeUtil.format(LocalDate.now(), "yyMMdd") + String.format("%010d", context.getStrategyDo().getId()));
            blankGroupDetail.setStatus(-1);
            blankGroupDetail.setDispatchTime(now);
            blankGroupDetail.setTriggerDatetime(now);
            blankGroupDetail.setCreatedTime(now);
            blankGroupDetail.setUpdatedTime(now);
            blankGroupDetail.setExtDetail(JsonUtil.toJson(crowdDetailDo.getExtDetailData(blankGroupDetail.getMarketChannel())));
            return blankGroupDetail;
        }).collect(Collectors.toList());
        statUserBlank(blankGroupDetailList, StrategyRulerEnum.getInstance(context.getStrategyDo().getSendRuler()));
        userBlankGroupDetailRepository.saveBatch(context.getDetailTableNo(), blankGroupDetailList);
        return blankGroupDetailList.size();
    }

    private void statUserBlank(List<UserBlankGroupDetailDo> blankGroupDetailList, StrategyRulerEnum strategyRulerEnum) {
        if (StrategyRulerEnum.EVENT == strategyRulerEnum) {
            for (UserBlankGroupDetailDo item : blankGroupDetailList) {
                statUserBlank(item, strategyRulerEnum);
            }
        }
    }

    private void statUserBlank(UserBlankGroupDetailDo item, StrategyRulerEnum strategyRulerEnum) {
        if (StrategyRulerEnum.EVENT == strategyRulerEnum) {
            String curDate = LocalDateTimeUtil.format(item.getTriggerDatetime(), "yyyyMMdd");
            if (item.getStrategyChannelId() == null || item.getStrategyChannelId() < 1){
                return;
            }
            StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.cacheSelectById(item.getStrategyChannelId());
            if (strategyMarketChannelDo == null){
                return;
            }
            String key = RedisKeyUtils.genStatDecnUserBlank(curDate, item.getStrategyId(), strategyMarketChannelDo.getStrategyGroupId());
            redisUtils.pfAddTwoDay(key, item.getUserId());
        }
    }

    private Triple<String, String, String> getKey(Long strategyId) {
        String execKey = String.format(RedisKeyConstants.STRATEGY_BLANK_GROUP_EXEC_FLAG, strategyId);
        String failKey = String.format(RedisKeyConstants.STRATEGY_BLANK_GROUP_FAIL_FLAG, strategyId);
        String succKey = String.format(RedisKeyConstants.STRATEGY_BLANK_GROUP_SUCC_FLAG, strategyId);
        return Triple.of(execKey, failKey, succKey);
    }

    private void updateStatus(String tableNameNo, Long strategyId, UserDispatchDetailStatusEnum statusEnum) {
        while (true) {
            Integer count = userBlankGroupDetailRepository.setStatusByStrategyId(tableNameNo, strategyId, statusEnum);
            if (count == 0) {
                break;
            }
        }
    }

    private Long getSeconds() {
        return Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.MAX)).getSeconds();
    }

    @Override
    protected Integer setBatchSize() {
        return strategyConfig.getBlankBatchSize();
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<T> templateParam) {
        Long strategyGroupId = strategyContext.getStrategyGroupDo().getId();
        int dispatchCount = 0;
        try {
            dispatchCount = saveBatch(strategyContext, strategyGroupId, batch);
            setSuccStatus(strategyContext.getDetailTableNo(), strategyContext.getStrategyDo().getId());
            log.info("[不营销组dispatchHandler] 执行成功, 策略id:{}, dispatchCount: {}", strategyContext.getStrategyDo().getId(), dispatchCount);
        } catch (Exception e) {
            log.warn("[不营销组dispatchHandler] 保存留白组明细异常, 策略ID:{}", strategyContext.getStrategyDo().getId(), e);
            this.setFailStatus(strategyContext.getDetailTableNo(), strategyContext.getStrategyDo().getId());
        }

        //执行流程需要pair.left作为sendCount，进而标记任务执行完成
        return ImmutablePair.of(dispatchCount, null);
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext context, String tableNameNo, String app, String innerApp, List<CrowdDetailDo> detailList, List<T> templateParam) {
        Long strategyGroupId = context.getStrategyGroupDo().getId();
        int dispatchCount = 0;
        try {
            dispatchCount = saveBatch(context, strategyGroupId, detailList);
            setSuccStatus(context.getDetailTableNo(), context.getStrategyDo().getId());
            log.info("[不营销组retryDispatchHandler] 执行成功, 策略id:{}, dispatchCount: {}", context.getStrategyDo().getId(), dispatchCount);
        } catch (Exception e) {
            log.warn("[不营销组retryDispatchHandler] 保存留白组明细异常, 策略ID:{}", context.getStrategyDo().getId(), e);
            this.setFailStatus(context.getDetailTableNo(), context.getStrategyDo().getId());
        }

        //执行流程需要pair.left作为sendCount，进而标记任务执行完成
        return ImmutablePair.of(dispatchCount, null);
    }
}
