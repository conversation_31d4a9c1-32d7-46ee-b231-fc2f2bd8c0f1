package com.xftech.cdp.domain.stat.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatStrategyFlowDataEntity implements Serializable {
    private static final long serialVersionUID = -8761492218013336125L;

    /**
     *
     * pk
     */
    private Long id;

    /**
     *
     * 批次日期
     */
    private Integer dateValue;

    /**
     *
     * 策略id
     */
    private Long strategyId;

    /**
     *
     * 策略name
     */
    private String strategyName;

    /**
     *
     * 层级
     */
    private Integer levelNum;

    /**
     *
     * 画布no
     */
    private String flowNo;

    /**
     *
     * 画布批次no
     */
    private String batchNo;

    /**
     *
     * 应发数
     */
    private Integer dispatchNum;

    /**
     *
     * 逻辑删除
     */
    private Short dFlag;

    private Date createdTime;

    private Date updatedTime;

}