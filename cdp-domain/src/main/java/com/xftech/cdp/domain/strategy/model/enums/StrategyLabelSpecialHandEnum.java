package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @<NAME_EMAIL>
 */
@Getter
@AllArgsConstructor
public enum StrategyLabelSpecialHandEnum {
    /**
     * 需要特殊处理
     */
    YES(1),
    /**
     * 不需要特殊处理
     */
    NO(0);

    private final Integer type;

    public static StrategyLabelSpecialHandEnum getInstance(Integer type) {
        for (StrategyLabelSpecialHandEnum specialHandEnum : StrategyLabelSpecialHandEnum.values()) {
            if (Objects.equals(specialHandEnum.getType(), type)) {
                return specialHandEnum;
            }
        }
        return NO;
    }
}
