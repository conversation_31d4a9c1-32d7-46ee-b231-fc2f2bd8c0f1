/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.enums;

import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DispatchTaskStatusEnum, v 0.1 2023/11/2 20:27 yye.xu Exp $
 */

public enum DispatchTaskStatusEnum {
    INIT(0, "初始待执行"),
    EXECUTING(1, "任务执行中"),
    SUCCEED(2, "任务执行完成"),
    FAILED(3, "任务失败"),


    ;
    private final int code;
    private final String description;

    DispatchTaskStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static List<DispatchTaskStatusEnum> getEffectiveList() {
        return Arrays.asList(INIT, EXECUTING, SUCCEED);
    }

    public static List<DispatchTaskStatusEnum> getTodoList() {
        return Arrays.asList(INIT);
    }

    public static List<DispatchTaskStatusEnum> getNotFinishedList() {
        return Arrays.asList(INIT, EXECUTING);
    }
}