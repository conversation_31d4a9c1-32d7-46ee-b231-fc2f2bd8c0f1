package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.strategy.repository.UserSendCounterRepository;
import com.xftech.cdp.domain.strategy.service.UserSendCounterService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserSendCounterDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class UserSendCounterServiceImpl implements UserSendCounterService {

    private final RedisUtils redisUtils;
    private final UserSendCounterRepository userSendCounterRepository;
    private final CacheStrategyService cacheStrategyService;

    @Override
    public long countSum(long userId, long strategyId, int marketChannel, int startDay, int endDay) {
        return userSendCounterRepository.countSum(userId, strategyId,
                marketChannel, startDay, endDay);
    }

    @Override
    public void counterIncrementSum(long userId, String mobile, long strategyId, int marketChannel, int dateValue, int count) {
        int ret = userSendCounterRepository.incrementSum(userId, strategyId, marketChannel, dateValue, count);
        if (ret == 0) {
            boolean lockRet = false;
            String lockKey = String.format("insert:userSendCounter:%s:%s:%s:%s", userId, strategyId, marketChannel, dateValue);
            try {
                lockRet = redisUtils.lock(lockKey, 1, 3);
                if (lockRet) {
                    ret = userSendCounterRepository.incrementSum(userId, strategyId, marketChannel, dateValue, count);
                    if (ret == 0) {
                        UserSendCounterDo record = new UserSendCounterDo();
                        record.setAppUserId(userId);
                        record.setMarketChannel((short) marketChannel);
                        record.setStrategyId(strategyId);
                        record.setMobile(mobile);
                        record.setDateValue(dateValue);
                        record.setSumValue(count);
                        record.setFailedValue(0);
                        userSendCounterRepository.insert(record);
                    }
                } else {
                    // 场景不严格，暂时sleep一下，
                    Thread.sleep(20);
                    userSendCounterRepository.incrementSum(userId, strategyId, marketChannel, dateValue, count);
                }
            } catch (Exception ex) {
                log.error("插入数据库失败, userId:{}", userId, ex);
            } finally {
                if (lockRet) {
                    redisUtils.delete(lockKey);
                }
            }
        }
    }

    @Override
    public void counterIncrementFailed(long userId, long strategyId, int marketChannel, int dateValue) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
        if (strategyDo != null && strategyDo.isEngineStrategy()) {
            userSendCounterRepository.incrementFailed(userId, strategyId, marketChannel, dateValue);
        }
    }
}
