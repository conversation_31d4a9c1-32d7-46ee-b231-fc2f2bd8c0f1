package com.xftech.cdp.domain.param.service.impl;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.param.service.TemplateParamQueryService;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.domain.strategy.model.dto.UserDispatchFailDetailDto;
import com.xftech.cdp.domain.strategy.service.UserDispatchFailDetailService;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/16 15:18
 */
@Slf4j
@Service
public class TemplateParamQueryServiceImpl implements TemplateParamQueryService {

    @Autowired
    private TemplateParamService templateParamService;
    @Autowired
    private UserDispatchFailDetailService userDispatchFailDetailService;

    /**
     * 获取用户短信参数
     *
     * @param reach 策略上下文
     * @param app   app
     * @param batch 手机号
     * @return 用户短信参数
     */
    @Override
    public Triple<Boolean, List<CrowdDetailDo>, List<SmsBatchWithParamArgs.Sms>> smsParamBatchQuery(DispatchDto reach, String app, List<CrowdDetailDo> batch) {
        try {
            Triple<Boolean, Integer, Map<Long, Map<String, Object>>> pair = templateParamService.getBatchSmsTempParam(reach.getStrategyId(), app, reach.getStrategyMarketChannelTemplateId(), batch);
            if (Objects.equals(Boolean.FALSE, pair.getLeft())) {
                return Triple.of(pair.getLeft(), batch, Collections.emptyList());
            }

            Map<Long, Map<String, Object>> paramMap = pair.getRight();
            Map<Boolean, List<CrowdDetailDo>> resultMap = batch.stream().collect(Collectors.groupingBy(item -> smsParamFilter(pair, paramMap, item)));

            List<CrowdDetailDo> crowdDetailList = Optional.ofNullable(resultMap.get(Boolean.FALSE)).orElse(new ArrayList<>());
            List<SmsBatchWithParamArgs.Sms> list = Objects.equals(pair.getLeft(), Boolean.TRUE) ? crowdDetailList.stream().map(item -> {
                SmsBatchWithParamArgs.Sms sms = new SmsBatchWithParamArgs.Sms();
                sms.setMobile(item.getMobile());
                sms.setUserNo(item.getUserId());
                sms.setData(paramMap.get(item.getUserId()));
                return sms;
            }).collect(Collectors.toList()) : Collections.emptyList();

            if (!CollectionUtils.isEmpty(resultMap.get(Boolean.TRUE))) {
                UserDispatchFailDetailDto dispatchFailDetail = new UserDispatchFailDetailDto();
                dispatchFailDetail.setStrategyId(reach.getStrategyId());
                dispatchFailDetail.setMarketChannel(reach.getStrategyChannel());
                dispatchFailDetail.setTemplateNo(reach.getStrategyMarketChannelTemplateId());
                dispatchFailDetail.setList(resultMap.get(Boolean.TRUE).stream().map(crowdDetailDo -> {
                    UserDispatchFailDetailDto.UserInfo userInfo = new UserDispatchFailDetailDto.UserInfo();
                    userInfo.setUserId(crowdDetailDo.getUserId());
                    userInfo.setApp(crowdDetailDo.getApp());
                    userInfo.setMobile(crowdDetailDo.getMobile());
                    return userInfo;
                }).collect(Collectors.toList()));
                userDispatchFailDetailService.batchSave(dispatchFailDetail);
                log.info("全部或部分短信参数为空的用户保存至失败记录表成功,总数:{}", resultMap.get(Boolean.TRUE).size());
            }
            return Triple.of(pair.getLeft(), crowdDetailList, list);
        } catch (Exception e) {
            log.warn("批量查询用户短信参数异常", e);
            return Triple.of(null, Collections.emptyList(), Collections.emptyList());
        }
    }

    @Override
    public Triple<Boolean, List<CrowdDetailDo>, List<PushUserData>> pushParamBatchQuery(DispatchDto reach, String app, List<CrowdDetailDo> batch) {
        try {
            Triple<Boolean, Integer, Map<Long, Map<String, Object>>> pair = templateParamService.getBatchPushTempParam(reach.getStrategyId(), app, reach.getStrategyMarketChannelTemplateId(), batch);
            if (Objects.equals(Boolean.FALSE, pair.getLeft())) {
                return Triple.of(pair.getLeft(), batch, Collections.emptyList());
            }

            Map<Long, Map<String, Object>> paramMap = pair.getRight();
            Map<Boolean, List<CrowdDetailDo>> resultMap = batch.stream().collect(Collectors.groupingBy(item -> smsParamFilter(pair, paramMap, item)));

            List<CrowdDetailDo> crowdDetailList = Optional.ofNullable(resultMap.get(Boolean.FALSE)).orElse(new ArrayList<>());
            List<PushUserData> list = Objects.equals(pair.getLeft(), Boolean.TRUE) ? crowdDetailList.stream().map(item -> {
                PushUserData push = new PushUserData();
                push.setUserNo(item.getUserId().toString());
                push.setDataMap(paramMap.get(item.getUserId()));
                return push;
            }).collect(Collectors.toList()) : Collections.emptyList();

            if (!CollectionUtils.isEmpty(resultMap.get(Boolean.TRUE))) {
                UserDispatchFailDetailDto dispatchFailDetail = new UserDispatchFailDetailDto();
                dispatchFailDetail.setStrategyId(reach.getStrategyId());
                dispatchFailDetail.setMarketChannel(reach.getStrategyChannel());
                dispatchFailDetail.setTemplateNo(reach.getStrategyMarketChannelTemplateId());
                dispatchFailDetail.setList(resultMap.get(Boolean.TRUE).stream().map(crowdDetailDo -> {
                    UserDispatchFailDetailDto.UserInfo userInfo = new UserDispatchFailDetailDto.UserInfo();
                    userInfo.setUserId(crowdDetailDo.getUserId());
                    userInfo.setApp(crowdDetailDo.getApp());
                    userInfo.setMobile(crowdDetailDo.getMobile());
                    return userInfo;
                }).collect(Collectors.toList()));
                userDispatchFailDetailService.batchSave(dispatchFailDetail);
                log.info("全部或部分push参数为空的用户保存至失败记录表成功，总数：{}", resultMap.get(Boolean.TRUE).size());
            }
            return Triple.of(pair.getLeft(), crowdDetailList, list);
        } catch (Exception e) {
            log.warn("批量查询用户push参数异常", e);
            return Triple.of(null, Collections.emptyList(), Collections.emptyList());
        }
    }

    @Override
    public Triple<Boolean, List<CrowdDetailDo>, List<AiUserData>> aiParamBatchQuery(DispatchDto reach, String app, List<CrowdDetailDo> batch) {
        try {
            Triple<Boolean, Integer, Map<Long, Map<String, Object>>> pair = templateParamService.getBatchAiTempParam(reach.getStrategyId(), app, reach.getAiProntoChannelDto(), batch);
            if (Objects.equals(Boolean.FALSE, pair.getLeft())) {
                return Triple.of(pair.getLeft(), batch, Collections.emptyList());
            }

            Map<Long, Map<String, Object>> paramMap = pair.getRight();
            log.info("testSmsParamFilter,pair:{},paramMap:{}", JsonUtil.toJson(pair),JsonUtil.toJson(paramMap));
            Map<Boolean, List<CrowdDetailDo>> resultMap = batch.stream().collect(Collectors.groupingBy(item -> smsParamFilter(pair, paramMap, item)));
            log.info("testSmsParamFilter,result:{}",JsonUtil.toJson(resultMap));
            List<CrowdDetailDo> crowdDetailList = Optional.ofNullable(resultMap.get(Boolean.FALSE)).orElse(new ArrayList<>());
            List<AiUserData> list = Objects.equals(pair.getLeft(), Boolean.TRUE) ? crowdDetailList.stream().map(item -> {
                AiUserData aiUserData = new AiUserData();
                aiUserData.setUserNo(item.getUserId());
                aiUserData.setApp(item.getApp());
                aiUserData.setParams(paramMap.get(item.getUserId()));
                return aiUserData;
            }).collect(Collectors.toList()) : Collections.emptyList();

            if (!CollectionUtils.isEmpty(resultMap.get(Boolean.TRUE))) {
                UserDispatchFailDetailDto dispatchFailDetail = new UserDispatchFailDetailDto();
                dispatchFailDetail.setStrategyId(reach.getStrategyId());
                dispatchFailDetail.setMarketChannel(reach.getStrategyChannel());
                dispatchFailDetail.setTemplateNo(reach.getStrategyMarketChannelTemplateId());
                dispatchFailDetail.setList(resultMap.get(Boolean.TRUE).stream().map(crowdDetailDo -> {
                    UserDispatchFailDetailDto.UserInfo userInfo = new UserDispatchFailDetailDto.UserInfo();
                    userInfo.setUserId(crowdDetailDo.getUserId());
                    userInfo.setApp(crowdDetailDo.getApp());
                    return userInfo;
                }).collect(Collectors.toList()));
                userDispatchFailDetailService.batchSave(dispatchFailDetail);
                log.info("全部或部分ai参数为空的用户保存至失败记录表成功，总数：{}", resultMap.get(Boolean.TRUE).size());
            }
            return Triple.of(pair.getLeft(), crowdDetailList, list);
        } catch (Exception e) {
            log.warn("批量查询用户ai参数异常", e);
            return Triple.of(null, Collections.emptyList(), Collections.emptyList());
        }
    }

    /**
     * 判断用户对应的参数是否齐全
     */
    /*private boolean smsParamFilter(Triple<Boolean, Integer, Map<Long, Map<String, Object>>> pair, Map<Long, Map<String, Object>> paramMap, CrowdDetailDo item) {
        Map<String, Object> userParamMap = paramMap.get(item.getUserId());
        return CollectionUtils.isEmpty(userParamMap) ||
                userParamMap.keySet().size() < pair.getMiddle() ||
                userParamMap.values().stream().anyMatch(Objects::isNull) ||
                userParamMap.values().stream().map(param -> Convert.toStr(param, null)).anyMatch(StringUtils::isBlank);
    }*/

    private <T> boolean smsParamFilter(Triple<Boolean, Integer, Map<Long, T>> pair, Map<Long, T> paramMap, CrowdDetailDo item) {
        Map<Long, T> userParamMap = (Map<Long, T>) paramMap.get(item.getUserId());
        if (CollectionUtils.isEmpty(userParamMap) || userParamMap.keySet().size() < pair.getMiddle()) {
            return true;
        }
        return userParamMap.values().stream()
                .anyMatch(value -> value == null || StringUtils.isBlank(Convert.toStr(value, null)));
    }



}
