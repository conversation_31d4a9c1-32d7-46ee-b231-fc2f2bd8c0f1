package com.xftech.cdp.domain.cache.impl;

import com.alibaba.fastjson.JSONArray;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventConditionRepository;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventConditionService;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Service
public class CacheStrategyMarketEventConditionServiceImpl implements CacheStrategyMarketEventConditionService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyMarketEventConditionRepository strategyMarketEventConditionRepository;

    private static final String DEFAULT_VALUE = "(null)";

    /**
     * 根据策略ID查询实时标签配置
     *
     * @param strategyId 策略ID
     * @return 实时标签配置
     */
    public List<StrategyMarketEventConditionDo> getByStrategyId(Long strategyId) {
        String redisKey = RedisKeyUtils.genStrategyConditionKey(strategyId);
        String data = redisUtils.get(redisKey);
        if (StringUtils.isNotBlank(data) && data.equals(DEFAULT_VALUE)) {
            return Collections.emptyList();
        }

        List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList = Collections.emptyList();

        if (StringUtils.isNotBlank(data)) {
            strategyMarketEventConditionDoList = JSONArray.parseArray(data, StrategyMarketEventConditionDo.class);
        }

        if (CollectionUtils.isEmpty(strategyMarketEventConditionDoList)) {
            strategyMarketEventConditionDoList = strategyMarketEventConditionRepository.getByStrategyId(strategyId);
            if (CollectionUtils.isEmpty(strategyMarketEventConditionDoList)) {
                redisUtils.set(redisKey, DEFAULT_VALUE, RedisUtils.DEFAULT_EXPIRE_SECONDS * 10);
            } else {
                redisUtils.set(redisKey, strategyMarketEventConditionDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS * 10);
            }
        }
        return strategyMarketEventConditionDoList;
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyMarketEventConditionDo param) {
        boolean isInsert = strategyMarketEventConditionRepository.insert(param);
        if (isInsert) {
            this.updateEventConditionCache(param.getStrategyId());
        }
        return isInsert;
    }

    /**
     * 插入
     *
     * @param strategyMarketEventConditionDoList 对象
     * @return 是否插入成功标识
     */
    public boolean insertBatch(List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList) {
        boolean isInsert = strategyMarketEventConditionRepository.insertBatch(strategyMarketEventConditionDoList);
        if (isInsert) {
            this.updateEventConditionCache(strategyMarketEventConditionDoList.get(0).getStrategyId());
        }
        return isInsert;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    @Override
    public boolean updateById(StrategyMarketEventConditionDo param) {
        boolean isUpdate = strategyMarketEventConditionRepository.updateById(param);
        if (isUpdate) {
            updateEventConditionCache(param.getStrategyId());
        }
        return isUpdate;
    }

    @Override
    public void deleteBatch(List<Long> delEventConditionIdList) {
        StrategyMarketEventConditionDo strategyMarketEventConditionDo = strategyMarketEventConditionRepository.selectById(delEventConditionIdList.get(0));
        strategyMarketEventConditionRepository.deleteBatch(delEventConditionIdList);
        this.updateEventConditionCache(strategyMarketEventConditionDo.getStrategyId());
    }

    @Override
    public void updateEventConditionCache(Long strategyId) {
        String redisKey = RedisKeyUtils.genStrategyConditionKey(strategyId);
        List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList = strategyMarketEventConditionRepository.getByStrategyId(strategyId);
        redisUtils.set(redisKey, strategyMarketEventConditionDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
    }

}
