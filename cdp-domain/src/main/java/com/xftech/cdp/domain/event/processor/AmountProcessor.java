package com.xftech.cdp.domain.event.processor;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import com.xftech.cdp.domain.event.model.config.AmountConfig;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.constants.EventConfigConstants;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * 金额转换数据处理器
 * <AUTHOR>
 * @version $ AmountProcessor, v 0.1 2024/11/19 13:57 snail Exp $
 */
@Slf4j
@Component
@ProcessorRule(processor = DataProcessEnum.AMOUNT)
public class AmountProcessor extends AbstractDataProcessor{
    @Override
    public Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String, Object> values) {
        AmountConfig amountConfig = (AmountConfig) fieldConfig;
        return new Field(detail.getTargetField(),parseAmount(detail,amountConfig,values));
    }

    private Object parseAmount(FieldDetail detail, AmountConfig amountConfig, Map<String, Object> values){
        if(EventConfigConstants.TO_FEN.equalsIgnoreCase(amountConfig.getOperate())){
            return toFen(detail,values);
        }else {
            return toYuan(detail,values);
        }
    }

    /**
     * 将金额转为元
     * @param detail 字段映射配置信息
     * @param values 消息体信息
     * @return 金额（元）
     */
    private BigDecimal toYuan(FieldDetail detail, Map<String,Object> values){
        Object originVal = values.get(detail.getTargetField());
        if(Objects.isNull(originVal)){
            return null;
        }

        Long convertedVal = Convert.toLong(originVal);
        BigDecimal resultVal = BigDecimal.valueOf(convertedVal).divide(BigDecimal.valueOf(EventConfigConstants.YUAN_TO_FEN));
        return resultVal;
    }

    /**
     * 将金额转为分
     * @param detail 字段映射配置信息
     * @param values 消息体信息
     * @return 金额（分）
     */
    private Long toFen(FieldDetail detail, Map<String,Object> values){
        Object originVal = values.get(detail.getTargetField());
        if(Objects.isNull(originVal)){
            return null;
        }

        BigDecimal convertedVal = Convert.toBigDecimal(originVal);
        BigDecimal resultVal = convertedVal.multiply(BigDecimal.valueOf(EventConfigConstants.YUAN_TO_FEN));

        return resultVal.longValue();
    }
}
