package com.xftech.cdp.domain.stat.entity;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.infra.rabbitmq.vo.HitResult;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 离线策略决策记录表
 */
@Data
public class OfflineDecisionRecordEntity {

    /**
     * 自增id
     */
    private Long id;
    /**
     * 策略id
     */
    private Long strategyId;
    /**
     * 营销渠道，0: 不营销，1:短信，2:电销，3:优惠券
     */
    private Integer marketChannel;
    /**
     * 营销渠道id
     */
    private Long marketChannelId;
    /**
     * app
     */
    private String app;
    /**
     * innerApp
     */
    private String innerApp;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * appUserId
     */
    private Long appUserId;
    /**
     * traceId
     */
    private String traceId;
    /**
     * 决策结果 0：不通过 1：通过
     */
    private Integer decisionResult;
    /**
     * 失败原因码
     */
    private Integer failCode;
    /**
     * 失败原因描述
     */
    private String failReason;
    /**
     * List<HitResult>对象json串
     */
    private String decisionDetail;
    /**
     * 年月,yyyyMM
     */
    private String month;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 按月分表名称
     */
    private String tableName;

    /**
     * 按月分表序号
     */
    private String tableNameNo;
    /**
     * 规则命中结果
     */
    List<HitResult> hitResultList;

    public String getTableName() {
        return "offline_decision_record_" + getTableNameNo();
    }

    public String getDecisionDetail() {
        return JSONObject.toJSONString(this.hitResultList);
    }

}
