package com.xftech.cdp.domain.strategy.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AdbRealTimeVariableGrayConfig {

    @Data
    public static class StrategyGrayConfig {
        // 全部开关
        private boolean allHits;

        // 灰度列表
        private List<Long> strategyList;

        public boolean isInGray(Long strategyId) {
            if (strategyList == null) {
                strategyList = new ArrayList<>(0);
            }
            return allHits || strategyList.contains(strategyId);
        }
    }

    @Data
    public static class VariableGrayConfig {
        // 全部开关
        private boolean allHits;

        // 灰度列表
        private List<String> variableList;

        public boolean isInGray(String varName) {
            if (variableList == null) {
                variableList = new ArrayList<>(0);
            }
            return allHits || variableList.contains(varName);
        }
    }
}
