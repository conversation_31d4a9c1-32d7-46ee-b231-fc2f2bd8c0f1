package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.crowd.model.enums.CrowdLabelRelationEnum;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/18
 */
@Component
public class CrowdLabelRelationDictFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<CrowdLabelRelationEnum> enums = Arrays.stream(CrowdLabelRelationEnum.values()).collect(Collectors.toList());
        for (CrowdLabelRelationEnum value : enums) {
            result.add(Dict.builder().dictCode(String.valueOf(value.getCode())).dictValue(value.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.CROWD_LABEL_RELATION;
    }

    @Override
    public String getDescription() {
        return "与上一个一级标签的关系";
    }
}
