package com.xftech.cdp.domain.stat.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlInterceptionLogRepository;
import com.xftech.cdp.domain.stat.entity.StatOfflineStrategyFlowDataEntity;
import com.xftech.cdp.domain.stat.entity.StatOfflineStrategyGroupDataEntity;
import com.xftech.cdp.domain.stat.repository.OfflineDecisionRecordDao;
import com.xftech.cdp.domain.stat.repository.StatOfflineStrategyFlowDataRepository;
import com.xftech.cdp.domain.stat.repository.StatOfflineStrategyGroupDataRepository;
import com.xftech.cdp.domain.stat.service.StatOfflineStrategyFlowDataService;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.repository.UserBlankGroupDetailRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.TransactionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/27 14:13
 */
@Slf4j
@Service
public class StatOfflineStrategyFlowDataServiceImpl implements StatOfflineStrategyFlowDataService {

    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private FlowCtrlInterceptionLogRepository flowCtrlInterceptionLogRepository;
    @Autowired
    private OfflineDecisionRecordDao offlineDecisionRecordDao;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private UserBlankGroupDetailRepository userBlankGroupDetailRepository;
    @Autowired
    private StatOfflineStrategyFlowDataRepository statOfflineStrategyFlowDataRepository;
    @Autowired
    private StatOfflineStrategyGroupDataRepository statOfflineStrategyGroupDataRepository;

    /**
     * 同步离线策略的数据流
     *
     * @param date 日期
     */
    @Override
    public void statOfflineStrategyFlowData(LocalDate date) {
        final TimeInterval timer = new TimeInterval();

        timer.start("1");
        String startDate = LocalDateTimeUtil.format(date, "yyyy-MM-dd");
        String endDate = LocalDateTimeUtil.format(date.plusDays(1), "yyyy-MM-dd");
        String tableNo = LocalDateTimeUtil.format(date, "yyyyMM");

        List<StrategyDo> strategyList = strategyRepository.getStrategyByExecTime(date).stream().filter(x->x.getType() != 1)
                .collect(Collectors.toList());
        for (StrategyDo strategyDo : strategyList) {
            timer.start("2");
            TransactionUtil.transactional(() -> {
                List<StrategyExecLogDo> strategyExecLogList = strategyExecLogRepository.getByStrategyIdAndExecTime(strategyDo.getId(), date);
                LocalDateTime execTime = strategyExecLogList.stream().min(Comparator.comparing(StrategyExecLogDo::getExecTime)).map(StrategyExecLogDo::getExecTime).orElse(null);

                StatOfflineStrategyFlowDataEntity flowDataEntity = new StatOfflineStrategyFlowDataEntity();
                // 统计日期（当天首个执行渠道的执行时间）
                flowDataEntity.setBizDate(execTime);
                // 策略ID
                flowDataEntity.setStrategyId(strategyDo.getId());
                // 人群包人数
                flowDataEntity.setCrowdUserNum(crowdExecLogRepository.countCrowdPackUserNum(date, strategyDo.getCrowdPackId().split(";")));
                // 人群包更新完成时间（取最近一条的更新时间）
                LocalDateTime refreshSuccessTime = crowdExecLogRepository.selectMaxRefreshSuccessTimeByCrowdIds(date, strategyDo.getCrowdPackId().split(";"));
                flowDataEntity.setCrowdRefreshTime(refreshSuccessTime);

                StatOfflineStrategyFlowDataEntity historyRecord = statOfflineStrategyFlowDataRepository.getByStrategyIdAndBizDate(strategyDo.getId(), execTime);
                if (Objects.nonNull(historyRecord)) {
                    flowDataEntity.setId(historyRecord.getId());
                    flowDataEntity.setUpdatedTime(LocalDateTime.now());
                    statOfflineStrategyFlowDataRepository.updateById(flowDataEntity);
                } else {
                    statOfflineStrategyFlowDataRepository.insert(flowDataEntity);
                }
                log.info("离线策略流控数据统计表：{}", JSON.toJSONString(flowDataEntity, SerializerFeature.WriteMapNullValue));


                strategyGroupRepository.selectListByStrategyId(strategyDo.getId()).stream().map(strategyGroupDo -> {
                    try {
                        List<StrategyMarketChannelDo> marketChannelList = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupDo.getId());
                        if (CollectionUtils.isEmpty(marketChannelList)){
                            return null;
                        }
                        List<Long> strategyChannelIds = marketChannelList.stream().map(StrategyMarketChannelDo::getId).collect(Collectors.toList());
                        boolean noMarket = marketChannelList.stream().allMatch(item -> StrategyMarketChannelEnum.getInstance(item.getMarketChannel()) == StrategyMarketChannelEnum.NONE);

                        StatOfflineStrategyGroupDataEntity groupDataEntity = new StatOfflineStrategyGroupDataEntity();
                        // 统计日期
                        groupDataEntity.setBizDate(flowDataEntity.getBizDate());
                        // 数据流统计表ID
                        groupDataEntity.setStrategyFlowDataId(flowDataEntity.getId());
                        // 策略ID
                        groupDataEntity.setStrategyId(strategyDo.getId());
                        // 策略分组ID
                        groupDataEntity.setStrategyGroupId(strategyGroupDo.getId());
                        // 分组名称
                        groupDataEntity.setGroupName(strategyGroupDo.getName());
                        // 流控过滤用户数（不营销组0）
                        Integer flowCtrlNum = flowCtrlInterceptionLogRepository.countStrategyGroupFlowCtrl(strategyDo.getId(), strategyChannelIds, startDate, endDate);
                        groupDataEntity.setFlowControlNum(flowCtrlNum);
                        // 实时标签过滤用户数
                        Integer labelNum = offlineDecisionRecordDao.countLabelNum(tableNo, strategyDo.getId(), strategyChannelIds, startDate, endDate, DecisionResultEnum.INSTANT_LABEL_FILTER_FAIL);
                        groupDataEntity.setFilterLabelNum(labelNum);
                        // 实时排除项过滤用户数
                        labelNum = offlineDecisionRecordDao.countLabelNum(tableNo, strategyDo.getId(), strategyChannelIds, startDate, endDate, DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL);
                        groupDataEntity.setFilterExcludeNum(labelNum);
                        // 留白组
                        if (noMarket) {
                            // 应发用户数（留白组）
                            int blankGroupNum = userBlankGroupDetailRepository.countUserBlankGroupData(tableNo, strategyDo.getId(), strategyChannelIds, startDate, endDate);
                            groupDataEntity.setDispatchNum(blankGroupNum);
                            // 分组用户数（流控用户数+实时标签+排查标签+应发用户）
                            groupDataEntity.setGroupUserNum(flowCtrlNum + groupDataEntity.getFilterLabelNum() + groupDataEntity.getFilterExcludeNum() + groupDataEntity.getDispatchNum());
                        }
                        // 非留白组
                        else {
                            // 应发用户数（渠道应发）
                            int dispatchNum = userDispatchDetailRepository.countStrategyGroupDispatchUserNum(tableNo, strategyDo.getId(), strategyChannelIds, startDate, endDate);
                            groupDataEntity.setDispatchNum(dispatchNum);
                            // 分组总人数（取当前分组最大值）
                            List<StrategyExecLogDo> execLogList = strategyExecLogList.stream().filter(item -> Objects.equals(item.getStrategyGroupId(), strategyGroupDo.getId())).collect(Collectors.toList());
                            Integer groupNum = execLogList.stream().max(Comparator.comparing(StrategyExecLogDo::getGroupCount)).map(StrategyExecLogDo::getGroupCount).orElse(0);
                            groupDataEntity.setGroupUserNum(groupNum);
                        }
                        return groupDataEntity;
                    }catch (Exception ex){
                        log.error("strategyGroupRepository.selectAllByStrategyId", ex);
                    }
                    return null;
                }).forEach(item -> {
                    if (item == null || item.getStrategyGroupId() == null) {
                        return;
                    }
                    StatOfflineStrategyGroupDataEntity groupHistoryRecord = statOfflineStrategyGroupDataRepository.getByStrategyGroupIdAndBizDateAndFlowDataId(item.getStrategyGroupId(), item.getBizDate(), item.getStrategyFlowDataId());
                    if (Objects.nonNull(groupHistoryRecord)) {
                        item.setId(groupHistoryRecord.getId());
                        item.setUpdatedTime(LocalDateTime.now());
                        statOfflineStrategyGroupDataRepository.updateById(item);
                    } else {
                        statOfflineStrategyGroupDataRepository.insert(item);
                    }
                    log.info("离线策略分组统计表：{}", JSON.toJSONString(item, SerializerFeature.WriteMapNullValue));
                });
            });
            log.info("离线策略数据流统计结束，策略ID：{}，耗时：{}ms", strategyDo.getId(), timer.intervalMs("2"));
        }
        log.info("离线策略数据流统计结束，总耗时：{}ms", timer.intervalMs("1"));
    }
}
