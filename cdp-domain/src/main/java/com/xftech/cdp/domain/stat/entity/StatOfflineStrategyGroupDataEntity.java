package com.xftech.cdp.domain.stat.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 离线策略分组统计表
 *
 * @TableName stat_offline_strategy_group_data
 */
@Data
public class StatOfflineStrategyGroupDataEntity implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 统计日期
     */
    private LocalDateTime bizDate;

    /**
     * 数据流统计表ID
     */
    private Long strategyFlowDataId;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 策略分组ID
     */
    private Long strategyGroupId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组用户数
     */
    private Integer groupUserNum;

    /**
     * 流控过滤用户数
     */
    private Integer flowControlNum;

    /**
     * 实时标签过滤用户数
     */
    private Integer filterLabelNum;

    /**
     * 实时排除项过滤用户数
     */
    private Integer filterExcludeNum;

    /**
     * 应发用户数
     */
    private Integer dispatchNum;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    private static final long serialVersionUID = 1L;
}