/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.strategy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.TimeFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ FlowListQueryBO, v 0.1 2023/12/21 10:57 yye.xu Exp $
 */

@Data
public class FlowListQueryBO {
    // "业务线 new-cust-新客  old-cust-老客"
    private String businessType;
    // "画布id"
    private Long id;
    // "画布名称"
    private String name;
    // "更新人"
    private String updatedOp;
    // "状态"
    private Integer status;
    // "触达渠道,数组对象:[1,2]"
    private List<Integer> marketChannel;
    // "发送规则 发送类型：0-单次, 1-例行, 2-事件, 3-周期, 9-引擎"
    private Integer sendRuler;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updateStartTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime updateEndTime;
}