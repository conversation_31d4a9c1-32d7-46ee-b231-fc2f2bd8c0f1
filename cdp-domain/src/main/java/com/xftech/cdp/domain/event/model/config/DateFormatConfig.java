package com.xftech.cdp.domain.event.model.config;

import com.xftech.cdp.domain.event.model.annotation.ProcessConfig;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 日期格式转换处理器配置信息
 * <AUTHOR>
 * @version $ DateFormatConfig, v 0.1 2024/11/18 10:37 snail Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ProcessConfig(processor = DataProcessEnum.FORMAT)
public class DateFormatConfig extends FieldConfig {
    /** 日期格式化类型 */
    private String format;
}
