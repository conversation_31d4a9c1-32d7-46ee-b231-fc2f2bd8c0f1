package com.xftech.cdp.domain.stat.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 离线策略流控数据统计表
 *
 * @TableName stat_offline_strategy_flow_data
 */
@Data
public class StatOfflineStrategyFlowDataEntity implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 统计日期
     */
    private LocalDateTime bizDate;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 人群包更新完成时间
     */
    private LocalDateTime crowdRefreshTime;

    /**
     * 人群包人数
     */
    private Integer crowdUserNum;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    private static final long serialVersionUID = 1L;
}