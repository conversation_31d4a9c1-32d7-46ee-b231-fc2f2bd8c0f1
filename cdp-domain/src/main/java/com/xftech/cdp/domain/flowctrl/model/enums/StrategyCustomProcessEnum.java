package com.xftech.cdp.domain.flowctrl.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum StrategyCustomProcessEnum {
    /**
     * 不需要自定义加工
     */
    UN_NEED_PROCESS(0, "不需要自定义加工"),

    /**
     * 需要自定义加工
     */
    NEED_PROCESS(1, "需要自定义加工"),
    /**
     * 查询接口类型
     */
    IN_INTERFACE(2, "查询接口");


    private final Integer code;

    private final String description;
}
