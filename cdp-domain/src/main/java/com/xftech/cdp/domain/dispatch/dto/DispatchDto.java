package com.xftech.cdp.domain.dispatch.dto;

import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;

import com.google.common.collect.Maps;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
@Data
public class DispatchDto {
    /**
     * 策略执行ID
     */
    private String strategyExecId;
    /**
     * 明细表序号
     */
    private String detailTableNo;
    /**
     * 策略ID
     */
    private Long strategyId;
    /**
     * 策略组ID
     */
    private Long strategyGroupId;

    /**
     * 策略组名
     */
    private String strategyGroupName;

    /**
     * 策略渠道ID
     */
    private Long strategyChannelId;
    /**
     * 策略渠道xxlId
     */
    private Integer strategyChannelXxlJobId;
    /**
     * 策略渠道
     */
    private Integer strategyChannel;
    /**
     * 渠道模板ID
     */
    private String strategyMarketChannelTemplateId;
    /**
     * 策略日志Id
     */
    private Long strategyExecLogId;
    /**
     * 策略日志重试Id
     */
    private Long strategyExecLogRetryId;
    /**
     * 消息ID
     */
    private String messageId;
    /**
     * 上报时间
     */
    private LocalDateTime triggerDatetime;

    /**
     * 策略类型
     */
    private StrategyRulerEnum strategyRulerEnum;

    private String failReason;

    /**
     * 事件名
     **/
    private String bizEventType;

    /*
     * 触达渠道
     */
    private StrategyMarketChannelDo strategyMarketChannelDo;

    /**
     * 流控规则
     */
    private List<FlowCtrlDo> flowCtrlList;
    
    /**
     * 类型ID
     */
    private String nameTypeId;

    /**
     * 优惠券渠道-券id
     */
    private String activityId;

    private IncreaseAmtParamDto increaseAmtParamDto;

    private AiProntoChannelDto aiProntoChannelDto;

    /**
     * NOTIFY为通知，不流控
     */
    private String dispatchType;

    /**
     * 短信签名
     */
    private String signatureKey;

    /**
     * 事件参数，透传至业务引擎使用
     */
    private Map<String, Object> eventParamMap = Maps.newHashMap();

    /**
     * 业务线类型
     */
    private String bizType;
}

