/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.param.service.impl;

import com.xftech.cdp.domain.param.service.ReplaceStrategy;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @version $ PhoneReplaceStrategy, v 0.1 2024/3/1 16:03 wancheng.qu Exp $
 */
@Service
public class PhoneReplaceStrategy implements ReplaceStrategy {


    @Override
    public String replace(UserInfoResp input) {
        String mobile = validateAndGetMobile(input);
        return mobile.substring(mobile.length() - 4);
    }

    private String validateAndGetMobile(UserInfoResp input) {
        return Optional.ofNullable(input)
                .map(UserInfoResp::getMobile)
                .filter(StringUtils::isNotBlank)
                .filter(mobile -> mobile.length() >= 4)
                .orElseThrow(() -> new IllegalArgumentException("Invalid UserInfoResp or mobile number"));

    }
}