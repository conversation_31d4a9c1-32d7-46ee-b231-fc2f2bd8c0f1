package com.xftech.cdp.domain.redecision.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

import com.xinfei.enginebase.util.apollo.ApolloUtil;

import com.xftech.cdp.domain.redecision.enums.ReDecisionResultStatus;
import com.xftech.cdp.domain.redecision.enums.ReDecisionStatus;
import com.xftech.cdp.domain.redecision.mq.ReDecisionMQProducer;
import com.xftech.cdp.domain.strategy.repository.EngineReDecisionDelayRepository;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EngineReDecisionDelayDo;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.HashUtils;
import com.xftech.cdp.infra.utils.RedisUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.xftech.cdp.domain.redecision.enums.TopicEnum.RE_DECISION_TOPIC;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/9
 * @description ReDecisionService
 */
@Slf4j
@Service
public class ReDecisionService {

    private static final String PARAM_RE_INPUT_COUNT = "reInputCount";
    private static final String PARAM_BIZ_TYPE = "biz_type";
    private static final String BIZ_TYPE_RE_INPUT = "reInput";
    private static final String PARAM_EVENT_FIRST_TIME = "event_first_time";

    @Value("${ReDecisionService.RE_INPUT_COUNT_MAX:20}")
    private Integer RE_INPUT_COUNT_MAX;

    @Value("${ReDecisionService.LOCK_SECONDS:60}")
    private Integer LOCK_SECONDS;
    @Value("${ReDecisionService.MAX_RETRY_TIMES:5}")
    private Integer MAX_RETRY_TIMES;
    @Value("${ReDecisionService.RETRY_INTERVAL_MILLIS:50}")
    private Integer RETRY_INTERVAL_MILLIS;

    @Resource
    private ReDecisionMQProducer reDecisionMQProducer;
    @Resource
    private EngineReDecisionDelayRepository engineReDecisionDelayRepository;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 延迟重推
     *
     * @param groupName          分组名称
     * @param reInputDelaySecond 延迟时间,单位s
     * @param bizEventVO         重推需要的事件信息
     */
    public void reDecision(String groupName, Integer reInputDelaySecond, BizEventVO bizEventVO) {
        try {
            if (bizEventVO == null) {
                return;
            }
            if (!ApolloUtil.containsInJSONStrList("ReDecisionWhiteList", String.valueOf(bizEventVO.getStrategyId()))) {
                return;
            }
            Long strategyId = bizEventVO.getStrategyId();
            Long userId = bizEventVO.getAppUserId();
            String messageId = bizEventVO.getMessageId();
            if (strategyId == null || userId == null || StringUtils.isBlank(messageId)) {
                return;
            }

            // 分布式锁
            String lockKey = String.format("ReDecision_%s_%s_%s", strategyId, userId, HashUtils.crc32Hash(messageId));
            boolean lock = redisUtils.tryLock(lockKey, LOCK_SECONDS, MAX_RETRY_TIMES, RETRY_INTERVAL_MILLIS);
            if (!lock) {
                log.error("ReDecisionService reDecision 获取分布式锁失败, lockKey={}", lockKey);
                return;
            }

            // 检查当前重试次数=已重试次数+1
            Map<String, Object> ext = Optional.ofNullable(bizEventVO.getExt()).orElse(Maps.newHashMap());
            int reInputCount = calReInputCount(ext);
            if (reInputCount <= 0 || reInputCount > RE_INPUT_COUNT_MAX) {
                log.info("ReDecisionService reDecision 本次超过最大重推次数 reInputCount={}", reInputCount);
                return;
            }
            EngineReDecisionDelayDo lastRecord = engineReDecisionDelayRepository.selectLastRecordByStrategyMsgId(strategyId, messageId);
            if (lastRecord != null && lastRecord.getReInputCount() >= RE_INPUT_COUNT_MAX) {
                log.info("ReDecisionService reDecision 历史超过最大重推次数 lastReInputCount={}", lastRecord.getReInputCount());
                return;
            }
            if (lastRecord != null && lastRecord.getReInputCount() >= reInputCount) {
                log.info("ReDecisionService reDecision 本次重推幂等校验失败 lastReInputCount={} >= reInputCount={}", lastRecord.getReInputCount(), reInputCount);
                return;
            }
            Optional<Long> lastReDecisionId = Optional.ofNullable(lastRecord).map(EngineReDecisionDelayDo::getId);

            // 落库: 用于数据记录+消费端异常时,兜底处理
            EngineReDecisionDelayDo engineReDecisionDelayDo = new EngineReDecisionDelayDo();
            engineReDecisionDelayDo.setStrategyId(strategyId);
            engineReDecisionDelayDo.setUserId(userId);
            engineReDecisionDelayDo.setGroupName(groupName);
            engineReDecisionDelayDo.setReInputCount(reInputCount);
            engineReDecisionDelayDo.setReInputDelaySecond(reInputDelaySecond);
            engineReDecisionDelayDo.setReInputTime(addSecondsToNowLegacy(reInputDelaySecond));
            LocalDateTime triggerDatetime = bizEventVO.getTriggerDatetime();
            if (triggerDatetime != null) {
                engineReDecisionDelayDo.setEventFirstTime(new Date(DateUtil.getMills(triggerDatetime)));
            } else {
                engineReDecisionDelayDo.setEventFirstTime(new Date());
            }
            engineReDecisionDelayDo.setStatus(ReDecisionStatus.INIT.getStatus());
            engineReDecisionDelayDo.setReInputResult(ReDecisionResultStatus.DEFAULT.getStatus());
            engineReDecisionDelayDo.setMessageId(messageId);
            lastReDecisionId.ifPresent(engineReDecisionDelayDo::setLastReDecisionId);
            engineReDecisionDelayDo.setDateValue(getCurrentDateAsInt());
            boolean insertSuccess = engineReDecisionDelayRepository.insert(engineReDecisionDelayDo);
            if (!insertSuccess) {
                return;
            }

            // 发送延迟消息
            bizEventVO.setEngineReDecisionId(engineReDecisionDelayDo.getId());
            bizEventVO.setBizEventType(BIZ_TYPE_RE_INPUT);
            ext.put(PARAM_BIZ_TYPE, BIZ_TYPE_RE_INPUT); // reInput=延迟类型
            ext.put(PARAM_RE_INPUT_COUNT, reInputCount); // 第几次延迟推入引擎
            ext.put(PARAM_EVENT_FIRST_TIME, engineReDecisionDelayDo.getEventFirstTime()); // 初始事件触发时间
            bizEventVO.setExt(ext);
            reDecisionMQProducer.send(RE_DECISION_TOPIC.getTopic(), JSON.toJSONString(bizEventVO), reInputDelaySecond);
        } catch (Exception e) {
            log.error("ReDecisionService reDecision error", e);
        }
    }

    private int calReInputCount(Map<String, Object> extMap) {
        Integer reInputCount = MapUtils.getInteger(extMap, PARAM_RE_INPUT_COUNT, 0);
        return reInputCount + 1;
    }

    public Date addSecondsToNowLegacy(int seconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, seconds);
        return calendar.getTime();
    }

    public int getCurrentDateAsInt() {
        LocalDate today = LocalDate.now();
        int year = today.getYear();
        int month = today.getMonthValue();
        int day = today.getDayOfMonth();
        // 拼接成年月日整数，确保月和日是两位数
        return year * 10000 + month * 100 + day;
    }

    /**
     * 更新延迟重推结果
     *
     * @param id     延迟重推ID
     * @param result 延迟重推结果
     */
    public void updateReDecisionResult(Long id, Integer result) {
        if (id == null || result == null) {
            return;
        }
        try {
            EngineReDecisionDelayDo engineReDecisionDelayDo = new EngineReDecisionDelayDo();
            engineReDecisionDelayDo.setId(id);
            engineReDecisionDelayDo.setReInputResult(result);
            engineReDecisionDelayDo.setUpdatedTime(new Date());
            engineReDecisionDelayRepository.updateByPrimaryKeySelective(engineReDecisionDelayDo);
        } catch (Exception e) {
            log.error("ReDecisionService updateReDecisionResult error", e);
        }
    }

}
