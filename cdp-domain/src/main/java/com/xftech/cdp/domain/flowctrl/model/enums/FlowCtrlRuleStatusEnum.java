package com.xftech.cdp.domain.flowctrl.model.enums;

import com.xftech.cdp.infra.exception.BizException;
import lombok.Getter;

import java.util.Objects;

/**
 * 流控规则状态
 * @<NAME_EMAIL>
 */

@Getter
public enum FlowCtrlRuleStatusEnum {
    /**
     * 初始化
     */
    INIT(0, "初始化"),
    /**
     * 生效中
     */
    EFFECTIVE(1, "生效中"),

    /**
     * 已关闭
     */
    CLOSE(2, "已关闭"),
    /**
     * 已删除
     */
    DELETED(3, "已删除");

    private final Integer code;

    private final String desc;

    FlowCtrlRuleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FlowCtrlRuleStatusEnum getInstance(Integer code) {
        for (FlowCtrlRuleStatusEnum flowCtrlRuleStatusEnum : FlowCtrlRuleStatusEnum.values()) {
            if (Objects.equals(flowCtrlRuleStatusEnum.getCode(), code)) {
                return flowCtrlRuleStatusEnum;
            }
        }
        throw new BizException(String.format("流控规则状态异常，状态：%s", code));
    }
}
