package com.xftech.cdp.domain.crowd.model.enums;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 人群包执行结果状态
 *
 * <AUTHOR>
 * @since 2023/2/17
 */
@AllArgsConstructor
@Getter
public enum CrowdExecResultEnum {

    /**
     * 正在执行
     */
    EXECUTING(0, "正在执行", 2),

    /**
     * 成功
     */
    SUCCESS(1, "成功", 3),

    /**
     * 失败
     */
    FAIL(2, "失败", 1);


    private final int code;

    private final String description;

    private final int sort;

    public static int getEnumSort(Integer code) {
        for (CrowdExecResultEnum crowdExecResultEnum : CrowdExecResultEnum.values()) {
            if (Objects.equals(crowdExecResultEnum.getCode(), code)) {
                return crowdExecResultEnum.getSort();
            }
        }
        return 1;
    }

    public static CrowdExecResultEnum getInstance(Integer code) {
        for (CrowdExecResultEnum crowdExecResultEnum : CrowdExecResultEnum.values()) {
            if (Objects.equals(crowdExecResultEnum.getCode(), code)) {
                return crowdExecResultEnum;
            }
        }
        return null;
    }
}
