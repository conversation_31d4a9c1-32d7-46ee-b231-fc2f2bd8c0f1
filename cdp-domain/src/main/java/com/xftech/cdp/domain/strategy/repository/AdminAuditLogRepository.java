/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @version $ AminAuditLogRepository, v 0.1 2024/1/2 20:24 yye.xu Exp $
 */

@Repository
@AllArgsConstructor
public class AdminAuditLogRepository {

    public void insert(AdminAuditLogDo aminAuditLogDo) {
        DBUtil.insert("adminAuditLogMapper.insertSelective", aminAuditLogDo);
    }
}