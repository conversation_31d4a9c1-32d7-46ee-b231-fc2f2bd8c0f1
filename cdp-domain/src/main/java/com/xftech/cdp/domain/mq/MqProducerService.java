package com.xftech.cdp.domain.mq;

import com.xftech.cdp.domain.strategy.model.enums.StrategyTypeEnum;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;

public interface MqProducerService {

    /**
     * 事件消息延迟消费
     * @param bizEventVO
     * @param seconds
     * @param strategyType
     */
    void bizEventDelay(BizEventVO bizEventVO, long seconds, StrategyTypeEnum strategyType);

    /**
     * 各渠道消息投递
     *
     * @param bizEventVO 事件消息
     */
    void channelDelivery(BizEventVO bizEventVO);
}