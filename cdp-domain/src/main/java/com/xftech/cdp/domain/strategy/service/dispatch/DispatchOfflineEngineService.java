/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.dispatch;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;
import com.xftech.cdp.domain.ads.model.PredictDecisionDto;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.strategy.model.dto.EngineStrategyGroupIdInfoDto;
import com.xftech.cdp.domain.strategy.model.enums.IncreaseTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.utils.*;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xftech.cdp.infra.utils.SerialNumberUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;

/**
 *
 * <AUTHOR>
 * @version $ DispatchOfflineEngine, v 0.1 2024/1/4 15:25 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class DispatchOfflineEngineService {
    private ModelPlatformService modelPlatformService;

    @Autowired
    private RedisUtils redisUtils;

    @Async("dispatchEngineExecutorWrapper")
    public Future<List<DispatchUserDelayDo>> pushEngine(Long strategyId, Integer strategySendRuler, Long strategyExecLogId,
                                                        StrategyGroupDo strategyGroupDo, String engineCode, CrowdDetailDo crowdDetailDo) {
        String currDate = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
        redisUtils.increment(RedisKeyUtils.genIntoEngineSum(currDate, strategyId), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
        Date now = new Date();
        List<DispatchUserDelayDo> dispatchUserDelayDos = new ArrayList<>();
        try {
            ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
                    .model_name(engineCode)
                    .biz_data(ModelPredictionReq.BizData.builder()
                            .requestId(SerialNumberUtil.nextId())
                            .biz_type(null)
                            .mobile(crowdDetailDo.getMobile())
                            .app_user_id(crowdDetailDo.getUserId())
                            .user_no(crowdDetailDo.getUserId())
                            .trigger_datetime(System.currentTimeMillis())
                            .timestamp(System.currentTimeMillis())
                            .callerCount(1L)
                            .device_id(crowdDetailDo.getDeviceId())
                            .requestType("OFFLINE")
                            .build())
                    .build();
            addStrategyIdParam(modelPredictionReq, strategyId);
            JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
            Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data")
                    .get(engineCode);
            PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);
            if (predictDecisionDto != null && predictDecisionDto.isSucced()) {
                for (PredictDecisionDto.DecisionData.Action action : predictDecisionDto.getActions()) {
                    if (CollectionUtils.isEmpty(action.getDispatch())) {
                        continue;
                    }
                    for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : action.getDispatch()) {
                        //redis记录groupId+渠道+template
                        saveRedis(strategyGroupDo.getId(), action, dispatch);
                        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
                        if (strategyMarketChannelEnum == null) {
                            continue;
                        }
                        if (strategyMarketChannelEnum == StrategyMarketChannelEnum.NONE) {
                            redisUtils.pfAddTwoDay(RedisKeyUtils.genNotMarketNum(currDate, strategyId), crowdDetailDo.getUserId());
                        }
                        Date dispatchTime = DateUtil.convert(dispatch.getDispatchTime());
                        if (dispatchTime == null) {
                            continue;
                        }
                        List<Object> detailObjects = new ArrayList<>();
                        if (strategyMarketChannelEnum == StrategyMarketChannelEnum.SALE_TICKET ||
                                strategyMarketChannelEnum == StrategyMarketChannelEnum.X_DAY_INTEREST_FREE) {
                            if (dispatch.getDetail_info().get("batch") == null) {
                                continue;
                            }
                            detailObjects = JsonUtil.toList(JsonUtil.toJson(dispatch.getDetail_info().get("batch")), Object.class);
                        } else {
                            detailObjects.add(dispatch.getDetail_info());
                        }
                        Map<String, Object> extInfo = new HashMap<>();
                        Map userDetail = crowdDetailDo.getUserDetailData();
                        userDetail.put("strategyExecLogId", strategyExecLogId);
                        if (strategyGroupDo != null) {
                            userDetail.put("strategyGroupId", strategyGroupDo.getId());
                            userDetail.put("strategyGroupName", strategyGroupDo.getName());
                            userDetail.put("strategySendRuler", strategySendRuler);
                        }
                        extInfo.put("userDetail", userDetail);
                        Optional.ofNullable(action.getGroup_source()).ifPresent(e->extInfo.put("group_source", e));
                        for (Object detail : detailObjects) {
                            extInfo.put("dispatchDetail", detail);
                            DispatchUserDelayDo dispatchUserDelayDo = new DispatchUserDelayDo();
                            dispatchUserDelayDo.setStrategyId(strategyId);
                            dispatchUserDelayDo.setUserId(crowdDetailDo.getUserId());
                            dispatchUserDelayDo.setMarketChannel((short) strategyMarketChannelEnum.getCode());
                            dispatchUserDelayDo.setGroupName(action.getGroup_id());
                            dispatchUserDelayDo.setDispatchTime(dispatchTime);
                            dispatchUserDelayDo.setDateValue(DateUtil.dayOfInt(now));
                            dispatchUserDelayDo.setExtInfo(JsonUtil.toJson(extInfo));
                            dispatchUserDelayDos.add(dispatchUserDelayDo);
                        }
                    }
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genMarketNum(currDate, strategyId), crowdDetailDo.getUserId());
                }
            } else {
                redisUtils.increment(RedisKeyUtils.genExecuteEngineFailSum(currDate,  strategyId), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
                Tracer.logEvent("offlineEngineError", String.valueOf(strategyId));
            }
        } catch (Exception ex) {
            log.error("pushEngine error, strategyId={}", strategyId, ex);
        }
        return new AsyncResult<>(dispatchUserDelayDos);
    }
    private void saveRedis(Long strategyGroupId, PredictDecisionDto.DecisionData.Action action, PredictDecisionDto.DecisionData.Action.Dispatch dispatch) {
        try {

            String groupId = action.getGroup_id();
            EngineStrategyGroupIdInfoDto engineStrategyGroupIdInfoDto = new EngineStrategyGroupIdInfoDto();
            engineStrategyGroupIdInfoDto.setGroupId(groupId);
            engineStrategyGroupIdInfoDto.setGroupSource(action.getGroup_source());

            List<EngineStrategyGroupIdInfoDto.ChannelInfo> channelInfoList = new ArrayList<>();

            StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(dispatch.getType());
            switch (strategyMarketChannelEnum) {
                case SMS:
                case PUSH:
                    if (dispatch.getDetail_info().get("template_id") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo smsOrPushChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    smsOrPushChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    smsOrPushChannelInfo.setTemplate(dispatch.getDetail_info().get("template_id").toString());
                    channelInfoList.add(smsOrPushChannelInfo);
                    break;
                case VOICE:
                    if (dispatch.getDetail_info().get("user_type") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo voiceChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    voiceChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    voiceChannelInfo.setTemplate(dispatch.getDetail_info().get("user_type").toString());
                    channelInfoList.add(voiceChannelInfo);
                    break;
                case SALE_TICKET:
                    List<PredictDecisionDto.DetailCouponDto> couponDtos = JsonUtil.toList(JsonUtil.toJson(dispatch.getDetail_info().get("batch")),
                            PredictDecisionDto.DetailCouponDto.class);
                    if (CollectionUtils.isEmpty(couponDtos)) {
                        return;
                    }
                    for (PredictDecisionDto.DetailCouponDto couponDto : couponDtos) {
                        EngineStrategyGroupIdInfoDto.ChannelInfo couponChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                        couponChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                        couponChannelInfo.setTemplate(couponDto.getActivity_id().toString());
                        channelInfoList.add(couponChannelInfo);
                    }
                    break;
                case VOICE_NEW:
                    if (dispatch.getDetail_info().get("name_type_id") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo newVoiceChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    newVoiceChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    newVoiceChannelInfo.setTemplate(dispatch.getDetail_info().get("name_type_id").toString());
                    channelInfoList.add(newVoiceChannelInfo);
                    break;
                case AI_PRONTO:
                    if (dispatch.getDetail_info().get("biz_source_code") == null
                            && dispatch.getDetail_info().get("name_type_id") == null) {
                        return;
                    }
                    EngineStrategyGroupIdInfoDto.ChannelInfo aiChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    aiChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    Object templateId = Objects.nonNull(dispatch.getDetail_info().get("biz_source_code")) ? dispatch.getDetail_info().get("biz_source_code") : dispatch.getDetail_info().get("name_type_id");
                    aiChannelInfo.setTemplate(String.valueOf(templateId));
                    channelInfoList.add(aiChannelInfo);
                    break;
                case INCREASE_AMOUNT:
                    EngineStrategyGroupIdInfoDto.ChannelInfo increaseAmountChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    increaseAmountChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    //提额类型：默认TEMP_CREDIT
                    IncreaseTypeEnum increaseType = IncreaseTypeEnum.getEnum(dispatch.getDetail_info().get("increase_type").toString());
                    String increaseTemplateId = StringUtils.isBlank(dispatch.getDetail_info().get("increase_type").toString()) ?
                            IncreaseTypeEnum.TEMP_CREDIT.getCode() : increaseType == null ?
                            IncreaseTypeEnum.TEMP_CREDIT.getCode() : increaseType.getDesc();
                    increaseAmountChannelInfo.setTemplate(increaseTemplateId);
                    channelInfoList.add(increaseAmountChannelInfo);
                    break;
                case NONE:
                    EngineStrategyGroupIdInfoDto.ChannelInfo noneChannelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
                    noneChannelInfo.setMarketChannel(strategyMarketChannelEnum.getCode());
                    channelInfoList.add(noneChannelInfo);
                    break;
                default:
                    break;
            }
            engineStrategyGroupIdInfoDto.setChannelInfoList(channelInfoList);

            String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
            String redisKey = String.format(RedisKeyConstants.ENGINE_STRATEGY_GROUP_ID_DATA, curDate, strategyGroupId);
            String redisHashValue = redisUtils.hGet(redisKey, groupId);
            if (StringUtils.isNotBlank(redisHashValue)) {
                EngineStrategyGroupIdInfoDto redisGroupIdInfo = JSON.parseObject(redisHashValue, EngineStrategyGroupIdInfoDto.class);
                List<EngineStrategyGroupIdInfoDto.ChannelInfo> redisChannelInfo = redisGroupIdInfo.getChannelInfoList();
                //比较当前渠道redis中是否存在
                for (EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo : channelInfoList) {
                    if (!redisChannelInfo.contains(channelInfo)) {
                        redisChannelInfo.add(channelInfo);
                    }
                }
                redisGroupIdInfo.setChannelInfoList(redisChannelInfo);
                redisUtils.hPut(redisKey, groupId, JSON.toJSONString(redisGroupIdInfo), RedisUtils.DEFAULT_EXPIRE_DAYS);
            } else {
                redisUtils.hPut(redisKey, groupId, JSON.toJSONString(engineStrategyGroupIdInfoDto), RedisUtils.DEFAULT_EXPIRE_DAYS);
            }
        } catch (Exception e) {
            log.info("offline engine save redis engine groupId info fail", e);
        }
    }

    private void addStrategyIdParam(ModelPredictionReq modelPredictionReq, Long strategyId) {

        if(!WhitelistSwitchUtil.boolSwitchByApollo("addStrategyIdParamEnable") || modelPredictionReq == null
                || strategyId == null) {
            return ;
        }

        ModelPredictionReq.BizData bizData = modelPredictionReq.getBiz_data();
        if(bizData != null) {
            bizData.setStrategy_id(strategyId);
        }
    }
}