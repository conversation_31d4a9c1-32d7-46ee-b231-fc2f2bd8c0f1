package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人群包标签配置表操作
 *
 * <AUTHOR>
 * @since 2023/2/13
 */

@Component
public class CrowdLabelRepository {

    /**
     * 根据人群包id查询标签配置
     *
     * @param crowdId 人群包id
     * @return 人群包标签配置列表
     */
    public List<CrowdLabelDo> selectListByCrowdId(Long crowdId) {
        return DBUtil.selectList("crowdLabel.selectListByCrowdId", crowdId);
    }

    /**
     * 根据人群包圈选一级标签id查询标签配置
     *
     * @param labelPrimaryId 人群包圈选一级标签id
     * @return 人群包标签配置列表
     */
    public List<CrowdLabelDo> selectListByLabelPrimaryId(Long labelPrimaryId) {
        return DBUtil.selectList("crowdLabel.selectListByLabelPrimaryId", labelPrimaryId);

    }

    /**
     * 根据人群包id删除标签配置
     *
     * @param crowdId 人群包id
     */
    public void deleteByCrowdId(Long crowdId) {
        DBUtil.delete("crowdLabel.deleteByCrowdId", crowdId);
    }

    /**
     * 插入一条人群包标签配置记录
     *
     * @param crowdLabelDo 人群包标签配置
     */
    public void insert(CrowdLabelDo crowdLabelDo) {
        DBUtil.insert("crowdLabel.insertSelective", crowdLabelDo);
    }
}
