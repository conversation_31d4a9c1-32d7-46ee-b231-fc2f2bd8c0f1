/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.label.service;

import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.label.LabelConfigListReq;
import com.xftech.cdp.domain.label.dto.MetaLabelJoinLabelDto;
import com.xftech.cdp.domain.label.repository.MetaLabelRepository;
import com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version $ MetaLabelService, v 0.1 2024/6/19 15:12 lingang.han Exp $
 */

@Service
@Slf4j
@AllArgsConstructor
public class MetaLabelService {
    private final MetaLabelRepository metaLabelRepository;

    public void saveMetaLabel(MetaLabelDo metaLabelDo) {
        metaLabelRepository.insert(metaLabelDo);
    }

    public MetaLabelDo getByLabelCode(String labelCode) {
        return metaLabelRepository.queryByLabelCode(labelCode);
    }

    public boolean exitsMetaLabel(MetaLabelDo metaLabelDo) {
        return metaLabelRepository.exitsMetaLabel(metaLabelDo);
    }

    public Boolean updateByLabelCode(MetaLabelDo metaLabelDo) {
        return metaLabelRepository.updateByLabelCode(metaLabelDo);
    }

    public Boolean updateById(MetaLabelDo metaLabelDo) {
        return metaLabelRepository.updateSelective(metaLabelDo);
    }

    public Page<MetaLabelJoinLabelDto> queryJoinLabelPage(LabelConfigListReq labelConfigListReq) {
        return metaLabelRepository.queryJoinLabelPage(labelConfigListReq);
    }

    public Boolean clearCheckResult(Long id){
        return metaLabelRepository.clearCheckResultById(id);
    }
}