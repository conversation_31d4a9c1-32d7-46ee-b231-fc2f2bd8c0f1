package com.xftech.cdp.domain.mq.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;
import com.dianping.cat.proxy.spi.Transaction;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.external.PushReportReq;
import com.xftech.cdp.domain.crowd.repository.CrowdPushBatchRepository;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.model.dto.EventFlcConfig;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.repository.DecisionRecordRepository;
import com.xftech.cdp.domain.strategy.repository.EventPushBatchRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.UserSendCounterService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.constant.TransConstants;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.rabbitmq.vo.CouponCallbackVO;
import com.xftech.cdp.infra.rabbitmq.vo.SmsReportVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.rocketmq.dto.*;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MqConsumeServiceImpl implements MqConsumeService {
    private static final Logger logger = LoggerFactory.getLogger(MqConsumeServiceImpl.class);

    @Autowired
    private CrowdPushBatchRepository crowdPushBatchRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;
    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private DecisionRecordRepository decisionRecordRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private UserSendCounterService userSendCounterService;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private MqTemplate mqTemplate;

    @Override
    public void smsReportProcess(List<SmsReportVO> smsReportVOList) {
        log.info("短信发送结果通知, sms={}", JsonUtil.toJson(smsReportVOList));
        List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();
        List<UserDispatchDetailDo> countDispatchBatchList = new ArrayList<>();
        Map<String, Long> batchGroupIdMap = new HashMap<>();
        smsReportVOList.forEach(smsReportVO -> {
            if (smsReportVO == null || smsReportVO.getReport() == null) {
                logger.warn("批次内容异常：批次内容为空");
                return;
            }
            SmsReportVO.Report report = smsReportVO.getReport();
            if (!SmsStatusEnum.DELIVERED.getFinalStatus().equals(report.getFinalStatus())
                    && !SmsStatusEnum.FAILED.getFinalStatus().equals(report.getFinalStatus())) {
                logger.warn("该用户触达明细最终状态不正确，批次号：{}，最终状态：{}", report.getBatchNum(), report.getFinalStatus());
                return;
            }
            // 根据批次号获取 crowd_push_batch 对应明细表序号
            String tableNo = crowdPushBatchRepository.selectTableNoByBatchNum(report.getBatchNum());
            EventPushBatchDo eventPushBatchDo = null;
            boolean isRealTime = StringUtils.isBlank(tableNo) ? true : false;
            if (StringUtils.isBlank(tableNo)) {
                eventPushBatchDo = eventPushBatchRepository.getByChannelAndBatchNum(StrategyMarketChannelEnum.SMS, report.getBatchNum());
                if (eventPushBatchDo != null && StringUtils.isNotBlank(eventPushBatchDo.getDetailTableNo())) {
                    tableNo = eventPushBatchDo.getDetailTableNo();
                    report.setBatchNum(eventPushBatchDo.getInnerBatchNum());
                    isRealTime = true;
                    // 失败计数更新
                    if (SmsStatusEnum.FAILED.getFinalStatus().equals(report.getFinalStatus())) {
                        // 失败计数器
                        log.info("短信回执结果失败, userid:{},strategyId:{},batchnum:{}", eventPushBatchDo.getUserId(),
                                eventPushBatchDo.getStrategyId(), report.getBatchNum());
                        Tracer.logMetricForCount("failed_sms_" + eventPushBatchDo.getStrategyId());
                        userSendCounterService.counterIncrementFailed(eventPushBatchDo.getUserId(),
                                eventPushBatchDo.getStrategyId(), eventPushBatchDo.getMarketChannel(),
                                DateUtil.dayOfInt(DateUtil.convert(eventPushBatchDo.getCreatedTime())));
                    }
                }
            }
            if (StringUtils.isBlank(tableNo)) {
                logger.warn("无对应短信批次下发表记录，批次号：{}", report.getBatchNum());
                return;
            }
            // 更新用户触达明细表：下发状态、下发时间
            UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
            userDispatchDetailDo.setTableName(TransConstants.userDispatchDetailTableName(tableNo));
            userDispatchDetailDo.setBatchNum(report.getBatchNum());
            userDispatchDetailDo.setUserId(report.getUserNo());
            userDispatchDetailDo.setStatus(SmsStatusEnum.getEnum(report.getFinalStatus()).getStatus());
            userDispatchDetailDo.setUsedStatus(SmsStatusEnum.getEnum(report.getFinalStatus()).getUsedStatus());
            userDispatchDetailDo.setDispatchTime(report.getDeliveryTime());
            userDispatchDetailDoList.add(userDispatchDetailDo);

            // 是否需要统计
            if (isRealTime) {
                StrategyExecLogDo strategyExecLogDo = strategyExecLogRepository.selectById(eventPushBatchDo.getExecLogId());
                if (strategyExecLogDo != null) {
                    if (strategyExecLogDo.getExecTime().toLocalDate().equals(LocalDate.now())) {
                        boolean shouldCount = redisUtils.lock(String.format(RedisKeyConstants.SMS_REPORT_MESSAGE, report.getBatchNum(), report.getMobile()), 1, RedisUtils.DEFAULT_EXPIRE_DAYS * 2, TimeUnit.SECONDS);
                        if (shouldCount) {
                            StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(eventPushBatchDo.getMarketChannelId());
                            if (strategyMarketChannelDo != null) {
                                userDispatchDetailDo.setStrategyId(eventPushBatchDo.getStrategyId());
                                userDispatchDetailDo.setMarketChannel(eventPushBatchDo.getMarketChannel());
                                userDispatchDetailDo.setCreatedTime(eventPushBatchDo.getCreatedTime());
                                countDispatchBatchList.add(userDispatchDetailDo);
                                batchGroupIdMap.put(report.getBatchNum(), strategyMarketChannelDo.getStrategyGroupId());
                            }
                        }
                    }
                }
            }
        });
        int size = (int) userDispatchDetailDoList.stream().filter(x -> x.getUserId() != null && x.getUserId() > 0L).count();
        if (size > 0) {
            log.info("短信回执中有用户号");
        }
        // 使用手机号更新，或者用户号更新
        boolean updateByMobile = userDispatchDetailRepository.updateBybatchNumAndMobile(userDispatchDetailDoList.stream().filter(x -> x.getUserId() == null || x.getUserId() == 0L).collect(Collectors.toList()));
        boolean updateByUserNo = userDispatchDetailRepository.updateByBatchNumAndUserNo(userDispatchDetailDoList.stream().filter(x -> x.getUserId() != null && x.getUserId() > 0L).collect(Collectors.toList()));
        if (updateByMobile || updateByUserNo) {
            if (CollectionUtils.isEmpty(batchGroupIdMap) || CollectionUtils.isEmpty(countDispatchBatchList)) {
                return;
            }
            this.redisCount(countDispatchBatchList, batchGroupIdMap);
        }
    }

    @Override
    public void pushReportProcess(PushReportReq pushReportReq) {
        log.info("push发送结果通知, push={}", JsonUtil.toJson(pushReportReq));
        List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();
        List<UserDispatchDetailDo> countDispatchBatchList = new ArrayList<>();
        Map<String, Long> batchGroupIdMap = new HashMap<>();
        if (Objects.isNull(pushReportReq)) {
            logger.warn("push回执内容异常：push回执内容为空");
            return;
        }
        if (PushCallbackStatusEnum.getEnum(pushReportReq.getStatus()) == null) {
            logger.warn("该用户触达明细最终状态不正确，批次号：{}，最终状态：{}", pushReportReq.getBatchNum(), pushReportReq.getStatus());
            return;
        }
        // 根据批次号获取 crowd_push_batch 对应明细表序号
        String tableNo = crowdPushBatchRepository.selectTableNoByBatchNum(pushReportReq.getBatchNum());
        EventPushBatchDo eventPushBatchDo = null;
        boolean isRealTime = StringUtils.isBlank(tableNo) ? true : false;
        if (StringUtils.isBlank(tableNo)) {
            eventPushBatchDo = eventPushBatchRepository.getByChannelAndBatchNum(StrategyMarketChannelEnum.PUSH, pushReportReq.getBatchNum());
            if (eventPushBatchDo != null && StringUtils.isNotBlank(eventPushBatchDo.getDetailTableNo())) {
                tableNo = eventPushBatchDo.getDetailTableNo();
                pushReportReq.setBatchNum(eventPushBatchDo.getInnerBatchNum());
                isRealTime = true;
                // 失败计数更新
                if (PushCallbackStatusEnum.FAILED.getCode().equals(pushReportReq.getStatus())) {
                    // 失败计数器
                    log.info("push回执结果失败, userid:{},strategyId:{},batchnum:{}", eventPushBatchDo.getUserId(),
                            eventPushBatchDo.getStrategyId(), pushReportReq.getBatchNum());
                    Tracer.logMetricForCount("failed_push_" + eventPushBatchDo.getStrategyId());
                    userSendCounterService.counterIncrementFailed(eventPushBatchDo.getUserId(),
                            eventPushBatchDo.getStrategyId(), eventPushBatchDo.getMarketChannel(),
                            DateUtil.dayOfInt(DateUtil.convert(eventPushBatchDo.getCreatedTime())));
                }
            }
        }
        if (StringUtils.isBlank(tableNo)) {
            logger.warn("无对应push批次下发表记录，批次号：{}", pushReportReq.getBatchNum());
            return;
        }
        // 更新用户触达明细表：下发状态、下发时间
        UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
        userDispatchDetailDo.setTableName(TransConstants.userDispatchDetailTableName(tableNo));
        userDispatchDetailDo.setBatchNum(pushReportReq.getBatchNum());
        userDispatchDetailDo.setUserId(pushReportReq.getUserNo());
        userDispatchDetailDo.setStatus(PushCallbackStatusEnum.getDispatchStatus(pushReportReq.getStatus()));
        userDispatchDetailDo.setDispatchTime(pushReportReq.getDeliveryTime() == null ? LocalDateTime.now() : LocalDateTimeUtil.parse(pushReportReq.getDeliveryTime(), TimeFormat.DATE_TIME));
        userDispatchDetailDoList.add(userDispatchDetailDo);

        // 是否需要统计
        if (isRealTime) {
            StrategyExecLogDo strategyExecLogDo = strategyExecLogRepository.selectById(eventPushBatchDo.getExecLogId());
            if (strategyExecLogDo != null) {
                if (strategyExecLogDo.getExecTime().toLocalDate().equals(LocalDate.now())) {
                    boolean shouldCount = redisUtils.lock(String.format(RedisKeyConstants.PUSH_REPORT_MESSAGE, pushReportReq.getBatchNum(), pushReportReq.getUserNo()), 1, RedisUtils.DEFAULT_EXPIRE_DAYS * 2, TimeUnit.SECONDS);
                    if (shouldCount) {
                        StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(eventPushBatchDo.getMarketChannelId());
                        if (strategyMarketChannelDo != null) {
                            userDispatchDetailDo.setStrategyId(eventPushBatchDo.getStrategyId());
                            userDispatchDetailDo.setMarketChannel(eventPushBatchDo.getMarketChannel());
                            userDispatchDetailDo.setCreatedTime(eventPushBatchDo.getCreatedTime());
                            countDispatchBatchList.add(userDispatchDetailDo);
                            batchGroupIdMap.put(pushReportReq.getBatchNum(), strategyMarketChannelDo.getStrategyGroupId());
                        }
                    }
                }
            }
        }
        boolean isUpdate = userDispatchDetailRepository.updateByBatchNumAndUserNo(userDispatchDetailDoList);
        if (isUpdate) {
            if (CollectionUtils.isEmpty(batchGroupIdMap) || CollectionUtils.isEmpty(countDispatchBatchList)) {
                return;
            }
            this.redisCount(countDispatchBatchList, batchGroupIdMap);
        }
    }

    @Override
    public void couponCallbackProcess(List<CouponCallbackVO> couponCallbackVOList) {
        log.info("优惠券发送结果通知, coupons={}", JsonUtil.toJson(couponCallbackVOList));
        List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();
        List<UserDispatchDetailDo> countDispatchBatchList = new ArrayList<>();
        Map<String, Long> batchGroupIdMap = new HashMap<>();
        couponCallbackVOList.forEach(couponCallbackVO -> {
            if (couponCallbackVO == null || couponCallbackVO.getReport() == null) {
                logger.warn("批次内容异常：批次内容为空");
                return;
            }
            CouponCallbackVO.Report report = couponCallbackVO.getReport();
            // 根据批次号获取 crowd_push_batch 对应明细表序号
            if (StringUtils.isEmpty(report.getBatchNum())) {
                logger.warn("批次内容异常：批次号为空");
                return;
            }
            String tableNo = crowdPushBatchRepository.selectTableNoByBatchNum(report.getBatchNum());
            EventPushBatchDo eventPushBatchDo = null;
            boolean isRealTime = StringUtils.isBlank(tableNo) ? true : false;
            if (StringUtils.isBlank(tableNo)) {
                StrategyMarketChannelEnum currentChannel = StrategyMarketChannelEnum.SALE_TICKET;
                if (report.getType() == 2) {
                    currentChannel = StrategyMarketChannelEnum.LIFE_RIGHTS;
                } else if (report.getCouponType() == 5) {
                    currentChannel = StrategyMarketChannelEnum.X_DAY_INTEREST_FREE;
                }
                eventPushBatchDo = eventPushBatchRepository.getByChannelAndBatchNum(currentChannel, report.getBatchNum());
                if (eventPushBatchDo != null && StringUtils.isNotBlank(eventPushBatchDo.getDetailTableNo())) {
                    tableNo = eventPushBatchDo.getDetailTableNo();
                    report.setBatchNum(eventPushBatchDo.getInnerBatchNum());
                    isRealTime = true;

                    // 失败计数更新
                    if (CouponStatusEnum.FAILED.getFinalStatus().equals(report.getFinalStatus())) {
                        log.info("优惠券回执结果失败, userid:{},strategyId:{},batchnum:{}", eventPushBatchDo.getUserId(),
                                eventPushBatchDo.getStrategyId(), report.getBatchNum());
                        Tracer.logMetricForCount("failed_coupon_" + eventPushBatchDo.getStrategyId());
                        userSendCounterService.counterIncrementFailed(eventPushBatchDo.getUserId(),
                                eventPushBatchDo.getStrategyId(), eventPushBatchDo.getMarketChannel(),
                                DateUtil.dayOfInt(DateUtil.convert(eventPushBatchDo.getCreatedTime())));
                    }
                }
            }
            if (StringUtils.isBlank(tableNo)) {
                logger.warn("无对应优惠券批次下发表记录，批次号：{}", report.getBatchNum());
                return;
            }

            //生活权益
            if (couponCallbackVO.getReport().getUserId() != null && StringUtils.isNotBlank(tableNo) && Objects.equals(couponCallbackVO.getReport().getType(), 2)) {
                //查dispatch表
                UserDispatchDetailDo userDispatchDetailDo = userDispatchDetailRepository.selectByBatchNoAndUserId(tableNo, report.getBatchNum(), report.getUserId());
                if (userDispatchDetailDo == null) {
                    log.info("生活权益查询dispatch表，查出非一条,tableNo:{},batchNum:{},userId:{}", tableNo, report.getBatchNum(), report.getUserId());
                    return;
                }
                BizEventRocketMessageVO bizEventRocketMessageVO = new BizEventRocketMessageVO();
                bizEventRocketMessageVO.setAppUserId(userDispatchDetailDo.getUserId());
                Map extDetail = JSONObject.parseObject(userDispatchDetailDo.getExtDetail(), Map.class);
                Map userInfo = null;
                if (!Objects.isNull(extDetail) && !Objects.isNull(extDetail.get("userInfo"))) {
                    userInfo = JSONObject.parseObject(extDetail.get("userInfo").toString(), Map.class);
                }
                if (!Objects.isNull(userInfo) && userInfo.containsKey("app") && userInfo.containsKey("innerApp")) {
                    bizEventRocketMessageVO.setApp(String.valueOf(userInfo.get("app")));
                    bizEventRocketMessageVO.setInnerApp(String.valueOf(userInfo.get("innerApp")));
                } else {
                    log.info("生活权益查询dispatch表，未查到userInfo中app,innerApp信息,tableNo:{},batchNum:{},userId:{}", tableNo, report.getBatchNum(), report.getUserId());
                    return;
                }
                bizEventRocketMessageVO.setEventTime(System.currentTimeMillis());
                BizEventRocketMessageVO.ExtraData extraData = new BizEventRocketMessageVO.ExtraData();
                extraData.setLifeRightsCallBackStrategyId(userDispatchDetailDo.getStrategyId());
                extraData.setLifeRightCallBackStatus(Objects.requireNonNull(CouponStatusEnum.getEnum(report.getFinalStatus())).getStatus());
                bizEventRocketMessageVO.setExtraData(extraData);
                bizEventRocketMessageVO.setEventTime(System.currentTimeMillis());
                // 发送回执消息
                mqTemplate.syncSend("tp_xyf_cdp_notify:tg_liferights", JsonUtil.toJson(bizEventRocketMessageVO));
                log.info("生活权益回执，发送消息, topic:tp_xyf_cdp_notify:tg_liferights, 消息内容:{}", JsonUtil.toJson(bizEventRocketMessageVO));
            }

            //X天免息
            if (couponCallbackVO.getReport().getUserId() != null && StringUtils.isNotBlank(tableNo) && Objects.equals(couponCallbackVO.getReport().getCouponType(), 5)) {

                //查dispatch表
                UserDispatchDetailDo userDispatchDetailDo = userDispatchDetailRepository.selectByBatchNoAndUserId(tableNo, report.getBatchNum(), report.getUserId());
                if (userDispatchDetailDo == null) {
                    log.info("X天免息查询dispatch表，查出非一条,tableNo:{},batchNum:{},userId:{}", tableNo, report.getBatchNum(), report.getUserId());
                    return;
                }
                BizEventRocketMessageVO bizEventRocketMessageVO = new BizEventRocketMessageVO();
                bizEventRocketMessageVO.setAppUserId(userDispatchDetailDo.getUserId());
                Map extDetail = JSONObject.parseObject(userDispatchDetailDo.getExtDetail(), Map.class);
                Map userInfo = null;
                if (!Objects.isNull(extDetail) && !Objects.isNull(extDetail.get("userInfo"))) {
                    userInfo = JSONObject.parseObject(extDetail.get("userInfo").toString(), Map.class);
                }
                if (!Objects.isNull(userInfo) && userInfo.containsKey("app") && userInfo.containsKey("innerApp")) {
                    bizEventRocketMessageVO.setApp(String.valueOf(userInfo.get("app")));
                    bizEventRocketMessageVO.setInnerApp(String.valueOf(userInfo.get("innerApp")));
                } else {
                    log.info("X天免息dispatch表，未查到userInfo中app,innerApp信息,tableNo:{},batchNum:{},userId:{}", tableNo, report.getBatchNum(), report.getUserId());
                    return;
                }
                bizEventRocketMessageVO.setEventTime(System.currentTimeMillis());
                BizEventRocketMessageVO.ExtraData extraData = new BizEventRocketMessageVO.ExtraData();
                extraData.setXDayInterestFreeStrategyId(userDispatchDetailDo.getStrategyId());
                extraData.setXDayInterestFreeStatus(Objects.requireNonNull(CouponStatusEnum.getEnum(report.getFinalStatus())).getStatus());
                bizEventRocketMessageVO.setExtraData(extraData);
                bizEventRocketMessageVO.setEventTime(System.currentTimeMillis());
                // 发送回执消息
                mqTemplate.syncSend("tp_xyf_cdp_notify:tg_xDayInterestFree", JsonUtil.toJson(bizEventRocketMessageVO));
                log.info("X天免息回执，发送消息, topic:tp_xyf_cdp_notify:tg_xDayInterestFree, 消息内容:{}", JsonUtil.toJson(bizEventRocketMessageVO));
            }
            // 更新用户触达明细表：下发状态、下发时间
            UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
            userDispatchDetailDo.setTableName(TransConstants.userDispatchDetailTableName(tableNo));
            userDispatchDetailDo.setBatchNum(report.getBatchNum());
            userDispatchDetailDo.setUserId(Long.parseLong(report.getUserId()));
            userDispatchDetailDo.setUserId(report.getUserId() != null ? Long.valueOf(report.getUserId()) : 0);
            userDispatchDetailDo.setStatus(CouponStatusEnum.getEnum(report.getFinalStatus()).getStatus());
            userDispatchDetailDo.setUsedStatus(CouponStatusEnum.getEnum(report.getFinalStatus()).getUsedStatus());
            userDispatchDetailDo.setDispatchTime(LocalDateTime.parse(report.getPublishTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS")));
            userDispatchDetailDoList.add(userDispatchDetailDo);

            // 是否需要统计
            if (isRealTime) {
                StrategyExecLogDo strategyExecLogDo = strategyExecLogRepository.selectById(eventPushBatchDo.getExecLogId());
                if (strategyExecLogDo != null) {
                    if (strategyExecLogDo.getCreatedTime().toLocalDate().equals(LocalDate.now())) {
                        boolean shouldCount = redisUtils.lock(String.format(RedisKeyConstants.COUPON_CALLBACK_MESSAGE, report.getBatchNum(), report.getUserId()), 1, RedisUtils.DEFAULT_EXPIRE_DAYS * 2, TimeUnit.SECONDS);
                        if (shouldCount) {
                            StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(eventPushBatchDo.getMarketChannelId());
                            if (strategyMarketChannelDo != null) {
                                userDispatchDetailDo.setStrategyId(eventPushBatchDo.getStrategyId());
                                userDispatchDetailDo.setMarketChannel(eventPushBatchDo.getMarketChannel());
                                userDispatchDetailDo.setCreatedTime(eventPushBatchDo.getCreatedTime());
                                countDispatchBatchList.add(userDispatchDetailDo);
                                batchGroupIdMap.put(report.getBatchNum(), strategyMarketChannelDo.getStrategyGroupId());
                            }
                        }
                    }
                }
            }
        });
        boolean isUpdate = userDispatchDetailRepository.updateByBatchNumAndUserNo(userDispatchDetailDoList);
        if (isUpdate) {
            if (CollectionUtils.isEmpty(batchGroupIdMap) || CollectionUtils.isEmpty(countDispatchBatchList)) {
                return;
            }
            this.redisCount(countDispatchBatchList, batchGroupIdMap);
        }
    }

    private void redisCount(List<UserDispatchDetailDo> countDispatchBatchList, Map<String, Long> batchGroupIdMap) {
        if (!CollectionUtils.isEmpty(countDispatchBatchList)) {
            countDispatchBatchList.forEach(countDispatchBatch -> {
                String redisKey = null;

                if (Objects.equals(countDispatchBatch.getStatus(), Constants.SUCCESS_STATUS)) {
                    redisKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_SUCCESS_COUNT, LocalDateTimeUtil.format(countDispatchBatch.getCreatedTime(), "yyyyMMdd"), countDispatchBatch.getStrategyId(), countDispatchBatch.getMarketChannel(), batchGroupIdMap.get(countDispatchBatch.getBatchNum()));
                }
                if (Objects.equals(countDispatchBatch.getStatus(), Constants.FAIL_STATUS)) {
                    redisKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_FAIL_COUNT, LocalDateTimeUtil.format(countDispatchBatch.getCreatedTime(), "yyyyMMdd"), countDispatchBatch.getStrategyId(), countDispatchBatch.getMarketChannel(), batchGroupIdMap.get(countDispatchBatch.getBatchNum()));
                }
                if (Objects.equals(countDispatchBatch.getUsedStatus(), Constants.SUCCESS_STATUS)) {
                    redisKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_USED_COUNT, LocalDateTimeUtil.format(countDispatchBatch.getCreatedTime(), "yyyyMMdd"), countDispatchBatch.getStrategyId(), countDispatchBatch.getMarketChannel(), batchGroupIdMap.get(countDispatchBatch.getBatchNum()));
                }
                if (redisKey != null) {
                    redisUtils.increment(redisKey, 1L);
                    redisUtils.expire(redisKey, getSeconds(LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MAX)), TimeUnit.SECONDS);
                }
            });
        }
    }

    /**
     * 获取时间区间
     *
     * @param endTime 结束时间
     * @return 时间区间
     */
    private static Long getSeconds(LocalDateTime endTime) {
        return Duration.between(LocalDateTime.now(), endTime).getSeconds();
    }

    public boolean isReject(BizEventMessageVO bizEventMessageVO) {
        try {
            // 获取限制的时间， 如果是null不限制， 如果小于等于0，全部限制，否则限制N秒多少次;
            Long userId = bizEventMessageVO.getCreditUserId();
            if (StringUtils.equalsIgnoreCase("Start", bizEventMessageVO.getBizEventType())) {
                // 特殊处理
                if (userId == null) {
                    userId = bizEventMessageVO.getUser_id();
                }
            }
            log.info("开始进行事件流控逻辑, userId:{}, 事件名称:{}", userId,
                    bizEventMessageVO.getBizEventType());
            if (StringUtils.isNotEmpty(bizEventMessageVO.getBizEventType())
                    && userId != null
                    && userId > 0) {
                EventFlcConfig eventFlcConfig = appConfigService.getEventFlcConfig();
                if (eventFlcConfig != null) {
                    Integer limitSeconds = eventFlcConfig
                            .getLimitSeconds(bizEventMessageVO.getBizEventType());
                    if (limitSeconds != null) {
                        if (limitSeconds <= 0) {
                            return true;
                        }
                        String limitKey = String.format("eventflc:%s:%s", bizEventMessageVO.getBizEventType(), userId);
                        boolean ret = redisUtils.lock(limitKey, "1", limitSeconds);
                        if (!ret) {
                            return true;
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("isReject", ex);
        }
        return false;
    }

    @Override
    public void bizEventProcess(String messageId, BizEventMessageVO bizEventMessageVO) {
        Transaction transaction = Tracer.newTransaction("MqConsumeService", "bizEventProcess");
        try {
            if (isReject(bizEventMessageVO)) {
                Tracer.logEvent("RejectT0Event", bizEventMessageVO.getBizEventType());
                log.info("事件命中流控丢弃, event:{}, userId:{}", bizEventMessageVO.getBizEventType(), Optional.ofNullable(bizEventMessageVO.getCreditUserId())
                        .orElse(bizEventMessageVO.getUser_id()));
                transaction.setStatus(Transaction.SUCCESS);
                return;
            }
            strategyEventDispatchService.prescreen(messageId, bizEventMessageVO);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            logger.warn("策略预筛方法异常", e);
            // 暂且
            transaction.setStatus(Transaction.SUCCESS);
        } finally {
            transaction.complete();
        }
    }

    @Override
    public void bizEventDelayProcess(BizEventVO bizEventVO) {
        Transaction transaction = Tracer.newTransaction("MqConsumeService", "bizEventDelayProcess");
        try {
            strategyEventDispatchService.rescreen(bizEventVO);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            logger.warn("策略复筛方法异常", e);
            // 暂且
            transaction.setStatus(Transaction.SUCCESS);
        } finally {
            transaction.complete();
        }
    }

    // 触达
    @Override
    public void bizEventDispatchProcess(Message message) {
        try {
            BizEventVO bizEventVO = JSONObject.parseObject(new String(message.getBody(), "UTF-8"), BizEventVO.class);
            logger.info("触达消息实体内容：{}", JsonUtil.toJson(bizEventVO));
            strategyEventDispatchService.dispatch(bizEventVO);
        } catch (Exception e) {
            logger.warn("策略触达方法异常", e);
        }
    }

    @Override
    public void bizEventDecisionResultProcess(List<BizEventVO> bizEventVOList) {
        try {
            List<DecisionRecordDo> decisionRecordDoList = new ArrayList<>();
            bizEventVOList.forEach(bizEventVO -> {
                if (bizEventVO == null) {
                    return;
                }
                DecisionRecordDo decisionRecordDo = new DecisionRecordDo();
                decisionRecordDo.setTableName("decision_record_" + LocalDateTimeUtil.format(bizEventVO.getTriggerDatetime(), "yyyyMM"));
                decisionRecordDo.setApp(bizEventVO.getApp());
                decisionRecordDo.setInnerApp(bizEventVO.getInnerApp());
                decisionRecordDo.setOs(bizEventVO.getOs());
                decisionRecordDo.setMobile(bizEventVO.getMobile());
                decisionRecordDo.setAppUserId(bizEventVO.getAppUserId());
                decisionRecordDo.setUtmSource(bizEventVO.getUtmSource());
                decisionRecordDo.setEventName(bizEventVO.getBizEventType());
                decisionRecordDo.setRegisterTime(bizEventVO.getRegisterTime());
                decisionRecordDo.setTriggerDatetime(bizEventVO.getTriggerDatetime());
                decisionRecordDo.setAmount(bizEventVO.getAmount());
                decisionRecordDo.setAdjustAmount(bizEventVO.getAdjustAmount());
                decisionRecordDo.setMessageId(bizEventVO.getMessageId());
                decisionRecordDo.setRecordType(bizEventVO.getRetryNum() == null || bizEventVO.getRetryNum() < 1 ? 1 : 2);
                decisionRecordDo.setTraceId(bizEventVO.getTraceId());
                decisionRecordDo.setStrategyId(bizEventVO.getStrategyId());
                decisionRecordDo.setDecisionResult(0);
                decisionRecordDo.setDecisionTime(bizEventVO.getDecisionTime());
                decisionRecordDo.setGroupId(bizEventVO.getStrategyGroupId());
                decisionRecordDo.setGroupName(bizEventVO.getGroupName());
                if (bizEventVO.getDecisionResult() != null) {
                    decisionRecordDo.setDecisionResult(bizEventVO.getDecisionResult() ? 1 : 0);
                }
                decisionRecordDo.setFailCode(Objects.nonNull(bizEventVO.getFailCode()) ? bizEventVO.getFailCode() : DecisionResultEnum.NONE.getFailCode());
                decisionRecordDo.setFailReason(bizEventVO.getFailReason());
                if (!CollectionUtils.isEmpty(bizEventVO.getHitResultList())) {
                    decisionRecordDo.setDecisionDetail(JSONObject.toJSONString(bizEventVO.getHitResultList()));
                }
                decisionRecordDo.setCurrentUtmSource(bizEventVO.getCurrentUtmSource());
                decisionRecordDo.setSourceType(bizEventVO.getSourceType());
                decisionRecordDo.setEngineDetail(bizEventVO.getEngineDetail());
                decisionRecordDo.setExtrData(bizEventVO.getExtrData());
                decisionRecordDoList.add(decisionRecordDo);
            });
//            log.info("决策结果消息内容 {}", JsonUtil.toJson(decisionRecordDoList));
            decisionRecordRepository.insertBatch(decisionRecordDoList);
        } catch (Exception e) {
            logger.warn("决策结果消息方法异常", e);
        }
    }

    @Override
    public void aiCallbackProcess(AiCallBackMessageVO aiCallBackMessageVO) {
        log.info("ai发送结果通知,ai={}", JsonUtil.toJson(aiCallBackMessageVO));
        List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();
        List<UserDispatchDetailDo> countDispatchBatchList = new ArrayList<>();
        Map<String, Long> batchGroupIdMap = new HashMap<>();
        if (Objects.isNull(aiCallBackMessageVO)) {
            logger.error("ai回执内容异常:ai回执内容为空");
            return;
        }
        if (StringUtils.isBlank(aiCallBackMessageVO.getBatchNo()) || StringUtils.isBlank(aiCallBackMessageVO.getStatus())) {
            logger.error("ai回执内容异常:缺失batchNo or status");
            return;
        }
        if (AiProntoCallbackStatusEnum.getEnum(aiCallBackMessageVO.getStatus()) == null) {
            logger.error("ai回执内容异常:status未识别");
            return;
        }
        if (CollectionUtils.isEmpty(aiCallBackMessageVO.getFilterDetails())
                && CollectionUtils.isEmpty(aiCallBackMessageVO.getCallDetails())
                && CollectionUtils.isEmpty(aiCallBackMessageVO.getFailureDetails())) {
            logger.error("ai回执内容异常:缺失名单信息");
            return;
        }
        List<Long> failUserNoList = new ArrayList<>();
        List<AiCallBackCallDetail> successUserNoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(aiCallBackMessageVO.getFilterDetails())) {
            failUserNoList.addAll(aiCallBackMessageVO.getFilterDetails().stream().map(AiCallBackFilterDetail::getUserNo).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(aiCallBackMessageVO.getFailureDetails())) {
            failUserNoList.addAll(aiCallBackMessageVO.getFailureDetails().stream().map(AiCallBackFailureDetail::getUserNo).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(aiCallBackMessageVO.getCallDetails())) {
            failUserNoList.addAll(aiCallBackMessageVO.getCallDetails().stream().filter(item -> "-1".equals(item.getCallStatus())).map(AiCallBackCallDetail::getUserNo).collect(Collectors.toList()));
            successUserNoList = aiCallBackMessageVO.getCallDetails().stream().filter(item -> "1".equals(item.getCallStatus())).collect(Collectors.toList());
        }

        // 根据批次号获取 crowd_push_batch 对应明细表序号
        String tableNo = crowdPushBatchRepository.selectTableNoByBatchNum(aiCallBackMessageVO.getBatchNo());
        EventPushBatchDo eventPushBatchDo = null;
        boolean isRealTime = StringUtils.isBlank(tableNo);
        if (StringUtils.isBlank(tableNo)) {
            eventPushBatchDo = eventPushBatchRepository.getByChannelAndBatchNum(StrategyMarketChannelEnum.AI_PRONTO, aiCallBackMessageVO.getBatchNo());
            if (eventPushBatchDo != null && StringUtils.isNotBlank(eventPushBatchDo.getDetailTableNo())) {
                tableNo = eventPushBatchDo.getDetailTableNo();
                aiCallBackMessageVO.setBatchNo(eventPushBatchDo.getInnerBatchNum());
                isRealTime = true;
                for (Long failUserNo : failUserNoList) {
                    // 失败计数更新
                    // 失败计数器
                    log.info("ai回执结果失败, userid:{},strategyId:{},batchnum:{}", eventPushBatchDo.getUserId(),
                            eventPushBatchDo.getStrategyId(), aiCallBackMessageVO.getBatchNo());
                    Tracer.logMetricForCount("failed_ai_" + eventPushBatchDo.getStrategyId());
                    userSendCounterService.counterIncrementFailed(eventPushBatchDo.getUserId(),
                            eventPushBatchDo.getStrategyId(), eventPushBatchDo.getMarketChannel(),
                            DateUtil.dayOfInt(DateUtil.convert(eventPushBatchDo.getCreatedTime())));
                }
            }
        }
        if (StringUtils.isBlank(tableNo)) {
            logger.warn("无对应ai批次下发表记录，批次号：{}", aiCallBackMessageVO.getBatchNo());
            return;
        }

        for (AiCallBackCallDetail successUser : successUserNoList) {
            // 更新用户触达明细表：下发状态、下发时间
            UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
            userDispatchDetailDo.setTableName(TransConstants.userDispatchDetailTableName(tableNo));
            userDispatchDetailDo.setBatchNum(aiCallBackMessageVO.getBatchNo());
            userDispatchDetailDo.setUserId(successUser.getUserNo());
            userDispatchDetailDo.setStatus(UserDispatchDetailStatusEnum.SUCCESS.getStatus());
            userDispatchDetailDo.setDispatchTime(LocalDateTimeUtil.parse(successUser.getBeginTime(), TimeFormat.DATE_TIME));
            userDispatchDetailDoList.add(userDispatchDetailDo);
        }

        for (Long failUserNo : failUserNoList) {
            // 更新用户触达明细表：下发状态、下发时间
            UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
            userDispatchDetailDo.setTableName(TransConstants.userDispatchDetailTableName(tableNo));
            userDispatchDetailDo.setBatchNum(aiCallBackMessageVO.getBatchNo());
            userDispatchDetailDo.setUserId(failUserNo);
            userDispatchDetailDo.setStatus(UserDispatchDetailStatusEnum.FAIL.getStatus());
            userDispatchDetailDo.setDispatchTime(LocalDateTime.now());
            userDispatchDetailDoList.add(userDispatchDetailDo);
        }

        // 是否需要统计
        if (isRealTime) {
            StrategyExecLogDo strategyExecLogDo = strategyExecLogRepository.selectById(eventPushBatchDo.getExecLogId());
            if (strategyExecLogDo != null) {
                if (strategyExecLogDo.getExecTime().toLocalDate().equals(LocalDate.now())) {
                    boolean shouldCount = redisUtils.lock(String.format(RedisKeyConstants.AI_REPORT_MESSAGE, aiCallBackMessageVO.getBatchNo(), aiCallBackMessageVO.getBatchNo()), 1, RedisUtils.DEFAULT_EXPIRE_DAYS * 2, TimeUnit.SECONDS);
                    if (shouldCount) {
                        StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(eventPushBatchDo.getMarketChannelId());
                        if (strategyMarketChannelDo != null) {
                            for (UserDispatchDetailDo userDispatchDetailDo : userDispatchDetailDoList) {
                                userDispatchDetailDo.setStrategyId(eventPushBatchDo.getStrategyId());
                                userDispatchDetailDo.setMarketChannel(eventPushBatchDo.getMarketChannel());
                                userDispatchDetailDo.setCreatedTime(eventPushBatchDo.getCreatedTime());
                                countDispatchBatchList.add(userDispatchDetailDo);
                                batchGroupIdMap.put(aiCallBackMessageVO.getBatchNo(), strategyMarketChannelDo.getStrategyGroupId());
                            }
                        }
                    }
                }
            }
        }
        boolean isUpdate = userDispatchDetailRepository.updateByBatchNumAndUserNo(userDispatchDetailDoList);
        if (isUpdate) {
            if (CollectionUtils.isEmpty(batchGroupIdMap) || CollectionUtils.isEmpty(countDispatchBatchList)) {
                return;
            }
            this.redisCount(countDispatchBatchList, batchGroupIdMap);
        }
    }
}
