/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.domain.crowd.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 0:初始化 1：执行中 2：成功 3：失败&锁超时，待重试 4：已达到最大重试次数，失败
 * <AUTHOR>
 * @version $ SliceExecLogEnum, v 0.1 2025/5/6 00:10 xu.fan Exp $
 */
@AllArgsConstructor
@Getter
public enum SliceExecLogEnum {

    INIT(0, "初始化"),

    EXECUTING(1, "执行中"),

    SUCCESS(2, "成功"),

    RETRY(3, "失败&锁超时，待重试"),

    FAILED(4, "已达到最大重试次数，失败");

    private final int code;

    private final String description;

    public static SliceExecLogEnum getInstance(Integer code) {
        for (SliceExecLogEnum sliceExecLogEnum : SliceExecLogEnum.values()) {
            if (Objects.equals(sliceExecLogEnum.getCode(), code)) {
                return sliceExecLogEnum;
            }
        }
        return null;
    }
}
