package com.xftech.cdp.domain.cache.impl;

import com.alibaba.fastjson.JSONArray;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.cache.CacheStrategyMarketChannelService;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import com.xftech.cdp.infra.utils.RedisKeyUtils;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 策略营销渠道关系表操作
 */
@Service
public class CacheStrategyMarketChannelServiceImpl implements CacheStrategyMarketChannelService {
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;

    /**
     * 根据策略id删除营销渠道记录
     *
     * @param strategyId 策略id
     */
    public void deleteByStrategyId(Long strategyId) {
        strategyMarketChannelRepository.deleteByStrategyId(strategyId);

        this.updateMarketCahennelCache(strategyId);

        List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyId(strategyId);
        strategyMarketChannelDoList.forEach(strategyMarketChannel -> {
            this.delOneByGroupKey(strategyMarketChannel.getStrategyGroupId(), strategyMarketChannel.getMarketChannel());
        });
    }

    /**
     * 根据策略id查询营销渠道记录
     *
     * @param strategyId 策略id
     * @return 该策略下的所有渠道列表
     */
    public List<StrategyMarketChannelDo> selectByStrategyId(Long strategyId) {
        String redisKey = RedisKeyUtils.genListByStrategyIdKey(strategyId);
        String data = redisUtils.get(redisKey);
        List<StrategyMarketChannelDo> strategyMarketChannelDoList = JSONArray.parseArray(data, StrategyMarketChannelDo.class);
        if (CollectionUtils.isEmpty(strategyMarketChannelDoList)) {
            strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyId(strategyId);
            redisUtils.set(redisKey, strategyMarketChannelDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyMarketChannelDoList;
    }

    /**
     * 根据策略分组id查询营销渠道记录
     *
     * @param strategyGroupId 策略分组id
     * @return 该策略某个分组下的所有营销渠道列表
     */
    public List<StrategyMarketChannelDo> selectByStrategyGroupId(Long strategyGroupId) {
        String redisKey = RedisKeyUtils.genListByStrategyGroupIdKey(strategyGroupId);
        String data = redisUtils.get(redisKey);
        List<StrategyMarketChannelDo> strategyMarketChannelDoList = JSONArray.parseArray(data, StrategyMarketChannelDo.class);
        if (CollectionUtils.isEmpty(strategyMarketChannelDoList)) {
            strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupId);
            redisUtils.set(redisKey, strategyMarketChannelDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyMarketChannelDoList;

    }

    /**
     * 插入一条营销渠道记录
     *
     * @param strategyMarketChannelDo 营销渠道对象
     */
    public void insert(StrategyMarketChannelDo strategyMarketChannelDo) {
        strategyMarketChannelRepository.insert(strategyMarketChannelDo);

        this.updateMarketCahennelCache(strategyMarketChannelDo.getStrategyId());

        this.updateMarketCahennelCacheByGroup(strategyMarketChannelDo.getStrategyGroupId(), strategyMarketChannelDo.getMarketChannel());
    }

    /**
     * 根据渠道id(主键)更新某一条记录
     *
     * @param strategyMarketChannelDo 营销渠道对象
     */
    public void updateById(StrategyMarketChannelDo strategyMarketChannelDo) {
        strategyMarketChannelRepository.updateById(strategyMarketChannelDo);

        this.updateMarketCahennelCache(strategyMarketChannelDo.getStrategyId());

        this.updateMarketCahennelCacheByGroup(strategyMarketChannelDo.getStrategyGroupId(), strategyMarketChannelDo.getMarketChannel());
    }

    /**
     * 根据id批量删除营销渠道记录
     *
     * @param channelIdList 渠道id列表
     */
    public void deleteByIdBatch(List<Long> channelIdList) {
        List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByIds(channelIdList);
        strategyMarketChannelDoList.forEach(strategyMarketChannel -> {
            this.delOneByGroupKey(strategyMarketChannel.getStrategyGroupId(), strategyMarketChannel.getMarketChannel());
        });

        StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(channelIdList.get(0));
        strategyMarketChannelRepository.deleteByIdBatch(channelIdList);

        this.updateMarketCahennelCache(strategyMarketChannelDo.getStrategyId());
    }

    public StrategyMarketChannelDo selectByStrategyGroupIdAndChannel(Long strategyGroupId, Integer marketChannel) {
        String redisKey = RedisKeyUtils.genOneByGroupKey(strategyGroupId, marketChannel);
        StrategyMarketChannelDo strategyMarketChannelDo = redisUtils.get(redisKey, StrategyMarketChannelDo.class);
        if (strategyMarketChannelDo == null) {
            strategyMarketChannelDo = strategyMarketChannelRepository.selectByStrategyGroupIdAndChannel(strategyGroupId, marketChannel);
            redisUtils.set(redisKey, strategyMarketChannelDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
        }
        return strategyMarketChannelDo;
    }

    @Override
    public StrategyMarketChannelDo cacheSelectById(Long channelId) {
        if (channelId == null || channelId == 0L){
            return null;
        }
        return strategyMarketChannelRepository.cacheSelectById(channelId);
    }

    private void updateMarketCahennelCache(Long strategyId) {
        String redisKey = RedisKeyUtils.genListByStrategyIdKey(strategyId);
        List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyGroupId(strategyId);
        redisUtils.set(redisKey, strategyMarketChannelDoList, RedisUtils.DEFAULT_EXPIRE_SECONDS);
    }

    private void updateMarketCahennelCacheByGroup(Long strategyGroupId, Integer marketChannel) {
        String redisKey = RedisKeyUtils.genOneByGroupKey(strategyGroupId, marketChannel);
        StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectByStrategyGroupIdAndChannel(strategyGroupId, marketChannel);
        redisUtils.set(redisKey, strategyMarketChannelDo, RedisUtils.DEFAULT_EXPIRE_SECONDS);
    }







    private void delOneByGroupKey(Long strategyGroupId, Integer marketChannel) {
        String redisKey = RedisKeyUtils.genOneByGroupKey(strategyGroupId, marketChannel);
        if (redisUtils.hasKey(redisKey)) {
            redisUtils.delete(redisKey);
        }
    }
}
