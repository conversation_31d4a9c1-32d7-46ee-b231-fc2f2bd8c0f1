package com.xftech.cdp.domain.event.model.annotation;

import com.xftech.cdp.domain.event.model.enums.DataParserEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 参数解析处理器注解
 * <AUTHOR>
 * @version $ Parser, v 0.1 2024/11/15 14:24 snail Exp $
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface Parser {
    DataParserEnum parser();
}
