package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Getter
@AllArgsConstructor
public enum StrategyFrequencyEnum {

    EVERY_DAY(0, "每日"),
    EVERY_WEEK(1, "每周"),
    EVERY_MONTH(2, "每月"),
    CYCLE_DAY(3, "每日循环周期"),


    ;

    private final int code;

    private final String description;

    public static StrategyFrequencyEnum getInstance(Integer type) {
        for (StrategyFrequencyEnum frequencyEnum : StrategyFrequencyEnum.values()) {
            if (Objects.equals(frequencyEnum.getCode(), type)) {
                return frequencyEnum;
            }
        }
        throw new StrategyException(String.format("发送频次异常：%s", type));
    }
}
