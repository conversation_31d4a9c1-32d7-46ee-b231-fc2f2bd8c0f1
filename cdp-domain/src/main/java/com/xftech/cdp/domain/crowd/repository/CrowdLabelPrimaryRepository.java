package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelPrimaryDo;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人群包圈选规则一级标签表操作
 *
 * <AUTHOR>
 * @since 2023/2/13
 */

@Component
public class CrowdLabelPrimaryRepository {

    /**
     * 根据人群包id查询圈选规则一级标签
     *
     * @param crowdId 人群包id
     * @return 圈选规则一级标签列表
     */
    public List<CrowdLabelPrimaryDo> selectListByCrowdId(Long crowdId) {
        return DBUtil.selectList("crowdLabelPrimary.selectListByCrowdId", crowdId);
    }

    /**
     * 根据人群包id删除圈选规则一级标签
     *
     * @param crowdId 人群包id
     */
    public void deleteByCrowdId(Long crowdId) {
        DBUtil.delete("crowdLabelPrimary.deleteByCrowdId", crowdId);
    }

    /**
     * 插入一条人群包圈选规则一级标签记录
     *
     * @param crowdLabelPrimaryDo 人群包圈选规则一级标签对象
     * @return 是否插入成功标识
     */
    public boolean insert(CrowdLabelPrimaryDo crowdLabelPrimaryDo) {
        int count = DBUtil.insert("crowdLabelPrimary.insertSelective", crowdLabelPrimaryDo);
        return count > 0;
    }

    /**
     * 根据主键id删除记录
     *
     * @param id 主键id
     * @return 是否删除成功标识
     */
    public boolean deleteById(Long id) {
        return DBUtil.delete("crowdLabelPrimary.deleteById", id) > 0;

    }
}
