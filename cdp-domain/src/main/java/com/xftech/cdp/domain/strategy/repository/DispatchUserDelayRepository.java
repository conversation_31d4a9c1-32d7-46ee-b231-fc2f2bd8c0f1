/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchUserDelayDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ DispatchUserDelayRepository, v 0.1 2024/1/3 17:18 yye.xu Exp $
 */

@Slf4j
@Repository
@AllArgsConstructor
public class DispatchUserDelayRepository {
    
    public void batchInsert(List<DispatchUserDelayDo> records) {
        try {
            DBUtil.insertBatchWithoutTx("dispatchUserDelayDoMapper.insertSelective", records);
        } catch (Exception ex) {
            log.error("batchInsert list={}", JsonUtil.toJson(records), ex);
        }
    }

    public List<DispatchUserDelayDo> selectTodoList(int startIndex, int pageSize, int status, int dateValue, Date at) {
        Map<String, Object> params = new HashMap<>();
        params.put("startIndex", startIndex);
        params.put("pageSize", pageSize);
        params.put("status", status);
        params.put("dateValue", dateValue);
        params.put("at", at);
        return DBUtil.selectList("dispatchUserDelayDoMapper.selectTodoList", params);
    }

    public List<DispatchUserDelayDo> selectNotTelTodoList(int startIndex, int pageSize, int status, int dateValue, Date at, List<Integer> telTypes) {
        Map<String, Object> params = new HashMap<>();
        params.put("startIndex", startIndex);
        params.put("pageSize", pageSize);
        params.put("status", status);
        params.put("dateValue", dateValue);
        params.put("at", at);
        params.put("telTypes", telTypes);
        return DBUtil.selectList("dispatchUserDelayDoMapper.selectNotTelTodoList", params);
    }

    public List<DispatchUserDelayDo> selectTodoListByType(int startIndex, int pageSize, int status, int dateValue, Date at, Integer type) {
        Map<String, Object> params = new HashMap<>();
        params.put("startIndex", startIndex);
        params.put("pageSize", pageSize);
        params.put("status", status);
        params.put("dateValue", dateValue);
        params.put("at", at);
        params.put("type", type);
        return DBUtil.selectList("dispatchUserDelayDoMapper.selectTodoListByType", params);
    }

    public List<DispatchUserDelayDo> selectTelTodoList(int startIndex, int pageSize, int status, int dateValue, Date at, List<Integer> telTypes) {
        Map<String, Object> params = new HashMap<>();
        params.put("startIndex", startIndex);
        params.put("pageSize", pageSize);
        params.put("status", status);
        params.put("dateValue", dateValue);
        params.put("at", at);
        params.put("telTypes", telTypes);
        return DBUtil.selectList("dispatchUserDelayDoMapper.selectTelTodoList", params);
    }

    public void updateFinishedStatus(long id, int status){
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("status", status);

        DBUtil.update("dispatchUserDelayDoMapper.updateFinishedStatus", params);
    }
}