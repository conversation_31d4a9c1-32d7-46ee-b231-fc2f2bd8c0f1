package com.xftech.cdp.domain.crowd.service.impl;

import com.xftech.cdp.domain.crowd.repository.CrowdLabelPrimaryRepository;
import com.xftech.cdp.domain.crowd.service.CrowdLabelPrimaryService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelPrimaryDo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Service
public class CrowdLabelPrimaryServiceImpl implements CrowdLabelPrimaryService {

    @Autowired
    public CrowdLabelPrimaryRepository crowdLabelPrimaryRepository;

    @Override
    public boolean insert(CrowdLabelPrimaryDo crowdLabelPrimaryDo) {
        return this.crowdLabelPrimaryRepository.insert(crowdLabelPrimaryDo);
    }

    @Override
    public boolean delete(CrowdLabelPrimaryDo crowdLabelPrimaryDo) {
        return this.crowdLabelPrimaryRepository.deleteById(crowdLabelPrimaryDo.getId());
    }
}
