package com.xftech.cdp.domain.risk.service.impl;

import com.xftech.cdp.domain.risk.service.RefreshRangeService;
import com.xftech.cdp.domain.risk.service.RiskService;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.BaseAdsRequest;
import com.xftech.cdp.infra.client.ads.model.req.DTRiskUserListReq;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.ads.model.resp.DTRiskUserListResp;
import com.xftech.cdp.infra.config.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/5/24
 */
@Service
@Slf4j
public class RiskServiceImpl implements RiskService {

    @Resource
    private Config config;

    @Resource
    private AdsClient adsClient;

    @Resource
    private RefreshRangeService refreshRangeService;

    @Override
    public void RefreshRiskScore(LocalDate refreshDate) {

        Integer pageNum = 1;
        Integer pageSize = config.getRefreshRiskPageSize();
        String dayNumber = LocalDate.now().format(DateTimeFormatter.ofPattern("d"));
        String reqDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        DTRiskUserListReq dtRiskUserListReq = new DTRiskUserListReq();

        if (refreshDate != null) {
            dayNumber = refreshDate.format(DateTimeFormatter.ofPattern("d"));
            reqDate = refreshDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        log.info("模型分跑批日期:{}, 当前切片:{}", reqDate, dayNumber);

        if (Integer.parseInt(dayNumber) > 28) {
            return;
        }

        dtRiskUserListReq.setReqDate(reqDate);
        dtRiskUserListReq.setSection(dayNumber);
        dtRiskUserListReq.setPageNum(pageNum);
        dtRiskUserListReq.setPageSize(pageSize);

        // 首次请求获取当天数据量
        BaseAdsResponse<DTRiskUserListResp> shakeReps = adsClient.dtRiskUserList(new BaseAdsRequest<>(dtRiskUserListReq));
        if (shakeReps.getPayload().getPageNum() == null || shakeReps.getPayload().getPageNum() == 0) {
            return;
        }

        // 获取总页数，计算分配线程
        double totalPage = Math.ceil(shakeReps.getPayload().getTotalNum().doubleValue() / pageSize);
        Map<Integer, List<Integer>> ranges = distribute(pageNum, (int) totalPage, config.getRefreshRiskThreadNum());

        // 调用异步刷新
        ranges.forEach((k, numberRange) -> {
            refreshRangeService.refreshRange(dtRiskUserListReq, numberRange);
        });
    }

    /**
     * 计算分片范围
     * [[5, 10], [1, 6], [2, 7], [3, 8], [4, 9]]
     * @param min 最小值
     * @param max 最大值
     * @param pieceNum 分块数量
     */
    public Map<Integer, List<Integer>> distribute(Integer min, Integer max, Integer pieceNum) {
        Map<Integer, List<Integer>> result = new HashMap<>();
        for (; min <= max; min++) {
            Integer mod = min % pieceNum;
            result.computeIfAbsent(mod, k -> new ArrayList<>());
            result.get(mod).add(min);
        }
        return result;
    }
}
