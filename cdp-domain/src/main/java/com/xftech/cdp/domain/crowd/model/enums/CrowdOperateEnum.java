package com.xftech.cdp.domain.crowd.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/2/24
 */
@AllArgsConstructor
@Getter
public enum CrowdOperateEnum {
    PAUSE(0, "暂停"),
    ENABLE(1, "开始"),
    REFRESH(2, "刷新"),
    DELETE(3,"删除"),
    RECOVER(4,"恢复");
    private final int code;
    private final String description;

    public static CrowdOperateEnum getInstance(Integer code) {
        for (CrowdOperateEnum value : CrowdOperateEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
