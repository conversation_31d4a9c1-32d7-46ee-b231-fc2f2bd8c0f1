package com.xftech.cdp.domain.crowd.model.label.crowd;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Random extends CrowdLabelOption {
    private int digits;
    private int positionStart;
    private int positionEnd;
    private String crowdLabelOption;

    @Override
    public StringBuilder condition(String column, String configOptionReflect) {
        return getLabelOption().condition(" cast(SUBSTR( " + column + ", " + positionStart + ", " + (positionEnd - positionStart + 1) + ") as int)", configOptionReflect);
    }

    public CrowdLabelOption getLabelOption() {
        return JSON.parseObject(crowdLabelOption, LabelEnum.LabelOptionDigitsEnum.ofCode(digits).getLabelOptionTypeEnum().getClazz());
    }


    public void verify() {

        if (positionEnd - positionStart > 1 || positionStart > positionEnd) {
            throw new CrowdException("数据格式有误：选择2位时，仅支持选择相邻的两位，填写所需位数");
        }

        try {
            getLabelOption();
        } catch (Exception e) {
            throw new CrowdException("随机数数据格式有误");
        }

    }
}
