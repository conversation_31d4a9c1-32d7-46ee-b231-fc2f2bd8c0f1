package com.xftech.cdp.domain.auth.service.impl;

import com.xftech.cdp.api.dto.req.auth.GetTokenReq;
import com.xftech.cdp.api.dto.resp.auth.GetTokenResp;
import com.xftech.cdp.domain.auth.exception.AuthException;
import com.xftech.cdp.domain.auth.service.AuthService;
import com.xftech.cdp.infra.client.sso.SsoClient;
import com.xftech.cdp.infra.client.sso.model.GetTokenRequester;
import com.xftech.cdp.infra.client.sso.model.GetTokenResponse;
import com.xftech.cdp.infra.client.sso.model.LoginDetail;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/2/9
 */
@Service
public class AuthServiceImpl implements AuthService {

    private final SsoClient ssoClient;

    public AuthServiceImpl(SsoClient ssoClient) {
        this.ssoClient = ssoClient;
    }


    @Override
    public GetTokenResp getToken(GetTokenReq req) {
        GetTokenResp resp = new GetTokenResp();

        GetTokenRequester requester = new GetTokenRequester();
        requester.setMobile(req.getMobile());
        requester.setCaptcha(req.getCaptcha());
        requester.setType(req.getType());
        requester.setCode(req.getCode());
        requester.setToken(req.getToken());

        GetTokenResponse tokenResp = ssoClient.getToken(requester);
        if (!tokenResp.isSuccess()) {
            throw new AuthException(tokenResp.getMessage());
        }

        LoginDetail data = tokenResp.getData();
        if (data != null) {
            resp.setUser(data.getUser());
            //            resp.setApi( data.getApi() );
            resp.setMenu(data.getMenu());
            resp.setRoleTag(data.getRoleTag());
            resp.setRoleId(data.getRoleId());
            resp.setRoleName(data.getRoleName());
            resp.setToken(data.getToken());
            resp.setApiAll(data.getApiAll());
        }

        return resp;
    }
}
