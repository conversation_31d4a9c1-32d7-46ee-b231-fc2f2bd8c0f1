package com.xftech.cdp.domain.crowd.service;

import com.xftech.cdp.domain.crowd.model.enums.UtmTypeEnum;
import com.xftech.cdp.domain.stat.entity.UtmResult;
import com.xftech.cdp.infra.client.loanmarket.enums.UtmSourceTypeEnum;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 渠道过滤service
 */
public interface UtmSourceService {

    UtmResult filterSingleUtmSource(String singleUtmSource, UtmSourceTypeEnum utmSourceTypeEnum, LocalDateTime registerTime, String utmType);

    List<UtmResult> filterBatchUtmSource(String batchUtmSource, UtmSourceTypeEnum utmSourceTypeEnum, LocalDateTime registerTime, String utmType);

}
