package com.xftech.cdp.domain.event.parse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息体解析策略类，用于根据不同的处理类型匹配对应的解析处理器
 * <AUTHOR>
 * @version $ DataParserStrategy, v 0.1 2024/11/15 14:41 snail Exp $
 */
@Slf4j
@Component
public class DataParserStrategy {
    private final Map<String,DataParser> PARSER_MAP = new HashMap<>();

    /**
     * 添加一个消息体解析的处理器
     * @param parserType 处理器解析类型
     * @param parser 处理器解析处理类
     */
    public void register(String parserType, DataParser parser){
        if(!PARSER_MAP.containsKey(parserType)){
            PARSER_MAP.put(parserType,parser);
        }
    }

    /**
     * 跟进处理器类型获取对应的处理类
     * @param parserType 处理器类型
     * @return 处理器
     */
    public DataParser getParser(String parserType){
        return PARSER_MAP.get(parserType);
    }
}
