/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.xftech.cdp.domain.dispatch.BatchDispatchService;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchForPushService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ StrategyDispatchForPushServiceImpl, v 0.1 2024/1/19 16:28 lingang.han Exp $
 */

@Slf4j
@RefreshScope
@Service(StrategyDispatchConstants.PUSH_SERVICE)
public class StrategyDispatchForPushServiceImpl extends AbstractStrategyDispatchService implements StrategyDispatchService, StrategyDispatchForPushService {
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private BatchDispatchService batchDispatchService;

    @Override
    protected Integer setBatchSize() {
        return strategyConfig.getPushBatchSize();
    }

    /**
     * 下发
     *
     * @param strategyContext 策略执行初始化内容
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           按分组配置过滤出来的用户集合
     * @return ImmutablePair Left-下发成功数 Right-下发批次记录
     */
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<T> params) {
        innerApp = StringUtils.isBlank(innerApp) ? app : innerApp;
        return this.sendPush(strategyContext, app, innerApp, batch,Convert.convert(new TypeReference<List<PushUserData>>() {}, params));
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext strategyContext, String tableNameNo, String app, String innerApp, List<CrowdDetailDo> batch, List<T> params) {
        innerApp = StringUtils.isBlank(innerApp) ? app : innerApp;
        return this.sendPush(strategyContext, app, innerApp, batch,Convert.convert(new TypeReference<List<PushUserData>>() {}, params));
    }


    /**
     * 下发push
     *
     * @param strategyContext 策略上下文
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           手机号
     * @return 下发数量
     */
    public ImmutablePair<Integer, CrowdPushBatchDo> sendPush(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<PushUserData> params) {
        if (CollectionUtils.isEmpty(params)) {
            return batchDispatchService.sendPush(convertToDispatchDto(strategyContext), app, innerApp, batch);
        }
        return batchDispatchService.sendPushWithParam(convertToDispatchDto(strategyContext), app, innerApp, batch, params);
    }

}