/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.distribute.offline.StrategyTaskDistributeHandler;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.service.DispatchUserDelayService;
import com.xftech.cdp.domain.strategy.service.dispatch.DispatchOfflineEngineService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchForOfflineEngineService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.roaringbitmap.longlong.Roaring64Bitmap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 *
 * <AUTHOR>
 * @version $ StrategyDispatchForOfflineEngineServiceImpl, v 0.1 2024/1/3 21:50 yye.xu Exp $
 */

@Slf4j
@Service(StrategyDispatchConstants.OFFLINE_ENGINE_SERVICE)
public class StrategyDispatchForOfflineEngineServiceImpl extends AbstractStrategyDispatchService implements StrategyDispatchService, StrategyDispatchForOfflineEngineService {
    @Autowired
    private DispatchUserDelayService dispatchUserDelayService;
    @Autowired
    private DispatchOfflineEngineService dispatchOfflineEngineService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyTaskDistributeHandler strategyTaskDistributeHandler;

    @Override
    protected Integer setBatchSize() {
        return getStrategyConfig().getOfflieEngineBatchSize();
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app,
                                                                           String innerApp, List<CrowdDetailDo> batch, List<T> templateParam) {
        // 处理推送逻辑

        AtomicInteger count = new AtomicInteger();
        if (CollectionUtils.isEmpty(batch)) {
            return ImmutablePair.of(count.get(), null);
        }
        Long strategyExecLogId = 0L;
        if (strategyContext.getStrategyExecLogDo() != null) {
            strategyExecLogId = strategyContext.getStrategyExecLogDo().getId();
        }
        List<Future> tasks = new ArrayList<>();
        List<DispatchUserDelayDo> dispatchUserDelayDos = new ArrayList<>();
        for (CrowdDetailDo crowdDetailDo : batch) {
            Future<List<DispatchUserDelayDo>> listFuture = dispatchOfflineEngineService.pushEngine(strategyContext.getStrategyDo().getId(),
                    strategyContext.getStrategyDo().getSendRuler(),
                    strategyExecLogId, strategyContext.getStrategyGroupDo(),
                    strategyContext.getEngineCode(), crowdDetailDo);
            tasks.add(listFuture);
        }
        tasks.forEach(t -> {
            try {
                List<DispatchUserDelayDo> dispatchs = (List<DispatchUserDelayDo>) t.get();
                if (dispatchs.size() > 0) {
                    count.getAndIncrement();
                    dispatchUserDelayDos.addAll(dispatchs);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        dispatchUserDelayService.batchInsert(dispatchUserDelayDos);
        return ImmutablePair.of(count.get(), null);
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext context, String tableNameNo,
                                                                                String app, String innerApp, List<CrowdDetailDo> detailList, List<T> templateParam) {
        return null;
    }

    @Override
    public void execute(Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
        StrategyContext strategyContext = initContext(marketChannelId, dispatchTaskDo);

        if(WhitelistSwitchUtil.commonGraySwitchByApollo(dispatchTaskDo.getBizId(), "offEngineStrategyDispatchSwitch")) {
            log.info("offEngineStrategyDispatchSwitch hit, 分布式执行策略 strategyId:{}", dispatchTaskDo.getBizId());
            // 校验通过灰度走分布式执行
            strategyTaskDistributeHandler.createStrategyDistributeSliceTasks(strategyContext.getStrategyDo());
        } else {
            try {
                beginExecute(strategyContext); // 写入执行记录strategyExecLog、strategySnapshot
                preHandler(strategyContext); // 人群包校验
                coreLogicExecute(strategyContext);
                successExecute(strategyContext);
                getDispatchTaskService().updateTaskFinish(dispatchTaskDo, "SUCCEED");
            } catch (Exception e) {
                getDispatchTaskService().updateDispatchTaskFailedRetry(dispatchTaskDo, e.getMessage());

                StrategyDo strategyDo = strategyContext.getStrategyDo();
                StrategyMarketChannelDo marketChannelDo = strategyContext.getStrategyMarketChannelDo();
                StrategyMarketChannelEnum channelEnum = StrategyMarketChannelEnum.getInstance(marketChannelDo.getMarketChannel());
                failedExecute(strategyContext);
                List<String> atMobileList = new ArrayList<>(getDingTalkConfig().atMobileList());
                atMobileList.add(strategyDo.getUpdatedOpMobile());
                strategyDo.alarmDingTalk(getDingTalkConfig().getAlarmUrl(), atMobileList, e);
                log.warn("策略执行异常, 策略ID:{}, 渠道类型:{}, 渠道ID:{}", strategyDo.getId(), channelEnum.getDescription(), marketChannelDo.getId(), e);
            }
        }
    }

    @Override
    protected StrategyContext initContext(@NonNull Long channelId, DispatchTaskDo dispatchTaskDo) {
        LocalDateTime now = LocalDateTime.now();
        Long strategyId = Long.parseLong(dispatchTaskDo.getBizId());

        StrategyDo strategyDo = getStrategyRepository().selectById(strategyId);
        if (strategyDo == null) {
            getDispatchTaskService().updateTaskFailed(dispatchTaskDo, "不存在该策略");
            throw new StrategyException(String.format("不存在该策略,策略id:%s", strategyId));
        }
        if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
            getDispatchTaskService().updateTaskFailed(dispatchTaskDo, "策略状态暂停或者结束不营销");
            throw new StrategyException(String.format("策略状态暂停或者结束不营销,策略id:%s", strategyId));
        }
        if (now.isBefore(strategyDo.getValidityBegin())) {
            getDispatchTaskService().updateDispatchTaskReset(dispatchTaskDo, "策略未到执行时间");
            throw new StrategyException(String.format("该策略未到执行时间，策略id：%s", strategyDo.getId()));
        }

        StrategyRulerEnum rulerEnum = StrategyRulerEnum.getInstance(strategyDo.getSendRuler());
        if ((StrategyRulerEnum.getCycleCodes().contains(rulerEnum.getCode()) &&
                now.isAfter(strategyDo.getValidityEnd())) || (rulerEnum == StrategyRulerEnum.ONCE &&
                now.isAfter(strategyDo.getValidityEnd().plusDays(1)))) {
            getDispatchTaskService().updateTaskFailed(dispatchTaskDo, "该策略已结束");
            throw new StrategyException(String.format("该策略已结束，策略id：%s", strategyDo.getId()));
        }
        if (StringUtils.isEmpty(strategyDo.getEngineCode())) {
            getDispatchTaskService().updateTaskFailed(dispatchTaskDo, "该策略不存在引擎code");
            throw new StrategyException(String.format("该策略不存在引擎code，策略id：%s", strategyDo.getId()));
        }
        List<StrategyExecLogDo> strategyExecLogDos = getExecLogSuccessRecord(strategyDo, 0L);
        if (!CollectionUtils.isEmpty(strategyExecLogDos)) {
            getDispatchTaskService().updateTaskFinish(dispatchTaskDo, "该策略当天已经执行过");
            throw new StrategyException(String.format("该策略触达渠道，当天已经执行过，策略ID：%s", strategyDo.getId()));
        }
        strategyExecLogDos = getExecLogExecutingRecord(strategyDo, 0L);
        if (!CollectionUtils.isEmpty(strategyExecLogDos)) {
            getDispatchTaskService().updateTaskFailed(dispatchTaskDo, "存在执行中的记录请勿重复执行");
            throw new StrategyException(String.format("该策略触达渠道，存在执行中的记录，请勿重复执行，策略ID：%s", strategyDo.getId()));
        }
        List<StrategyCrowdPackDo> strategyCrowdPackDos = getStrategyCrowdPackRepository().selectByStrategyId(strategyDo.getId());
        if (CollectionUtils.isEmpty(strategyCrowdPackDos)) {
            getDispatchTaskService().updateTaskFailed(dispatchTaskDo, "该策略不存在人群包");
            throw new StrategyException("该策略不存在人群包！");
        }
        Map<Long, CrowdContext> crowdContent = getCrowdContent(ListUtils.distinctMap(strategyCrowdPackDos, StrategyCrowdPackDo::getCrowdPackId));
        StrategyContext.CycleStrategy cycleStrategy = loadCycleStrategy(dispatchTaskDo, strategyDo);

        StrategyMarketChannelDo strategyMarketChannelDo = new StrategyMarketChannelDo();
        strategyMarketChannelDo.setId(0L);
        strategyMarketChannelDo.setStrategyId(strategyId);
        strategyMarketChannelDo.setStrategyGroupId(0L);
        strategyMarketChannelDo.setMarketChannel(-2);

        StrategyGroupDo strategyGroupDo = new StrategyGroupDo();
        strategyGroupDo.setStrategyId(strategyId);
        strategyGroupDo.setId(0L);

        StrategyContext strategyContext = StrategyContext.initContext(
                strategyDo,
                strategyGroupDo,
                strategyMarketChannelDo,
                null,
                ListUtils.distinctMap(strategyCrowdPackDos, StrategyCrowdPackDo::getCrowdPackId),
                cycleStrategy,
                crowdContent,
                null
        );
        strategyContext.setEngineCode(strategyDo.getEngineCode());
        return strategyContext;
    }

    @Override
    protected void coreLogicExecute(StrategyContext strategyContext) {
        StrategyDo strategyDo = strategyContext.getStrategyDo();
        StrategyAbTestEnum abTestEnum = StrategyAbTestEnum.getInstance(strategyDo.getAbTest());

        Map<Long, BiPredicate<String, Integer>> matchFunctions = new HashMap<>();
        List<StrategyGroupDo> strategyGroupDoList = getStrategyGroupRepository().selectListByStrategyId(strategyDo.getId());
        if (abTestEnum == StrategyAbTestEnum.YES) {
            // 获取分组匹配规则
            for (StrategyGroupDo group : strategyGroupDoList) {
                matchFunctions.put(group.getId(), group.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType())));
            }
        }
        // 分页查询+按分组规则过滤+下发
        batchDispatch(strategyContext, Triple.of(strategyDo.getBizKey(), matchFunctions, strategyGroupDoList));
    }

    protected void batchDispatch(StrategyContext context, Triple<String, Map<Long, BiPredicate<String, Integer>>, List<StrategyGroupDo>> funPair) {
        long start = Instant.now().toEpochMilli();
        Roaring64Bitmap container = new Roaring64Bitmap();
        AtomicInteger groupCount = new AtomicInteger(0);
        AtomicInteger totalCount = new AtomicInteger(0);
        AtomicInteger sendCount = new AtomicInteger(0);
        try {
            Long strategyId = context.getStrategyDo().getId();
            // crowid, execlogid, tables
            List<Triple<Long, Long, List<String>>> crowdList = getCrowdPackService().getExecLogIdAndTablePairList(context.getCrowdIds(), context.getCrowdContent());
            for (Triple<Long, Long, List<String>> tableTriple : crowdList) {
                for (String tableName : tableTriple.getRight()) {
                    PageUtil.PaginationParam paginationParam = new PageUtil.PaginationParam(setBatchSize());

                    log.info("tableName:{}, tableTriple.getMiddle():{},paginationParam.getPageSize():{},context.getCrowdContent().get(tableTriple.getLeft()):{}", tableName,
                            tableTriple.getMiddle(), paginationParam.getPageSize(), context.getCrowdContent().get(tableTriple.getLeft()));

                    PageUtil.invokePaginationNew(PageUtil.PaginationTypeEnum.ID_PAGING,
                            paginationParam,
                            () -> getCrowdPackService().getCrowdDetailList(tableTriple.getLeft(),tableName, tableTriple.getMiddle(),
                                    paginationParam.getId(), paginationParam.getPageSize(), context.getCrowdContent().get(tableTriple.getLeft())),
                            list -> list.stream().collect(groupingBy(CrowdDetailDo::getApp)).forEach((app, detailList) -> {
                                filterAndDispatch(context, funPair, container, groupCount, totalCount, sendCount, app, detailList);
                            })
                    );
                }
            }
        } finally {
            context.setCompleteDispatch(sendCount.get() > 0);
            context.setCountTriple(ImmutableTriple.of(groupCount.get(), totalCount.get(), sendCount.get()));
            context.getStrategyExecLogDo().setGroupCount(groupCount.get());
            context.getStrategyExecLogDo().setExecCount(totalCount.get());
            context.getStrategyExecLogDo().setSendCount(sendCount.get());
            log.info("分组匹配人数：{}，发送成功人数：{}，耗时：{}ms", totalCount.get(), sendCount.get(), Instant.now().toEpochMilli() - start);
        }
    }

    protected void filterAndDispatch(StrategyContext context, Triple<String, Map<Long, BiPredicate<String, Integer>>, List<StrategyGroupDo>> funPair,
                                     Roaring64Bitmap container, AtomicInteger groupCount, AtomicInteger totalCount, AtomicInteger sendCount, String app, List<CrowdDetailDo> detailList) {
        // 250326 新增离线引擎过滤开关，修复异常情况不能停止策略执行的问题
        Long strategyId = context.getStrategyDo().getId();
        if (WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "offlineEngineFilterSwitch")) {
            log.warn("离线策略引擎推送 --> 过滤命中白名单直接丢弃, strategyId={}", strategyId);
            return;
        }

        int size = detailList.size();
        detailList = context.cycleFilter(detailList, getStrategyService(), getRandomNumClient());
        if (CollectionUtils.isEmpty(detailList)) {
            log.info("周期策略筛选, 本批次无人满足条件, 策略id:{}, 渠道id:{}", context.getStrategyDo().getId(),
                    context.getStrategyMarketChannelDo().getId());
            return;
        }
        log.info("周期策略筛选, 策略id:{}, 渠道id:{}, 过滤前大小:{}, 本批次剩余人数:{}", context.getStrategyDo().getId(),
                context.getStrategyMarketChannelDo().getId(), size, detailList.size());
        // 获取随机数
        detailList = getRandomNumService().randomNum(context, detailList);
        //1.1 麻雀-fxk老客转xyf01下发 ,AB分组之后，触达下发前，流控判断前
        detailList = getAbstractAdsStrategyLabelService().convertCrowdList(detailList, context.getStrategyDo().getUserConvert());
        // 2.根据用户ID全局去重
        detailList = detailList.stream().filter(item -> !container.contains(item.getUserId())).collect(Collectors.toList());
        // 3.去重后的用户ID加入到容器中，参与下次去重
        detailList.stream().map(CrowdDetailDo::getUserId).forEachOrdered(container::add);
        // 4.根据手机号去重
        detailList = detailList.stream().filter(ListUtils.distinctByKey(CrowdDetailDo::getMobile)).collect(Collectors.toList());
        // 进入营销分组
        final StrategyGroupDo groupDo = context.getStrategyGroupDo();
        for (StrategyGroupDo strategyGroupDo : funPair.getRight()) {
            StrategyCreateReq.GroupConfig groupConfig = JsonUtil.parse(strategyGroupDo.getGroupConfig(),
                    StrategyCreateReq.GroupConfig.class);
            if (groupConfig == null || !Objects.equals(1, groupConfig.getSelected())) { // 离线引擎策略，不营销组
                String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(detailList) && detailList.get(0) != null) {
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genOffEngineNotIntoEngineNum(curDate, context.getStrategyDo().getId()), detailList.get(0).getUserId());
                }
                log.info("离线策略引擎推送 --> 分组为不营销, 不进入引擎, 直接丢弃, strategyId = {}, groupId = {}", strategyGroupDo.getStrategyId(), strategyGroupDo.getId());
                continue;
            }
            context.setStrategyGroupDo(strategyGroupDo);

            List<CrowdDetailDo> newList = new ArrayList<>(detailList);
            newList = getStrategyGroupService().matchGroupRule(funPair.getLeft(), funPair.getMiddle().get(strategyGroupDo.getId()), newList);
            // 分组人数统计, 营销的进行统计
            groupCount(context, groupCount, totalCount, sendCount, newList.size());
            // 5.标签和模板参数查询
            AtomicReference<List<CrowdDetailDo>> availableDetails = new AtomicReference<>();
            availableDetails.set(new ArrayList<>());
            Pair<List<CrowdDetailDo>, List<Object>> pair = labelAndTempParamQuery(context, app, newList, availableDetails);
            //记录排除标签排除人数
            redisUtils.increment(RedisKeyUtils.genOffEngineLabelExcludeSum(LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd"), context.getStrategyDo().getId()), (long) (newList.size() - pair.getLeft().size()), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            // 7.执行下发
            dispatchHandler(context, groupCount, totalCount, sendCount, app, pair.getLeft(), pair.getRight());
        }
        context.setStrategyGroupDo(groupDo);
    }
}