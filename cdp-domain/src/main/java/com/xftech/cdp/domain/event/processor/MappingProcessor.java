package com.xftech.cdp.domain.event.processor;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.config.MappingConfig;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import com.xftech.cdp.domain.event.model.enums.DataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 关系映射数据处理器
 * <AUTHOR>
 * @version $ MappingProcessor, v 0.1 2024/11/19 14:43 snail Exp $
 */
@Slf4j
@Component
@ProcessorRule(processor = DataProcessEnum.MAPPING)
public class MappingProcessor extends AbstractDataProcessor {
    /** 映射配置之间的连接符，如1_2#2_5 */
    private static final String SPLIT = "#";
    /** 映射值之间的连接符，如1_2#2_5 */
    private static final String JOIN = "_";

    @Override
    public Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String, Object> values) {
        MappingConfig mappingConfig = (MappingConfig) fieldConfig;
        Object originVal = values.get(detail.getTargetField());
        if(Objects.isNull(originVal)){
            return null;
        }

        DataTypeEnum dataType = DataTypeEnum.getDataType(mappingConfig.getDataType());
        if(Objects.isNull(dataType)){
            return null;
        }

        Map<String,String> mapping = format(mappingConfig.getMapping());
        String convertedVal = mapping.get(originVal.toString());
        Object resultVal = Convert.convert(dataType.getClazz(),convertedVal);
        return new Field(detail.getTargetField(),resultVal);
    }

    /**
     * 将配置转为map映射,格式如下：1_2#4_5
     * @param info 配置的映射信息
     * @return 转换后的映射关系信息
     */
    private Map<String,String> format(String info){
        String[] mappingArray = info.split(SPLIT);

        Map<String,String> result = new HashMap<>(mappingArray.length);
        for (String mapping : mappingArray){
            String[] keyAndValue = mapping.split(JOIN);
            result.put(keyAndValue[0],keyAndValue[1]);
        }

        return result;
    }
}
