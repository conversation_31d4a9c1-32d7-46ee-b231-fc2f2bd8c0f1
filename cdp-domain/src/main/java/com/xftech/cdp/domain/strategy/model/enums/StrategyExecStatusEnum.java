package com.xftech.cdp.domain.strategy.model.enums;

import com.xftech.cdp.domain.crowd.model.enums.CrowdExecResultEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/2/23
 */
@AllArgsConstructor
@Getter
public enum StrategyExecStatusEnum {

    EXECUTING(0, "执行中",2),

    SUCCESS(1, "成功",3),

    FAIL(2, "失败",1),

    FINISHED(99, "已结束",4);

    private final int code;

    private final String description;

    private final int sort;

    public static StrategyExecStatusEnum getInstance(Integer code) {
        for (StrategyExecStatusEnum strategyExecStatusEnum : StrategyExecStatusEnum.values()) {
            if (Objects.equals(strategyExecStatusEnum.getCode(), code)) {
                return strategyExecStatusEnum;
            }
        }
        return null;
    }

    public static int getEnumSort(Integer code) {
        for (StrategyExecStatusEnum strategyExecStatusEnum : StrategyExecStatusEnum.values()) {
            if (Objects.equals(strategyExecStatusEnum.getCode(), code)) {
                return strategyExecStatusEnum.getSort();
            }
        }
        return 1;
    }
}
