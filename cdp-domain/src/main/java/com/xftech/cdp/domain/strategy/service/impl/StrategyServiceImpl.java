package com.xftech.cdp.domain.strategy.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.ListUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.aitel.AITelBizSourceRequest;
import com.xftech.cdp.api.dto.aitel.AiParam;
import com.xftech.cdp.api.dto.aitel.BizSourceInfo;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.req.sms.SmsGroupReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateCheckReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.api.dto.strategy.StrategyDispatchSmsRetryXxlJobParam;
import com.xftech.cdp.api.dto.strategy.StrategyXxlJobParam;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.domain.cache.*;
import com.xftech.cdp.domain.crowd.exception.CrowdException;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdExecLogRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.ChangeService;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlRuleStatusEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlTypeEnum;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.domain.stat.entity.*;
import com.xftech.cdp.domain.stat.repository.*;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.factory.*;
import com.xftech.cdp.domain.strategy.model.dto.SendFrequencyConfig;
import com.xftech.cdp.domain.strategy.model.dto.StrategyCycleDayConfig;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.repository.*;
import com.xftech.cdp.domain.strategy.service.*;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventCatchService;
import com.xftech.cdp.feign.GoodsFeignClient;
import com.xftech.cdp.feign.PushFeignClient;
import com.xftech.cdp.feign.common.BizResponse;
import com.xftech.cdp.feign.model.GoodsList;
import com.xftech.cdp.feign.model.PushTemplateList;
import com.xftech.cdp.feign.model.aitel.BizSourceResponse;
import com.xftech.cdp.feign.model.aitel.BizSourceVendorStrategyVO;
import com.xftech.cdp.feign.model.requset.GoodsListRequest;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.TemplateListRequest;
import com.xftech.cdp.feign.model.response.GoodsResponse;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sms.model.SmsGroupArgs;
import com.xftech.cdp.infra.client.sms.model.SmsGroupRequester;
import com.xftech.cdp.infra.client.sms.model.resp.SmsGroup;
import com.xftech.cdp.infra.client.sms.model.resp.SmsGroupResp;
import com.xftech.cdp.infra.client.telemarketing.TelemarketingClient;
import com.xftech.cdp.infra.client.telemarketing.model.TeleNameTypeArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TelePolicyConfigListRequest;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameConfigResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameTypeResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TelePolicyConfigListResp;
import com.xftech.cdp.infra.config.*;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.utils.*;
import com.xftech.xxljob.XxlJobAdminClient;
import com.xftech.xxljob.exception.InfraException;
import com.xftech.xxljob.model.XxlJobDto;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cronutils.model.Cron;
import com.google.common.collect.Maps;
import com.google.gson.JsonObject;
import io.swagger.util.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import static com.xftech.cdp.infra.config.ApolloKeyConstants.*;

/**
 * <AUTHOR>
 * @since 2023/2/9 01:30
 */
@Slf4j
@Service
public class StrategyServiceImpl implements StrategyService {

    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private SmsClient smsClient;
    @Autowired
    private StrategyEventCatchService strategyEventCatchService;
    @Autowired
    private XxlJobAdminClient xxlJobAdminClient;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private CacheStrategyMarketChannelService cacheStrategyMarketChannelService;
    @Autowired
    private StrategyCrowdPackRepository strategyCrowdPackRepository;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private MonitorListFactory monitorListFactory;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    StrategyCommonService strategyCommonService;
    @Autowired
    StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private CacheStrategyGroupService cacheStrategyGroupService;
    @Autowired
    private TemplateParamService templateParamService;
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private FlowCtrlRepository flowCtrlRepository;
    @Autowired
    private CacheStrategyMarketEventConditionService cacheStrategyMarketEventConditionService;
    @Autowired
    private StrategyMarketEventConditionRepository strategyMarketEventConditionRepository;
    @Autowired
    private StatRealtimeStrategyFlowDataRepository statRealtimeStrategyFlowDataRepository;
    @Autowired
    private StatRealtimeStrategyGroupDataRepository statRealtimeStrategyGroupDataRepository;
    @Autowired
    private CacheFlowCtrlSerivce cacheFlowCtrlSerivce;
    @Autowired
    private StatOfflineStrategyFlowDataRepository statOfflineStrategyFlowDataRepository;
    @Autowired
    private StatOfflineStrategyGroupDataRepository statOfflineStrategyGroupDataRepository;
    @Autowired
    private CrowdExecLogRepository crowdExecLogRepository;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private StrategyExecCycleService strategyExecCycleService;
    @Autowired
    private TelePushService telePushService;
    @Autowired
    private TelemarketingClient telemarketingClient;
    @Autowired
    private ReportDailyStrategyService reportDailyStrategyService;
    @Autowired
    private ReportDailyTaskService reportDailyTaskService;
    @Autowired
    private StatStrategyEngineFlowDataRepository strategyEngineFlowDataRepository;
    @Autowired
    private StatStrategyGroupDataRepository statStrategyGroupDataRepository;
    @Autowired
    private PushFeignClient pushFeignClient;
    @Autowired
    private GoodsFeignClient goodsFeignClient;
    @Autowired
    private HttpClientUtil httpClientUtil;
    @Autowired
    private ChangeService changeService;
    @Resource
    private CrowdInfoRepository crowdInfoRepository;

    // 默认在策略找不到对应用户的时候，通知新客老客业务负责人
    private static final List<String> ALARM_MOBILE_AT = Arrays.asList("***********", "***********");

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public boolean insert(StrategyCreateReq strategyCreateReq) {
        StrategyDo strategy = strategyRepository.getByNameAndBusinessType(strategyCreateReq.getName(), strategyCreateReq.getBusinessType());
        if (strategy != null) {
            throw new StrategyException("策略[" + strategyCreateReq.getName() + "]已存在");
        }

        boolean allNoneOrAppBannerChannel = strategyCreateReq.getStrategyGroups().stream().allMatch(strategyGroup -> {
            if (CollectionUtils.isEmpty(strategyGroup.getStrategyMarketChannels())) {
                return false;
            }
            return strategyGroup.getStrategyMarketChannels().stream().allMatch(strategyMarketChannel -> {
                return strategyMarketChannel.getMarketChannel() == StrategyMarketChannelEnum.NONE.getCode() ||
                        strategyMarketChannel.getMarketChannel() == StrategyMarketChannelEnum.APP_BANNER.getCode();
            });
        });

        //校验人群包
        if (allNoneOrAppBannerChannel) {
            strategyCommonService.verifyCrowIdsV2(strategyCreateReq.getCrowdPackIds(), strategyCreateReq.getBusinessType());
        } else {
            strategyCommonService.verifyCrowIds(strategyCreateReq.getCrowdPackIds(), strategyCreateReq.getBusinessType());
        }
        //校验分组配置
        strategyCommonService.verifyGroups(strategyCreateReq.getStrategyGroups(), strategyCreateReq.getAbTest(), strategyCreateReq.getAbType(), strategyCreateReq.getBizKey());

        //校验发送时间
        if (Objects.equals(0, strategyCreateReq.getForceExec()) && LocalDate.now().isEqual(strategyCreateReq.getValidityBegin().toLocalDate())) {
            this.verifySendTime(strategyCreateReq.getSendFrequency(), strategyCreateReq.getStrategyGroups());
        }
        // 例行刷新
        if (StrategyRulerEnum.getCycleCodes()
                .contains(strategyCreateReq.getSendRuler())) {
            strategyCommonService.verifyRuleConfig(strategyCreateReq.getLimitDays(), strategyCreateReq.getLimitTimes());
        }

        if (!strategyCreateReq.isEngineCodeValid()) {
            throw new StrategyException("未填写策略引擎code");
        }
        // 策略主表
        StrategyDo strategyDo = new StrategyDo();
        strategyDo.setName(strategyCreateReq.getName());
        strategyDo.setUserConvert(strategyCreateReq.getUserConvert());
        strategyDo.setDetailDescription(strategyCreateReq.getDetailDescription());
        strategyDo.setAbTest(strategyCreateReq.getAbTest());
        strategyDo.setAbType(strategyCreateReq.getAbType());
        strategyDo.setSendRuler(strategyCreateReq.getSendRuler());
        strategyDo.setValidityBegin(strategyCreateReq.getValidityBegin());
        strategyDo.setValidityEnd(strategyCreateReq.getValidityEnd());
        strategyDo.setSendFrequency(JSON.toJSONString(strategyCreateReq.getSendFrequency()));
        strategyDo.setStatus(strategyCreateReq.getPublishType());
        //  strategyDo.setStatus(StrategyStatusEnum.getInstance(strategyCreateReq.getPublishType()).getCode());
        strategyDo.setUpdatedOp(SsoUtil.get().getName());
        strategyDo.setUpdatedTime(LocalDateTime.now());
        strategyDo.setUpdatedOpMobile(SsoUtil.get().getMobile());
        strategyDo.setBusinessType(strategyCreateReq.getBusinessType());
        strategyDo.setCrowdPackId(strategyCreateReq.getCrowdPackIds());
        strategyDo.setMarketChannel(strategyCommonService.getMarketChannelStr(strategyCreateReq.getStrategyGroups()));
        strategyDo.setGroupType(strategyCreateReq.getStrategyGroupType());
        strategyDo.setBizKey(strategyCommonService.getBizKey(strategyDo.getAbType(), strategyCreateReq.getBizKey()));
        strategyDo.verifyStrategy();

        strategyDo.setType(strategyCreateReq.getType());
        strategyDo.setEngineCode(strategyCreateReq.getEngineCode());
        cacheStrategyService.insert(strategyDo);

        //人群包
        List<StrategyCrowdPackDo> strategyCrowdPackDoList = new ArrayList<>();
        this.updateStrategyCrowdPack(strategyCreateReq.getCrowdPackIds(), strategyCrowdPackDoList, strategyDo.getId());
        strategyCrowdPackRepository.saveBatch(strategyCrowdPackDoList);

        if (StrategyRulerEnum.getCycleCodes()
                .contains(strategyCreateReq.getSendRuler()) || Objects.equals(strategyCreateReq.getType(), 1)) {
            //流控规则入库
            strategyCommonService.createFlowCtrlRule(strategyCreateReq.getBusinessTypeName(), strategyDo, strategyCreateReq.getLimitDays(), strategyCreateReq.getLimitTimes(), StrategyTypeEnum.OFFLINE_STRATEGY);
        }
        //保存营销节点信息
        strategyCommonService.saveMarketConditionInfo(strategyDo, strategyCreateReq.getMarketCondition(), strategyCreateReq.getExcludeType(), strategyCreateReq.getBusinessType(), StrategyTypeEnum.OFFLINE_STRATEGY);

        // 分组配置
        strategyCreateReq.getStrategyGroups().forEach(strategyGroup -> {
            StrategyGroupDo strategyGroupDo = new StrategyGroupDo();
            strategyGroupDo.setStrategyId(strategyDo.getId());
            strategyGroupDo.setGroupConfig(JSON.toJSONString(strategyGroup.getGroupConfig()));
            strategyGroupDo.setName(strategyGroup.getName());
            strategyGroupDo.setExtInfo(strategyGroup.getExtInfo());
            strategyGroupDo.setIsExecutable(Objects.nonNull(strategyGroup.getIsExecutable()) ? strategyGroup.getIsExecutable() : 1);
            cacheStrategyGroupService.insert(strategyGroupDo);

            if (CollectionUtils.isEmpty(strategyGroup.getStrategyMarketChannels())) {
                return;
            }
            // 触达渠道
            strategyGroup.getStrategyMarketChannels().forEach(strategyMarketChannel -> {
                this.verifyChannel(strategyMarketChannel);

                String extInfo = strategyMarketChannel.getExtInfo();
                if (Objects.equals(StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode(),
                        strategyMarketChannel.getMarketChannel())) {
                    if (!strategyCommonService
                            .checkIncreaseAmtChannnel(extInfo)) {
                        throw new StrategyException("提额渠道配置信息验证错误");
                    }
                } else if (Objects.equals(StrategyMarketChannelEnum.API_OPEN_AMOUNT.getCode(),
                        strategyMarketChannel.getMarketChannel())) {
                    if (!strategyCommonService
                            .checkAPIOpenAmountExtInfo(extInfo)) {
                        throw new StrategyException("api放开额度渠道配置信息验证错误");
                    }
                } else if (Objects.equals(StrategyMarketChannelEnum.AI_PRONTO.getCode(),
                        strategyMarketChannel.getMarketChannel())) {
                    if (!strategyCommonService
                            .checkAiProntoChannel(extInfo)) {
                        throw new StrategyException("AI-即时触达渠道配置信息验证错误");
                    }
                }
                StrategyMarketChannelDo strategyMarketChannelDo = new StrategyMarketChannelDo();
                strategyMarketChannelDo.setStrategyId(strategyDo.getId());
                strategyMarketChannelDo.setStrategyGroupId(strategyGroupDo.getId());
                strategyMarketChannelDo.setSendTime(strategyMarketChannel.getSendTime());
                strategyMarketChannelDo.setMarketChannel(strategyMarketChannel.getMarketChannel());
                strategyMarketChannelDo.setTemplateId(strategyMarketChannel.getTemplateId());
                strategyMarketChannelDo.setApp(strategyMarketChannel.getApp());
                if (!StringUtils.isEmpty(extInfo)) {
                    strategyMarketChannelDo.setExtInfo(extInfo);
                }
                cacheStrategyMarketChannelService.insert(strategyMarketChannelDo);

                if (strategyMarketChannel.getMarketChannel() != StrategyMarketChannelEnum.NONE.getCode() &&
                        strategyMarketChannel.getMarketChannel() != StrategyMarketChannelEnum.APP_BANNER.getCode()) {
                    String cronStr = null;
                    StrategyCreateReq.SendFrequency sendFrequency = strategyCreateReq.getSendFrequency();
                    LocalTime sendTime = strategyMarketChannel.getSendTime();

                    Cron cron = strategyCommonService.convertToCron(sendFrequency, sendTime);
                    if (cron != null) {
                        cronStr = cron.asString();
                    }
                    ;
                    strategyMarketChannelDo.setCron(cronStr);
                    if (strategyCreateReq.getSendRuler() == StrategyRulerEnum.CYCLE_DAY.getCode()) {
                        cacheStrategyMarketChannelService.updateById(strategyMarketChannelDo);
                        return;
                    }
                    if (strategyDo.getType() == 1 ||
                            strategyMarketChannel.getMarketChannel() == StrategyMarketChannelEnum.NONE.getCode()) {
                        // 引擎策略不需要创建Job
                        // “不营销”分组也不需要创建Job
                        return;
                    }
                    // 添加xxl-job
                    XxlJobDto xxlJobDto = new XxlJobDto();
                    StrategyXxlJobParam strategyXxlJobParam = new StrategyXxlJobParam(strategyMarketChannelDo.getId());
                    xxlJobDto.setExecutorHandler(XxlJobConstants.STRATEGY_DISPATCH);
                    xxlJobDto.setJobCron(cronStr);
                    StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(strategyMarketChannel.getMarketChannel());
                    xxlJobDto.setJobDesc("策略组:" + strategyCreateReq.getName() + "_" + strategyGroup.getName() + "_" + strategyMarketChannelEnum.getDescription());
                    xxlJobDto.setAddTime(new Date());
                    xxlJobDto.setExecutorParam(JSON.toJSONString(strategyXxlJobParam));
                    xxlJobDto.setTriggerStatus(1);
                    if (Objects.equals(-1, strategyCreateReq.getPublishType())) {
                        xxlJobDto.setTriggerStatus(0);
                    }
                    xxlJobDto.setAuthor(SsoUtil.get().getName());
                    try {
                        strategyMarketChannelDo.setXxlJobId(xxlJobAdminClient.addJob(xxlJobDto));
                        cacheStrategyMarketChannelService.updateById(strategyMarketChannelDo);
                    } catch (InfraException e) {
                        log.error("保存xxlJob失败, {},", JsonUtil.toJson(xxlJobDto), e);
                    }
                }
            });
        });
        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(strategyDo.getId()));
        changeService.asyncSubmitInsertStrategyChange(strategyDo.getId()+ "", strategyCreateReq, strategyDo);
        return true;
    }

    /**
     * @param strategyMarketChannel:
     * @return void
     * <AUTHOR>
     * @description 推送配置给新电销
     * @date 2023/10/16 16:22
     */
    private void pushTeleAndSetTemplateId(StrategyCreateReq.StrategyMarketChannel strategyMarketChannel) {
        telePushService.pushTeleAndSetTemplateId(strategyMarketChannel);
    }

    @Override
    public List<Dict> getSmsTemplateGroup(SmsGroupReq smsGroupReq) {
        SmsGroupArgs smsGroupArgs = new SmsGroupArgs();
        smsGroupArgs.setType(smsGroupReq.getType());
        SmsGroupRequester requester = new SmsGroupRequester();
        requester.setArgs(smsGroupArgs);
        SmsGroupResp smsGroupResp = smsClient.queryGroup(requester);
        if (!smsGroupResp.isSuccess()) {
            throw new StrategyException("查询短信类型异常");
        }
        List<SmsGroup> smsGroups = smsGroupResp.getResponse();
        return smsGroups.stream().map(item -> {
            Dict dict = Dict.builder().build();
            dict.setDictCode(item.getDescription());
            dict.setDictValue(item.getGroup());
            return dict;
        }).collect(Collectors.toList());
    }

    @Override
    public boolean templateParamCheck(SmsTemplateCheckReq smsTemplateCheckReq) {
        if ( !CollectionUtils.isEmpty(smsTemplateCheckReq.getTemplateList())) {
            for (SmsTemplateCheckReq.SingleTemplate singleTemplate : smsTemplateCheckReq.getTemplateList()) {
                Pair<Boolean, Integer> flag = templateParamService.templateParamCheck(
                        StrategyMarketChannelEnum.SMS,
                        singleTemplate.getApp(),
                        singleTemplate.getTemplate());
                if (!(flag.getLeft() == null || Boolean.TRUE.equals(flag.getLeft()))) {
                    throw new StrategyException("参数配置有误，请检查后重试");
                }
            }
        }
        return true;
    }

    @Override
    public boolean existStrategyFlowCtrlUpdate() {
        Map<String, String> businessType = new HashMap<>();
        businessType.put("new-cust", "新客");
        businessType.put("old-cust", "老客");
        businessType.put("test-cust", "测试专用");

        List<StrategyDo> strategyDoList = strategyRepository.selectExistOfflineStrategy();
        strategyDoList.forEach(item -> {
            String strategyTypeName = businessType.get(item.getBusinessType());
            FlowCtrlDo flowCtrlDo = new FlowCtrlDo();
            flowCtrlDo.setName(strategyTypeName + "-" + item.getName() + "-" + item.getValidityBegin().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            flowCtrlDo.setEffectiveStrategy(String.valueOf(item.getId()));
            flowCtrlDo.setType(FlowCtrlTypeEnum.STRATEGY.getType());
            flowCtrlDo.setStrategyType(StrategyTypeEnum.OFFLINE_STRATEGY.getCode());
            flowCtrlDo.setLimitDays(1);
            flowCtrlDo.setLimitTimes(2);
            flowCtrlDo.setStatus(item.getStatus() == 5 ? FlowCtrlRuleStatusEnum.CLOSE.getCode() : FlowCtrlRuleStatusEnum.EFFECTIVE.getCode());
            flowCtrlDo.setCreatedOp(item.getCreatedOp());
            flowCtrlDo.setUpdatedOp(item.getUpdatedOp());
            flowCtrlRepository.insert(flowCtrlDo);
            item.setFlowCtrlId(flowCtrlDo.getId());
            strategyRepository.updateById(item);
        });

        return true;
    }

    @Override
    public boolean update(StrategyUpdateReq strategyUpdateReq) {
        StrategyDo strategyDo = strategyRepository.selectById(strategyUpdateReq.getId());

        if (StrategyStatusEnum.EXECUTING.getCode() == strategyDo.getStatus() || StrategyStatusEnum.ENDED.getCode() == strategyDo.getStatus()) {
            throw new StrategyException("策略状态为执行中或者已结束不支持更新");
        }
        // 不允许更新成其他策略
        if (strategyDo.getSendRuler() == StrategyRulerEnum.CYCLE_DAY.getCode()) {
            if (!Objects.equals(strategyDo.getSendRuler(),
                    strategyUpdateReq.getSendRuler())) {
                throw new StrategyException("策略发送规则不可以修改");
            }
            if (strategyUpdateReq.getSendFrequency().getType()
                    != StrategyFrequencyEnum.CYCLE_DAY.getCode()) {
                throw new StrategyException("策略发送类型错误");
            }
            // 判定当前的时间
            StrategyExecCycleDo strategyExecCycleDo = strategyExecCycleService
                    .selectStrategyCycle(strategyDo.getId(), null);
            if (strategyExecCycleDo != null) {
                StrategyCreateReq.SendFrequency sendFrequency = JsonUtil.parse(strategyDo.getSendFrequency(),
                        StrategyCreateReq.SendFrequency.class);
                if (!Objects.equals(sendFrequency.getValue().get(0),
                        strategyUpdateReq.getSendFrequency().getValue().get(0))) {
                    throw new StrategyException("周期不可以修改");
                }
            }
        }
        if (strategyDo.getType() == 1 && StringUtils
                .isEmpty(strategyUpdateReq.getEngineCode())) {
            throw new StrategyException("没有传引擎code");
        }

        //校验策略名称
        strategyCommonService.verifyStrategyName(strategyDo, strategyUpdateReq.getName(), strategyUpdateReq.getId());
        //校验人群包
        boolean allNoneOrAppBannerChannel = strategyUpdateReq.getStrategyGroups().stream().allMatch(strategyGroup -> {
            if (CollectionUtils.isEmpty(strategyGroup.getStrategyMarketChannels())) {
                return false;
            }
            return strategyGroup.getStrategyMarketChannels().stream().allMatch(strategyMarketChannel -> {
                return strategyMarketChannel.getMarketChannel() == StrategyMarketChannelEnum.NONE.getCode() ||
                        strategyMarketChannel.getMarketChannel() == StrategyMarketChannelEnum.APP_BANNER.getCode();
            });
        });
        if (allNoneOrAppBannerChannel) {
            strategyCommonService.verifyCrowIdsV2(strategyUpdateReq.getCrowdPackIds(), strategyDo.getBusinessType());
        } else {
            strategyCommonService.verifyCrowIds(strategyUpdateReq.getCrowdPackIds(), strategyDo.getBusinessType());
        }
        //校验分组配置
        strategyCommonService.verifyGroups(strategyUpdateReq.getStrategyGroups(), strategyUpdateReq.getAbTest(), strategyUpdateReq.getAbType(), strategyUpdateReq.getBizKey());

        //校验发送时间
        if (Objects.equals(0, strategyUpdateReq.getForceExec()) && LocalDate.now().isEqual(strategyUpdateReq.getValidityBegin().toLocalDate())) {
            this.verifySendTime(strategyUpdateReq.getSendFrequency(), strategyUpdateReq.getStrategyGroups());
        }
        // 例行刷新校验流控配置
        if (StrategyRulerEnum.getCycleCodes()
                .contains(strategyUpdateReq.getSendRuler()) || Objects.equals(strategyDo.getType(), 1)) {
            strategyCommonService.verifyRuleConfig(strategyUpdateReq.getLimitDays(), strategyUpdateReq.getLimitTimes());
        }
        //更新策略信息
        this.updateStrategyDo(strategyUpdateReq, strategyDo);

        //人群包
        List<StrategyCrowdPackDo> strategyCrowdPackDoList = new ArrayList<>();
        this.updateStrategyCrowdPack(strategyUpdateReq.getCrowdPackIds(), strategyCrowdPackDoList, strategyDo.getId());

        List<Long> strategyGroupIdList = new ArrayList<>();
        List<Long> strategyChannelIdList = new ArrayList<>();
        List<Long> strategyMarketConditionIdList = new ArrayList<>();


        return TransactionUtil.transactional(() -> {
            // 更新策略组
            strategyUpdateReq.getStrategyGroups().forEach(strategyGroup -> {
                //更新策略组
                StrategyGroupDo strategyGroupDo = strategyCommonService.updateStrategyGroup(strategyGroup, strategyDo.getId());
                strategyGroupIdList.add(strategyGroupDo.getId());
                if (CollectionUtils.isEmpty(strategyGroup.getStrategyMarketChannels())) {
                    return;
                }
                // 更新触达渠道
                strategyGroup.getStrategyMarketChannels().forEach(strategyMarketChannel -> {
                    this.verifyChannel(strategyMarketChannel);
                    StrategyMarketChannelDo strategyMarketChannelDo = strategyCommonService.updateStrategyMarketChannels(strategyMarketChannel, strategyDo, strategyGroupDo, strategyUpdateReq.getSendFrequency(), strategyDo.getName());
                    strategyChannelIdList.add(strategyMarketChannelDo.getId());
                });
            });
            // 更新策略
            cacheStrategyService.updateById(strategyDo);

            // 更新策略人群包
            strategyCrowdPackRepository.deleteByStrategyId(strategyUpdateReq.getId());
            strategyCrowdPackRepository.saveBatch(strategyCrowdPackDoList);

            // 更新策略营销规则
            // 更新触达渠道
            strategyUpdateReq.getMarketCondition().forEach(marketCondition -> {
                StrategyMarketEventConditionDo strategyMarketEventConditionDo = strategyCommonService.updateMarketConditionInfo(marketCondition, strategyDo.getId(), StrategyTypeEnum.OFFLINE_STRATEGY);
                strategyMarketConditionIdList.add(strategyMarketEventConditionDo.getId());
            });

            // 更新营销排除项
            strategyCommonService.updateExcludeLabel(strategyDo.getId(), strategyUpdateReq.getExcludeType(), strategyDo.getBusinessType(), StrategyTypeEnum.OFFLINE_STRATEGY);

            // 删除 被删除组
            List<StrategyGroupDo> strategyGroupDoList = strategyGroupRepository.selectListByStrategyId(strategyUpdateReq.getId());
            List<Long> delGroupIdList = strategyGroupDoList.stream().map(StrategyGroupDo::getId).filter(id -> !strategyGroupIdList.contains(id)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delGroupIdList)) {
                cacheStrategyGroupService.deleteBatch(delGroupIdList);
            }

            // 删除 被删除渠道
            List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectByStrategyId(strategyUpdateReq.getId());
            List<StrategyMarketChannelDo> delChannelIdList = strategyMarketChannelDos.stream().filter(item -> !strategyChannelIdList.contains(item.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delChannelIdList)) {
                List<Long> delIdList = delChannelIdList.stream().map(StrategyMarketChannelDo::getId).collect(Collectors.toList());
                cacheStrategyMarketChannelService.deleteByIdBatch(delIdList);
                delChannelIdList.forEach(item -> {
                    if (Objects.nonNull(item.getXxlJobId())) {
                        xxlJobAdminClient.removeJob(item.getXxlJobId());
                    }
                });
            }
            // 删除 被删除营销条件(默认排除项除外)
            List<StrategyMarketEventConditionDo> strategyMarketEventConditionDoList = strategyMarketEventConditionRepository.getByStrategyIdAndOption(strategyUpdateReq.getId());
            List<Long> delEventConditionIdList = strategyMarketEventConditionDoList.stream().filter(t -> StrategyInstantLabelOptionEnum.INPUT_OPTIONAL.getCode() == t.getOptional()).map(StrategyMarketEventConditionDo::getId).filter(id -> !strategyMarketConditionIdList.contains(id)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delEventConditionIdList)) {
                cacheStrategyMarketEventConditionService.deleteBatch(delEventConditionIdList);
            }
            //更新流控配置
            this.updateRuleConfig(strategyUpdateReq, strategyDo);

            changeService.asyncSubmitUpdateStrategyChange(strategyUpdateReq, strategyDo);

            //收集操作对象id
            OperateLogObjectIdUtils.set(Collections.singletonList(strategyDo.getId()));
        });
    }


    void updateStrategyDo(StrategyUpdateReq strategyUpdateReq, StrategyDo strategyDo) {
        // 策略主表
        strategyDo.setName(strategyUpdateReq.getName());
        strategyDo.setUserConvert(strategyUpdateReq.getUserConvert());
        strategyDo.setDetailDescription(strategyUpdateReq.getDetailDescription());
        strategyDo.setAbTest(strategyUpdateReq.getAbTest());
        strategyDo.setAbType(strategyUpdateReq.getAbType());
        strategyDo.setSendRuler(strategyUpdateReq.getSendRuler());
        strategyDo.setValidityBegin(strategyUpdateReq.getValidityBegin());
        strategyDo.setValidityEnd(strategyUpdateReq.getValidityEnd());
        strategyDo.setSendFrequency(JSON.toJSONString(strategyUpdateReq.getSendFrequency()));
        strategyDo.setCrowdPackId(strategyUpdateReq.getCrowdPackIds());
        strategyDo.setMarketChannel(strategyCommonService.getMarketChannelStr(strategyUpdateReq.getStrategyGroups()));
        strategyDo.setGroupType(strategyUpdateReq.getStrategyGroupType());
        strategyDo.setBizKey(strategyCommonService.getBizKey(strategyDo.getAbType(), strategyUpdateReq.getBizKey()));
        strategyDo.setUpdatedOp(SsoUtil.get().getName());
        strategyDo.setUpdatedTime(LocalDateTime.now());
        strategyDo.setUpdatedOpMobile(SsoUtil.get().getMobile());
        strategyDo.setEngineCode(strategyUpdateReq.getEngineCode());
        strategyDo.setDispatchType(strategyUpdateReq.getDispatchType());
        strategyDo.verifyStrategy();
    }

    @Override
    public void updateStrategyCrowdPack(String crowdPackIds, List<StrategyCrowdPackDo> strategyCrowdPackDoList, Long strategyId) {
        if (StringUtils.isEmpty(crowdPackIds)) {
            return;
        }
        String[] crowdIds = crowdPackIds.split(";");
        for (String crowdPackId : crowdIds) {
            if (!StringUtils.isNumeric(crowdPackId)) {
                throw new StrategyException("人群包id[" + crowdPackId + "]有误");
            }
            StrategyCrowdPackDo strategyCrowdPackDo = new StrategyCrowdPackDo();
            strategyCrowdPackDo.setStrategyId(strategyId);
            strategyCrowdPackDo.setCrowdPackId(Long.valueOf(crowdPackId));
            strategyCrowdPackDoList.add(strategyCrowdPackDo);
        }
    }

    @Override
    public void updateRuleConfig(StrategyUpdateReq strategyUpdateReq, StrategyDo strategyDo) {
        //更新流控 如果之前是单次，更新为例行，写入一条流控，如果之前是例行，更新为单次，要删除流控
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(strategyDo.getFlowCtrlId());
        if (flowCtrlDo == null) {
            log.warn("updateRuleConfig, flowCtrlDo 为空, strategyId = {}, ctrlId={}", strategyDo.getId(), strategyDo.getFlowCtrlId());
        }
        if (StrategyRulerEnum.getCycleCodes()
                .contains(strategyUpdateReq.getSendRuler()) || Objects.equals(strategyDo.getType(), 1)) {
            if (flowCtrlDo == null) {
                strategyCommonService.createFlowCtrlRule(strategyUpdateReq.getBusinessTypeName(), strategyDo, strategyUpdateReq.getLimitDays(), strategyUpdateReq.getLimitTimes(), StrategyTypeEnum.OFFLINE_STRATEGY);
            } else {
                if (strategyUpdateReq.getLimitDays() == null
                        || strategyUpdateReq.getLimitTimes() == null){
                    return;
                }
                flowCtrlDo.setLimitDays(strategyUpdateReq.getLimitDays());
                flowCtrlDo.setLimitTimes(strategyUpdateReq.getLimitTimes());
                flowCtrlDo.setUpdatedOp(SsoUtil.get().getName());
                flowCtrlRepository.updateById(flowCtrlDo);
            }
        } else {
            if (Objects.nonNull(flowCtrlDo)) {
                flowCtrlRepository.delete(flowCtrlDo.getId());
            }
        }
    }

    @Override
    public int deleteById(Long id) {
        if (id != null && id > 0) {
            return strategyRepository.deleteById(id);
        }
        return 0;
    }

    @Override
    public PageResultResponse<PolicyListResp> getPolicyList(PolicyListReq policyListReq) {
        PageResultResponse<PolicyListResp> result = new PageResultResponse<>();
        TelePolicyConfigListRequest req = new TelePolicyConfigListRequest(policyListReq);
        req.format();
        TelePolicyConfigListResp resp = telemarketingClient.getPolicyList(req);
        if (Objects.nonNull(resp) && Objects.nonNull(resp.getResponse())) {
            result.setCurrent(resp.getResponse().getCurrentPage());
            result.setSize(resp.getResponse().getPageSize());
            result.setPages(resp.getResponse().getTotalPage());
            result.setTotal(resp.getResponse().getTotal());
            if (!CollectionUtils.isEmpty(resp.getResponse().getList())) {
                List<TelePolicyConfigListResp.ResponseData.Item> list = resp.getResponse().getList();
                List<PolicyListResp> records = list.stream()
                        .map(t -> new PolicyListResp(t.getId(), t.getPolicyName(), t.getType(), t.getStatus(), t.getUpdateBy(), t.getUpdatedTime(), t.getPriority()))
                        .sorted(Comparator.comparingInt(PolicyListResp::getPriority).reversed())
                        .collect(Collectors.toList());
                result.setRecords(records);
            }
            ;
        }
        return result;
    }

    @Override
    public boolean operatePolicy(OperatePolicyReq operatePolicyReq) {
        StrategyCreateReq.StrategyMarketChannel strategyMarketChannel = new StrategyCreateReq.StrategyMarketChannel();
        strategyMarketChannel.setTemplate(operatePolicyReq.getTemplate());
        pushTeleAndSetTemplateId(strategyMarketChannel);
        return true;
    }

    @Override
    public boolean updatePolicyPriority(UpdatePolicyPriorityReq req) {
        return telePushService.updatePolicyPriority(req);
    }

    @Override
    public String policyDetail(Integer policyId) {
        return telePushService.getPolicyDetail(policyId);
    }

    @Override
    public NameTypeResp getNameTypeDetail(Integer nameTypeId) {
        NameTypeReq req = new NameTypeReq();
        req.setNameTypeId(nameTypeId);
        PageResultResponse<NameTypeResp> nameTypeList = getNameTypeList(req);
        if (!CollectionUtils.isEmpty(nameTypeList.getRecords())) {
            return nameTypeList.getRecords().get(0);
        }
        return null;
    }

    @Override
    public PageResultResponse<PushTemplateResp> getPushTemplateList(PushTemplateReq pushTemplateReq) {
        PageResultResponse<PushTemplateResp> result = new PageResultResponse<>();
        PushBaseRequest<TemplateListRequest> pushBaseRequest = new PushBaseRequest<>();
        pushBaseRequest.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
        pushBaseRequest.setArgs(new TemplateListRequest(pushTemplateReq));
        PushResponse<PushTemplateList> pushTemplateListPushResponse = pushFeignClient.queryPushTemplateList(pushBaseRequest);
        log.info("queryPushTemplateList request:{},response:{}", pushBaseRequest, pushTemplateListPushResponse);
        if (Objects.isNull(pushTemplateListPushResponse) || !pushTemplateListPushResponse.isSuccess() || Objects.isNull(pushTemplateListPushResponse.getResponse())) {
            throw new StrategyException("查询push模板列表失败");
        }
        PushTemplateList pushResp = pushTemplateListPushResponse.getResponse();
        result.setCurrent(pushResp.getPageInfo().getPage());
        result.setSize(pushResp.getPageInfo().getPageSize());
        result.setTotal(pushResp.getPageInfo().getCount());
        result.setRecords(pushResp.getDataList().stream().map(item -> {
            PushTemplateResp pushTemplateResp = new PushTemplateResp();
            BeanUtils.copyProperties(item, pushTemplateResp);
            return pushTemplateResp;
        }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public PageResultResponse<GoodsListResp> getGoodsList(GoodsListReq goodsListReq) {
        GoodsListRequest goodsListRequest = new GoodsListRequest(goodsListReq);
        GoodsResponse<GoodsList> goodsListResp = goodsFeignClient.queryGoodsList(goodsListRequest,"xyf-cdp");
        log.info("goods url:/goods/queryGoodsList req:{},resp:{}", goodsListRequest, goodsListResp);
        if (!goodsListResp.isSuccess()) {
            log.error("查询商品信息失败, req:{}, resp:{}", JsonUtil.toJson(goodsListRequest), JsonUtil.toJson(goodsListResp));
            throw new BizException("查询商品信息异常");
        }
        GoodsList data = goodsListResp.getData();
        PageResultResponse<GoodsListResp> result = new PageResultResponse<>();
        result.setCurrent(data.getPageInfo().getPage());
        result.setSize(data.getPageInfo().getPageSize());
        result.setTotal(data.getPageInfo().getTotal());
        result.setRecords(data.getList().stream().map(item -> {
            GoodsListResp goodsDetail = new GoodsListResp();
            BeanUtils.copyProperties(item, goodsDetail);
            return goodsDetail;
        }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public PageResultResponse<StrategyListResp> list(StrategyListReq strategyListReq) {

        Page<StrategyDo> records = strategyRepository.selectPage(strategyListReq);
        List<StrategyDo> strategyBoList = records.getList();

        if (CollectionUtils.isEmpty(strategyBoList)) {
            return null;
        }

        List<String> crowdPackIdList = new ArrayList<>();
        List<CrowdPackDo> crowdPackDoList = new ArrayList<>();
        List<String> crowdPackIdStrList = strategyBoList.stream().map(StrategyDo::getCrowdPackId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(crowdPackIdStrList)) {
            crowdPackIdStrList.forEach(item -> crowdPackIdList.addAll(Arrays.asList(item.split(";"))));
            crowdPackDoList = crowdPackRepository.selectByIds(crowdPackIdList.toArray(new String[0]));
        }

        Map<Long, String> crowdPackNameMap = crowdPackDoList.stream().collect(Collectors.toMap(CrowdPackDo::getId, CrowdPackDo::getCrowdName));
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

        // 新增洞察平台人群包
        List<CrowdPackDo> insightCrowdPackDoList = new ArrayList<>();
        if (ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_BUSINESS_LINE, strategyListReq.getBusinessType())) {
            List<CrowdInfoDo> crowdInfoDos = crowdInfoRepository.selectAllOnline();
            if (!CollectionUtils.isEmpty(crowdInfoDos)) {
                crowdInfoDos.stream()
                        .filter(x -> ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK, x.getCrowdId()))
                        .forEach(x -> {
                            CrowdPackDo crowdPackDo = new CrowdPackDo();
                            crowdPackDo.setId(x.getCrowdId());
                            crowdPackDo.setCrowdName("[洞察平台]" + x.getCrowdName());

                            insightCrowdPackDoList.add(crowdPackDo);
                        });
            }
        }
        Map<Long, String> insightCrowdPackNameMap = insightCrowdPackDoList.stream().collect(Collectors.toMap(CrowdPackDo::getId, CrowdPackDo::getCrowdName));

        List<StrategyListResp> strategyListResponse = new ArrayList<>();
        for (StrategyDo strategyDo : strategyBoList) {
            StrategyListResp strategyListResp = new StrategyListResp();
            strategyListResp.setName(strategyDo.getName());
            strategyListResp.setId(strategyDo.getId());
            strategyListResp.setSendRuler(strategyDo.getSendRuler());
            strategyListResp.setStatus(strategyDo.getStatus());
            strategyListResp.setUpdateOp(strategyDo.getUpdatedOp());
            strategyListResp.setUpdatedTime(strategyDo.getUpdatedTime());

            if (StringUtils.isNotBlank(strategyDo.getCrowdPackId())) {
                String[] crowdPackId = strategyDo.getCrowdPackId().split(";");
                List<String> crowdPackName = Arrays.stream(crowdPackId)
                        .map(item -> {
                            String crowdName = crowdPackNameMap.get(Long.valueOf(item));
                            if (StringUtils.isBlank(crowdName)) {
                                crowdName = insightCrowdPackNameMap.get(Long.valueOf(item));
                            }
                            return crowdName;
                        })
                        .collect(Collectors.toList());
                strategyListResp.setCrowdPackId(StringUtils.join(crowdPackName, ";"));
            }

            strategyListResp.setMarketChannel(Arrays.asList(strategyDo.getMarketChannel().split(",")));
            strategyListResp.setStrategyGroupType(strategyDo.getGroupType());
            String execCount = "-";
            String marketSuccRate = "-";
            // 查询当日触达人数、成功人数
            String all = redisUtils.get(this.genRedisKey(strategyDo.getId(), startTime, "all"));

            if (StringUtils.isNotBlank(all)) {
                int totalCount = Integer.parseInt(all);
                String suc = redisUtils.get(this.genRedisKey(strategyDo.getId(), startTime, "suc"));
                execCount = suc;
                int succCount = StringUtils.isNotBlank(suc) ? Integer.parseInt(suc) : 0;
                if (totalCount > 0) {
                    marketSuccRate = BigDecimal.valueOf(((float) succCount / totalCount) * 100).setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
                }
            }

            strategyListResp.setExecCount(execCount);
            strategyListResp.setMarketSuccRate(marketSuccRate);

            strategyListResp.setType(strategyDo.getType());
            strategyListResp.setEngineCode(strategyDo.getEngineCode());

            List<StrategyGroupDo> strategyGroupDos = strategyGroupRepository.selectListByStrategyId(strategyDo.getId());

            List<StrategyListResp.StrategyGroupInfo> groupInfoList = new ArrayList<>();
            for (StrategyGroupDo strategyGroupDo : strategyGroupDos) {
                StrategyListResp.StrategyGroupInfo strategyGroupInfo = new StrategyListResp.StrategyGroupInfo();
                strategyGroupInfo.setGroupId(strategyGroupDo.getId().toString());
                strategyGroupInfo.setGroupName(strategyGroupDo.getName());
                groupInfoList.add(strategyGroupInfo);
            }
            strategyListResp.setGroupInfoList(groupInfoList);

            strategyListResponse.add(strategyListResp);
        }

        return new PageResultResponse<>(strategyListResponse, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    private String genRedisKey(Long strategyId, LocalDateTime startTime, String suffix) {
        String date = startTime.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format(RedisKeyConstants.STRATEGY_TODAY_DISPATCH_COUNT, strategyId, date, suffix);
    }

    @Override
    public StrategyDetailResp getDetail(Long strategyId) {
        StrategyDetailResp strategyDetailResp = new StrategyDetailResp();

        StrategyDo strategyDo = strategyRepository.selectById(strategyId);
        strategyDetailResp.setUserConvert(strategyDo.getUserConvert());
        strategyDetailResp.setName(strategyDo.getName());
        strategyDetailResp.setDetailDescription(strategyDo.getDetailDescription());
        strategyDetailResp.setAbTest(strategyDo.getAbTest());
        strategyDetailResp.setAbType(strategyDo.getAbType());
        strategyDetailResp.setSendRuler(strategyDo.getSendRuler());
        strategyDetailResp.setValidityBegin(strategyDo.getValidityBegin());
        strategyDetailResp.setValidityEnd(strategyDo.getValidityEnd());
        strategyDetailResp.setSendFrequency(JSON.parseObject(strategyDo.getSendFrequency(), StrategyDetailResp.SendFrequency.class));
        strategyDetailResp.setStrategyGroupType(strategyDo.getGroupType());
        strategyDetailResp.setStatus(strategyDo.getStatus());
        strategyDetailResp.setType(strategyDo.getType());
        strategyDetailResp.setEngineCode(strategyDo.getEngineCode());
        strategyDetailResp.setDispatchType(strategyDo.getDispatchType());

        if (StringUtils.isNotEmpty(strategyDo.getMarketChannel())) {
            strategyDetailResp.setMarketChannels(Arrays.stream(strategyDo.getMarketChannel().split(",")).map(Integer::parseInt)
                    .collect(Collectors.toList()));
        }
        strategyDetailResp.setDispatchConfig(strategyDo.getDispatchConfig());
        strategyDetailResp.setFlowNo(strategyDo.getFlowNo());
        if (strategyDo.getAbType() == StrategyGroupTypeEnum.NEW_RANDOM.getCode()) {
            strategyDetailResp.setRandomItem(strategyCommonService.getRandomItem(strategyDo.getBizKey()));
        }
        //人群包
        List<StrategyCrowdPackDo> crowdPackDoList = strategyCrowdPackRepository.selectByStrategyId(strategyDo.getId());
        strategyDetailResp.setCrowdPackIds(StringUtils.join(crowdPackDoList.stream().map(StrategyCrowdPackDo::getCrowdPackId).collect(Collectors.toList()), ";"));

        //流控配置信息
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(strategyDo.getFlowCtrlId());
        if (Objects.nonNull(flowCtrlDo)) {
            strategyDetailResp.setLimitDays(flowCtrlDo.getLimitDays());
            strategyDetailResp.setLimitTimes(flowCtrlDo.getLimitTimes());
        }
        //获取分组配置信息
        this.getGroupInfo(strategyId, strategyDetailResp);

        //获取营销节点配置信息
        Pair<List<InstantStrategyDetailResp.MarketCondition>, List<Integer>> pair = strategyCommonService.getMarketConditionList(strategyId, StrategyTypeEnum.OFFLINE_STRATEGY);
        strategyDetailResp.setMarketCondition(pair.getLeft());
        strategyDetailResp.setExcludeType(pair.getRight());
        return strategyDetailResp;
    }

    void getGroupInfo(Long strategyId, StrategyDetailResp strategyDetailResp) {
        List<StrategyGroupDo> strategyGroupDoList = strategyGroupRepository.selectListByStrategyId(strategyId);
        List<StrategyDetailResp.StrategyGroup> strategyGroupList = new ArrayList<>();

        for (StrategyGroupDo strategyGroupDo : strategyGroupDoList) {
            StrategyDetailResp.StrategyGroup strategyGroup = new StrategyDetailResp.StrategyGroup();
            strategyGroup.setIsExecutable(Objects.nonNull(strategyGroupDo.getIsExecutable()) ? strategyGroupDo.getIsExecutable() : 1);
            strategyGroup.setName(strategyGroupDo.getName());
            strategyGroup.setGroupConfig(JSON.parseObject(strategyGroupDo.getGroupConfig(), StrategyDetailResp.GroupConfig.class));
            strategyGroup.setGroupId(String.valueOf(strategyGroupDo.getId()));
            strategyGroup.setExtInfo(strategyGroupDo.getExtInfo());

            List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyGroupId(strategyGroupDo.getId());
            List<StrategyDetailResp.StrategyMarketChannel> strategyMarketChannelList = strategyMarketChannelDoList.stream().map(item -> {
                StrategyDetailResp.StrategyMarketChannel strategyMarketChannel = new StrategyDetailResp.StrategyMarketChannel();
                strategyMarketChannel.setChannelId(String.valueOf(item.getId()));
                strategyMarketChannel.setTemplate(strategyCommonService.getTemplate(item));
                strategyMarketChannel.setMarketChannel(item.getMarketChannel());
                strategyMarketChannel.setTemplateId(item.getTemplateId());
                strategyMarketChannel.setApp(item.getApp());
                strategyMarketChannel.setSendTime(item.getSendTime());
                strategyMarketChannel.setExtInfo(item.getExtInfo());
                return strategyMarketChannel;
            }).collect(Collectors.toList());

            strategyGroup.setStrategyMarketChannels(strategyMarketChannelList);
            strategyGroupList.add(strategyGroup);
        }
        strategyDetailResp.setStrategyGroups(strategyGroupList);
    }

    @Override
    public PageResultResponse<AbsMonitorListResp> monitorList(MonitorListReq monitorListReq) {
        StrategyDo strategyDo = strategyRepository.selectById(monitorListReq.getStrategyId());
        if (strategyDo == null) {
            throw new StrategyException("策略不存在");
        }

        //数据流统计
        if (Objects.equals(-2, monitorListReq.getStrategyMarketChannel())) {
            List<AbsMonitorListResp> monitorStatFlowDataRespList;
            if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler())) {
                Page<StatRealtimeStrategyFlowDataEntity> records = statRealtimeStrategyFlowDataRepository.listByStrategyId(monitorListReq.getStrategyId(), monitorListReq.getBeginNum(), monitorListReq.getSize());
                if (CollectionUtils.isEmpty(records.getList())) {
                    return null;
                }
                monitorStatFlowDataRespList = this.statRealtimeStrategyFlowData(records.getList(), strategyDo);
                return new PageResultResponse<>(monitorStatFlowDataRespList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
            } else {
                Page<StatOfflineStrategyFlowDataEntity> records = statOfflineStrategyFlowDataRepository.selectByStrategyIdAndPage(monitorListReq.getStrategyId(), monitorListReq.getBeginNum(), monitorListReq.getSize());
                if (CollectionUtils.isEmpty(records.getList())) {
                    return null;
                }
                monitorStatFlowDataRespList = this.statOfflineStrategyFlowData(records.getList(), monitorListReq.getStrategyId());
                if (Objects.equals(StrategyRulerEnum.CYCLE_DAY.getCode(), strategyDo.getSendRuler())) {
                    monitorStatFlowDataRespList.forEach(x -> {
                        if (x.getBizDate() == null) {
                            return;
                        }
                        LocalDateTime localDateTime = null;
                        if (x.getBizDate() instanceof String) {
                            localDateTime = DateUtil.convert(Objects.requireNonNull(DateUtil.convert(String.valueOf(x.getBizDate()))));
                        } else {
                            localDateTime = x.getBizDate();
                        }
                        StrategyExecCycleDo strategyExecCycleDo = strategyExecCycleService.selectStrategyCycle(monitorListReq.getStrategyId(),
                                DateUtil.dayOfInt(DateUtil.convert(localDateTime)));
                        if (strategyExecCycleDo != null) {
                            x.setCycleFormat(String.format("%s/%s", strategyExecCycleDo.getCurVal(), strategyExecCycleDo.getTotalVal()));
                        }
                    });
                }
                return new PageResultResponse<>(monitorStatFlowDataRespList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
            }
        }

        Page<StrategyExecLogDo> records = strategyExecLogRepository.selectPageByChannel(monitorListReq);
        if (CollectionUtils.isEmpty(records.getList())) {
            return null;
        }
        List<AbsMonitorListResp> monitorListResp = this.covertToResp(strategyDo, records.getList(), monitorListReq.getStrategyMarketChannel());
        if (Objects.equals(StrategyRulerEnum.CYCLE_DAY.getCode(), strategyDo.getSendRuler())) {
            monitorListResp.forEach(x -> {
                if (x.getDateTime() == null) {
                    return;
                }
                StrategyExecCycleDo strategyExecCycleDo = strategyExecCycleService.selectStrategyCycle(monitorListReq.getStrategyId(),
                        DateUtil.dayOfInt(DateUtil.convert(x.getDateTime())));
                if (strategyExecCycleDo != null) {
                    x.setCycleFormat(String.format("%s/%s", strategyExecCycleDo.getCurVal(), strategyExecCycleDo.getTotalVal()));
                }
            });
        }
        return new PageResultResponse<>(monitorListResp, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    List<AbsMonitorListResp> statRealtimeStrategyFlowData(List<StatRealtimeStrategyFlowDataEntity> dataEntityList, StrategyDo strategyDo) {
        List<AbsMonitorListResp> respList = new ArrayList<>();
        dataEntityList.forEach(item -> {
            List<StatRealtimeStrategyGroupDataEntity> strategyGroupDataEntityList = statRealtimeStrategyGroupDataRepository.listByStrategyIdAndBizDate(strategyDo.getId(), String.valueOf(item.getBizDate()));
            if (!CollectionUtils.isEmpty(strategyGroupDataEntityList)) {
                for (StatRealtimeStrategyGroupDataEntity tmp : strategyGroupDataEntityList) {
                    this.genStatFlowDataResp(item, tmp.getFlowControlNum(), tmp.getGroupName(), tmp.getDispatchNum(), respList, strategyDo);
                }
            } else {
                this.genStatFlowDataResp(item, item.getFlowControlNum(), "-", item.getDispatchNum(), respList, strategyDo);
            }
        });
        return respList;
    }

    void genStatFlowDataResp(StatRealtimeStrategyFlowDataEntity item, Integer flowControlNum, String groupName, Integer dispatchNum, List<AbsMonitorListResp> respList, StrategyDo strategyDo) {
        boolean noLimit = MarketCrowdTypeEnum.NO_LIMIT.getCode().equals(strategyDo.getMarketCrowdType());
        MonitorStatRealtimeFlowDataResp monitorStatFlowDataResp = new MonitorStatRealtimeFlowDataResp();
        monitorStatFlowDataResp.setBizDate(String.valueOf(item.getBizDate()));
        monitorStatFlowDataResp.setEventSum(item.getEventSum());
        monitorStatFlowDataResp.setUserSum(item.getUserSum());
        monitorStatFlowDataResp.setFilterEventNum(item.getFilterEventNum());
        monitorStatFlowDataResp.setFilterRegTimNum(noLimit ? "-" : String.valueOf(item.getFilterRegTimNum()));
        monitorStatFlowDataResp.setFilterCrowdNum(noLimit ? "-" : String.valueOf(item.getFilterCrowdNum()));
        monitorStatFlowDataResp.setFilterLabelNum(item.getFilterLabelNum());
        monitorStatFlowDataResp.setFilterExcludeNum(item.getFilterExcludeNum());
        monitorStatFlowDataResp.setPassNum(item.getPassNum());
        monitorStatFlowDataResp.setFlowControlNum(flowControlNum);
        monitorStatFlowDataResp.setGroupName(groupName);
        monitorStatFlowDataResp.setDispatchNum(dispatchNum);
        respList.add(monitorStatFlowDataResp);
    }


    List<AbsMonitorListResp> statOfflineStrategyFlowData(List<StatOfflineStrategyFlowDataEntity> dataEntityList, Long strategyId) {
        List<AbsMonitorListResp> respList = new ArrayList<>();
        dataEntityList.forEach(item -> {
            List<StatOfflineStrategyGroupDataEntity> strategyGroupDataEntityList = statOfflineStrategyGroupDataRepository.selectByStrategyIdAndFlowDataId(strategyId, item.getId());
            if (!CollectionUtils.isEmpty(strategyGroupDataEntityList)) {
                for (StatOfflineStrategyGroupDataEntity tmp : strategyGroupDataEntityList) {
                    MonitorStatOfflineFlowDataResp monitorStatFlowDataResp = new MonitorStatOfflineFlowDataResp();
                    monitorStatFlowDataResp.setBizDate(item.getBizDate());
                    monitorStatFlowDataResp.setCrowdRefreshTime(item.getCrowdRefreshTime());
                    monitorStatFlowDataResp.setCrowdUserNum(item.getCrowdUserNum());
                    monitorStatFlowDataResp.setGroupName(tmp.getGroupName());
                    monitorStatFlowDataResp.setGroupUserNum(tmp.getGroupUserNum());
                    monitorStatFlowDataResp.setFlowControlNum(tmp.getFlowControlNum());
                    monitorStatFlowDataResp.setFilterLabelNum(tmp.getFilterLabelNum());
                    monitorStatFlowDataResp.setFilterExcludeNum(tmp.getFilterExcludeNum());
                    monitorStatFlowDataResp.setDispatchNum(tmp.getDispatchNum());
                    respList.add(monitorStatFlowDataResp);
                }
            }
        });
        return respList;
    }

    @Override
    public PageResultResponse<AbsMonitorListResp> queryStrategyEngineMonitorList(MonitorEngineListReq request) {

        StrategyDo strategyDo = strategyRepository.selectById(request.getStrategyId());
        if (strategyDo == null) {
            throw new StrategyException("策略不存在");
        }
        List<AbsMonitorListResp> respList;

        if (StringUtils.isBlank(request.getStrategyGroupId())) {
            Page<StatStrategyEngineFlowDataEntity> records = strategyEngineFlowDataRepository.selectPageByStrategyId(request.getStrategyId(), request.getBeginNum(), request.getSize());
            if (CollectionUtils.isEmpty(records.getList())) {
                return null;
            }
            respList = covertToStrategyFlowDataResp(records.getList());
            return new PageResultResponse<>(respList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
        }
        Page<StatStrategyGroupDataEntity> groupRecords = statStrategyGroupDataRepository.selectPageByStrategyGroupId(request.getStrategyGroupId(), request.getMarketChannel(), request.getTemplateId(),request.getBeginNum(), request.getSize());
        if (CollectionUtils.isEmpty(groupRecords.getList())) {
            return null;
        }
        respList = covertToGroupFlowDataResp(groupRecords.getList());
        return new PageResultResponse<>(respList, groupRecords.getBeginNum(), groupRecords.getFetchNum(), groupRecords.getTotalNum());
    }

    private List<AbsMonitorListResp> covertToGroupFlowDataResp(List<StatStrategyGroupDataEntity> records) {
        List<AbsMonitorListResp> respList = new ArrayList<>();
        for (StatStrategyGroupDataEntity record : records) {
            respList.add(convertRes(record));
        }

        return respList;
    }

    public MonitorListGroupResp convertRes(StatStrategyGroupDataEntity statStrategyGroupData) {

        MonitorListGroupResp monitorListGroupResp = new MonitorListGroupResp();
        Integer status = statStrategyGroupData.getStatus();
        monitorListGroupResp.setBizDate(statStrategyGroupData.getBizDate());
        monitorListGroupResp.setIfEngine(Objects.equals(statStrategyGroupData.getIfIntoEngine(), 1));
        monitorListGroupResp.setGroupId(statStrategyGroupData.getEngineGroupId());
        monitorListGroupResp.setGroupSource(statStrategyGroupData.getGroupSource());
        if (statStrategyGroupData.getMarketChannel() != null) {
            monitorListGroupResp.setSendChannel(StrategyMarketChannelEnum.getInstance(statStrategyGroupData.getMarketChannel()).getDescription());
        }
        monitorListGroupResp.setTemplateId(statStrategyGroupData.getTemplateId());
        monitorListGroupResp.setStatus(status == 0 ? "执行中" : "已完成");
        monitorListGroupResp.setExecNum(statStrategyGroupData.getGroupCount());
        monitorListGroupResp.setTriggerNum(statStrategyGroupData.getExecCount());
        monitorListGroupResp.setPushNum(statStrategyGroupData.getSendCount());
        monitorListGroupResp.setReceiveNum(statStrategyGroupData.getReceiveCount());
        //麻雀发送率：渠道接收人数/麻雀推送人数
        String sendRate = "-";
        if (monitorListGroupResp.getReceiveNum() != null && !Objects.equals(monitorListGroupResp.getReceiveNum(), 0)
                && monitorListGroupResp.getPushNum() != null && !Objects.equals(monitorListGroupResp.getPushNum(), 0)) {
            BigDecimal rate = new BigDecimal(monitorListGroupResp.getReceiveNum()).multiply(new BigDecimal(100)).divide(new BigDecimal(monitorListGroupResp.getPushNum()), 2, RoundingMode.HALF_UP);
            sendRate = rate + "%";
        }
        monitorListGroupResp.setSendRate(sendRate);
        monitorListGroupResp.setChannelSuccNum(statStrategyGroupData.getSuccCount());
        //渠道触达成功率：渠道成功人数/渠道接收人数
        String channelSuccRate = "-";
        if (monitorListGroupResp.getChannelSuccNum() != null && !Objects.equals(monitorListGroupResp.getChannelSuccNum(), 0)
                && monitorListGroupResp.getReceiveNum() != null && !Objects.equals(monitorListGroupResp.getReceiveNum(), 0)) {
            BigDecimal rate = new BigDecimal(monitorListGroupResp.getChannelSuccNum()).multiply(new BigDecimal(100)).divide(new BigDecimal(monitorListGroupResp.getReceiveNum()), 2, RoundingMode.HALF_UP);
            channelSuccRate = rate + "%";
        }
        monitorListGroupResp.setChannelSuccRate(channelSuccRate);
        //营销触达成功率:渠道成功人数/麻雀推送人数
        String reachSuccRate = "-";
        if (monitorListGroupResp.getChannelSuccNum() != null && !Objects.equals(monitorListGroupResp.getChannelSuccNum(), 0)
                && monitorListGroupResp.getPushNum() != null && !Objects.equals(monitorListGroupResp.getPushNum(), 0)) {
            BigDecimal rate = new BigDecimal(monitorListGroupResp.getChannelSuccNum()).multiply(new BigDecimal(100)).divide(new BigDecimal(monitorListGroupResp.getPushNum()), 2, RoundingMode.HALF_UP);
            reachSuccRate = rate + "%";
        }
        monitorListGroupResp.setReachSuccRate(reachSuccRate);

        return monitorListGroupResp;
    }

    private List<AbsMonitorListResp> covertToStrategyFlowDataResp(List<StatStrategyEngineFlowDataEntity> records) {
        List<AbsMonitorListResp> respList = new ArrayList<>();
        for (StatStrategyEngineFlowDataEntity record : records) {
            MonitorStatRealtimeEngineFlowDataResp monitorStatRealtimeEngineFlowDataResp = new MonitorStatRealtimeEngineFlowDataResp();
            BeanUtils.copyProperties(record, monitorStatRealtimeEngineFlowDataResp);
            respList.add(monitorStatRealtimeEngineFlowDataResp);
        }
        return respList;
    }

    @Override
    public Integer countCrowdPackUserNum(CrowdPackUserNumReq crowdPackUserNumReq) {
        String crowdPackIdsStr = crowdPackUserNumReq.getCrowdPackId();
        strategyCommonService.verifyCrowIds(crowdPackIdsStr, crowdPackUserNumReq.getBusinessType());
        String[] crowdPackIds = crowdPackIdsStr.split(";");
        return crowdPackRepository.countCrowdPackUserNum(crowdPackIds);
    }

    @Override
    public PageResultResponse<CrowdPackListResp> getCrowdPackList(CrowdPackListReq crowdPackListReq) {
        List<CrowdPackDo> crowdPackDoList = crowdPackRepository.getCrowdPackList(crowdPackListReq);
        if (CollectionUtils.isEmpty(crowdPackDoList)) {
            return null;
        }
        List<CrowdPackListResp> crowdPackListResponse = new ArrayList<>();
        for (CrowdPackDo crowdPackDo : crowdPackDoList) {
            CrowdPackListResp crowdPackListResp = new CrowdPackListResp();
            crowdPackListResp.setId(crowdPackDo.getId());
            crowdPackListResp.setCrowdName(crowdPackDo.getCrowdName());
            crowdPackListResponse.add(crowdPackListResp);
        }

        // 新增洞察平台人群包
        if (ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_BUSINESS_LINE, crowdPackListReq.getBusinessType())) {
            List<CrowdInfoDo> crowdInfoDos = crowdInfoRepository.selectAllOnline();
            if (!CollectionUtils.isEmpty(crowdInfoDos)) {
                crowdInfoDos.stream()
                        .filter(x -> ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK, x.getCrowdId()))
                        .filter(x -> checkBusinessType(crowdPackListReq.getBusinessType(), x.getCrowdCategory()))
                        .forEach(x -> {
                            CrowdPackListResp crowdPackListResp = new CrowdPackListResp();
                            crowdPackListResp.setId(x.getCrowdId());
                            crowdPackListResp.setCrowdName("[洞察平台]" + x.getCrowdName());

                            crowdPackListResponse.add(crowdPackListResp);
                        });
            }
        }

        Page<CrowdPackListResp> records = new Page<>();
        records.setList(crowdPackListResponse);
        return new PageResultResponse(crowdPackListResponse, crowdPackListReq.getBeginNum(), crowdPackListReq.getSize(), crowdPackListResponse.size());
    }

    private boolean checkBusinessType(String businessType, String crowdCategory) {
        try {
            if (StringUtils.isAnyBlank(businessType, crowdCategory)) {
                return false;
            }

            String bizLineMappingConfig = ApolloUtil.getAppProperty(INSIGHT_CROWD_PACK_BIZ_LINE_MAPPING, "{}");
            Map<String, String> bizLineMapping = JSONObject.parseObject(bizLineMappingConfig, Map.class);

            String bizLine = bizLineMapping.getOrDefault(businessType, StringUtils.EMPTY);
            return  StringUtils.equals(bizLine, crowdCategory.split(",")[0]);
        } catch (Exception e) {
            log.error("StrategyServiceImpl checkBusinessType error businessType={}, crowdCategory={}", businessType, crowdCategory, e);
        }
        return false;
    }

    @Override
    public void exportMonitor(ExportMonitorReq exportMonitorReq, HttpServletResponse response) throws IOException {
        // 下个版本控制一下下载大小
        String fileName = "发送明细监控";
        response.setContentType("application/vnd.ms-excel; charset=utf-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8") + ".xlsx");
        StrategyDo strategyDo = strategyRepository.selectById(exportMonitorReq.getStrategyId());
        if (Objects.equals(-2, exportMonitorReq.getStrategyMarketChannel())) {
            if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler())) {
                List<StatRealtimeStrategyFlowDataEntity> dataEntityList = statRealtimeStrategyFlowDataRepository.selectByStrategyId(exportMonitorReq.getStrategyId());
                List<AbsMonitorListResp> monitorStatFlowDataRespList = this.statRealtimeStrategyFlowData(dataEntityList, strategyDo);
                EasyExcelFactory.write(response.getOutputStream(), MonitorStatRealtimeFlowDataResp.class).registerConverter(new LongStringConverter()).sheet(fileName).doWrite(monitorStatFlowDataRespList);
            } else {
                List<StatOfflineStrategyFlowDataEntity> dataEntityList = statOfflineStrategyFlowDataRepository.selectByStrategyId(exportMonitorReq.getStrategyId());
                List<AbsMonitorListResp> monitorStatFlowDataRespList = this.statOfflineStrategyFlowData(dataEntityList, exportMonitorReq.getStrategyId());
                EasyExcelFactory.write(response.getOutputStream(), MonitorStatOfflineFlowDataResp.class).registerConverter(new LongStringConverter()).sheet(fileName).doWrite(monitorStatFlowDataRespList);
            }
            return;
        }
        //各渠道监控数据
        List<StrategyExecLogDo> strategyExecLogDoList = strategyExecLogRepository.selectByStrategyIdAndChannel(exportMonitorReq.getStrategyId(), exportMonitorReq.getStrategyMarketChannel());
        List<AbsMonitorListResp> monitorListResp = this.covertToResp(strategyDo, strategyExecLogDoList, exportMonitorReq.getStrategyMarketChannel());
        boolean filterGroup = Objects.equals(StrategyMarketChannelEnum.FILTER.getCode(), exportMonitorReq.getStrategyMarketChannel());
        EasyExcelFactory.write(response.getOutputStream(), filterGroup ? MonitorListFilterGroupResp.class : monitorListFactory.createOpt(exportMonitorReq.getStrategyMarketChannel()).getClass()).registerConverter(new LongStringConverter()).sheet(fileName).doWrite(monitorListResp);
    }

    @Override
    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public boolean operate(StrategyOperateReq strategyOperateReq) {

        StrategyDo strategyDo = strategyRepository.selectById(strategyOperateReq.getStrategyId());
        if (strategyDo == null) {
            throw new StrategyException("策略不存在");
        }
        StrategyStatusEnum strategyStatusEnum = StrategyStatusEnum.getInstance(strategyDo.getStatus());
        StrategyOperateEnum operateEnum = StrategyOperateEnum.getInstance(strategyOperateReq.getRunType());
        if (operateEnum == null) {
            return false;
        }
        //收集操作对象id
        OperateLogObjectIdUtils.set(Collections.singletonList(strategyDo.getId()));

        List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyId(strategyOperateReq.getStrategyId());
        strategyMarketChannelDoList = strategyMarketChannelDoList.stream().filter(item -> !Objects.equals(0, item.getMarketChannel())).collect(Collectors.toList());
        switch (operateEnum) {
            case PAUSE:
                this.pause(strategyDo, strategyMarketChannelDoList, strategyStatusEnum);
                break;
            case ENABLE:
                this.start(strategyDo, strategyMarketChannelDoList, strategyStatusEnum);
                break;
            case RETRY:
                this.retry(strategyDo, strategyStatusEnum);
                break;
            case PUBLISH:
                // 离线策略
                if (!Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler())) {
                    if (strategyDo.getValidityEnd().isBefore(LocalDateTime.now())) {
                        throw new StrategyException("策略已经过期");
                    }
                    if (!Objects.equals(StrategyStatusEnum.DRAFT.getCode(), strategyDo.getStatus())) {
                        throw new StrategyException("策略未处于待发布状态，不可以发布");
                    }
                    strategyMarketChannelDoList.stream().filter(x -> null != x.getXxlJobId() && x.getXxlJobId() > 0).forEach(item -> xxlJobAdminClient.startJob(item.getXxlJobId()));
                    strategyDo.setStatus(StrategyStatusEnum.INIT.getCode());
                } else {
                    strategyCommonService.publish(strategyDo);
                }
                break;
            case DELETE:
                this.delete(strategyDo, strategyMarketChannelDoList, strategyStatusEnum);
                break;
            case RECOVER:
                this.recover(strategyDo);
                break;
            default:
                return false;
        }
        cacheStrategyService.updateById(strategyDo);
        changeService.asyncSubmitOperateStrategyChange(strategyOperateReq, strategyDo);
        return true;
    }

    @Override
    public String batchDelete(StrategyBatchDeleteReq strategyBatchDeleteReq) {
        List<Long> strategyIds = strategyBatchDeleteReq.getStrategyIds();
        List<StrategyDo> strategyDoList = strategyRepository.getByIds(strategyIds);

        if (!SsoUtil.get().getIfAdmin()) {
            List<Long> noOwnId = strategyDoList.stream().filter(crowdPackDo -> !SsoUtil.get().getName().equals(crowdPackDo.getUpdatedOp()))
                    .map(StrategyDo::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(noOwnId)) {
                throw new CrowdException(String.format("存在无权限操作策略,不可删除，策略id:%s", noOwnId));
            }
        }

        List<Long> canNotDeleteIds = strategyDoList.stream().filter(item ->
                        Objects.equals(StrategyStatusEnum.EXECUTING.getCode(), item.getStatus()) || Objects.equals(StrategyStatusEnum.INIT.getCode(), item.getStatus()))
                .map(StrategyDo::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(canNotDeleteIds)) {
            throw new StrategyException(String.format("存在执行中或已发布策略，不可删除，策略id:%s", canNotDeleteIds));
        }

        List<Integer> allFailXxlJobIds = new ArrayList<>();
        List<Long> failStrategyIds = new ArrayList<>();
        strategyDoList.forEach(item ->
        {
            if (StringUtils.isNotEmpty(item.getFlowNo())) {
                return;
            }
            List<Integer> oneStrategyFailXxlJobIds = new ArrayList<>();
            List<StrategyMarketChannelDo> strategyMarketChannelDoList = strategyMarketChannelRepository.selectByStrategyId(item.getId());
            strategyMarketChannelDoList.stream().filter(x -> !Objects.equals(0, x.getMarketChannel()) && null != x.getXxlJobId() && x.getXxlJobId() > 0)
                    .forEach(y -> {
                                try {
                                    xxlJobAdminClient.stopJob(y.getXxlJobId());
                                } catch (Exception e) {
                                    log.warn("策略编号{}：批量删除，暂停xxl-job异常", item.getId(), e);
                                    oneStrategyFailXxlJobIds.add(y.getXxlJobId());
                                }
                            }
                    );
            if (CollectionUtils.isEmpty(oneStrategyFailXxlJobIds)) {
                item.setStatus(StrategyStatusEnum.DEPRECATED.getCode());
                item.setUpdatedOp(SsoUtil.get().getName());
                item.setUpdatedTime(LocalDateTime.now());
            } else {
                failStrategyIds.add(item.getId());
                allFailXxlJobIds.addAll(oneStrategyFailXxlJobIds);
            }

        });
        cacheStrategyService.batchUpdate(strategyDoList);

        //收集操作对象id
        OperateLogObjectIdUtils.set(strategyIds);

        if (CollectionUtils.isEmpty(failStrategyIds) && CollectionUtils.isEmpty(allFailXxlJobIds)) {
            changeService.asyncSubmitDeleteStrategyChange(strategyBatchDeleteReq, strategyDoList);
            return "操作成功";
        } else {
            return String.format("存在关闭xxl-job任务的策略失败,策略:%s，xxl-job:%s", failStrategyIds, allFailXxlJobIds);
        }
    }

    void pause(StrategyDo strategyDo, List<StrategyMarketChannelDo> strategyMarketChannelDoList, StrategyStatusEnum strategyStatusEnum) {
        if (!Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler())) {
            if (strategyDo.getStatus() == StrategyStatusEnum.EXECUTING.getCode() || strategyDo.getStatus() == StrategyStatusEnum.ENDED.getCode()) {
                throw new StrategyException("策略处于" + strategyStatusEnum.getDescription() + "状态,不支持暂停操作");
            }
            strategyMarketChannelDoList.stream().filter(x -> null != x.getXxlJobId() && x.getXxlJobId() > 0).forEach(item -> xxlJobAdminClient.stopJob(item.getXxlJobId()));
        }
        strategyDo.setStatus(StrategyStatusEnum.PAUSING.getCode());
    }


    void delete(StrategyDo strategyDo, List<StrategyMarketChannelDo> strategyMarketChannelDoList, StrategyStatusEnum strategyStatusEnum) {
        if (!SsoUtil.get().getIfAdmin() && !SsoUtil.get().getName().equals(strategyDo.getUpdatedOp())) {
            throw new CrowdException("您没有权限删除该条记录");
        }
        if (Objects.equals(StrategyStatusEnum.EXECUTING.getCode(), strategyDo.getStatus()) || Objects.equals(StrategyStatusEnum.INIT.getCode(), strategyDo.getStatus())) {
            throw new StrategyException("策略处于" + strategyStatusEnum.getDescription() + "状态,不支持删除操作");
        }
        strategyMarketChannelDoList.stream().filter(x -> null != x.getXxlJobId() && x.getXxlJobId() > 0).forEach(item -> xxlJobAdminClient.stopJob(item.getXxlJobId()));

        strategyDo.setStatus(StrategyStatusEnum.DEPRECATED.getCode());
        strategyDo.setUpdatedOp(SsoUtil.get().getName());
        strategyDo.setUpdatedTime(LocalDateTime.now());
        log.info("策略删除操作,策略id:{},操作人:{}", strategyDo.getId(), strategyDo.getUpdatedOp());
    }

    void recover(StrategyDo strategyDo) {
        if (strategyDo.getValidityBegin().isAfter(LocalDateTime.now())) {
            strategyDo.setStatus(StrategyStatusEnum.DRAFT.getCode());
        } else if (strategyDo.getValidityEnd().isAfter(LocalDateTime.now())) {
            strategyDo.setStatus(StrategyStatusEnum.PAUSING.getCode());
        } else {
            strategyDo.setStatus(StrategyStatusEnum.ENDED.getCode());
        }
        strategyDo.setUpdatedOp(SsoUtil.get().getName());
        strategyDo.setUpdatedTime(LocalDateTime.now());
        log.info("策略恢复操作,策略id:{},操作人:{}", strategyDo.getId(), strategyDo.getUpdatedOp());
    }

    void start(StrategyDo strategyDo, List<StrategyMarketChannelDo> strategyMarketChannelDoList, StrategyStatusEnum strategyStatusEnum) {
        if (strategyDo.getStatus() != StrategyStatusEnum.PAUSING.getCode()) {
            throw new StrategyException("策略处于" + strategyStatusEnum.getDescription() + "状态,不支持开始操作");
        }

        //事件能暂停的都是执行中
        if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler())) {
            strategyDo.setStatus(StrategyStatusEnum.EXECUTING.getCode());
            List<StrategyMarketEventDo> strategyMarketEventDo = strategyMarketEventRepository.selectByStrategyId(strategyDo.getId());
            strategyMarketEventDo.forEach(t -> {
                strategyEventCatchService.resetEventTriggerTime(t.getEventName(), strategyDo);
                strategyEventCatchService.removeHasEventButNoMatchFlag(t.getEventName(), strategyDo.getId());
            });
            return;
        }

        strategyMarketChannelDoList.stream().filter(x -> x.getXxlJobId() != null && x.getXxlJobId() > 0).forEach(item -> xxlJobAdminClient.startJob(item.getXxlJobId()));
        int lastStatus;
        StrategyExecLogDo strategyExecLogDo = strategyExecLogRepository.selectLatestStatusByStrategyId(strategyDo.getId());
        if (strategyExecLogDo == null) {
            lastStatus = StrategyStatusEnum.INIT.getCode();
        } else if (strategyExecLogDo.getExecStatus() == StrategyExecStatusEnum.SUCCESS.getCode()) {
            lastStatus = StrategyStatusEnum.SUCCESS.getCode();
        } else if (strategyExecLogDo.getExecStatus() == StrategyExecStatusEnum.FAIL.getCode()) {
            lastStatus = StrategyStatusEnum.FAIL.getCode();
        } else {
            throw new StrategyException("策略处于执行中状态,不支持开始操作");
        }
        strategyDo.setStatus(lastStatus);
    }

    void retry(StrategyDo strategyDo, StrategyStatusEnum strategyStatusEnum) {
        if ((strategyDo.getStatus() != StrategyStatusEnum.FAIL.getCode())) {
            throw new StrategyException("策略处于" + strategyStatusEnum.getDescription() + "状态,不支持重试操作");
        }
        if ((strategyDo.getSendRuler() == StrategyRulerEnum.CYCLE_DAY.getCode())) {
            throw new StrategyException("周期策略不支持点击重试操作");
        }

        List<StrategyExecLogDo> list = strategyExecLogRepository.selectRetryList(strategyDo.getId());
        List<Long> idList = list.stream().map(StrategyExecLogDo::getStrategyMarketChannelId).collect(Collectors.toList());
        Map<Long, StrategyMarketChannelDo> map = strategyMarketChannelRepository.selectByIds(idList).stream()
                .collect(Collectors.toMap(StrategyMarketChannelDo::getId, Function.identity()));
        list = list.stream().filter(item -> map.containsKey(item.getStrategyMarketChannelId())).collect(Collectors.toList());

        // 存在重试ID的 执行重试任务
        list.stream().filter(item -> Objects.nonNull(item.getRetryId())).forEach(item -> {
            StrategyDispatchSmsRetryXxlJobParam jobParam = new StrategyDispatchSmsRetryXxlJobParam(item.getId());
            xxlJobAdminClient.triggerJob(item.getRetryId().intValue(), JSON.toJSONString(jobParam));
        });

        // 不存在重试ID的 执行正常任务
        list.stream().filter(item -> Objects.isNull(item.getRetryId())).forEach(item -> {
            StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository.selectById(item.getStrategyMarketChannelId());
            StrategyXxlJobParam strategyXxlJobParam = new StrategyXxlJobParam(strategyMarketChannelDo.getId());
            xxlJobAdminClient.triggerJob(strategyMarketChannelDo.getXxlJobId(), JSON.toJSONString(strategyXxlJobParam));
        });

        strategyDo.setStatus(StrategyStatusEnum.EXECUTING.getCode());
    }


    @Override
    public void verifyChannel(StrategyCreateReq.StrategyMarketChannel strategyMarketChannel) {
        if (strategyMarketChannel.getMarketChannel() != StrategyMarketChannelEnum.NONE.getCode()) {

            int marketChannel = strategyMarketChannel.getMarketChannel();
            if (StringUtils.isBlank(strategyMarketChannel.getTemplateId()) && marketChannel != StrategyMarketChannelEnum.VOICE_NEW.getCode()) {
                if (marketChannel != StrategyMarketChannelEnum.INCREASE_AMOUNT.getCode() &&
                        marketChannel != StrategyMarketChannelEnum.API_OPEN_AMOUNT.getCode()) {
                    throw new StrategyException("模板id不能为空");
                }
            }
            if (marketChannel != StrategyMarketChannelEnum.APP_BANNER.getCode() &&
                    strategyMarketChannel.getSendTime() == null) {
                throw new StrategyException("发送时间不能为空");
            }
        }
    }


    void verifySendTime(StrategyCreateReq.SendFrequency sendFrequency, List<StrategyCreateReq.StrategyGroup> strategyGroupList) {
        List<StrategyCreateReq.StrategyMarketChannel> channelList = new ArrayList<>();
        strategyGroupList.stream().filter(item -> !CollectionUtils.isEmpty(item.getStrategyMarketChannels())).forEach(item ->
                channelList.addAll(item.getStrategyMarketChannels()));
        List<LocalTime> sendTimeList = channelList.stream().map(StrategyCreateReq.StrategyMarketChannel::getSendTime).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> dateValues = sendFrequency.getValue();
        Integer week = null;
        // 周期验证
        if (sendFrequency.getType() == StrategyFrequencyEnum.CYCLE_DAY.getCode()) {
            if (!CollectionUtils.isEmpty(sendFrequency.getValue())
                    && sendFrequency.getValue().get(0) <= 0) {
                throw new StrategyException(1001, "循环周期需要大于0");
            }
        }
        // 每周
        if (sendFrequency.getType() == StrategyFrequencyEnum.EVERY_WEEK.getCode()) {
            week = LocalDateTime.now().getDayOfWeek().getValue() + 1;
            if (week == 8) {
                week = 1;
            }
        }
        // 每月
        if (sendFrequency.getType() == StrategyFrequencyEnum.EVERY_MONTH.getCode()) {
            week = LocalDate.now().getDayOfMonth();
        }
        if (CollectionUtils.isEmpty(dateValues) || dateValues.contains(week)) {
            if (CollectionUtils.isEmpty(sendTimeList)) {
                return;
            }
            boolean flag = sendTimeList.stream().anyMatch(item -> LocalTime.now().isBefore(item) && Duration.between(LocalTime.now(), item).toMinutes() <= 10);
            if (flag) {
                throw new StrategyException(1001, "发送时间与当前时间间隔较近");
            }
        }

    }


    public List<AbsMonitorListResp> covertToResp(StrategyDo strategyDo, List<StrategyExecLogDo> strategyExecLogDoList, Integer marketChannel) {
        if (strategyExecLogDoList.isEmpty()) {
            return Collections.emptyList();
        }
        //事件留白组 （页面上已删除）
        if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler()) && marketChannel.equals(StrategyMarketChannelEnum.NONE.getCode())) {
            MonitorNoMarketGroupResp noMarketGroupResp = new MonitorNoMarketGroupResp();
            strategyExecLogDoList.stream().filter(item -> item.getExecTime().toLocalDate().compareTo(LocalDate.now()) == 0).forEach(t -> {
                String today = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
                String execCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_EXEC_COUNT, today, t.getStrategyGroupId(), t.getStrategyMarketChannel());
                t.setExecCount(Convert.toInt(redisUtils.get(execCountKey), t.getExecCount()));
            });
            return strategyExecLogDoList.stream().map(noMarketGroupResp::convertRes).collect(Collectors.toList());
        }

        AbsMonitorListRespFinal monitorListRespFinal = monitorListFactory.createOpt(marketChannel);
        //当日的数据读redis
        if (Objects.equals(StrategyRulerEnum.EVENT.getCode(), strategyDo.getSendRuler()) && strategyConfig.getMonitorUpdateFromCache() == 1) {
            this.getTodayCountFromCache(strategyExecLogDoList, marketChannel, strategyDo);
        }
        return strategyExecLogDoList.stream().map(item -> monitorListRespFinal.convertRes(strategyDo, item)).collect(Collectors.toList());
    }

    void getTodayCountFromCache(List<StrategyExecLogDo> strategyExecLogDoList, Integer marketChannel, StrategyDo strategyDo) {
        strategyExecLogDoList.stream().filter(item -> item.getExecTime().toLocalDate().compareTo(LocalDate.now()) == 0).forEach(t -> {
            String today = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
            String sendCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_SEND_COUNT, today, t.getStrategyGroupId(), t.getStrategyMarketChannel());
            String execCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_EXEC_COUNT, today, t.getStrategyGroupId(), t.getStrategyMarketChannel());
            String succCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_SUCCESS_COUNT, today, strategyDo.getId(), t.getStrategyMarketChannel(), t.getStrategyGroupId());
            String failCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_FAIL_COUNT, today, strategyDo.getId(), t.getStrategyMarketChannel(), t.getStrategyGroupId());
            t.setSendCount(Convert.toInt(redisUtils.get(sendCountKey), t.getSendCount()));
            t.setExecCount(Convert.toInt(redisUtils.get(execCountKey), t.getExecCount()));
            t.setSuccCount(Convert.toInt(redisUtils.get(succCountKey), t.getSuccCount()));
            int failCount = Convert.toInt(redisUtils.get(failCountKey), 0);

            boolean succOrFailNotNull = Objects.nonNull(redisUtils.get(succCountKey)) && Objects.nonNull(redisUtils.get(failCountKey));
            if (succOrFailNotNull && marketChannel.equals(StrategyMarketChannelEnum.SMS.getCode())) {
                t.setSupplierCount(t.getSendCount());
                t.setActualCount(t.getSendCount());
            }
            if (succOrFailNotNull && (marketChannel.equals(StrategyMarketChannelEnum.VOICE.getCode()) || marketChannel.equals(StrategyMarketChannelEnum.VOICE_NEW.getCode()))) {
                t.setReceiveCount(t.getSuccCount() + failCount);
            }
            if (succOrFailNotNull && marketChannel.equals(StrategyMarketChannelEnum.SALE_TICKET.getCode())) {
                t.setActualCount(t.getSuccCount() + failCount);
            }
            //优惠券的就更新使用数
            if (marketChannel.equals(StrategyMarketChannelEnum.SALE_TICKET.getCode())) {
                String useCountKey = String.format(RedisKeyConstants.EVENT_CHANNEL_EXEC_LOG_USED_COUNT, today, strategyDo.getId(), t.getStrategyMarketChannel(), t.getStrategyGroupId());
                t.setUsedCount(Convert.toInt(redisUtils.get(useCountKey), t.getUsedCount()));
            }
        });
    }

    /**
     * 更新策略状态
     */
    @Override
    public void refreshStatus() {
        AtomicReference<List<StrategyDo>> failExpireList = new AtomicReference<>();
        boolean transactional = TransactionUtil.transactional(() -> {
            failExpireList.set(strategyRepository.getBatchStrategy().stream().filter(this::filter).collect(Collectors.toList()));

            failExpireList.get().forEach(strategyDo -> {
                strategyDo.setStatus(StrategyStatusEnum.ENDED.getCode());
                strategyDo.setUpdatedTime(LocalDateTime.now());
            });

            failExpireList.get().forEach(strategyDo -> {
                cacheStrategyService.updateById(strategyDo);
                cacheFlowCtrlSerivce.closeByStrategyId(strategyDo.getId());
            });
        });

        if (transactional) {
            // 关闭策略任务
            failExpireList.get().stream().map(item -> strategyMarketChannelRepository.selectByStrategyId(item.getId()))
                    .forEach(marketChannelDoList -> marketChannelDoList.stream()
                            .map(StrategyMarketChannelDo::getXxlJobId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .forEach(this::stopXxlJob)
                    );

            // 关闭重试任务
            failExpireList.get().stream().map(item -> strategyExecLogRepository.selectRetryList(item.getId()))
                    .forEach(strategyExecLogDos -> strategyExecLogDos.stream()
                            .map(StrategyExecLogDo::getRetryId)
                            .map(Convert::toInt)
                            .filter(Objects::nonNull)
                            .distinct()
                            .forEach(this::stopXxlJob)
                    );

            // 告警：此策略已结束
            failExpireList.get().forEach(this::strategyEndAlarm);
        }
    }

    private void stopXxlJob(Integer xxlJobId) {
        try {
            if (xxlJobId == null) {
                return;
            }
            xxlJobAdminClient.stopJob(xxlJobId);
        } catch (Exception e) {
            log.warn("关闭xxl任务异常，任务ID：{}", xxlJobId, e);
        }
    }

    /**
     * 暂停/失败/成功并且已过期
     */
    private boolean filter(StrategyDo strategyDo) {
        List<StrategyStatusEnum> list = Arrays.asList(StrategyStatusEnum.INIT, StrategyStatusEnum.PAUSING, StrategyStatusEnum.FAIL, StrategyStatusEnum.SUCCESS);
        return (Objects.nonNull(strategyDo.getStatus()) && Objects.nonNull(strategyDo.getValidityEnd())) &&
                list.contains(StrategyStatusEnum.getInstance(strategyDo.getStatus())) &&
                LocalDateTime.now().isAfter(strategyDo.getValidityEnd());
    }

    @Override
    public void strategyEvent30MinAlarm() {
        List<StrategyDo> eventStrategy = strategyRepository.getEventStrategy();
        Map<Integer, List<StrategyDo>> strategyMap = eventStrategy.stream().collect(Collectors.groupingBy(StrategyDo::getStatus));
        List<StrategyDo> list = strategyMap.get(StrategyStatusEnum.EXECUTING.getCode());
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().filter(strategyDo -> {
                LocalDateTime validityBegin = strategyDo.getValidityBegin();
                LocalDateTime validityEnd = strategyDo.getValidityEnd();
                LocalDateTime now = LocalDateTime.now();
                return now.compareTo(validityBegin) >= 0 && now.compareTo(validityEnd) <= 0;
            }).forEach(strategyDo -> {
                List<StrategyMarketEventDo> marketEventDo = strategyMarketEventRepository.selectByStrategyId(strategyDo.getId());
                marketEventDo.forEach(t -> {
                    strategyEventCatchService.noEventAlarm(t.getEventName(), strategyDo);

                    if (StringUtils.isBlank(strategyDo.getCrowdPackId())) {
                        strategyEventCatchService.hasEventButNoMatchAlarm(t.getEventName(), strategyDo);
                        return;
                    }

                    List<CrowdPackDo> crowdPackDoList = crowdPackRepository.selectByIds(strategyDo.getCrowdPackId().split(";"));
                    List<CrowdPackDo> crowdPackDoList1 = crowdPackDoList.stream()
                            .filter(crowdPackDo -> crowdPackDo.getStatus() == CrowdStatusEnum.SUCCESS.getCode() || crowdPackDo.getStatus() == CrowdStatusEnum.ENDED.getCode())
                            .collect(Collectors.toList());

                    if (crowdPackDoList.size() == crowdPackDoList1.size()) {
                        LocalDateTime dateTime = crowdExecLogRepository.selectMaxRefreshSuccessTimeByCrowdIds(LocalDate.now(), strategyDo.getCrowdPackId().split(";"));
                        if (Objects.nonNull(dateTime) && Duration.between(dateTime, LocalDateTime.now()).toMinutes() >= 30) {
                            strategyEventCatchService.hasEventButNoMatchAlarm(t.getEventName(), strategyDo);
                        }
                    }
                });

            });
        }
    }


    @Override
    public void countTodayDispatch() {
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        String tableNameNo = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
        // 查询触达明细表当日触达策略id
        List<UserDispatchDetailDo> userDispatchDetailDoList = userDispatchDetailRepository.selectTodayDispatchStrategy(tableNameNo, startTime, endTime);
        userDispatchDetailDoList.forEach(userDispatchDetailDo -> {
            // 查询当日触达人数
            Integer totalCount = userDispatchDetailRepository.getCount(tableNameNo, userDispatchDetailDo.getStrategyId(), startTime, endTime, null);
            redisUtils.set(this.genRedisKey(userDispatchDetailDo.getStrategyId(), startTime, "all"), totalCount, 30 * 60);
            // 查询当日成功人数
            Integer succCount = userDispatchDetailRepository.getCount(tableNameNo, userDispatchDetailDo.getStrategyId(), startTime, endTime, 1);
            redisUtils.set(this.genRedisKey(userDispatchDetailDo.getStrategyId(), startTime, "suc"), succCount, 30 * 60);

            log.info("策略id：{}，当日触达人数：{}，成功人数：{}", userDispatchDetailDo.getStrategyId(), totalCount, succCount);
        });
    }

    @Override
    public void offlineStrategyCrowdStatusAlarm() {
        LocalDateTime now = LocalDateTime.now();
        List<StrategyDo> list = strategyRepository.offlineStrategyCrowdStatusAlarm(now.toLocalTime().plusMinutes(20), strategyConfig.getOfflineStrategyCrowdStatusAlarmInterval())
                .stream()
                .filter(currExec(now.toLocalDate()))
                .collect(Collectors.toList());

        list.parallelStream().forEach(strategyDo -> {
            String offlineStrategyCrowdStatusAlarmKey = RedisKeyUtils.genOfflineStrategyCrowdStatusAlarmKey(strategyDo.getId());
            String alarmTimeStr = redisUtils.get(offlineStrategyCrowdStatusAlarmKey);
            if (StringUtils.isNotBlank(alarmTimeStr)) {
                log.warn("离线策略要用的人群包未更新完报警-当天已告警，策略ID：{}，告警时间：{}", strategyDo.getId(), alarmTimeStr);
                return;
            }

            AtomicReference<String> msg = new AtomicReference<>("");
            if (StringUtils.isEmpty(strategyDo.getCrowdPackId())) {
                return;
            }
            crowdPackRepository.selectByIds(strategyDo.getCrowdPackId().split(";"))
                    .stream()
                    .filter(crowdPackDo -> crowdPackDo.getStatus() != CrowdStatusEnum.SUCCESS.getCode() && crowdPackDo.getStatus() != CrowdStatusEnum.ENDED.getCode())
                    .findFirst().ifPresent(crowdPackDo -> {
                        msg.updateAndGet(v -> String.format("此策略所需人群包还未刷新完，建议将策略执行时间延后30分钟。人群包编号：%s，人群包名称：%s", crowdPackDo.getId(), crowdPackDo.getCrowdName()));
                    });
            if (StringUtils.isNotEmpty(msg.get())) {
                strategyDo.alarmDingTalk(dingTalkConfig.getAlarmUrl(), Collections.singletonList(strategyDo.getUpdatedOpMobile()), msg.get(), null);

                Duration duration = Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)));
                redisUtils.set(offlineStrategyCrowdStatusAlarmKey, LocalDateTimeUtil.format(LocalDateTime.now(), TimeFormat.DATE_TIME), duration.getSeconds());
            }
        });
    }

    @Override
    public void strategyCrowdPackExpireAlarm() {
        LocalDate today = LocalDate.now();
        List<StrategyDo> allNotExpired = strategyRepository.getAllNotExpired();
        allNotExpired.stream()
                .filter(strategyDo -> StringUtils.isNotBlank(strategyDo.getCrowdPackId()))
                .filter(strategyDo -> StrategyStatusEnum.getInstance(strategyDo.getStatus()) != StrategyStatusEnum.PAUSING)
                .forEach(strategyDo -> {
                    StrategyRulerEnum strategyRulerEnum = StrategyRulerEnum.getInstance(strategyDo.getSendRuler());
                    if (strategyRulerEnum != StrategyRulerEnum.EVENT && !this.currExec(today).test(strategyDo)) {
                        return;
                    }

                    List<CrowdPackDo> crowdPackDoList = crowdPackRepository.selectByIds(strategyDo.getCrowdPackId().split(";"));
                    crowdPackDoList.forEach(crowdPackDo -> {
                        CrowdFilterMethodEnum filterMethodEnum = CrowdFilterMethodEnum.getInstance(crowdPackDo.getFilterMethod());
                        if (filterMethodEnum == CrowdFilterMethodEnum.LABEL && crowdPackDo.getValidityEnd().compareTo(LocalDateTime.now()) < 0) {
                            String msg = String.format("此策略所用人群包已过期，请修改策略。人群包编号：%s，人群包名称：%s", crowdPackDo.getId(), crowdPackDo.getCrowdName());
                            strategyDo.alarmDingTalk(dingTalkConfig.getAlarmUrl(), Collections.singletonList(strategyDo.getUpdatedOpMobile()), msg, null);
                        }
                        if (filterMethodEnum == CrowdFilterMethodEnum.LABEL && crowdPackDo.getValidityEnd().compareTo(LocalDateTime.now().plusDays(14)) < 0
                                && crowdPackDo.getValidityEnd().compareTo(LocalDateTime.now()) > 0) {
                            long expireDays = ChronoUnit.DAYS.between(LocalDateTime.now(), crowdPackDo.getValidityEnd());
                            String msg = String.format("此策略所用人群包%d天后即将过期，请修改策略。人群包编号：%s，人群包名称：%s", expireDays, crowdPackDo.getId(), crowdPackDo.getCrowdName());
                            strategyDo.alarmDingTalk(dingTalkConfig.getAlarmUrl(), Collections.singletonList(strategyDo.getUpdatedOpMobile()), msg, null);
                        }
                    });
                });
        // 14天内策略即将过期告警
        allNotExpired.stream().filter(strategyDo -> LocalDateTime.now().isAfter(strategyDo.getValidityEnd().minusDays(14))
                && LocalDateTime.now().isAfter(strategyDo.getValidityEnd()))
                .forEach(this::strategy14DayEndAlarm);
    }

    private Predicate<StrategyDo> currExec(LocalDate now) {
        return strategyDo -> JSON.parseObject(strategyDo.getSendFrequency(), SendFrequencyConfig.class).isExecute(now);
    }

    /**
     * 策略过期告警
     *
     * @param strategyDo 策略
     */
    @Override
    public void strategyEndAlarm(StrategyDo strategyDo) {
        String key = RedisKeyUtils.genStrategyEndAlarmKey(strategyDo.getId());
        Duration duration = Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)));
        if (redisUtils.lock(key, LocalDateTimeUtil.format(LocalDateTime.now(), TimeFormat.DATE_TIME), duration.getSeconds())) {
            List<String> atMobiles = Collections.singletonList(strategyDo.getUpdatedOpMobile());
            strategyDo.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobiles, "此策略已结束", null);
        }
    }

    /**
     * 通知找不到有效用户，需要使用默认通知用户
     * @param strategyDo
     */
    @Override
    public void strategy14DayEndAlarm(StrategyDo strategyDo) {
        String key = RedisKeyUtils.genStrategyEndAlarmKey(strategyDo.getId());
        Duration duration = Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)));
        if (redisUtils.lock(key, LocalDateTimeUtil.format(LocalDateTime.now(), TimeFormat.DATE_TIME), duration.getSeconds())) {
            List<String> atMobiles = Collections.singletonList(strategyDo.getUpdatedOpMobile());
            long expireDays = ChronoUnit.DAYS.between(LocalDateTime.now(), strategyDo.getValidityEnd());
            String msg = String.format("此策略%d天内即将结束，请及时延长有效时间", expireDays);
            strategyDo.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobiles, msg, null);
        }
    }


    @Override
    public PageResultResponse<com.xftech.cdp.api.dto.resp.external.StrategyListResp> getStrategyList(com.xftech.cdp.api.dto.req.external.StrategyListReq args) {
        List<StrategyStatusEnum> statusList = null;
        if ( !CollectionUtils.isEmpty(args.getStrategyStatusList())) {
            statusList = new ArrayList<>(args.getStrategyStatusList().size());
            for (Integer reqStatus : args.getStrategyStatusList()) {
                statusList.add(StrategyStatusEnum.getInstance(reqStatus));
            }
        } else {

            if (args.getCallingSource().equals(CallingSourceEnum.Overloan)) {
                statusList = Arrays.asList(
                        StrategyStatusEnum.INIT,
                        StrategyStatusEnum.EXECUTING,
                        StrategyStatusEnum.SUCCESS,
                        StrategyStatusEnum.PAUSING,
                        StrategyStatusEnum.FAIL);
            } else {
                statusList = Arrays.asList(
                        StrategyStatusEnum.EXECUTING,
                        StrategyStatusEnum.SUCCESS,
                        StrategyStatusEnum.PAUSING,
                        StrategyStatusEnum.DRAFT);
            }
        }

        Integer pageNum = args.getPageNum();
        Integer pageSize = args.getPageSize();
        Page<StrategyDo> page = strategyRepository.selectPageByStatusAndCallingSource(statusList,
                args.getStrategyId(), args.getBusinessType(), args.getCallingSource(), pageNum, pageSize);
        List<com.xftech.cdp.api.dto.resp.external.StrategyListResp> respList = page.getList().stream().map(strategyDo -> {
            com.xftech.cdp.api.dto.resp.external.StrategyListResp resp = new com.xftech.cdp.api.dto.resp.external.StrategyListResp();
            resp.setBusinessType(strategyDo.getBusinessType());
            resp.setStrategyId(strategyDo.getId());
            resp.setStrategyType(strategyDo.getSendRuler());
            resp.setStrategyName(strategyDo.getName());
            resp.setStrategyStatus(strategyDo.getStatus());
            resp.setValidityEndTime(strategyDo.getValidityEnd());
            resp.setUpdateOp(strategyDo.getUpdatedOp());
            resp.setUpdatedTime(strategyDo.getUpdatedTime());
            return resp;
        }).collect(Collectors.toList());
        return new PageResultResponse<>(respList, page.getBeginNum(), page.getFetchNum(), page.getTotalNum());
    }

    @Override
    public List<StrategyDo> getStrategyList(List<Integer> sendRulerList, List<Integer> statusList) {
        return strategyRepository.getStrategyList(sendRulerList, statusList);
    }

    @Override
    public List<StrategyDo> getAllStrategyList(List<Integer> sendRulerList, List<Integer> statusList) {
        return strategyRepository.getAllStrategyList(sendRulerList, statusList);
    }

    @Override
    public List<CyclePreviewDto> cyclePreview(Integer cycleNum) {
        StrategyCycleDayConfig strategyCycleDayConfig = appConfigService.getStrategyCycleDayConfig();
        if (cycleNum == null || cycleNum <= 0) {
            return new ArrayList<>(0);
        }
        if (strategyCycleDayConfig == null
                || strategyCycleDayConfig.getDigits() == null) {
            return new ArrayList<>(0);
        }
        int index = 0;
        String format = "%0" + strategyCycleDayConfig.getDigits() + "d";
        List<CyclePreviewDto> ret = new ArrayList<>(0);
        int num = (int) Math.pow(10, strategyCycleDayConfig.getDigits());
        int step = num / cycleNum;
        int mod = num % cycleNum;
        for (int i = 1; i <= cycleNum; i++) {
            CyclePreviewDto cyclePreviewDto = CyclePreviewDto.builder()
                    .order(i).build();
            cyclePreviewDto.setOrder(i);
            CyclePreviewDto.Range range = CyclePreviewDto.Range.builder()
                    .begin(String.format(format, index)).build();
            index = index + step;
            if (cycleNum - mod < i) {
                index++;
            }
            range.setEnd(String.format(format, index - 1));
            cyclePreviewDto.setRange(range);
            ret.add(cyclePreviewDto);
        }
        return ret;
    }

    @Override
    public PageResultResponse<NameTypeResp> getNameTypeList(NameTypeReq nameTypeReq) {
        PageResultResponse<NameTypeResp> result = new PageResultResponse<>();
        TeleNameTypeArgs req = new TeleNameTypeArgs(nameTypeReq);
        TeleNameTypeResp resp = telemarketingClient.getNameType(req);
        if (Objects.nonNull(resp) && Objects.nonNull(resp.getResponse())) {
            result.setCurrent(resp.getResponse().getCurrentPage());
            result.setSize(resp.getResponse().getPageSize());
            result.setPages(resp.getResponse().getTotalPage());
            result.setTotal(resp.getResponse().getTotal());
            if (!CollectionUtils.isEmpty(resp.getResponse().getList())) {
                List<TeleNameTypeResp.ResponseData.Item> list = resp.getResponse().getList();
                result.setRecords(list.stream().map(t -> new NameTypeResp(t.getId(), t.getTypeName(), t.getBusinessType())).collect(Collectors.toList()));
            }
            ;
        }
        return result;
    }

    @Override
    public NameTypeConfigResp getNameTypeConfigList() {
        TeleNameConfigResp nameConfig = telemarketingClient.getNameConfig();
        NameTypeConfigResp result = new NameTypeConfigResp();

        if (nameConfig != null && nameConfig.getResponse() != null) {
            TeleNameConfigResp.ResponseData responseData = nameConfig.getResponse();

            List<NameTypeConfigResp.Config> policyType = mapToConfigList(responseData.getPolicyType());
            List<NameTypeConfigResp.Config> judgeMethod = mapToConfigList(responseData.getJudgeMethod());
            List<NameTypeConfigResp.Config> closeCaseEvent = mapToConfigList(responseData.getCloseCaseEvent());

            result.setPolicyType(policyType);
            result.setJudgeMethod(judgeMethod);
            result.setCloseCaseEvent(closeCaseEvent);
        }

        return result;
    }

    @Override
    public StrategyReportDailyResp queryReportDailyList() {
        StrategyReportDailyResp strategyReportDailyResp = new StrategyReportDailyResp();

        ReportDailyTaskDo taskDo = reportDailyTaskService.selectTodayRecord(ReportDailyTypeEnum.STRATEGY.getCode());
        if (taskDo == null) {
            return null;
        }
        strategyReportDailyResp.setRefreshTime(taskDo.getRefreshTime());

        //查出所有离线，有效，未删除策略id
        List<Integer> sendRulerList = Arrays.asList(StrategyRulerEnum.CYCLE.getCode(), StrategyRulerEnum.CYCLE_DAY.getCode());
        List<Integer> statusList = StrategyStatusEnum.getActiveCodes();
        List<StrategyDo> strategyDoList = getStrategyList(sendRulerList, statusList)
                .stream().filter(x -> StringUtils.isEmpty(x.getFlowNo())).collect(Collectors.toList());
        List<Long> strategyIds = strategyDoList.stream()
                .filter(item -> item.getValidityEnd().isAfter(LocalDateTime.now()) && item.getValidityBegin().isBefore(LocalDateTime.now()))
                .map(StrategyDo::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(strategyIds)) {
            //总策略数
            strategyReportDailyResp.setStrategySum(0);
            //总渠道数
            strategyReportDailyResp.setChannelSum(0);
            strategyReportDailyResp.setSuccessStrategyCount(0);
            strategyReportDailyResp.setSuccessChannelCount(0);
            strategyReportDailyResp.setFailChannelCount(0);
            strategyReportDailyResp.setDetailList(null);
            return strategyReportDailyResp;
        }

        List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectByStrategyIdList(strategyIds);
        //总策略数
        strategyReportDailyResp.setStrategySum(strategyIds.size());
        //总渠道数
        strategyReportDailyResp.setChannelSum(strategyMarketChannelDos.size());

        //查询当天统计数据
        List<ReportDailyStrategyDo> reportDailyStrategyDoList = reportDailyStrategyService.selectTodayList();

        //当天完成
        List<ReportDailyStrategyDo> successReportList = reportDailyStrategyDoList.stream().filter(item -> Objects.equals(StrategyExecStatusEnum.SUCCESS.getCode(), item.getExecStatus())).collect(Collectors.toList());
        List<Long> successStrategyIds = successReportList.stream().map(ReportDailyStrategyDo::getStrategyId).distinct().collect(Collectors.toList());
        strategyReportDailyResp.setSuccessStrategyCount(successStrategyIds.size());
        strategyReportDailyResp.setSuccessChannelCount(successReportList.size());
        //当天失败
        List<ReportDailyStrategyDo> failReportList = reportDailyStrategyDoList.stream().filter(item -> Objects.equals(StrategyExecStatusEnum.FAIL.getCode(), item.getExecStatus())).collect(Collectors.toList());
        strategyReportDailyResp.setFailChannelCount(failReportList.size());

        //记录排序
        strategyReportDailyResp.setDetailList(sortAndTransFromReportDailyResp(reportDailyStrategyDoList));

        return strategyReportDailyResp;
    }

    private List<StrategyReportDailyDetailResp> sortAndTransFromReportDailyResp(List<ReportDailyStrategyDo> reportDailyStrategyDoList) {
        List<StrategyReportDailyDetailResp> respList = new ArrayList<>();
        List<Long> failStrategyReport = reportDailyStrategyDoList.stream().filter(item -> Objects.equals(StrategyExecStatusEnum.FAIL.getCode(), item.getExecStatus()))
                .map(ReportDailyStrategyDo::getStrategyId).distinct().collect(Collectors.toList());
        List<Long> successStrategyReport = reportDailyStrategyDoList.stream().filter(item -> Objects.equals(StrategyExecStatusEnum.SUCCESS.getCode(), item.getExecStatus()))
                .map(ReportDailyStrategyDo::getStrategyId).distinct().collect(Collectors.toList());
        List<Long> executingStrategyReport = reportDailyStrategyDoList.stream().filter(item -> Objects.equals(StrategyExecStatusEnum.EXECUTING.getCode(), item.getExecStatus()))
                .map(ReportDailyStrategyDo::getStrategyId).distinct().collect(Collectors.toList());
        List<Long> finishedStrategyReport = reportDailyStrategyDoList.stream().filter(item -> Objects.equals(StrategyExecStatusEnum.FINISHED.getCode(), item.getExecStatus()))
                .map(ReportDailyStrategyDo::getStrategyId).distinct().collect(Collectors.toList());

        Set<Long> sortedStrategy = new HashSet<>();
        sortedStrategy.addAll(failStrategyReport);
        sortedStrategy.addAll(successStrategyReport);
        sortedStrategy.addAll(executingStrategyReport);
        sortedStrategy.addAll(finishedStrategyReport);

        Map<Long, List<ReportDailyStrategyDo>> strategyIdToInfo = reportDailyStrategyDoList.stream().collect(Collectors.groupingBy(ReportDailyStrategyDo::getStrategyId));

        for (Long strategyId : sortedStrategy) {
            List<ReportDailyStrategyDo> strategyChannelList = strategyIdToInfo.get(strategyId);
            if (!CollectionUtils.isEmpty(strategyChannelList)) {
                try {
                    respList.add(transformReportResp(strategyChannelList));
                } catch (Exception e) {
                    log.error(String.format("统计策略执行情况失败: strategyId:%s", strategyId), e);
                }
            }
        }

        return respList;
    }

    private StrategyReportDailyDetailResp transformReportResp(List<ReportDailyStrategyDo> reportDailyStrategyDoList) {
        StrategyReportDailyDetailResp strategyReportDailyDetailResp = new StrategyReportDailyDetailResp();
        strategyReportDailyDetailResp.setStrategyId(reportDailyStrategyDoList.get(0).getStrategyId());
        strategyReportDailyDetailResp.setStrategyName(reportDailyStrategyDoList.get(0).getStrategyName());

        Map<String, List<ReportDailyStrategyDo>> strategyGroupToInfo = reportDailyStrategyDoList.stream().collect(Collectors.groupingBy(ReportDailyStrategyDo::getStrategyGroupName));
        StringBuffer result = new StringBuffer();
        //按组名排序
        for (String groupName : strategyGroupToInfo.keySet().stream().sorted().collect(Collectors.toList())) {
            result.append("分组").append(groupName).append(":");
            List<ReportDailyStrategyDo> channelReportList = strategyGroupToInfo.get(groupName);
            for (ReportDailyStrategyDo reportDailyStrategyDo : channelReportList) {
                StrategyExecStatusEnum statusEnum = StrategyExecStatusEnum.getInstance(reportDailyStrategyDo.getExecStatus());
                result.append("<br />营销渠道:").append(StrategyMarketChannelEnum.getInstance(reportDailyStrategyDo.getStrategyMarketChannel()).getDescription());
                result.append("<br />渠道ID:").append(reportDailyStrategyDo.getStrategyMarketChannelId())
                        .append(",执行开始时间:").append(LocalDateTimeUtil.format(reportDailyStrategyDo.getExecStartTime(), "yyyy-MM-dd HH:mm:ss"))
                        .append(",执行结束时间:").append(LocalDateTimeUtil.format(reportDailyStrategyDo.getExecEndTime(), "yyyy-MM-dd HH:mm:ss"))
                        .append(",耗时:").append(Duration.between(reportDailyStrategyDo.getExecStartTime(), reportDailyStrategyDo.getExecEndTime()).toMillis() / 100)
                        .append(",状态:").append(statusEnum != null ? statusEnum.getDescription() : "未知")
                        .append(",发送数量:").append(reportDailyStrategyDo.getSendCount())
                        .append(",回执成功数:").append(reportDailyStrategyDo.getSuccCount());
                if (reportDailyStrategyDo.getSendCount() != null && reportDailyStrategyDo.getSuccCount() != null && reportDailyStrategyDo.getSendCount() != 0) {
                    result.append(",成功率:").append(new BigDecimal(reportDailyStrategyDo.getSuccCount()).multiply(new BigDecimal(100)).divide(new BigDecimal(reportDailyStrategyDo.getSendCount()), 2, RoundingMode.HALF_UP)).append("%");
                } else {
                    result.append(",成功率:--");
                }
                if (StringUtils.isNotBlank(reportDailyStrategyDo.getFailReason())) {
                    result.append(",备注:").append(reportDailyStrategyDo.getFailReason());
                }
                result.append("<br />");
            }
            result.append("<br />");
        }
        strategyReportDailyDetailResp.setStrategyInfo(result.toString());
        return strategyReportDailyDetailResp;
    }

    private List<NameTypeConfigResp.Config> mapToConfigList(List<TeleNameConfigResp.ResponseData.Config> configList) {
        if (!CollectionUtils.isEmpty(configList)) {
            return configList.stream()
                    .map(config -> new NameTypeConfigResp.Config(config.getCode(), config.getName()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<BizSourceInfo> bizSourceList(AITelBizSourceRequest aiTelBizSourceRequest) {
        List<BizSourceInfo> resultList = Lists.newArrayList();
        try {
            String bizLine = aiTelBizSourceRequest.getBizLine();
            // 请求url
            String callServiceUrl = ApolloUtil.getAppProperty("xf.call-service.url", "http://qa1-call.testxinfei.cn");
            callServiceUrl = callServiceUrl + "/api/v1/businessSource/queryBizSourceByBizLine";
            // 请求header
            Map<String, String> headers = Maps.newHashMap();
            headers.put("app_key", ApolloUtil.getAppProperty("xf.call-service.appKey", "xyf_cdp"));
            headers.put("sign", sign(ApolloUtil.getAppProperty("xf.call-service.appSecret", "vloeFwiBmL6duCHK"), new TreeMap<String, String>() {{
                put("bizLine", bizLine);
            }}));
            headers.put("timestamp", String.valueOf(new Date().getTime()));
            // 请求参数
            List<NameValuePair> parametersBody = Lists.newArrayList();
            parametersBody.add(new BasicNameValuePair("bizLine", bizLine));

            // 请求结果
            log.info("StrategyServiceImpl bizSourceList callServiceUrl={} headers={} parametersBody={}", callServiceUrl, JSONObject.toJSONString(headers), JSONObject.toJSONString(parametersBody));
            String resultValue = httpClientUtil.get(callServiceUrl, parametersBody, headers);
            log.info("StrategyServiceImpl bizSourceList resultValue={}", resultValue);
            if (StringUtils.isBlank(resultValue)) {
                return Collections.emptyList();
            }
            BizResponse<List<BizSourceResponse>> bizSourceResponse = JSONObject.parseObject(resultValue, new TypeReference<BizResponse<List<BizSourceResponse>>>(){});
            if (BizResponse.successData(bizSourceResponse)) {
               bizSourceResponse.getData().forEach(x -> {
                   try {
                       BizSourceInfo bizSourceInfo = new BizSourceInfo();
                       bizSourceInfo.setBizSourceId(Long.parseLong(x.getBizSourceId()));
                       bizSourceInfo.setBizSourceCode(x.getBizSourceCode());
                       bizSourceInfo.setBizSourceName(x.getBizSourceName());
                       bizSourceInfo.setBizLineName(x.getBizLineName());
                       bizSourceInfo.setDescribe(String.join("、", buildDescription(x)));
                       resultList.add(bizSourceInfo);
                   } catch (Exception e) {
                       log.warn("StrategyServiceImpl bizSourceList warn", e);
                   }
               });
            }
        } catch (Exception e) {
            log.error("StrategyServiceImpl bizSourceList error", e);
        }
        return resultList;
    }

    /**
     * 构建AI呼叫描述信息
     * @param bizSourceResponse 业务来源信息
     */
    private static List<String> buildDescription(BizSourceResponse bizSourceResponse) {
        List<String> description = Lists.newArrayList();
        List<BizSourceVendorStrategyVO> bizSourceVendorStrategyVOList = Optional.ofNullable(bizSourceResponse.getBizSourceVendorStrategyVOList()).orElse(Lists.newArrayList());
        for (BizSourceVendorStrategyVO bizSourceVendorStrategyVO : bizSourceVendorStrategyVOList) {
            // 厂商名称
            String vendorName = bizSourceVendorStrategyVO.getVendorName();
            // 策略CODE
            String strategyCode = bizSourceVendorStrategyVO.getStrategyCode();
            // 策略名称
            String strategyName = bizSourceVendorStrategyVO.getStrategyName();
            String desc = vendorName + "-" + strategyCode + " " + strategyName;
            description.add(desc);
        }
        return description;
    }

    /**
     * 外呼中台请求签名
     * @param appSecret 签名密钥
     * @param params 待签参数
     */
    private String sign(String appSecret, TreeMap<String, String> params) {
        StringBuilder paramValues = new StringBuilder();
        params.forEach((key, value) -> paramValues.append(key).append("=").append(value).append("&"));
        paramValues.append("app_secret").append("=").append(appSecret);
        return DigestUtils.md5DigestAsHex(paramValues.toString().getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public List<AiParam> aiParams() {
        try {
            String aiParamValue = ApolloUtil.getAppProperty("ai.param", "[]");
            return JSONObject.parseArray(aiParamValue, AiParam.class);
        } catch (Exception e) {
            log.error("StrategyServiceImpl aiParams error", e);
        }
        return Collections.emptyList();
    }

}



