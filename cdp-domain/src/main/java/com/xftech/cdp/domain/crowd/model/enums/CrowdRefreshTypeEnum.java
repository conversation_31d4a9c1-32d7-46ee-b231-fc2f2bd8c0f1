package com.xftech.cdp.domain.crowd.model.enums;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@AllArgsConstructor
@Getter
public enum CrowdRefreshTypeEnum {

    MANUAL(0, "手动刷新"),
    AUTOMATIC(1, "每日例行刷新"),
    NO_REFRESH(2, "无");

    private final int code;

    private final String description;


    public static CrowdRefreshTypeEnum getInstance(Integer code) {
        for (CrowdRefreshTypeEnum value : CrowdRefreshTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new CrowdException(String.format("不存在该刷新类型：%s", code));
    }
}
