package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.crowd.model.enums.LabelEnum;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/18
 */
@Component
public class LabelPrimaryLabelDictFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<LabelEnum.PrimaryLabelEnum> crowdStatusEnums = Arrays.stream(LabelEnum.PrimaryLabelEnum.values()).collect(Collectors.toList());
        for (LabelEnum.PrimaryLabelEnum crowdStatusEnum : crowdStatusEnums) {
            result.add(Dict.builder().dictCode(String.valueOf(crowdStatusEnum.getCode())).dictValue(crowdStatusEnum.getDescription()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.LABEL_PRIMARY_LABEL;
    }

    @Override
    public String getDescription() {
        return "一级标签";
    }
}
