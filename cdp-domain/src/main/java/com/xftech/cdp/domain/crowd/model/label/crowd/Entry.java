package com.xftech.cdp.domain.crowd.model.label.crowd;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/2/13 10:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Entry extends CrowdLabelOption {

    private String input;

    /**
     * column = input
     */
    @Override
    public StringBuilder condition(String column, String configOptionReflect) {
        if (input.contains("#")) {
            List<String> items = Arrays.stream(input.split("#")).collect(Collectors.toList());
            StringBuilder sql = super.condition(column, configOptionReflect);
            return sql.append(" in (").append(items.stream().map(a -> "'" + a + "'").collect(Collectors.joining(","))).append(") ");
        }
        return super.condition(column, configOptionReflect).append(" = '").append(input).append("' ");
    }

    @Override
    public void verify() {
        if (StringUtils.isEmpty(input)) {
            throw new CrowdException("输入项数据错误！");
        }
    }
}
