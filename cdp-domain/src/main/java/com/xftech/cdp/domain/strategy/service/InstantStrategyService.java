package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.InstantStrategyUpdateReq;
import com.xftech.cdp.api.dto.resp.EngineReInputReportResp;
import com.xftech.cdp.api.dto.resp.InstantStrategyDetailResp;

/**
 * @<NAME_EMAIL>
 */
public interface InstantStrategyService {

    boolean createInstantStrategy(InstantStrategyCreateReq strategyCreateReq);
    boolean updateInstantStrategy(InstantStrategyUpdateReq strategyCreateReq);
    InstantStrategyDetailResp getInstantStrategyDetail(Long strategyId);
    void strategyEventRefreshStatus();

    /**
     * T0策略昨天触达人数告警
     */
    void t0StrategyDispatchUserNumAlarm();

    PageResultResponse<EngineReInputReportResp> monitorEngineReInput(Long strategyId, Integer queryDate, Integer pageNum, Integer pageSize);

}
