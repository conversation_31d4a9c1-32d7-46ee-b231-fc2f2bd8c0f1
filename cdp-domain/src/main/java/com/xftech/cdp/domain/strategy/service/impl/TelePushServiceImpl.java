/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.UpdatePolicyPriorityReq;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.BlankMethod;
import com.xftech.cdp.domain.strategy.service.TelePushService;
import com.xftech.cdp.infra.client.telemarketing.TelemarketingClient;
import com.xftech.cdp.infra.client.telemarketing.model.TeleNameConfigDetailReq;
import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameConfigDetailResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameSaveResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleUpdatePriorityResp;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 *  推送新电销配置信息接口
 * <AUTHOR>
 * @version $ TelePushServiceImpl, v 0.1 2023/10/17 14:58 wancheng.qu Exp $
 */
@Service
public class TelePushServiceImpl implements TelePushService {

    private static final String RESPONSE="response";
    private static final Logger logger = LoggerFactory.getLogger(TelePushServiceImpl.class);

    @Autowired
    private TelemarketingClient telemarketingClient;
    @Autowired
    private RandomNumService randomNumService;
    @Override
    public void pushTeleAndSetTemplateId(StrategyCreateReq.StrategyMarketChannel strategyMarketChannel) {
        if (StringUtils.isBlank(strategyMarketChannel.getTemplate())) {
            throw new StrategyException("tele url=policy-config/operate return null");
        }

        TeleNameSaveResp teleNameSaveResp = telemarketingClient.saveNameConfig(strategyMarketChannel.getTemplate());

        if (teleNameSaveResp == null || !teleNameSaveResp.isSuccess()) {
            String errorMessage = teleNameSaveResp != null ? "tele url:policy-config/operate return error=" + teleNameSaveResp.getMessage() : "tele url=policy-config/operate return null";
            throw new StrategyException(errorMessage);
        }

        if (teleNameSaveResp.getResponse() != null && teleNameSaveResp.getResponse().getPolicyId() != null) {
            strategyMarketChannel.setTemplateId(String.valueOf(teleNameSaveResp.getResponse().getPolicyId()));
        } else {
            throw new StrategyException("tele url:policy-config/operate return id is null,error=" + teleNameSaveResp.getMessage());
        }
    }

    @Override
    public boolean updatePolicyPriority(UpdatePolicyPriorityReq req) {
        TeleUpdatePriorityResp resp = telemarketingClient.updatePolicyPriority(JsonUtil.toJson(req));
        return resp.getStatus().equals(1);
    }

    @Override
    public String getPolicyList(List<Integer> ids) {
        return null;
    }

    @Override
    public String getPolicyDetail(Integer id) {
        String result=null;
        TeleNameConfigDetailReq req = new TeleNameConfigDetailReq();
        req.setPolicyId(id);
        req.setTraceId(UUID.randomUUID().toString());
        result = telemarketingClient.getPolicyDetail(req);
        if(StringUtils.isNotBlank(result)){
            JsonParser parser = new JsonParser();
            JsonObject jsonObject = parser.parse(result).getAsJsonObject();

            if (jsonObject.has(RESPONSE)) {
                JsonObject jsonResponse = jsonObject.getAsJsonObject(RESPONSE);
                result=jsonResponse.toString();
            } else {
               logger.warn("getPolicyDetail errorm , id={},res={}",id,result);
            }
        }
        return result;
    }

    @Override
    public TelePushArgs getTelePushArgs(Integer id, List<CrowdDetailDo> userIdList, DispatchDto reach, String app) {
        TelePushArgs t =new TelePushArgs();
        try {
            String data = getPolicyDetail(id);
            if (StringUtils.isNotBlank(data)) {
                TeleNameConfigDetailResp detail = JsonUtil.parse(data, TeleNameConfigDetailResp.class);
                t.setPolicyId(detail.getId());
                t.setPolicyType(detail.getType());
                t.setData(getData(userIdList,app,detail));
                t.setStrategyType(reach.getStrategyRulerEnum().getStrategyType());
                t.setNameTypeId(reach.getNameTypeId());
                t.setCdpStrategyId(reach.getStrategyId());
            }
        }catch (Exception e){
            logger.warn("getTelePushArgs method error ,id={}", id,e);
            throw new StrategyException(-1, "method --> getTelePushArgs data error", e);
        }
        return t;
    }

    private List<TelePushArgs.UserData> getData(List<CrowdDetailDo> userIdList, String app, TeleNameConfigDetailResp detail) {
        List<TelePushArgs.UserData> result = Lists.newArrayList();
       /* Set<Long> randomUser = Sets.newHashSet();
        if (Objects.equals(BlankMethod.RATE.getMethod(), detail.getBlankMethod())) {
            String blankBizKey = detail.getBlankBizKey();
            randomUser = getRateUser(Integer.valueOf(blankBizKey), userIdList);
        }*/
        for (CrowdDetailDo cd : userIdList) {
            TelePushArgs.UserData tu = TelePushArgs.UserData.builder()
                    .mobile(cd.getMobile())
                    .userId(cd.getUserId())
                    //.isWhite(randomNumService.getRandomIsWhite(detail, cd.getUserId(), randomUser))
                    .app(app)
                    .build();
            result.add(tu);
        }

        return result;
    }

    public Set<Long> getRateUser(Integer blankBizKey, List<CrowdDetailDo> userIdList) {
        if (blankBizKey < 1 || blankBizKey > 100) {
            throw new IllegalArgumentException("blankBizKey must be between 1 and 100.");
        }

        List<CrowdDetailDo> shuffledUserIds = new ArrayList<>(userIdList);
        Collections.shuffle(shuffledUserIds);

        int totalUsers = shuffledUserIds.size();
        int selectedUsers = (int) Math.ceil(blankBizKey / 100.0 * totalUsers);

        return IntStream.range(0, selectedUsers)
                .mapToObj(i -> shuffledUserIds.get(i).getUserId())
                .collect(Collectors.toSet());
    }
}