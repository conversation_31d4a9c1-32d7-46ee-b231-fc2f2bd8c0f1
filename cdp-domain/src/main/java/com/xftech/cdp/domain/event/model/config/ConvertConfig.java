package com.xftech.cdp.domain.event.model.config;

import com.xftech.cdp.domain.event.model.annotation.ProcessConfig;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 类型转换数据处理器配置信息
 * <AUTHOR>
 * @version $ ConvertConfig, v 0.1 2024/11/12 17:56 snail Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ProcessConfig(processor = DataProcessEnum.CONVERT)
public class ConvertConfig extends FieldConfig {
    /** 目标数据类型 */
    private String targetType;
    /** 默认值 */
    private String defaultValue;
}
