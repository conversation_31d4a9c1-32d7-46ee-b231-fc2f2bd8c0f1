/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.param.service.impl;

import com.xftech.cdp.domain.param.service.ReplaceStrategy;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @version $ UserNameReplaceStrategy, v 0.1 2024/3/1 16:02 wancheng.qu Exp $
 */

@Service
public class UserNameReplaceStrategy implements ReplaceStrategy {

    @Override
    public String replace(UserInfoResp input) {
        String name = validateAndGetMobile(input);
        return StringUtils.substring(name, 0, 1);
    }

    private String validateAndGetMobile(UserInfoResp input) {
        return Optional.ofNullable(input)
                .map(UserInfoResp::getCustName)
                .filter(StringUtils::isNotBlank)
                .orElseThrow(() -> new IllegalArgumentException("Invalid UserInfoResp or custname number"));

    }
}