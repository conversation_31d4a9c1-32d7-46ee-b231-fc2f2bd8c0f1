package com.xftech.cdp.domain.crowd.model.label.crowd;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/2/13 10:57
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ValueRange extends CrowdLabelOption {

    private Float min;

    private Float max;


    /**
     * column between min and max
     */
    @Override
    public StringBuilder condition(String column, String configOptionReflect) {
        return super.condition(column, configOptionReflect).append(" between ").append(new BigDecimal(Float.toString(min))).append(" and ").append(new BigDecimal(Float.toString(max))).append(" ");
    }

    @Override
    public void verify() {
        if (min == null || max == null) {
            throw new CrowdException("数值段数据错误！");
        }
        if (min > max) {
            throw new CrowdException("数值段数据错误！");
        }
    }
}
