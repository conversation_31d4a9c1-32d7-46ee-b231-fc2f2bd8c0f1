package com.xftech.cdp.domain.touch.model;

import lombok.Data;
import java.util.Map;

/**
 * 触达用户信息模型
 * 统一的用户信息定义，兼容现有CrowdDetailDo和BizEventVO中的用户信息
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class TouchUserInfo {
    
    /**
     * 用户ID (对应CrowdDetailDo.userId, BizEventVO.appUserId)
     */
    private Long userId;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * APP标识
     */
    private String app;
    
    /**
     * 内部APP标识
     */
    private String innerApp;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * AB测试编号
     */
    private String abNum;
    
    /**
     * 用户ID后两位（用于分片等场景）
     */
    private Integer appUserIdLast2;
    
    /**
     * 人群包ID（离线触达场景）
     */
    private Long crowdId;
    
    /**
     * 信贷用户ID
     */
    private Long creditUserId;
    
    /**
     * 用户标签信息
     * 用于存储用户的各种标签数据
     */
    private Map<String, Object> userLabels;
    
    /**
     * 用户扩展信息
     * 用于存储其他用户相关的扩展数据
     */
    private Map<String, Object> userExt;
    
    /**
     * 验证用户信息
     */
    public void validate() {
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (mobile == null || mobile.trim().isEmpty()) {
            throw new IllegalArgumentException("mobile不能为空");
        }
        if (app == null || app.trim().isEmpty()) {
            throw new IllegalArgumentException("app不能为空");
        }
    }
    
    /**
     * 获取有效的信贷用户ID
     * 优先返回creditUserId，如果为空则返回userId
     */
    public Long getEffectiveCreditUserId() {
        return creditUserId != null ? creditUserId : userId;
    }
    
    /**
     * 判断是否有AB测试编号
     */
    public boolean hasAbNum() {
        return abNum != null && !abNum.trim().isEmpty();
    }
    
    /**
     * 判断是否属于指定人群包
     */
    public boolean belongsToCrowd(Long targetCrowdId) {
        return crowdId != null && crowdId.equals(targetCrowdId);
    }
    
    /**
     * 获取用户标签值
     */
    public Object getUserLabel(String labelKey) {
        return userLabels != null ? userLabels.get(labelKey) : null;
    }
    
    /**
     * 设置用户标签值
     */
    public void setUserLabel(String labelKey, Object labelValue) {
        if (userLabels == null) {
            userLabels = new java.util.HashMap<>();
        }
        userLabels.put(labelKey, labelValue);
    }
    
    /**
     * 获取用户扩展信息
     */
    public Object getUserExt(String extKey) {
        return userExt != null ? userExt.get(extKey) : null;
    }
    
    /**
     * 设置用户扩展信息
     */
    public void setUserExt(String extKey, Object extValue) {
        if (userExt == null) {
            userExt = new java.util.HashMap<>();
        }
        userExt.put(extKey, extValue);
    }
    
    /**
     * 复制基础用户信息
     */
    public TouchUserInfo copyBasicInfo() {
        TouchUserInfo copy = new TouchUserInfo();
        copy.setUserId(this.userId);
        copy.setMobile(this.mobile);
        copy.setApp(this.app);
        copy.setInnerApp(this.innerApp);
        copy.setDeviceId(this.deviceId);
        copy.setAbNum(this.abNum);
        copy.setAppUserIdLast2(this.appUserIdLast2);
        copy.setCrowdId(this.crowdId);
        copy.setCreditUserId(this.creditUserId);
        return copy;
    }
    
    /**
     * 转换为字符串（用于日志等场景）
     */
    @Override
    public String toString() {
        return String.format("TouchUserInfo{userId=%d, mobile=%s, app=%s, innerApp=%s}", 
                           userId, 
                           mobile != null ? mobile.substring(0, 3) + "****" + mobile.substring(7) : null,
                           app, 
                           innerApp);
    }
}
