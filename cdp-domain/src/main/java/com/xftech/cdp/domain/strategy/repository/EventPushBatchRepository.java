package com.xftech.cdp.domain.strategy.repository;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventPushBatchDo;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @<NAME_EMAIL>
 * @date 2023/5/23 13:56
 */
@Component
public class EventPushBatchRepository {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public EventPushBatchDo selectById(String tableNameNo, Long id) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("id", id);
        return DBUtil.selectOne("eventPushBatchMapper.selectByPrimaryKey", params);
    }

    /**
     * 插入
     *
     * @param eventPushBatchDo 对象
     * @return 是否插入成功标识
     */
    public boolean insert(String tableNameNo, EventPushBatchDo eventPushBatchDo) {
        eventPushBatchDo.setTableName(getTableName(tableNameNo));
        return DBUtil.insert("eventPushBatchMapper.insertSelective", eventPushBatchDo) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param eventPushBatchDo 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(String tableNameNo, EventPushBatchDo eventPushBatchDo) {
        eventPushBatchDo.setTableName(getTableName(tableNameNo));
        return DBUtil.update("eventPushBatchMapper.updateByPrimaryKeySelective", eventPushBatchDo) > 0;
    }

    /**
     * 根据渠道和批次号查询事件批次记录
     *
     * @param channelEnum 渠道
     * @param flowNo      批次号
     * @return 事件批次记录
     */
    public EventPushBatchDo getByChannelAndBatchNum(StrategyMarketChannelEnum channelEnum, String flowNo) {
        LocalDate now = LocalDate.now();
        String format = "yyyyMM";
        for (String tableNo : Arrays.asList(LocalDateTimeUtil.format(now, format), LocalDateTimeUtil.format(now.plusMonths(-1), format))) {
            Map<String, Object> params = new HashMap<>();
            params.put("tableName", getTableName(tableNo));
            params.put("channel", channelEnum.getCode());
            params.put("flowNo", flowNo);
            Optional<EventPushBatchDo> optional = Optional.ofNullable(DBUtil.selectOne("eventPushBatchMapper.getByChannelAndBatchNum", params));
            if (optional.isPresent()) {
                return optional.get();
            }
        }
        return null;
    }
    public Integer selectCountByStatusAndExecLogId(String tableNameNo, Long execLogId,Integer status) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("execLogId", execLogId);
        if(Objects.nonNull(status)){
            params.put("status", status);
        }
        return DBUtil.selectOne("eventPushBatchMapper.selectCountByStatusAndExecLogId", params);
    }

    /**
     * 根据表序号生成表名
     *
     * @param tableNameNo 表序号
     * @return 表名
     */
    private String getTableName(String tableNameNo) {
        return "event_push_batch_" + tableNameNo;
    }

    public Integer countByDetail(String tableNameNo, Long strategyDoId, Long strategyGroupId, String groupId, Integer marketChannel, String templateId, List<Integer> statusList, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("tableName", getTableName(tableNameNo));
        params.put("strategyDoId", strategyDoId);
        params.put("strategyGroupId", strategyGroupId);
        params.put("groupId", groupId);
        params.put("marketChannel", marketChannel);
        params.put("templateId", templateId);
        params.put("timeStart", startTime);
        params.put("timeEnd", endTime);
        if (!CollectionUtils.isEmpty(statusList)) {
            params.put("statusList", statusList);
        }
        return DBUtil.selectOne("eventPushBatchMapper.countByDetail", params);
    }
}
