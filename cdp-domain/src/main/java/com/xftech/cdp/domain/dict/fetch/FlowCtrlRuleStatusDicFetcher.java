package com.xftech.cdp.domain.dict.fetch;

import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.domain.dict.model.enums.DictCode;
import com.xftech.cdp.domain.flowctrl.model.enums.FlowCtrlRuleStatusEnum;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 */

@Component
public class FlowCtrlRuleStatusDicFetcher implements DictFetcher {
    @Override
    public List<Dict> getDict() {
        List<Dict> result = new ArrayList<>();
        List<FlowCtrlRuleStatusEnum> flowCtrlRuleStatusEnumList = Arrays.stream(FlowCtrlRuleStatusEnum.values()).collect(Collectors.toList());
        for (FlowCtrlRuleStatusEnum flowCtrlRuleStatusEnum : flowCtrlRuleStatusEnumList) {
            result.add(Dict.builder().dictCode(String.valueOf(flowCtrlRuleStatusEnum.getCode())).dictValue(flowCtrlRuleStatusEnum.getDesc()).build());
        }
        return result;
    }

    @Override
    public DictCode getDictCode() {
        return DictCode.FLOW_CTRL_RULE_STATUS;
    }

    @Override
    public String getDescription() {
        return "流控规则状态";
    }
}
