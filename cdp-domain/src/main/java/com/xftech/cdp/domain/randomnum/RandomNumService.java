package com.xftech.cdp.domain.randomnum;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.random.NewRandomListReq;
import com.xftech.cdp.api.dto.resp.random.RandomListResp;
import com.xftech.cdp.distribute.offline.dto.StrategyExecuteContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdWereHouse;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameConfigDetailResp;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;

import java.util.List;
import java.util.Set;


/**
 * @<NAME_EMAIL>
 */
public interface RandomNumService {
    PageResultResponse<RandomListResp> getRandomList(NewRandomListReq newRandomListReq, Integer type);


    /**
     * 获取随机数，失败记录入库
     *
     * @param context 初始化数据
     * @param list    当前批人群明细
     * @return 当前批人群明细
     */
    List<CrowdDetailDo> randomNum(StrategyContext context, List<CrowdDetailDo> list);

    /**
     * 获取用户随机数
     *
     * @param strategy   策略
     * @param channel    渠道类型
     * @param templateId 模板ID
     * @param list       当前批人群明细
     * @return 当前批人群明细
     */
    List<CrowdDetailDo> randomNum(StrategyDo strategy, Integer channel, String templateId, List<CrowdDetailDo> list);

    void ossCrowdFilterNewRandom(CrowdContext crowdContext, String labelValue, List<CrowdDetailDo> crowdDetailList, Integer labelGroupType);

    void crowdFilterNewRandom(CrowdContext crowdContext, String labelValue, List<CrowdWereHouse> crowdWereHouseList, Integer labelGroupType);

    String getRandomItem(String bizKey);

    Integer getRandomIsWhite(TeleNameConfigDetailResp detail, Long userId, Set<Long> randomUser);

    Long generateRandomNumber(String bizKey, Long userId, int numLength);
}
