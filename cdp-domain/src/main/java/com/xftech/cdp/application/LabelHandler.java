/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.application;

import java.util.*;
import java.util.stream.Collectors;

import com.xftech.cdp.domain.crowd.service.impl.LabelServiceImpl;
import com.xftech.cdp.domain.label.dto.LabelCheckBoxConfigurationOption;
import com.xftech.cdp.domain.label.dto.LabelConstants;
import com.xftech.cdp.domain.label.service.MetaLabelService;
import com.xftech.cdp.domain.strategy.service.impl.DataInsightServiceImpl;
import com.xftech.cdp.domain.strategy.service.impl.DpsServiceImpl;
import com.xftech.cdp.feign.model.requset.DpsPageRequest;
import com.xftech.cdp.feign.model.response.MetaLabelDto;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo;
import com.xftech.cdp.infra.repository.cdp.label.po.MetaLabelDo;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @version $ LabelHandler, v 0.1 2024/6/19 11:46 lingang.han Exp $
 */

@Service
@Slf4j
@AllArgsConstructor
public class LabelHandler {
    private final DpsServiceImpl dpsService;
    private final MetaLabelService metaLabelService;
    private final LabelServiceImpl labelService;
    private final DataInsightServiceImpl dataInsightService;

    public void syncMetaLabel() {
        DpsPageRequest dpsPageRequest = new DpsPageRequest();
        while (true) {
            List<MetaLabelDto> metaLabel;
            if (ApolloUtil.switchStatus("data.insight.switch")) {
                metaLabel = dataInsightService.getMetaLabel(dpsPageRequest);
            } else {
                metaLabel = dpsService.getMetaLabel(dpsPageRequest);
            }
            if (CollectionUtils.isEmpty(metaLabel)) {
                break;
            }
            metaLabel.forEach(this::metaLabelExecute);
            dpsPageRequest.setPageNum(dpsPageRequest.getPageNum() + 1);
        }
    }

    public void metaLabelExecute(MetaLabelDto metaLabelDto) {
        try {
            log.info("metaLabel begin execute label:{}", JsonUtil.toJson(metaLabelDto));
            metaLabelCheck(metaLabelDto);
            MetaLabelDo metaLabelDo = new MetaLabelDo(metaLabelDto);
            List<LabelDo> labelDoList = labelService.getByLabelCode(metaLabelDo.getLabelCode());
            MetaLabelDo extisMetaLabelDo = metaLabelService.getByLabelCode(metaLabelDo.getLabelCode());

            //标签是否存在metaLabel
            if (!CollectionUtils.isEmpty(labelDoList)) {
                metaLabelDo.setOnline(1);
                //不可用  判断该标签 麻雀是否 在使用
                if (Objects.equals(metaLabelDo.getAvailable(), LabelConstants.LABEL_UNAVAILABLE)) {
                    //标签元数据发生变化时，metaLabel表available字段不存最新状态(如果存的话，页面会查询不到该条记录，无法操作label表该标签的下线)
                    metaLabelDo.setAvailable(LabelConstants.LABEL_AVAILABLE);
                    metaLabelDo.addCheckResult("元数据标签不可用;");
                    log.info("元数据标签不可用,labelCode:{}", metaLabelDo.getLabelCode());
                    //todo 钉钉通知 to 产品and研发
                }

                //校验标签 标签值类型，聚合维度 聚合维度变化
                if (extisMetaLabelDo != null) {
                    if (!Objects.equals(extisMetaLabelDo.getLabelDataValueType(), metaLabelDo.getLabelDataValueType())) {
                        metaLabelDo.addCheckResult("元数据标签值类型发生变化");
                        log.info("元数据标签值类型发生变化,labelCode:{},oldType:{},newType:{}", extisMetaLabelDo.getLabelCode(), extisMetaLabelDo.getLabelDataValueType(), metaLabelDo.getLabelDataValueType());
                        //todo 钉钉通知 to 产品and研发
                    }
                    if (!Objects.equals(extisMetaLabelDo.getAggDimension(), metaLabelDo.getAggDimension())) {
                        metaLabelDo.addCheckResult("元数据标签聚合维度发生变化");
                        log.info("元数据标签聚合维度发生变化,labelCode:{},oldAggDimension:{},newAggDimension:{}", extisMetaLabelDo.getLabelCode(), extisMetaLabelDo.getAggDimension(), metaLabelDo.getAggDimension());
                        //todo 钉钉通知 to 产品and研发
                    }
                }
                //校验 枚举
                if (labelEnumChangeCheck(labelDoList, metaLabelDo)) {
                    metaLabelDo.addCheckResult("元数据标签枚举值发生变化");
                    //todo 钉钉通知 to 产品and研发
                }
            }
            //保存或更新数据
            if (metaLabelService.exitsMetaLabel(metaLabelDo)) {
                metaLabelService.updateByLabelCode(metaLabelDo);
            } else {
                metaLabelService.saveMetaLabel(metaLabelDo);
            }

            log.info("metaLabel end execute label:{}", metaLabelDto);
        } catch (Exception e) {
            log.error("metaLabel execute error", e);
        }
    }

    private boolean labelEnumChangeCheck(List<LabelDo> labelDoList, MetaLabelDo metaLabelDo) {
        for (LabelDo labelDo : labelDoList) {
            if (Objects.equals(labelDo.getConfigurationOptionType(), 1)) {
                if (StringUtils.isNotBlank(labelDo.getConfigurationOption()) && StringUtils.isNotBlank(metaLabelDo.getLabelEnumValue())) {
                    LabelCheckBoxConfigurationOption labelEnumConfig = JSONObject.parseObject(labelDo.getConfigurationOption(), LabelCheckBoxConfigurationOption.class);
                    List<String> oldEnums = labelEnumConfig.getItems();
                    List<String> newEnums = Arrays.asList(metaLabelDo.getLabelEnumValue().split(","));
                    if (StringUtils.isNotBlank(labelDo.getConfigurationReflect())) {
                        Map<String, String> map = JSONObject.parseObject(labelDo.getConfigurationReflect(), Map.class);
                        oldEnums = map.values().stream().filter(item -> !Objects.equals(item, "NULL")).map(Object::toString).collect(Collectors.toList());
                    }
                    //枚举值比较
                    boolean result = !new HashSet<>(oldEnums).equals(new HashSet<>(newEnums));
                    if (result) {
                        log.info("枚举值发生变化,labelCode:{},onlineEnums:{},newEnums:{}", labelDo.getDataWarehouseField(), oldEnums, newEnums);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private void metaLabelCheck(MetaLabelDto metaLabelDto) {
        if (StringUtils.isBlank(metaLabelDto.getLabelCode())
                || StringUtils.isBlank(metaLabelDto.getLabelName())
                || StringUtils.isBlank(metaLabelDto.getDataType())
                || StringUtils.isBlank(metaLabelDto.getAggDimension())
                || metaLabelDto.getState() == null
        ) {
            throw new BizException("标签元数据数据完整性校验不通过");
        }
    }

}