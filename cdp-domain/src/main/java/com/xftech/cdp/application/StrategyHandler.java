package com.xftech.cdp.application;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.xftech.base.common.util.UdpUtil;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.distribute.offline.StrategyTaskDistributeHandler;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.dict.service.DictService;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.dispatch.dto.EngineOfflineDispatchExtDto;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.model.strategy.DispatchTaskExtBO;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyGroupRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.service.*;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventCatchService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.impl.StrategyEventDispatchServiceImpl;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.thread.DispatchTaskExecutor;
import com.xftech.cdp.infra.utils.*;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/6 9:25
 */
@Component
@Slf4j
public class StrategyHandler {

    @Autowired
    private StrategyService strategyService;
    @Autowired
    private StrategyExecLogService strategyExecLogService;
    @Autowired
    private StrategyEventCatchService strategyEventCatchService;
    @Autowired
    private StrategyExecLogRepository strategyExecLogRepository;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private StrategyGroupRepository strategyGroupRepository;
    @Autowired
    private InstantStrategyService instantStrategyService;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private DictService dictService;
    @Autowired
    private UserDispatchDetailService userDispatchDetailService;
    @Autowired
    private DispatchTaskService dispatchTaskService;
    @Autowired
    private ReportDailyStrategyService reportDailyStrategyService;
    @Autowired
    private ReportDailyTaskService reportDailyTaskService;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private DispatchUserDelayService dispatchUserDelayService;
    @Autowired
    private StrategyEventDispatchServiceImpl strategyEventDispatchService;

    @Resource(name = "dispatchUserDelayExecutorWrapper")
    private Executor dispatchUserDelayExecutorWrapper;

    private static final String TelStrategyDispatchDelayEnableKey = "XxlJobTelStrategyDispatchDelayEnable";


    // 生成周期策略任务
    public void strategyDispatchTaskProducer() {
        createCycleStrategyTask();
        createStrategyOfflineEngineTask();

        //普通策略的不营销渠道
        createOfflineStrategyNoneMarketingTask();
    }

    public void createOfflineStrategyNoneMarketingTask() {
        LocalDateTime now = LocalDateTime.now();
        List<Integer> statusList = StrategyStatusEnum.getActiveCodes();
        List<Integer> sendRulerList = Arrays.asList(StrategyRulerEnum.ONCE.getCode(), StrategyRulerEnum.CYCLE.getCode());
        // 离线引擎版本
        List<StrategyDo> strategyDoList = strategyService.getStrategyList(sendRulerList, statusList);
        strategyDoList = strategyDoList.stream()
                .filter(x -> now.isAfter(x.getValidityBegin()) &&
                        now.isBefore(x.getValidityEnd()))
                .collect(Collectors.toList());

        log.info("DispatchTask任务生成 --> 待处理的有效的策略列表Id[普通离线策略]:{}",
                strategyDoList.stream().map(StrategyDo::getId).collect(Collectors.toList()));

        byte bizType = (byte) DispatchTaskBizTypeEnum.OFFLINE_STRATEGY.getCode();
        Set<StrategyMarketChannelEnum> marketChannelSet = new HashSet<>();
        marketChannelSet.add(StrategyMarketChannelEnum.NONE);

        DispatchTaskExtBO.NoneMarketExt noneMarketExt = new DispatchTaskExtBO.NoneMarketExt();
        for (StrategyDo strategyDo : strategyDoList) {
            createStrategyDispatchTasks(strategyDo.getId(), strategyDo.getSendFrequency(), bizType, now, noneMarketExt, marketChannelSet);
        }
    }

    public void createStrategyOfflineEngineTask() {
        List<Integer> statusList = StrategyStatusEnum.getActiveCodes();
        List<Integer> sendRulerList = Arrays.asList(StrategyRulerEnum.CYCLE_DAY.getCode(), StrategyRulerEnum.CYCLE.getCode(),
                StrategyRulerEnum.ONCE.getCode());
        List<StrategyDo> strategyDoList = strategyService.getStrategyList(sendRulerList, statusList);

        LocalDateTime now = LocalDateTime.now();
        strategyDoList = strategyDoList.stream()
                .filter(x -> x.getType() == 1)
                .filter(x -> now.isAfter(x.getValidityBegin()) &&
                        now.isBefore(x.getValidityEnd()))
                .collect(Collectors.toList());
        log.info("DispatchTask任务生成 --> 待处理的有效的策略列表Id:{}", strategyDoList.stream().map(StrategyDo::getId)
                .collect(Collectors.toList()));

        byte bizType = (byte) DispatchTaskBizTypeEnum.STRATEGY_OFF_ENGINE.getCode();
        for (StrategyDo strategyDo : strategyDoList) {
            createStrategyDispatchTasks(strategyDo.getId(), strategyDo.getSendFrequency(), bizType, now, null, null);
        }
    }

    public void createCycleStrategyTask() {
        LocalDateTime now = LocalDateTime.now();
        List<Integer> statusList = StrategyStatusEnum.getActiveCodes();
        List<Integer> sendRulerList = Collections.singletonList(StrategyRulerEnum.CYCLE_DAY.getCode());
        // 离线引擎版本
        List<StrategyDo> strategyDoList = strategyService.getStrategyList(sendRulerList, statusList);
        strategyDoList = strategyDoList.stream()
                .filter(x -> now.isAfter(x.getValidityBegin()) &&
                        now.isBefore(x.getValidityEnd()))
                .collect(Collectors.toList());

        log.info("DispatchTask任务生成 --> 待处理的有效的策略列表Id:{}", strategyDoList.stream().map(StrategyDo::getId)
                .collect(Collectors.toList()));

        byte bizType = (byte) DispatchTaskBizTypeEnum.STRATEGY.getCode();
        for (StrategyDo strategyDo : strategyDoList) {
            createStrategyDispatchTasks(strategyDo.getId(), strategyDo.getSendFrequency(), bizType, now, null, null);
        }
    }

    public void createStrategyDispatchTasks(Long strategyId, String sendFrequency, byte bizType, LocalDateTime now,
                                            DispatchTaskExtBO.DispatchTaskExt dispatchTaskExt, Set<StrategyMarketChannelEnum> marketChannelSet) {
        List<Integer> taskStatusList = DispatchTaskStatusEnum.getEffectiveList().stream()
                .map(DispatchTaskStatusEnum::getCode)
                .collect(Collectors.toList());

        StrategyCreateReq.SendFrequency sendFrequencyDto = JsonUtil.parse(sendFrequency,
                StrategyCreateReq.SendFrequency.class);
        if (sendFrequencyDto == null) {
            log.error("没有获取到策略执行周期, strategyId = {}", strategyId);
            return;
        }

        List<StrategyMarketChannelDo> marketChannelDoList = strategyMarketChannelRepository
                .selectByStrategyId(strategyId);
        if (!CollectionUtils.isEmpty(marketChannelSet)) {
            marketChannelDoList = marketChannelDoList.stream()
                    .filter(strategyMarketChannelDo ->
                            marketChannelSet.contains(StrategyMarketChannelEnum.getInstance(strategyMarketChannelDo.getMarketChannel())))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(marketChannelDoList)) {
            if (DispatchTaskBizTypeEnum.isOffEngine(bizType)) {
                // 构建一个虚拟营销渠道, 加入任务列表
                StrategyMarketChannelDo strategyMarketChannelDo = new StrategyMarketChannelDo();
                strategyMarketChannelDo.setId(0L);
                strategyMarketChannelDo.setSendTime(sendFrequencyDto.getSendTime());
                strategyMarketChannelDo.setMarketChannel(StrategyMarketChannelEnum.SMS.getCode());
                marketChannelDoList.add(strategyMarketChannelDo);
                if (sendFrequencyDto.getSendTime() == null){
                    return;
                }
            } else {
                return;
            }
        }

        for (StrategyMarketChannelDo strategyMarketChannelDo : marketChannelDoList) {
            LocalTime localTime = null;
            if (StrategyMarketChannelEnum.getIgnoreMarketingCodes()
                    .contains(strategyMarketChannelDo.getMarketChannel())) {
                continue;
            }


            //如果是不营销组，且配置为不执行，则不生成对应的dispatch_task
            if (strategyMarketChannelDo.getMarketChannel().equals(StrategyMarketChannelEnum.NONE.getCode())) {
                StrategyGroupDo groupDo = strategyGroupRepository.selectById(strategyMarketChannelDo.getStrategyGroupId());
                if (Objects.nonNull(groupDo) && groupDo.getIsExecutable().equals(0)) {
                    return;
                }
            }

            if (Arrays.asList(StrategyFrequencyEnum.CYCLE_DAY.getCode(), StrategyFrequencyEnum.EVERY_DAY.getCode())
                    .contains(sendFrequencyDto.getType())) {
                localTime = strategyMarketChannelDo.getSendTime();
            } else if (Objects.equals(StrategyFrequencyEnum.EVERY_WEEK.getCode(), sendFrequencyDto.getType())) {
                int dayOffWeek = DateUtil.dayOfWeek(DateUtil.convert(now));
                if (CollectionUtils.isEmpty(sendFrequencyDto.getValue())) {
                    break;
                }
                if (sendFrequencyDto.getValue().contains(dayOffWeek)) {
                    localTime = strategyMarketChannelDo.getSendTime();
                }
            } else if (Objects.equals(StrategyFrequencyEnum.EVERY_MONTH.getCode(), sendFrequencyDto.getType())) {
                int dayOfMonth = DateUtil.dayOfMonth(DateUtil.convert(now));
                if (CollectionUtils.isEmpty(sendFrequencyDto.getValue())) {
                    break;
                }
                if (sendFrequencyDto.getValue().contains(dayOfMonth)) {
                    localTime = strategyMarketChannelDo.getSendTime();
                }
            } else {
                log.warn("DispatchTask任务生成 --> 未知的任务, 不加入任务表, 策略id:{}, 渠道id:{}", strategyId,
                        strategyMarketChannelDo.getId());
                break;
            }

            if (localTime == null) {
                log.info("DispatchTask任务生成 --> 没有获取到渠道触达任务时间, 不加入任务表, 策略id:{}, 渠道id:{}", strategyId,
                        strategyMarketChannelDo.getId());
                continue;
            }
            if (localTime.isBefore(now.toLocalTime())) {
                log.info("DispatchTask任务生成 --> 策略触达渠道的时间已经过期, 不加入任务表, 策略id:{}, 渠道id:{}", strategyId,
                        strategyMarketChannelDo.getId());
                continue;
            }
            List<DispatchTaskDo> dispatchTaskDos = dispatchTaskService.selectList(strategyId.toString(),
                    strategyMarketChannelDo.getId().toString(), bizType, DateUtil.dayOfInt(DateUtil.convert(now)),
                    taskStatusList);
            if (dispatchTaskDos.size() > 0) {
                List<DispatchTaskDo> doList = dispatchTaskDos.stream().filter(x -> Objects.equals(x.getStatus().intValue(),
                                StrategyStatusEnum.INIT.getCode()))
                        .collect(Collectors.toList());
                Date curDate = DateUtil.convert(LocalDateTime.of(now.toLocalDate(), localTime));
                doList.stream().filter(x -> curDate.compareTo(x.getDispatchTime()) != 0).forEach(x -> {
                    log.info("DispatchTask任务生成 --> 策略触达渠道修正为最新修改的时间, 任务id:{}," +
                                    " 策略id:{}, 渠道id:{}, 任务最新时间:{}", x.getId(), strategyId,
                            strategyMarketChannelDo.getId(), curDate);
                    if (x.getNextDispatchTime() != null && x.getNextDispatchTime().before(curDate)) {
                        dispatchTaskService.updateDispatchTime(x.getId(), curDate, curDate);
                    } else {
                        dispatchTaskService.updateDispatchTime(x.getId(), curDate, x.getNextDispatchTime());
                    }
                });
                log.info("DispatchTask任务生成 --> 触达任务已经存在, 不加入任务表, 策略id:{}, 渠道id:{}", strategyId,
                        strategyMarketChannelDo.getId());
                continue;
            }
            // 开始加入待处理任务表
            DispatchTaskDo dispatchTaskDo = new DispatchTaskDo();
            dispatchTaskDo.setBizId(strategyId.toString());
            dispatchTaskDo.setAssociationId(strategyMarketChannelDo.getId().toString());
            dispatchTaskDo.setBizType(bizType);
            dispatchTaskDo.setDispatchTime(DateUtil.convert(LocalDateTime.of(now.toLocalDate(), localTime)));
            dispatchTaskDo.setDateValue(DateUtil.dayOfInt(DateUtil.convert(now)));
            dispatchTaskDo.setSerialNo(IdUtil.fastSimpleUUID());


            StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(strategyMarketChannelDo.getMarketChannel());
            if (dispatchTaskExt instanceof DispatchTaskExtBO.FlowExt) {
                DispatchTaskExtBO.FlowExt flowExt = (DispatchTaskExtBO.FlowExt)dispatchTaskExt;
                flowExt.setMarketChannelEnum(marketChannelEnum);
            } else if (dispatchTaskExt instanceof DispatchTaskExtBO.NoneMarketExt) {
                DispatchTaskExtBO.NoneMarketExt noneMarketExt = (DispatchTaskExtBO.NoneMarketExt) dispatchTaskExt;
                noneMarketExt.setMarketChannelEnum(marketChannelEnum);
            }
            dispatchTaskDo.setExtDetail(JsonUtil.toJson(dispatchTaskExt));
            dispatchTaskService.add(dispatchTaskDo);
            log.info("DispatchTask任务生成 --> 策略触达渠道加入任务表, 策略id:{}, 渠道id:{}, 生成的任务Id:{}, dispatchTime:{}", strategyId,
                    strategyMarketChannelDo.getId(),
                    dispatchTaskDo.getId(),
                    dispatchTaskDo.getDispatchTime());
        }
    }

    public void strategyDispatchTaskConsumer(int shardTotal, int shardIndex,
                                             int bizType) {
        Date dispatchTime = new Date();
        int dayValue = DateUtil.dayOfInt(dispatchTime);
        DispatchTaskBizTypeEnum dispatchTaskBizTypeEnum = DispatchTaskBizTypeEnum.getByCode(bizType);
        if (dispatchTaskBizTypeEnum == null) {
            return;
        }
        List<Integer> taskStatusList = DispatchTaskStatusEnum.getTodoList().stream().map(DispatchTaskStatusEnum::getCode)
                .collect(Collectors.toList());
        List<DispatchTaskDo> dispatchTaskDos = dispatchTaskService.selectTodoList((byte) dispatchTaskBizTypeEnum.getCode(),
                dayValue, dispatchTime, taskStatusList);
        dispatchTaskDos = dispatchTaskDos.stream().filter(x -> (HashUtils.crc32Hash(x.getSerialNo()) % shardTotal) == shardIndex)
                .collect(Collectors.toList());

        int maxRetryTimes = appConfigService.getDispatchTaskMaxRetryTimes();
        for (DispatchTaskDo dispatchTaskDo : dispatchTaskDos) {
            DispatchTaskExecutor.getPool().submit(() -> dispatchTaskExcuting(dispatchTime, maxRetryTimes, dispatchTaskDo));
        }
    }

    /**
     * 新版非电销单独处理delay记录，去除none（不营销）类型处理
     * by fanxu 2025-03-01
     */
    public void notTelStrategyDispatchOfflineEngine() {
        int startIndex = 0;
        int pageSize = 1000;
        Date now = new Date();
        int dayOfInt = DateUtil.dayOfInt(now);
        int initStatus = 0;
        List<Integer> telTypes = Arrays.asList(0, 2, 4, 6); // 电销类型（2，4，6） + 不下发类型（0）
        while (true) {
            if(!WhitelistSwitchUtil.boolSwitchByApollo(TelStrategyDispatchDelayEnableKey)) {
                log.info("Xxl-Job 引擎策略执行下发任务开关关闭，非电销下发直接返回");
                break;
            }
            // 拉取可以被消费的数据
            List<DispatchUserDelayDo> dispatchUserDelayDos = dispatchUserDelayService
                    .selectNotTelList(startIndex, pageSize, initStatus, dayOfInt, now, telTypes);
            if (CollectionUtils.isEmpty(dispatchUserDelayDos)) {
                break;
            }
            Stopwatch stopwatch = Stopwatch.createStarted();
            if (ApolloUtil.switchStatus("dispatchUserDelay.async.switch")) { // 多线程处理开关
                List<CompletableFuture<Void>> futures = Lists.newArrayList();
                Lists.partition(dispatchUserDelayDos, 50).forEach(partition -> futures.add(CompletableFuture.runAsync(() -> dealDispatchUserDelayDos(partition), dispatchUserDelayExecutorWrapper)));
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("StrategyHandler notTelStrategyDispatchOfflineEngine 批处理数量={} 并发批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            } else {
                dealDispatchUserDelayDos(dispatchUserDelayDos);
                log.info("StrategyHandler notTelStrategyDispatchOfflineEngine 批处理数量={} 串行批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            }
            if (dispatchUserDelayDos.size() < pageSize) {
                break;
            }
            startIndex = dispatchUserDelayDos
                    .get(dispatchUserDelayDos.size() - 1).getId().intValue();
        }
    }

    /**
     * 不营销单独处理delay记录
     */
    public void noneStrategyDispatchOfflineEngine() {
        int startIndex = 0;
        int pageSize = 1000;
        Date now = new Date();
        int dayOfInt = DateUtil.dayOfInt(now);
        int initStatus = 0;
        Integer noneType = StrategyMarketChannelEnum.NONE.getCode();
        while (true) {
            if(!WhitelistSwitchUtil.boolSwitchByApollo(TelStrategyDispatchDelayEnableKey)) {
                log.info("Xxl-Job 引擎策略执行下发任务开关关闭，none下发直接返回");
                break;
            }
            // 拉取可以被消费的数据
            List<DispatchUserDelayDo> dispatchUserDelayDos = dispatchUserDelayService
                    .selectListByType(startIndex, pageSize, initStatus, dayOfInt, now, noneType); // 不营销类型
            if (CollectionUtils.isEmpty(dispatchUserDelayDos)) {
                break;
            }
            Stopwatch stopwatch = Stopwatch.createStarted();
            if (ApolloUtil.switchStatus("dispatchUserDelay.async.switch")) { // 多线程处理开关
                List<CompletableFuture<Void>> futures = Lists.newArrayList();
                Lists.partition(dispatchUserDelayDos, 50).forEach(partition -> futures.add(CompletableFuture.runAsync(() -> dealDispatchUserDelayDos(partition), dispatchUserDelayExecutorWrapper)));
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("StrategyHandler noneStrategyDispatchOfflineEngine 批处理数量={} 并发批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            } else {
                dealDispatchUserDelayDos(dispatchUserDelayDos);
                log.info("StrategyHandler noneStrategyDispatchOfflineEngine 批处理数量={} 串行批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            }
            if (dispatchUserDelayDos.size() < pageSize) {
                break;
            }
            startIndex = dispatchUserDelayDos
                    .get(dispatchUserDelayDos.size() - 1).getId().intValue();
        }
    }

    /**
     * 新版电销单独处理delay记录
     * by fanxu 2025-03-01
     */
    public void telStrategyDispatchOfflineEngine() {
        int startIndex = 0;
        int pageSize = 1000;
        Date now = new Date();
        int dayOfInt = DateUtil.dayOfInt(now);
        int initStatus = 0;
        List<Integer> telTypes = Arrays.asList(2, 4, 6); // 电销类型
        while (true) {
            if(!WhitelistSwitchUtil.boolSwitchByApollo(TelStrategyDispatchDelayEnableKey)) {
                log.info("Xxl-Job 引擎策略执行下发任务开关关闭，电销下发直接返回");
                break;
            }
            // 拉取可以被消费的数据
            List<DispatchUserDelayDo> dispatchUserDelayDos = dispatchUserDelayService
                    .selectTelList(startIndex, pageSize, initStatus, dayOfInt, now, telTypes);
            if (CollectionUtils.isEmpty(dispatchUserDelayDos)) {
                break;
            }
            Stopwatch stopwatch = Stopwatch.createStarted();
            if (ApolloUtil.switchStatus("dispatchUserDelay.async.switch")) { // 多线程处理开关
                List<CompletableFuture<Void>> futures = Lists.newArrayList();
                Lists.partition(dispatchUserDelayDos, 50).forEach(partition -> futures.add(CompletableFuture.runAsync(() -> dealDispatchUserDelayDos(partition), dispatchUserDelayExecutorWrapper)));
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("StrategyHandler telStrategyDispatchOfflineEngine 批处理数量={} 并发批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            } else {
                dealDispatchUserDelayDos(dispatchUserDelayDos);
                log.info("StrategyHandler telStrategyDispatchOfflineEngine 批处理数量={} 串行批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            }
            if (dispatchUserDelayDos.size() < pageSize) {
                break;
            }
            startIndex = dispatchUserDelayDos
                    .get(dispatchUserDelayDos.size() - 1).getId().intValue();
        }
    }

    public void strategyDispatchOfflineEngine() {
        int startIndex = 0;
        int pageSize = 1000;
        Date now = new Date();
        int dayOfInt = DateUtil.dayOfInt(now);
        int initStatus = 0;
        while (true) {
            if(WhitelistSwitchUtil.boolSwitchByApollo(TelStrategyDispatchDelayEnableKey)) {
                log.info("Xxl-Job 引擎策略执行下发任务开关开启，全部类型下发直接返回");
                break;
            }
            // 拉取可以被消费的数据
            List<DispatchUserDelayDo> dispatchUserDelayDos = dispatchUserDelayService
                    .selectList(startIndex, pageSize, initStatus, dayOfInt, now);
            if (CollectionUtils.isEmpty(dispatchUserDelayDos)) {
                break;
            }
            Stopwatch stopwatch = Stopwatch.createStarted();
            if (ApolloUtil.switchStatus("dispatchUserDelay.async.switch")) { // 多线程处理开关
                List<CompletableFuture<Void>> futures = Lists.newArrayList();
                Lists.partition(dispatchUserDelayDos, 50).forEach(partition -> futures.add(CompletableFuture.runAsync(() -> dealDispatchUserDelayDos(partition), dispatchUserDelayExecutorWrapper)));
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("StrategyHandler strategyDispatchOfflineEngine 批处理数量={} 并发批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            } else {
                dealDispatchUserDelayDos(dispatchUserDelayDos);
                log.info("StrategyHandler strategyDispatchOfflineEngine 批处理数量={} 串行批处理耗时={}", dispatchUserDelayDos.size(), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            }
            if (dispatchUserDelayDos.size() < pageSize) {
                break;
            }
            startIndex = dispatchUserDelayDos
                    .get(dispatchUserDelayDos.size() - 1).getId().intValue();
        }
    }

    private void dealDispatchUserDelayDos(List<DispatchUserDelayDo> dispatchUserDelayDos) {
        for (DispatchUserDelayDo dispatchUserDelayDo : dispatchUserDelayDos) {
            log.info("处理离线引擎的营销数据: id={}", dispatchUserDelayDo.getId());
            dealDispatchUserDelayDo(dispatchUserDelayDo);
        }
    }

    private void dealDispatchUserDelayDo(DispatchUserDelayDo dispatchUserDelayDo) {
        int errorStatus = 2;
        int suceedStatus = 1;
        String currDate = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");

        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum
                .getInstance(dispatchUserDelayDo.getMarketChannel().intValue());
        try {
            // todo 发送数据  1-标记成功 2-标记失败
            EngineOfflineDispatchExtDto engineOfflineDispatchExtDto = JsonUtil.parse(dispatchUserDelayDo.getExtInfo(),
                    EngineOfflineDispatchExtDto.class);
            if (engineOfflineDispatchExtDto == null ||
                    !engineOfflineDispatchExtDto.isValid()) {
                dispatchUserDelayService.updateStatusFinished(dispatchUserDelayDo.getId(),
                        errorStatus);
            }

            EngineOfflineDispatchExtDto.UserDetailDto userDetailDto = engineOfflineDispatchExtDto
                    .getUserDetail();
            if (strategyMarketChannelEnum == StrategyMarketChannelEnum.SALE_TICKET ||
                    strategyMarketChannelEnum == StrategyMarketChannelEnum.X_DAY_INTEREST_FREE) {
                Map<String, Object> batchMap = new HashMap<>();
                batchMap.put("batch", Arrays.asList(engineOfflineDispatchExtDto.getDispatchDetail()));

                engineOfflineDispatchExtDto.setDispatchDetail(batchMap);
            }

            BizEventVO bizEventVO = new BizEventVO();
            bizEventVO.setTriggerDatetime(LocalDateTime.now());
            bizEventVO.setStrategyId(dispatchUserDelayDo.getStrategyId());
            bizEventVO.setStrategyGroupId(userDetailDto.getStrategyGroupId());
            bizEventVO.setGroupName(userDetailDto.getStrategyGroupName());
            bizEventVO.setCrowdPackId(userDetailDto.getCrowdId());

            DispatchDto dispatchDto = strategyEventDispatchService
                    .convertDispatchDto(bizEventVO, userDetailDto.getStrategyExecLogId(),
                            strategyMarketChannelEnum, StrategyRulerEnum.getInstance(
                                    userDetailDto.getStrategySendRuler()), null);

            CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
            crowdDetailDo.setUserId(dispatchUserDelayDo.getUserId());
            crowdDetailDo.setMobile(userDetailDto.getMobile());
            crowdDetailDo.setApp(userDetailDto.getApp());
            crowdDetailDo.setInnerApp(userDetailDto.getInnerApp());
            crowdDetailDo.setCrowdId(userDetailDto.getCrowdId());

            int ret = strategyEventDispatchService.marketingSend(dispatchDto, crowdDetailDo,
                    strategyMarketChannelEnum, dispatchUserDelayDo.getGroupName(),
                    engineOfflineDispatchExtDto.getDispatchDetail(), null);
            if (ret > 0) {
                dispatchUserDelayService.updateStatusFinished(dispatchUserDelayDo.getId(),
                        suceedStatus);
                redisUtils.pfAddTwoDay(RedisKeyUtils.genOffEngineSendNum(currDate, dispatchUserDelayDo.getStrategyId()), crowdDetailDo.getUserId());
            } else {
                if (StrategyMarketChannelEnum.getIgnoreCodes().contains(strategyMarketChannelEnum.getCode())) {
                    dispatchUserDelayService.updateStatusFinished(dispatchUserDelayDo.getId(),
                            suceedStatus);
                } else {
                    dispatchUserDelayService.updateStatusFinished(dispatchUserDelayDo.getId(),
                            errorStatus);
                }
                if (ret == -999) {
                    redisUtils.increment(RedisKeyUtils.genStatDecnFlowSum(currDate, dispatchUserDelayDo.getStrategyId()), 1L, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
                }
            }
        } catch (Exception ex) {
            log.error("离线策略引擎, 营销触达结果数据失败, 策略={}, 营销信息={}", dispatchUserDelayDo.getStrategyId(),
                    JsonUtil.toJson(dispatchUserDelayDo.getExtInfo()), ex);
            dispatchUserDelayService.updateStatusFinished(dispatchUserDelayDo.getId(),
                    errorStatus);
        }
    }

    /**
     * 101离线策略不营销组，2离线引擎，1周期策略，dispatch task解析执行
     * @param dispatchTime
     * @param maxRetryTimes
     * @param dispatchTaskDo
     */
    private void dispatchTaskExcuting(Date dispatchTime, int maxRetryTimes, DispatchTaskDo dispatchTaskDo) {
        if (dispatchTaskDo.getNextDispatchTime() != null) {
            if (dispatchTime.before(dispatchTaskDo.getNextDispatchTime())) {
                log.info("DispatchTask任务执行 --> nextDispatchTime 时间未到, 不执行, 任务id:{}", dispatchTaskDo.getId());
                return;
            }
        }
        if (dispatchTaskDo.getRetryTimes() > maxRetryTimes) {
            log.info("DispatchTask任务执行 --> retryTimes 重试次数超过限制, 最大重试次数:{}, 不执行, 任务id:{}",
                    maxRetryTimes, dispatchTaskDo.getId());
            return;
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        try {
            log.info("DispatchTask任务执行 --> 开始执行任务, 任务id:{}", dispatchTaskDo.getId());
            int ret = dispatchTaskService.updateTaskStatus(dispatchTaskDo.getId(),
                    DispatchTaskStatusEnum.INIT.getCode(),
                    DispatchTaskStatusEnum.EXECUTING.getCode());
            if (ret == 0) {
                log.info("DispatchTask任务执行 --> 任务状态已经在执行, 放弃任务, 任务id:{}", dispatchTaskDo.getId());
                return;
            }
            log.info("DispatchTask任务执行 --> 任务开始执行, 任务id:{}, 策略:{}, 渠道:{}", dispatchTaskDo.getId(),
                    dispatchTaskDo.getBizId(), dispatchTaskDo.getAssociationId());
            execute(Long.parseLong(dispatchTaskDo.getAssociationId()), dispatchTaskDo);
            stopwatch.stop();
            log.info("DispatchTask任务执行 --> 任务结束执行, 总计耗时:{}s, 任务id:{}, 策略:{}, 渠道:{}", stopwatch.elapsed(TimeUnit.SECONDS),
                    dispatchTaskDo.getId(), dispatchTaskDo.getBizId(), dispatchTaskDo.getAssociationId());
        } catch (Exception ex) {
            dispatchTaskService.updateDispatchTaskFailedRetry(dispatchTaskDo, ex.getMessage());

            if (stopwatch.isRunning()) {
                stopwatch.stop();
            }
            log.warn("DispatchTask任务执行 --> 任务触发异常, 任务id:{}, 策略:{}, 渠道:{}, 错误原因:{}", dispatchTaskDo.getId(), dispatchTaskDo.getBizId(),
                    dispatchTaskDo.getAssociationId(),
                    ex.toString());
        }
    }

    private boolean customAlarmConfigCheck(String strategyId, long durationMinute) {
        try {
            JSONObject config = JSON.parseObject(ApolloUtil.getAppProperty("alarm.custom.task.duration", "{}"));
            if (config != null && config.get(strategyId) != null) {
                Long spLimit = config.getLong(strategyId);
                if (spLimit != null && spLimit > 0 && durationMinute < spLimit) { // 未达到自定义告警阈值，不触发告警
                    return true;
                }
            }
        } catch (Exception ex) {
            log.error("customAlarmConfigCheck error, strategyId:{}, durationMinute:{}, error:{}", strategyId, durationMinute, ex.getMessage());
        }
        return false;
    }

    // 长时间未完成任务的告警
    public void strategyDispatchTaskExectuteAlarm(int delayMinutes) {
        Date now = new Date();
        int dayValue = DateUtil.dayOfInt(now);
        List<Integer> statusList = DispatchTaskStatusEnum.getNotFinishedList().stream().map(DispatchTaskStatusEnum::getCode)
                .collect(Collectors.toList());
        Date dispatchTime = DateUtils.addMinutes(now, -delayMinutes);
        List<DispatchTaskDo> dispatchTaskDos = dispatchTaskService.selectTodoList(null, dayValue, dispatchTime, statusList);
        dispatchTaskDos.forEach(x -> {
            try {
                String alarmLimitKey = String.format("dispatchTaskAlarm:%s", x.getId());
                if (redisUtils.get(alarmLimitKey) != null) {
                    log.info("告警忽略,taskId={}", x.getId());
                    return;
                }
                StringBuilder builder = new StringBuilder();
                builder.append("标题: 策略长时间未完成告警"); // 对特定策略定义单独告警标准 例如：1991
                builder.append("\n");
                builder.append(String.format("策略编号: %s", x.getBizId()));
                builder.append("\n");
                StrategyDo strategyDo = cacheStrategyService.selectById(Long.parseLong(x.getBizId()));
                if (strategyDo != null) {
                    builder.append(String.format("策略名称: %s", strategyDo.getName()));
                    builder.append("\n");
                }
                long durationMinute = Duration.between(DateUtil.convert(x.getDispatchTime()), DateUtil.convert(now)).toMinutes();
                if(customAlarmConfigCheck(x.getBizId(), durationMinute)) {
                    return;
                }
                builder.append(String.format("任务已超过%s分钟！", durationMinute));
                builder.append(",任务Id是").append(x.getId()).append(",任务设置的触达时间是").append(DateUtil.convertStr(x.getDispatchTime()));
                if (x.getRetryTimes() > 0) {
                    builder.append(String.format(",当前已重试%s次", x.getRetryTimes()));
                    if (x.getRetryTimes() > appConfigService.getDispatchTaskMaxRetryTimes()) {
                        builder.append("已达到最大重试次数！");
                    } else {
                        if (x.getNextDispatchTime() != null) {
                            builder.append(",预计下次自动重试时间是").append(DateUtil.convertStr(x.getNextDispatchTime()));
                        }
                    }
                }
                if (!StringUtils.isEmpty(x.getExecRetMsg())) {
                    builder.append("\n");
                    builder.append("报警原因: ");
                    builder.append(x.getExecRetMsg());
                }
                List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
                if (StringUtils.isNotEmpty(strategyDo.getUpdatedOpMobile())) {
                    atMobileList.add(strategyDo.getUpdatedOpMobile());
                }
                log.info("异常告警:{}", builder);
                DingTalkUtil.sendTextToDingTalk(dingTalkConfig.getAlarmUrl(), builder.toString(), atMobileList, false);
                int expire = 60 * 60;
                redisUtils.set(alarmLimitKey, "1", expire);
            } catch (Exception exception) {
                log.error("告警异常,", exception);
            }
        });
    }

    /**
     * 离线策略执行核心逻辑：离线引擎，离线策略，周期策略
     * 离线策略的不营销渠道一个任务
     * 离线引擎的整个策略一个任务，使用虚拟渠道
     * 周期任务
     *
     * @param marketChannelId 渠道ID
     */
    public void execute(Long marketChannelId, DispatchTaskDo dispatchTaskDo) {
        // 离线引擎策略
        if (dispatchTaskDo != null && DispatchTaskBizTypeEnum
                .isOffEngine(dispatchTaskDo.getBizType())) {
            UdpUtil.getInstance().getBean(StrategyDispatchConstants.OFFLINE_ENGINE_SERVICE, StrategyDispatchService.class)
                    .execute(marketChannelId, dispatchTaskDo);
            return;
        }

        StrategyMarketChannelDo strategyMarketChannelDo = strategyMarketChannelRepository
                .selectById(marketChannelId);
        if (strategyMarketChannelDo == null) {
            log.info("触达渠道不存在, marketChannelId={}", marketChannelId);
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "触达渠道已不存在");
            return;
        }
        Integer marketChannel = strategyMarketChannelDo.getMarketChannel();
        if (StrategyMarketChannelEnum.getIgnoreMarketingCodes().contains(marketChannel)) {
            log.info("触达渠道不营销, marketChannelId={}", marketChannelId);
            dispatchTaskService.updateTaskFailed(dispatchTaskDo, "触达渠道不营销");
            return;
        }
        try {
            this.build(marketChannel).execute(marketChannelId, dispatchTaskDo);
        } catch (Exception ex) {
            if (ex instanceof StrategyException) {
                if (StringUtils.isNotEmpty(ex.getMessage())) {
                    List<String> errors = appConfigService.getIgnoreStrategyJobExcutingErrors();
                    if (!CollectionUtils.isEmpty(errors)) {
                        boolean ignore = errors.stream().anyMatch(x -> ex.getMessage().contains(x));
                        if (ignore) {
                            log.warn("strategy job execute error, exception ignore,  marketChannelId:{}, errors:{}", marketChannelId, ex.getMessage());
                            return;
                        }
                    }
                }
            }
            log.error("strategy job execute error, marketChannelId:{}, errors:{}", marketChannelId, ex.getMessage());
            throw ex;
        }
    }

    /**
     * 策略重试
     *
     * @param strategyExecLogId 执行日志ID
     */
    public void retry(Long strategyExecLogId, DispatchTaskDo dispatchTaskDo) {
        Integer marketChannel = strategyExecLogRepository.selectById(strategyExecLogId).getStrategyMarketChannel();
        this.build(marketChannel).retry(strategyExecLogId, dispatchTaskDo);
    }

    /**
     * 更新策略状态
     */
    public void refreshStatus() {
        strategyService.refreshStatus();
    }

    /**
     * 事件策略更新
     */
    public void strategyEventRefreshStatus() {
        instantStrategyService.strategyEventRefreshStatus();
    }

    /**
     * 超过30分钟无事件告警
     */
    public void strategyEvent30MinAlarm() {
        strategyService.strategyEvent30MinAlarm();
    }

    /**
     * 当天结束后把日志状态修改为已结束
     */
    public void strategyEventRefreshExecLogStatus() {
        strategyExecLogService.strategyEventRefreshExecLogStatus();
    }

    /**
     * 缓存元数据
     */
    public void strategyEventCatchMetadata() {
        strategyEventCatchService.catchMetaData();
    }


    public void strategyEventExecLogRefresh(String param) {
        strategyExecLogService.strategyEventExecLogRefresh(param);
    }

    public void refreshCacheData() {
        // 加载标签元数据
        strategyEventCatchService.catchMetaData();
        // 加载T0（事件策略）状态为执行中的策略列表
        cacheStrategyService.refreshT0ExecutingStrategy();

    }

    /**
     * 获取对应渠道的service
     *
     * @param marketChannel 渠道
     * @return service
     */
    private StrategyDispatchService build(Integer marketChannel) {
        StrategyMarketChannelEnum strategyMarketChannelEnum = StrategyMarketChannelEnum.getInstance(marketChannel);
        return UdpUtil.getInstance().getBean(strategyMarketChannelEnum.getService(), StrategyDispatchService.class);
    }

    /**
     * 策略当天触达统计
     */
    public void countTodayDispatch() {
        strategyService.countTodayDispatch();
    }

    /**
     * T0策略昨天触达人数告警
     */
    public void t0StrategyDispatchUserNumAlarm() {
        instantStrategyService.t0StrategyDispatchUserNumAlarm();
    }

    /**
     * 更新超过回执推送时间，用户状态为失败
     */
    public void dispatchFailUserUpdate() {
        userDispatchDetailService.dispatchFailUserUpdate();
    }

    public void existStrategyFlowCtrlUpdate() {
        strategyService.existStrategyFlowCtrlUpdate();
    }

    public void offlineStrategyCrowdStatusAlarm() {
        strategyService.offlineStrategyCrowdStatusAlarm();
    }

    public void strategyCrowdPackExpireAlarm() {
        strategyService.strategyCrowdPackExpireAlarm();
    }

    public void reportDailyStrategyExecute() {
        //查出所有离线策略id
        List<Integer> sendRulerList = Arrays.asList(StrategyRulerEnum.CYCLE.getCode(), StrategyRulerEnum.CYCLE_DAY.getCode());
        List<StrategyDo> strategyDoList = strategyService.getStrategyList(sendRulerList, null).stream()
                .filter(x-> StringUtils.isEmpty(x.getFlowNo())).collect(Collectors.toList());
        //剔除失效的策略
        strategyDoList = strategyDoList.stream().filter(item -> item.getValidityEnd().isAfter(LocalDateTime.now()) && item.getValidityBegin().isBefore(LocalDateTime.now())).collect(Collectors.toList());
        List<Long> strategyIds = strategyDoList.stream().map(StrategyDo::getId).collect(Collectors.toList());
        log.info("统计离线策略：{}", strategyIds);
//        //通过id查出所有渠道
//        List<StrategyMarketChannelDo> strategyMarketChannelDos = strategyMarketChannelRepository.selectByStrategyIdList(strategyIds);
        //通过策略id，查询执行时间为当天的日志
        if (CollectionUtils.isEmpty(strategyIds)){
            log.info("未查询到存在当日执行的离线策略");
            return;
        }
        List<StrategyExecLogDo> strategyExecLogDos = strategyExecLogService.selectTodayByStrategyIds(strategyIds);
        List<StrategyExecLogDo> strategyExecLogDoList = new ArrayList<>();
        //同一渠道如果有多条取最新一条
        strategyExecLogDos.stream()
                .collect(Collectors.toMap(StrategyExecLogDo::getStrategyMarketChannelId, Function.identity(), (t1, t2) -> t1.getId() > t2.getId() ? t1 : t2))
                .forEach((k, v) -> strategyExecLogDoList.add(v));

        List<ReportDailyStrategyDo> reportDailyStrategyDoList = transformReportDailyStrategy(strategyExecLogDoList, strategyDoList);

        //保存或修改 按照日期 + 渠道id的维度
        reportDailyStrategyService.saveOrUpdateBatch(reportDailyStrategyDoList);
        //统计完之后，记录统计记录
        reportDailyTaskService.saveOrUpdate(ReportDailyTypeEnum.STRATEGY.getCode());
    }


    private List<ReportDailyStrategyDo> transformReportDailyStrategy(List<StrategyExecLogDo> strategyExecLogDos,List<StrategyDo> strategyDoList){
        List<ReportDailyStrategyDo> reportDailyStrategyDoList = new ArrayList<>();
        //策略list转map,  id -> name
        Map<Long, String> strategyIdToName = strategyDoList.stream().collect(Collectors.toMap(StrategyDo::getId, StrategyDo::getName));

        for (StrategyExecLogDo execLog : strategyExecLogDos) {
            ReportDailyStrategyDo reportDaily = new ReportDailyStrategyDo();
            reportDaily.setDate(new Date());
            reportDaily.setStrategyId(execLog.getStrategyId());
            reportDaily.setStrategyName(strategyIdToName.get(execLog.getStrategyId()));
            reportDaily.setStrategyGroupId(execLog.getStrategyGroupId());
            reportDaily.setStrategyGroupName(execLog.getStrategyGroupName());
            reportDaily.setStrategyMarketChannelId(execLog.getStrategyMarketChannelId());
            reportDaily.setStrategyMarketChannel(execLog.getStrategyMarketChannel());
            reportDaily.setExecStartTime(execLog.getExecTime());
            reportDaily.setExecEndTime(execLog.getFinishExecTime() == null ? execLog.getUpdatedTime() : execLog.getFinishExecTime());
            reportDaily.setExecStatus(execLog.getExecStatus());
            reportDaily.setFailReason(execLog.getFailReason());
            // 如果失败原因是策略关联的人群包数量为0，则将策略执行状态重置为"成功"
            if (StringUtils.equals(execLog.getFailReason(), StrategyExecFailReasonEnum.CROWD_PACKAGE_COUNT_IS_0.getReason())) {
                reportDaily.setExecStatus(1);
            }
            reportDaily.setSendCount(execLog.getSendCount());
            reportDaily.setSuccCount(execLog.getSuccCount());
            reportDailyStrategyDoList.add(reportDaily);
        }
        return reportDailyStrategyDoList;
    }

    public void reportDailyStrategyAlarm() {
        //查询当天执行失败的策略
        List<ReportDailyStrategyDo> reportDailyStrategyDoList = reportDailyStrategyService.selectTodayFail();

        //按照策略id分组
        Map<Long, List<ReportDailyStrategyDo>> strategyIdToDetail = reportDailyStrategyDoList.stream().collect(Collectors.groupingBy(ReportDailyStrategyDo::getStrategyId));

        //钉钉告警 todo
    }
}
