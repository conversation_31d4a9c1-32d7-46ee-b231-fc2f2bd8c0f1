package com.xftech.cdp.distribute.crowd.service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import com.xftech.cdp.distribute.crowd.enums.CrowdSliceStatusEnum;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoVersionRepository;
import com.xftech.cdp.distribute.crowd.repository.CrowdSliceRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.distribute.oss.RangeReadFileService;
import com.xftech.cdp.infra.utils.FileUtil;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version v1.0 2025/5/12
 * @description 人群分片处理类
 */
@Slf4j
@Service
public class CrowdSliceService {

    @Value("${CrowdSliceService.getOssFileSizeRetryCount:3}")
    private Integer getOssFileSizeRetryCount;
    @Value("${CrowdSliceService.crowSliceChunkSize:1024}")
    private Long crowSliceChunkSize; // 单位bytes: 1024=1KB; 1024*1024=1MB

    @Resource
    private RangeReadFileService rangeReadFileService;
    @Resource
    private CrowdInfoVersionRepository crowdInfoVersionRepository;
    @Resource
    private CrowdSliceRepository crowdSliceRepository;

    @Transactional(transactionManager = "transactionManager", rollbackFor = Exception.class)
    public void crowdSlice(CrowdInfoVersionDo crowdInfoVersionDo, String caller) {
        Long crowdId = crowdInfoVersionDo.getCrowdId();
        Long crowdVersion = crowdInfoVersionDo.getCrowdVersion();
        String crowdOssFolder = crowdInfoVersionDo.getCrowdOssFolder();
        String crowdOssFile = crowdInfoVersionDo.getCrowdOssFile();

        if (Objects.isNull(crowdId) || Objects.isNull(crowdVersion) || StringUtils.isAnyBlank(crowdOssFolder, crowdOssFile)) {
            // 人群元数据异常告警 TODO
            log.error("CrowdSliceService crowdSlice metadataError, crowdId={}, crowdInfoVersionDo={}, caller={}", crowdId, JSONObject.toJSONString(crowdInfoVersionDo), caller);
            crowdInfoVersionDo.setSliceStatus(CrowdSliceStatusEnum.FAILED.getStatus());
            crowdInfoVersionRepository.insertSelective(crowdInfoVersionDo);
            return;
        }

        List<String> ossFiles = Arrays.asList(crowdOssFile.split(","));
        int successCount = 0;

        for (String ossFile : ossFiles) {
            String ossFilePath = crowdOssFolder + "/" + ossFile;
            long fileSize = 0;
            int retryCount = 1;

            // 查询文件大小,异常重试3次
            while (fileSize <= 0 && retryCount <= getOssFileSizeRetryCount) {
                retryCount++;
                try {
                    fileSize = rangeReadFileService.getOssFileSize(ossFilePath);
                } catch (Exception e) {
                    log.warn("CrowdSliceService crowdSlice getOssFileSize error, crowdId={}, ossFilePath={}, caller={}", crowdId, ossFilePath, caller, e);
                }
            }

            if (fileSize > 0) {
                List<CrowdSliceDo> crowdSliceDoList = Lists.newArrayList();
                List<long[]> crowdChuckPositions = FileUtil.calculateChunkPositionsLastOneMerge(fileSize, crowSliceChunkSize);
                for (long[] chunkPosition : crowdChuckPositions) {
                    CrowdSliceDo crowdSliceDo = new CrowdSliceDo();
                    crowdSliceDo.setCrowdId(crowdId);
                    crowdSliceDo.setCrowdVersion(crowdVersion);
                    crowdSliceDo.setOssUri(ossFilePath);
                    crowdSliceDo.setStartPos(chunkPosition[0]);
                    crowdSliceDo.setEndPos(chunkPosition[1]);
                    crowdSliceDoList.add(crowdSliceDo);
                }
                crowdSliceRepository.insertBatch(crowdSliceDoList);
                successCount++;
            } else {
                log.error("CrowdSliceService crowdSlice fileSizeError, crowdId={}, ossFilePath={}, caller={}", crowdId, ossFilePath, caller);
            }
        }

        if (successCount == ossFiles.size()) {
            crowdInfoVersionDo.setSliceStatus(CrowdSliceStatusEnum.FINISHED.getStatus());
            log.info("CrowdSliceService crowdSlice finished, crowdInfoVersionDo={}, caller={}", JSONObject.toJSONString(crowdInfoVersionDo), caller);
            crowdInfoVersionRepository.insertSelective(crowdInfoVersionDo);
        } else {
            // 分片异常告警 TODO
            crowdInfoVersionDo.setSliceStatus(CrowdSliceStatusEnum.FAILED.getStatus());
            crowdInfoVersionRepository.insertSelective(crowdInfoVersionDo);
            log.error("CrowdSliceService crowdSlice sliceCountError, successCount={} != ossFiles={}, crowdId={}, caller={}", successCount, JSONObject.toJSONString(ossFiles), crowdId, caller);
        }
    }

}
