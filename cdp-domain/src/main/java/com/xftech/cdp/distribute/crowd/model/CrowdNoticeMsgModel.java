package com.xftech.cdp.distribute.crowd.model;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2025/5/7
 * @description CrowdNoticeMsgModel
 */
@Data
public class CrowdNoticeMsgModel {
    /**
     * 消息类型 1=上线;2=下线;3=删除;4=刷新
     */
    private Integer MessageType;
    /**
     * 人群ID
     */
    private Long crowdId;
    /**
     * oss 文件地址目录
     */
    private String ossPath;
    /**
     * oss 文件名称
     */
    private List<String> ossFiles;
    /**
     * 人群大小
     */
    private Long crowdSize;
    /**
     * 人群运行版本
     */
    private String runVersion;
    /**
     * 人群规则版本
     */
    private String ruleVersion;
    /**
     * 有效期开始时间,格式:yyyy-MM-dd HH:mm:ss
     */
    private Date effectStartTime;
    /**
     * 有效期结束时间,格式:yyyy-MM-dd HH:mm:ss
     */
    private Date effectEndTime;
    /**
     * 是否永久有效
     */
    private boolean isPermanent;
}
