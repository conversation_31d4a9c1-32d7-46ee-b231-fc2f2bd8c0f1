/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2004-2025. All Rights Reserved.
 */
package com.xftech.cdp.distribute.offline.engine;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.resp.CyclePreviewDto;
import com.xftech.cdp.distribute.crowd.repository.CrowdSliceRepository;
import com.xftech.cdp.distribute.offline.dto.StrategyExecuteContext;
import com.xftech.cdp.distribute.offline.repository.StrategySliceExecLogRepository;
import com.xftech.cdp.distribute.oss.StrategyOssReaderService;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.dispatch.impl.CrowdDispatchStartRockServiceImpl;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.service.DispatchUserDelayService;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.dispatch.DispatchOfflineEngineService;
import com.xftech.cdp.infra.client.randomnum.RandomNumClient;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.utils.*;
import com.xftech.cdp.distribute.offline.AbstractOfflineStrategyDispatchService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.roaringbitmap.longlong.Roaring64Bitmap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 分布式离线引擎执行核心逻辑
 * <AUTHOR>
 * @version $ AbstractDistributeProcessStrategyService, v 0.1 2025/4/10 14:22 xu.fan Exp $
 */
@Slf4j
@Data
@Service("strategyOfflineEngineDispatchService")
public class DistributeOfflineEngineDispatchServiceImpl extends AbstractOfflineStrategyDispatchService  {
    @Autowired
    private DispatchUserDelayService dispatchUserDelayService;
    @Autowired
    private DispatchOfflineEngineService dispatchOfflineEngineService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private CrowdSliceRepository crowdSliceRepository;
    @Autowired
    private StrategySliceExecLogRepository strategySliceExecLogRepository;
    @Autowired
    private StrategyOssReaderService ossReaderService;
    @Autowired
    private CrowdPackService crowdPackService; // crowdDispatchStartRockService.filter(crowdContent, crowdWereHouses);
    @Autowired
    private CrowdDispatchStartRockServiceImpl crowdDispatchStartRockService;

    @Override
    protected Integer setBatchSize() {
        return getStrategyConfig().getOfflieEngineBatchSize();
    }


    /**
     * OSS 分布式分片读取文件的起始分段，分发执行，执行统计，更新最终执行状态
     * 幂等，重跑，重跑后
     * 执行单位是某个策略的分片，计算完后分片处理的结果更新到统计表中，更新分片最终执行状态到表中
     * 定时任务启动执行，分片切割，分发完成，分发
     *
     * 分片ID,策略ID,user_id,人群包 执行核心逻辑
     * 1、XXL触发，批量查询待处理分片，任务分片，用户在线
     * 2、多个策略打成分片后，如何让小的任务不被大的任务影响，从而延迟太多，多个分片（排序依据，分片拆分时间离现在越近越优先），单机独立排。
     *
     *
     */
    @Override
    public void execute(StrategySliceExecLogDo sliceExecLogDo) {
        StrategyExecuteContext strategyContext = initContext(sliceExecLogDo);
        log.info("DistributeDispatch execute strategyContext:{}", strategyContext);
        try {
            coreLogicExecute(strategyContext);
            successExecute(strategyContext);
        } catch (Exception e) {
            StrategyDo strategyDo = strategyContext.getStrategyDo();
            // 失败状态变更
            String errorMsg = e.getMessage();
            failedExecute(strategyContext, errorMsg);

            List<String> atMobileList = new ArrayList<>(getDingTalkConfig().atMobileList());
            atMobileList.add(strategyDo.getUpdatedOpMobile());
            strategyDo.alarmDingTalk(getDingTalkConfig().getAlarmUrl(), atMobileList, e);
            log.warn("分布式离线引擎策略单片执行异常, 策略ID:{}", strategyDo.getId(), e);
        }
    }


    protected void coreLogicExecute(StrategyExecuteContext strategyContext) {
        StrategyDo strategyDo = strategyContext.getStrategyDo();
        StrategyAbTestEnum abTestEnum = StrategyAbTestEnum.getInstance(strategyDo.getAbTest());

        Map<Long, BiPredicate<String, Integer>> matchFunctions = new HashMap<>();
        List<StrategyGroupDo> strategyGroupDoList = strategyContext.getStrategyGroupDoList();
        if (abTestEnum == StrategyAbTestEnum.YES) {
            // 获取分组匹配规则
            for (StrategyGroupDo group : strategyGroupDoList) {
                matchFunctions.put(group.getId(), group.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType())));
            }
        }
        log.info("DistributeDispatch abTestEnum:{}", abTestEnum);
        // 分页查询+按分组规则过滤+下发
        batchDispatch(strategyContext, matchFunctions);
    }

    protected void batchDispatch(StrategyExecuteContext context, Map<Long, BiPredicate<String, Integer>> matchFunctions) {
        long start = Instant.now().toEpochMilli();
        Roaring64Bitmap container = new Roaring64Bitmap();
        AtomicInteger groupCount = new AtomicInteger(0);
        AtomicInteger totalCount = new AtomicInteger(0);
        AtomicInteger sendCount = new AtomicInteger(0);
        try {
                PageUtil.PaginationParam paginationParam = new PageUtil.PaginationParam(setBatchSize());

                // 分片查询从OSS获取数据
                List<CrowdDetailDo> crowdDetailList = ossReaderService.readBySliceExecLog(context.getStrategySliceExecLogDo());
                if(crowdDetailList == null || crowdDetailList.size() == 0) {
                    return;
                }
                totalCount.updateAndGet(v-> v + crowdDetailList.size());
                log.info("DistributeDispatch batchDispatch:{} crowdDetailList size:{} firstCrowdDetail:{}", context.getStrategyId(), crowdDetailList.size(), crowdDetailList.get(0));
                ossReaderService.filterNewRandom(context.getCrowdContent().get(context.getStrategySliceExecLogDo().getCrowdId()), crowdDetailList);

                int  fullPageNum = crowdDetailList.size() / (int) paginationParam.getPageSize();
                int lastPage = crowdDetailList.size() % (int) paginationParam.getPageSize() == 0? 0 : 1;
                fullPageNum = fullPageNum + lastPage;
                for (int i = 1; i <= fullPageNum; i++) {
                    List<CrowdDetailDo> list = subCrowdDetailList(crowdDetailList, i, (int) paginationParam.getPageSize());
                    log.info("DistributeDispatch batchDispatch:{} pageNum:{} pageSize:{}", list.size(), i, paginationParam.getPageSize());
                    list.stream().collect(groupingBy(CrowdDetailDo::getApp))
                            .forEach((app, detailList) -> {
                                filterAndDispatch(context, matchFunctions, container, groupCount, totalCount, sendCount, app, detailList);
                            });
                }

        } finally {
//            String statisticMapKey = RedisKeyUtils.strategyStatisticMapKey(context.getStrategyId(), LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd"));
            StrategySliceExecLogDo exeLog = context.getStrategySliceExecLogDo();
//            Long oldTotalCnt = exeLog.getTotalUserCnt();
//            Long oldDispatchCnt = exeLog.getDispatchCnt(); // 新增多维度统计字段
//            if(oldTotalCnt != null && oldTotalCnt > 0) {
//                redisUtils.hIncrease(statisticMapKey, "totalExecCount", -oldTotalCnt, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
//            }
//            if(oldDispatchCnt != null && oldDispatchCnt > 0) {
//                redisUtils.hIncrease(statisticMapKey, "sendCount", -oldDispatchCnt, RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
//            }

//            redisUtils.hIncrease(statisticMapKey, "groupCount", groupCount.longValue(), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
//            redisUtils.hIncrease(statisticMapKey, "totalExecCount", totalCount.longValue(), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
//            redisUtils.hIncrease(statisticMapKey, "sendCount", sendCount.longValue(), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            log.info("分组匹配人数:{}，发送成功人数:{}，耗时：{}ms", totalCount.get(), sendCount.get(), Instant.now().toEpochMilli() - start);
            // 写入分片数量 和发送成功数量
            strategySliceExecLogRepository.updateCntNum(exeLog.getId(),
                    totalCount.longValue(), groupCount.longValue(), sendCount.longValue());
        }
    }

    private List<CrowdDetailDo> subCrowdDetailList(List<CrowdDetailDo> crowdDetailList, int pageNum, int pageSize) {

        if(crowdDetailList.size() > (pageNum-1) * pageSize && crowdDetailList.size() >= pageNum * pageSize) {
            return crowdDetailList.subList(((pageNum-1) * pageSize), (pageNum * pageSize));
        } else if (crowdDetailList.size() > (pageNum-1) * pageSize && crowdDetailList.size() < pageNum * pageSize){
            return crowdDetailList.subList(((pageNum-1) * pageSize), crowdDetailList.size());
        } else {
            return Lists.newArrayList();
        }
    }

    protected void filterAndDispatch(StrategyExecuteContext context, Map<Long, BiPredicate<String, Integer>> matchFunctions,
                                     Roaring64Bitmap container, AtomicInteger groupCount, AtomicInteger totalCount, AtomicInteger sendCount,
                                     String app, List<CrowdDetailDo> detailList) {
        log.info("DistributeDispatch filterAndDispatch 开始执行, 策略id:{}, app:{}, 人群包大小:{}", context.getStrategyId(), app, detailList.size());
        // 250326 新增离线引擎过滤开关，修复异常情况不能停止策略执行的问题
        Long strategyId = context.getStrategyDo().getId();
        if (WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "offlineEngineFilterSwitch")) {
            log.warn("DistributeDispatch filterAndDispatch 离线策略引擎推送 --> 过滤命中白名单直接丢弃, strategyId={}", strategyId);
            return;
        }

        int size = detailList.size();
        detailList = cycleFilter(context, detailList, getStrategyService(), getRandomNumClient());
        if (CollectionUtils.isEmpty(detailList)) {
            log.info("DistributeDispatch filterAndDispatch 离线策略引擎推送 --> 周期策略筛选, 本批次无人满足条件, 策略id:{}", context.getStrategyDo().getId());
            return;
        }
        log.info("DistributeDispatch filterAndDispatch 离线引擎策略筛选, 策略id:{}, 过滤前大小:{}, 本批次剩余人数:{}", context.getStrategyDo().getId(), size, detailList.size());
        // 获取随机数  离线引擎部分参数写死
        detailList = getRandomNumService().randomNum(context.getStrategyDo(), -2,null,  detailList);
        //1.1 麻雀-fxk老客转xyf01下发 ,AB分组之后，触达下发前，流控判断前
        detailList = getAbstractAdsStrategyLabelService().convertCrowdList(detailList, context.getStrategyDo().getUserConvert());
        // 2.根据用户ID全局去重
        detailList = detailList.stream().filter(item -> !container.contains(item.getUserId())).collect(Collectors.toList());
        // 3.去重后的用户ID加入到容器中，参与下次去重
        detailList.stream().map(CrowdDetailDo::getUserId).forEachOrdered(container::add);
        // 4.根据手机号去重
        detailList = detailList.stream().filter(ListUtils.distinctByKey(CrowdDetailDo::getMobile)).collect(Collectors.toList());
        // 进入营销分组
        for (StrategyGroupDo strategyGroupDo : context.getStrategyGroupDoList()) {
            StrategyCreateReq.GroupConfig groupConfig = JsonUtil.parse(strategyGroupDo.getGroupConfig(),
                    StrategyCreateReq.GroupConfig.class);
            if (groupConfig == null || !Objects.equals(1, groupConfig.getSelected())) { // 离线引擎策略，不营销组
                String curDate = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMdd");
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(detailList) && detailList.get(0) != null) {
                    redisUtils.pfAddTwoDay(RedisKeyUtils.genOffEngineNotIntoEngineNum(curDate, context.getStrategyDo().getId()), detailList.get(0).getUserId());
                }
                log.info("DistributeDispatch 离线策略引擎推送 --> 分组为不营销, 不进入引擎, strategyId = {}, groupId = {}", strategyGroupDo.getStrategyId(), strategyGroupDo.getId());
                continue;
            }
            log.info("DistributeDispatch filterAndDispatch 分组执行 strategyId:{}, groupId:{} groupName:{}",
                    strategyGroupDo.getStrategyId(), strategyGroupDo.getId(), strategyGroupDo.getName());

            List<CrowdDetailDo> newList = new ArrayList<>(detailList);
            newList = getStrategyGroupService().matchGroupRule(context.getStrategyDo().getBizKey(),matchFunctions.get(strategyGroupDo.getId()), newList);
            // 分组人数统计, 营销的进行统计
            groupCount(context, groupCount, totalCount, sendCount, newList.size());
            // 幂等检查
            filterDuplicateUser(context.getStrategyId(), newList);
            // 5.标签和模板参数查询
            AtomicReference<List<CrowdDetailDo>> availableDetails = new AtomicReference<>();
            availableDetails.set(new ArrayList<>());
            Pair<List<CrowdDetailDo>, List<Object>> pair = labelAndTempParamQuery(context, app, newList, availableDetails);
            //记录排除标签排除人数
            redisUtils.increment(RedisKeyUtils.genOffEngineLabelExcludeSum(LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd"),
                    context.getStrategyDo().getId()), (long) (newList.size() - pair.getLeft().size()), RedisUtils.DEFAULT_EXPIRE_TWO_DAYS);
            // 7.执行下发
            dispatchHandler(context, strategyGroupDo, groupCount, totalCount, sendCount, app, pair.getLeft());
        }
    }

    public void filterDuplicateUser(Long strategyId, List<CrowdDetailDo> crowdDetailDoList) {
        if (CollectionUtils.isEmpty(crowdDetailDoList)) {
            return ;
        }
        int size =crowdDetailDoList.size();
        if(WhitelistSwitchUtil.boolSwitchByApollo("distributeOfflineDuplicateFilterEnable")) { // 对redis有影响时可不开启
            List<CrowdDetailDo> newList = crowdDetailDoList.stream().filter(item -> {
                // 幂等性校验
                boolean isNotDuplicate = isNotDuplicateCall(strategyId, item.getUserId());
                if (!isNotDuplicate) {
                    log.info("distribute离线策略引擎推送 --> 幂等校验, 重复调用, 直接跳过, strategyId = {}, userId = {}", strategyId, item.getUserId());
                }
                return isNotDuplicate;
            }).collect(Collectors.toList());
            crowdDetailDoList = newList;
            log.info("DistributeDispatch 幂等校验 过滤前大小:{} 过滤后大小:{}, 策略id:{}", size, newList.size(), strategyId);
        }
    }

    public List<CrowdDetailDo> cycleFilter(StrategyExecuteContext context, List<CrowdDetailDo> detailList, StrategyService strategyService, RandomNumClient randomNumClient) {
        if (CollectionUtils.isEmpty(detailList)) {
            return detailList;
        }
        if (context.getCycleStrategy() != null) {
            StrategyExecuteContext.CycleStrategy cycleStrategy = context.getCycleStrategy();
            List<CyclePreviewDto> cycleList = strategyService.cyclePreview(cycleStrategy.getCycleNum());
            CyclePreviewDto.Range range = cycleList.stream().filter(x -> Objects.equals(x.getOrder(), cycleStrategy.getCurrCycleNum()))
                    .map(CyclePreviewDto::getRange)
                    .findFirst()
                    .orElse(null);
            log.info("周期策略信息, 开始过滤, 策略id:{}, 总周期:{}, 本周期:{}, 当前随机数值区间:{}", context.getStrategyId(),
                    cycleStrategy.getCycleNum(), cycleStrategy.getCurrCycleNum(), JsonUtil.toJson(range));
            Set<Long> userIdSet = detailList.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toSet());
            Map<Long, String> randomNumMap = randomNumClient.randomNumber(cycleStrategy.getCycleDayConfig().getBizKey(), new ArrayList<>(userIdSet), null);
            return detailList.stream().filter(x -> {
                String ab = randomNumMap.get(x.getUserId());
                if (StringUtils.isEmpty(ab)) {
                    return false;
                }
                assert range != null;
                if (Integer.parseInt(range.getBegin()) <= Integer.parseInt(ab) &&
                        Integer.parseInt(range.getEnd()) >= Integer.parseInt(ab)) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }
        return detailList;
    }

    /**
     * 调用引擎模型，写入dispatch_user_delay表
     * @param strategyContext 策略执行初始化内容
     * @param app             app
     * @param innerApp        innerApp
     * @param batch           按分组配置过滤出来的用户集合
     * @return
     * @param <T>
     */
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyExecuteContext strategyContext, StrategyGroupDo strategyGroupDo, String app,
                                                                           String innerApp, List<CrowdDetailDo> batch) {
        // 处理推送逻辑

        AtomicInteger count = new AtomicInteger();
        if (CollectionUtils.isEmpty(batch)) {
            return ImmutablePair.of(count.get(), null);
        }
        Long strategyExecLogId = 0L;
//        if (strategyContext.getStrategyExecLogDo() != null) {
//            strategyExecLogId = strategyContext.getStrategyExecLogDo().getId();
//        }
        List<Future> tasks = new ArrayList<>();
        List<DispatchUserDelayDo> dispatchUserDelayDos = new ArrayList<>();
        for (CrowdDetailDo crowdDetailDo : batch) {

            Future<List<DispatchUserDelayDo>> listFuture = dispatchOfflineEngineService.pushEngine(strategyContext.getStrategyId(),
                    strategyContext.getStrategyDo().getSendRuler(),
                    strategyExecLogId, strategyGroupDo,
                    strategyContext.getStrategyDo().getEngineCode(), crowdDetailDo);

                tasks.add(listFuture);
        }
        tasks.forEach(t -> {
            try {
                List<DispatchUserDelayDo> dispatchs = (List<DispatchUserDelayDo>) t.get();
                if (dispatchs.size() > 0) {
                    count.getAndIncrement();
                    dispatchUserDelayDos.addAll(dispatchs);
                    DispatchUserDelayDo dispatchDo =dispatchs.get(0);
                    if(dispatchDo != null && dispatchDo.getUserId() != null) {
                        // 写入幂等
                        writeUserExecHis(strategyContext.getStrategyId(), dispatchDo.getUserId());
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        dispatchUserDelayService.batchInsert(dispatchUserDelayDos);

        return ImmutablePair.of(count.get(), null);
    }

    public boolean isNotDuplicateCall(Long strategyId, Long userNo) {
        String versionNo = ApolloUtil.getAppProperty("cdp.distribute.task.versionNo." + strategyId, "1"); // 可调整，保留跳过锁的能力
        String key = RedisKeyUtils.genStrategyUserHisKey(strategyId, userNo, LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd"), versionNo);

        return !redisUtils.hasKey(key);
    }

    public boolean writeUserExecHis(Long strategyId, Long userNo) {
        String versionNo = ApolloUtil.getAppProperty("cdp.distribute.task.versionNo." + strategyId, "1"); // 可调整，保留跳过锁的能力
        String key = RedisKeyUtils.genStrategyUserHisKey(strategyId, userNo, LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd"), versionNo);
        return redisUtils.lock(key, 1, RedisUtils.DEFAULT_EXPIRE_DAYS);
    }
}