/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.offline.dto;

import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.model.dto.StrategyCycleDayConfig;
import com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyGroupDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketChannelDo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ StrategyExecuteContext, v 0.1 2025/4/30 17:00 xu.fan Exp $
 */
@Data
public class StrategyExecuteContext {

    private Long strategyId;

    /**
     * 明确定义策略类型，用于后续扩展
     */
    private String strategyType;

    private StrategyDo strategyDo;

    private List<StrategyGroupDo> strategyGroupDoList;

    private StrategySliceExecLogDo strategySliceExecLogDo;

    private CrowdSliceDo crowdSliceDo; // 人群包分片信息，人群包ID，分片序号，分片大小等

    private CycleStrategy cycleStrategy;

    private StatisticInfo statisticInfo;

    private Map<Long, CrowdContext>  crowdContent;

    @Data
    public static class CycleStrategy {
        private Integer cycleNum;
        private Integer currCycleNum;
        private StrategyCycleDayConfig cycleDayConfig;
    }


    public static StrategyExecuteContext build(Long strategyId, String strategyType, StrategyDo strategyDo,
                                               List<StrategyGroupDo> strategyGroupDoList, StrategySliceExecLogDo strategySliceExecLogDo,
                                               CrowdSliceDo crowdSliceDo, CycleStrategy cycleStrategy, StatisticInfo statisticInfo,
                                               Map<Long, CrowdContext>  crowdContent) {

        StrategyExecuteContext strategyExecuteContext = new StrategyExecuteContext();
        strategyExecuteContext.strategyId = strategyId;
        strategyExecuteContext.strategyType = strategyType;
        strategyExecuteContext.strategyDo = strategyDo;
        strategyExecuteContext.strategyGroupDoList = strategyGroupDoList;
        strategyExecuteContext.strategySliceExecLogDo = strategySliceExecLogDo;
        strategyExecuteContext.crowdSliceDo = crowdSliceDo;
        strategyExecuteContext.cycleStrategy = cycleStrategy;
        strategyExecuteContext.statisticInfo = statisticInfo;
        strategyExecuteContext.crowdContent = crowdContent;
        return strategyExecuteContext;
    }
}
