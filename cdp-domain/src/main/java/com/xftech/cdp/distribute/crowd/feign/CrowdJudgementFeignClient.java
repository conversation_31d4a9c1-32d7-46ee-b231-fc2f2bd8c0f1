package com.xftech.cdp.distribute.crowd.feign;

import com.xftech.cdp.distribute.crowd.feign.interceptor.CrowdFeignClientInterceptor;

import com.leo.datainsight.client.rr.response.RestResponse;
import com.leo.datainsightcore.client.rr.request.ApiBatchJudgeSinglePersonRequest;
import com.leo.datainsightcore.client.rr.request.ApiJudgeSinglePersonRequest;
import com.leo.datainsightcore.client.rr.response.ApiBatchJudgeSinglePersonResponse;
import com.leo.datainsightcore.client.rr.response.ApiJudgeSinglePersonResponse;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @version v1.0 2025/5/9
 * @description CrowdJudgementFeignClient
 * @detail https://alidocs.dingtalk.com/i/nodes/amweZ92PV6v2r7yzhZZ51e7zVxEKBD6p?doc_type=wiki_doc
 */
@FeignClient(
        name = "datainsightcore",
        contextId = "datainsightcore.CrowdJudgementFeignClient",
        path = "/api/crowd",
        configuration = CrowdFeignClientInterceptor.class
)
public interface CrowdJudgementFeignClient {

    @PostMapping({"/judgeSinglePersonForLatestData"})
    @CircuitBreaker(
            name = "crowd-judge"
    )
    RestResponse<ApiJudgeSinglePersonResponse> judgeSinglePersonForLatestData(ApiJudgeSinglePersonRequest request);

    @PostMapping({"/batchJudgeSinglePeopleForLatestData"})
    @CircuitBreaker(
            name = "crowd-judge"
    )
    RestResponse<ApiBatchJudgeSinglePersonResponse> batchJudgeSinglePeopleForLatestData(ApiBatchJudgeSinglePersonRequest request);

    @PostMapping({"/judgeSinglePerson"})
    @CircuitBreaker(
            name = "crowd-judge"
    )
    RestResponse<ApiJudgeSinglePersonResponse> judgeSinglePerson(ApiJudgeSinglePersonRequest request);

    @PostMapping({"/batchJudgeSinglePeople"})
    @CircuitBreaker(
            name = "crowd-judge"
    )
    RestResponse<ApiBatchJudgeSinglePersonResponse> batchJudgeSinglePeople(ApiBatchJudgeSinglePersonRequest request);

}
