/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.interceptor;

import java.nio.charset.StandardCharsets;
import java.util.*;

import com.alibaba.fastjson.JSONObject;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

/**
 * call-service 拦截器
 */
@Slf4j
public class CallFeignClientInterceptor implements RequestInterceptor {

    private static final String APP_KEY = "xyf_cdp";

    private static final String APP_SECRET = "vloeFwiBmL6duCHK";

    private static final String PARAM_BIZ_LINE = "bizLine";

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // 设置请求头
        requestTemplate.header("app_key", APP_KEY);
        // 获取请求的查询参数
        Map<String, Collection<String>> queries = requestTemplate.queries();
        // 遍历参数
        for (Map.Entry<String, Collection<String>> entry : queries.entrySet()) {
            String paramName = entry.getKey();
            if (PARAM_BIZ_LINE.equals(paramName)) {
                Collection<String> paramValues = Optional.ofNullable(entry.getValue()).orElse(Collections.emptyList());
                paramValues.stream().findFirst().ifPresent(paramValue -> requestTemplate.header("sign", sign(APP_SECRET, new TreeMap<String, String>() {{
                    put(paramName, paramValue);
                }})));
            }
        }
        requestTemplate.header("timestamp", String.valueOf(new Date().getTime()));
        log.debug("CallFeignClientInterceptor apply headers={}", JSONObject.toJSON(requestTemplate.headers()));
    }

    private String sign(String appSecret, TreeMap<String, String> params) {
        StringBuilder paramValues = new StringBuilder();
        params.forEach((key, value) -> paramValues.append(key).append("=").append(value).append("&"));
        paramValues.append("app_secret").append("=").append(appSecret);
        return DigestUtils.md5DigestAsHex(paramValues.toString().getBytes(StandardCharsets.UTF_8));
    }

}