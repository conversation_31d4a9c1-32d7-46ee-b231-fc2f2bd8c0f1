package com.xftech.cdp.feign.model;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * push发送用户信息请求参数
 *
 * <AUTHOR>
 * @version $ PushUserData, v 0.1 2024/1/17 15:08 qu.lu Exp $
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PushUserData {
    /**
     * 要推送的用户id，不能与deviceId同时为空
     */
    private String userNo;
    /**
     * 设备id，针对没有登录的用户，不能与userId同时为空
     */
    private String deviceId;
    /**
     * 模板填充数据
     */
    private Map<String, Object> dataMap;

    public PushUserData(CrowdDetailDo crowdDetailDo) {
        this.userNo = crowdDetailDo.getUserId().toString();
        this.deviceId = crowdDetailDo.getDeviceId();
    }

    public PushUserData(String userId) {
        this.userNo = userId;
    }
    public PushUserData(String userId,Map<String, Object> dataMap) {
        this.userNo = userId;
        this.dataMap = dataMap;
    }
}
