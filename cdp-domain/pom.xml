<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xftech.cdp</groupId>
        <artifactId>xyf-cdp</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>cdp-domain</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <engine.new.version>1.0.0.20250228-SNAPSHOT</engine.new.version>
        <kafka.version>3.4.0</kafka.version>
        <oss.version>3.18.1</oss.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.leo.datainsight</groupId>
            <artifactId>client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.leo.datainsightcore</groupId>
            <artifactId>client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-context</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinfei.chgctrlmng</groupId>
            <artifactId>sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>${kafka.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xftech.cdp</groupId>
            <artifactId>cdp-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.xinfei.enginepredictcenter</groupId>
            <artifactId>enginepredictcenter-facade</artifactId>
            <version>${engine.new.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.appcore</groupId>
            <artifactId>appcore-facade</artifactId>
            <version>20250116.SNAPSHOT</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-jdbc</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-to-slf4j</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-starter-apollo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.baomidou</groupId>-->
        <!--            <artifactId>mybatis-plus-boot-starter</artifactId>-->
        <!--        </dependency>-->


        <!--        <dependency>-->
        <!--            <groupId>mysql</groupId>-->
        <!--            <artifactId>mysql-connector-java</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.baomidou</groupId>-->
        <!--            <artifactId>mybatis-plus-generator</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xftech</groupId>
            <artifactId>com.xftech.persistence</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.roaringbitmap/RoaringBitmap -->
        <dependency>
            <groupId>org.roaringbitmap</groupId>
            <artifactId>RoaringBitmap</artifactId>
        </dependency>


        <dependency>
            <groupId>com.cronutils</groupId>
            <artifactId>cron-utils</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk16</artifactId>
            <version>1.46</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-configuration-processor</artifactId>-->
        <!--            &lt;!&ndash;            <optional>true</optional>&ndash;&gt;-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.xftech</groupId>
            <artifactId>com.xftech.zipkin.brave</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xftech</groupId>
            <artifactId>com.xftech.xxljob</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xftech</groupId>
            <artifactId>com.xftech.rabbitmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.3.3</version>
        </dependency>

        <dependency>
            <groupId>com.xinfei.ssocore</groupId>
            <artifactId>ssocore-client</artifactId>
            <version>20240709</version>
        </dependency>

        <dependency>
            <groupId>com.xyf.common</groupId>
            <artifactId>xf-random-generator-client</artifactId>
            <version>20231226.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-spring-boot</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.xyf.common</groupId>-->
<!--            <artifactId>pulsar-stater</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>log4j-slf4j-impl</artifactId>-->
<!--                    <groupId>org.apache.logging.log4j</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client-proxy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-starter-mq</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xinfei.xfframework</groupId>
            <artifactId>xfframework-starter-feign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>2.9.3</version>
        </dependency>

        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>cis-query-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>user-device-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xyf.user</groupId>
            <artifactId>cis-secure-client</artifactId>
            <version>20240605</version>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.9</version>
        </dependency>
        <dependency>
            <groupId>com.xinfei.enginebase</groupId>
            <artifactId>util</artifactId>
            <version>1.0.4.20250417-3</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <!-- This is needed when using Lombok 1.18.16 and above -->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <compilerArg>
                            -Amapstruct.defaultComponentModel=spring
                        </compilerArg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.5</version>
                <configuration>
                    <!-- 在控制台打印执行日志 -->
                    <verbose>true</verbose>
                    <!-- 重复生成时会覆盖之前的文件-->
                    <overwrite>true</overwrite>
                    <configurationFile>src/main/resources/generatorConfig.xml</configurationFile>
                </configuration>
                <!-- 数据库连接选择8.0以上的，因为用的mysql8.0-->
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.25</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>