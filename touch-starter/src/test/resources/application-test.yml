spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.h2.Driver
      url: jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
      username: sa
      password: 
      initial-size: 1
      min-idle: 1
      max-active: 5

  redis:
    host: localhost
    port: 6379
    password: 
    database: 1

# 测试环境配置
touch:
  channel:
    sms:
      service-url: http://mock-sms-service
      timeout: 5000
      retry-times: 1
      enabled: true
    voice:
      service-url: http://mock-tele-service
      timeout: 5000
      retry-times: 1
      enabled: true
    push:
      service-url: http://mock-push-service
      timeout: 5000
      retry-times: 1
      enabled: true
    coupon:
      service-url: http://mock-coupon-service
      timeout: 5000
      retry-times: 1
      enabled: true

  flow-control:
    event:
      enabled: true
    touch:
      enabled: true
    distributed:
      enabled: true

logging:
  level:
    com.xinfei.touch: DEBUG
    org.springframework.web: INFO
