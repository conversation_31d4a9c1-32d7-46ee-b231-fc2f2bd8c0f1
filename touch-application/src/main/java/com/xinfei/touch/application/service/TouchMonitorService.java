package com.xinfei.touch.application.service;

import com.xinfei.touch.application.dto.TouchResponse;
import com.xinfei.touch.application.command.TouchCommand;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 触达监控服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TouchMonitorService {
    
    private final MeterRegistry meterRegistry;
    
    /**
     * 上报监控指标
     * 
     * @param command 触达命令
     * @param response 触达响应
     */
    public void report(TouchCommand command, TouchResponse response) {
        try {
            String channel = command.getChannel().name();
            
            // 记录触达结果
            if (response.getStatus().isSuccess()) {
                recordTouchSuccess(channel);
            } else if (response.getStatus().isFailed()) {
                recordTouchFailure(channel, response.getErrorCode());
            } else if (response.getStatus().isFlowControlled()) {
                recordFlowControl(channel, "TOUCH_LEVEL");
            }
            
            // 记录触达延迟
            if (command.getTimestamp() != null && response.getTimestamp() != null) {
                long latency = response.getTimestamp() - command.getTimestamp();
                recordTouchLatency(channel, latency);
            }
            
        } catch (Exception e) {
            log.error("监控上报失败: requestId={}", command.getRequestId(), e);
        }
    }
    
    /**
     * 记录触达成功
     */
    private void recordTouchSuccess(String channel) {
        Counter.builder("touch.success")
                .tag("channel", channel)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * 记录触达失败
     */
    private void recordTouchFailure(String channel, String errorCode) {
        Counter.builder("touch.failure")
                .tag("channel", channel)
                .tag("error_code", errorCode != null ? errorCode : "UNKNOWN")
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * 记录频控拦截
     */
    private void recordFlowControl(String channel, String ruleType) {
        Counter.builder("touch.flow_control")
                .tag("channel", channel)
                .tag("rule_type", ruleType)
                .register(meterRegistry)
                .increment();
    }
    
    /**
     * 记录触达延迟
     */
    private void recordTouchLatency(String channel, long latencyMs) {
        Timer.builder("touch.latency")
                .tag("channel", channel)
                .register(meterRegistry)
                .record(latencyMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 上报回执监控指标
     *
     * @param receiptMessage 回执消息
     */
    public void reportReceipt(com.xinfei.touch.domain.model.ReceiptMessage receiptMessage) {
        try {
            String channel = receiptMessage.getChannel().name();

            // 记录回执结果
            if (receiptMessage.getStatus().isSuccess()) {
                recordReceiptSuccess(channel);
            } else if (receiptMessage.getStatus().isFailed()) {
                recordReceiptFailure(channel, receiptMessage.getErrorCode());
            }

        } catch (Exception e) {
            log.error("回执监控上报失败: requestId={}", receiptMessage.getRequestId(), e);
        }
    }

    /**
     * 记录回执成功
     */
    private void recordReceiptSuccess(String channel) {
        Counter.builder("touch.receipt.success")
                .tag("channel", channel)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录回执失败
     */
    private void recordReceiptFailure(String channel, String errorCode) {
        Counter.builder("touch.receipt.failure")
                .tag("channel", channel)
                .tag("error_code", errorCode != null ? errorCode : "UNKNOWN")
                .register(meterRegistry)
                .increment();
    }
}
