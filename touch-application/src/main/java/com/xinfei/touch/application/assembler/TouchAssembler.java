package com.xinfei.touch.application.assembler;

import com.xinfei.touch.application.dto.TouchRequest;
import com.xinfei.touch.application.command.TouchCommand;
import com.xinfei.touch.application.command.TouchConfigCommand;
import org.springframework.stereotype.Component;

/**
 * 触达对象转换器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class TouchAssembler {
    
    /**
     * 转换为触达命令
     * 
     * @param request 触达请求
     * @return 触达命令
     */
    public TouchCommand toCommand(TouchRequest request) {
        TouchCommand command = new TouchCommand();
        command.setRequestId(request.getRequestId());
        command.setTouchType(request.getTouchType());
        command.setChannel(request.getChannel());
        command.setStrategyId(request.getStrategyId());
        command.setUserId(request.getUserId());
        command.setBizEventType(request.getBizEventType());
        command.setTemplateParams(request.getTemplateParams());
        command.setTimestamp(request.getTimestamp());
        command.setExtParams(request.getExtParams());
        
        // 转换触达配置
        if (request.getTouchConfig() != null) {
            TouchConfigCommand configCommand = new TouchConfigCommand();
            configCommand.setTemplateId(request.getTouchConfig().getTemplateId());
            configCommand.setTemplateContent(request.getTouchConfig().getTemplateContent());
            configCommand.setPriority(request.getTouchConfig().getPriority());
            configCommand.setTimeout(request.getTouchConfig().getTimeout());
            configCommand.setRetryTimes(request.getTouchConfig().getRetryTimes());
            configCommand.setAsync(request.getTouchConfig().getAsync());
            configCommand.setCallbackUrl(request.getTouchConfig().getCallbackUrl());
            configCommand.setExtConfig(request.getTouchConfig().getExtConfig());
            command.setTouchConfig(configCommand);
        }
        
        return command;
    }
}
