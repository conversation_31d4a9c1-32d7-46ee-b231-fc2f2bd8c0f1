package com.xinfei.touch.application.service;

import com.xinfei.touch.domain.model.TouchStatus;
import com.xinfei.touch.domain.model.TouchType;
import com.xinfei.touch.domain.model.unified.*;
import com.xinfei.touch.domain.service.UnifiedTouchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;

/**
 * 统一触达应用服务实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Slf4j
@Service
public class UnifiedTouchApplicationService implements UnifiedTouchService {
    
    /**
     * 存储异步处理的进度信息
     */
    private final ConcurrentMap<String, TouchResponse> progressCache = new ConcurrentHashMap<>();
    
    /**
     * 存储异步任务的Future对象，用于取消操作
     */
    private final ConcurrentMap<String, CompletableFuture<TouchResponse>> taskCache = new ConcurrentHashMap<>();
    
    @Override
    public TouchResponse processTouch(TouchRequest request) {
        log.info("开始处理触达请求: requestId={}, touchType={}, touchMode={}", 
                request.getRequestId(), request.getTouchType(), request.getTouchMode());
        
        try {
            // 1. 验证请求参数
            request.validate();
            
            // 2. 根据触达类型和模式选择处理策略
            if (request.isSingleMode()) {
                return processSingleTouch(request);
            } else {
                return processBatchTouch(request);
            }
            
        } catch (Exception e) {
            log.error("处理触达请求失败: requestId={}", request.getRequestId(), e);
            return TouchResponse.failed(request.getRequestId(), "PROCESS_ERROR", e.getMessage());
        }
    }
    
    @Override
    public TouchResponse processTouchAsync(TouchRequest request) {
        log.info("开始异步处理触达请求: requestId={}, touchType={}, touchMode={}", 
                request.getRequestId(), request.getTouchType(), request.getTouchMode());
        
        try {
            // 1. 验证请求参数
            request.validate();
            
            // 2. 创建初始响应
            String batchNo = generateBatchNo();
            TouchResponse initialResponse = TouchResponse.pending(request.getRequestId(), batchNo);
            progressCache.put(request.getRequestId(), initialResponse);
            
            // 3. 异步执行处理
            CompletableFuture<TouchResponse> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return processTouch(request);
                } catch (Exception e) {
                    log.error("异步处理触达请求失败: requestId={}", request.getRequestId(), e);
                    return TouchResponse.failed(request.getRequestId(), "ASYNC_PROCESS_ERROR", e.getMessage());
                }
            });
            
            // 4. 处理完成后更新进度
            future.whenComplete((result, throwable) -> {
                if (throwable != null) {
                    TouchResponse errorResponse = TouchResponse.failed(request.getRequestId(), "ASYNC_ERROR", throwable.getMessage());
                    progressCache.put(request.getRequestId(), errorResponse);
                } else {
                    progressCache.put(request.getRequestId(), result);
                }
                taskCache.remove(request.getRequestId());
            });
            
            taskCache.put(request.getRequestId(), future);
            
            return initialResponse;
            
        } catch (Exception e) {
            log.error("启动异步处理失败: requestId={}", request.getRequestId(), e);
            return TouchResponse.failed(request.getRequestId(), "ASYNC_START_ERROR", e.getMessage());
        }
    }
    
    @Override
    public TouchResponse queryTouchProgress(String requestId) {
        log.debug("查询触达进度: requestId={}", requestId);
        
        TouchResponse progress = progressCache.get(requestId);
        if (progress == null) {
            return TouchResponse.failed(requestId, "NOT_FOUND", "未找到对应的处理记录");
        }
        
        return progress;
    }
    
    @Override
    public boolean cancelTouch(String requestId) {
        log.info("取消触达处理: requestId={}", requestId);
        
        CompletableFuture<TouchResponse> future = taskCache.get(requestId);
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                TouchResponse cancelledResponse = TouchResponse.failed(requestId, "CANCELLED", "处理已被取消");
                progressCache.put(requestId, cancelledResponse);
                taskCache.remove(requestId);
                log.info("成功取消触达处理: requestId={}", requestId);
                return true;
            }
        }
        
        log.warn("无法取消触达处理: requestId={}", requestId);
        return false;
    }
    
    /**
     * 处理单用户触达
     */
    private TouchResponse processSingleTouch(TouchRequest request) {
        log.debug("处理单用户触达: requestId={}, userId={}", 
                request.getRequestId(), request.getUserInfo().getUserId());
        
        Long startTime = System.currentTimeMillis();
        String batchNo = generateBatchNo();
        
        try {
            // 1. 流控检查
            if (shouldFlowControl(request)) {
                log.info("触达被流控拦截: requestId={}, userId={}", 
                        request.getRequestId(), request.getUserInfo().getUserId());
                return TouchResponse.flowControlled(request.getRequestId(), "触达频率超限");
            }
            
            // 2. 执行具体的触达逻辑
            TouchUserResult userResult = executeSingleTouch(request, batchNo);
            
            // 3. 创建响应
            TouchResponse response = TouchResponse.success(request.getRequestId(), batchNo, userResult);
            
            // 4. 设置统计信息
            Long endTime = System.currentTimeMillis();
            response.setStatistics(startTime, endTime);
            
            log.info("单用户触达处理完成: requestId={}, userId={}, status={}", 
                    request.getRequestId(), request.getUserInfo().getUserId(), userResult.getStatus());
            
            return response;
            
        } catch (Exception e) {
            log.error("单用户触达处理失败: requestId={}, userId={}", 
                    request.getRequestId(), request.getUserInfo().getUserId(), e);
            return TouchResponse.failed(request.getRequestId(), "SINGLE_TOUCH_ERROR", e.getMessage());
        }
    }
    
    /**
     * 处理批量触达
     */
    private TouchResponse processBatchTouch(TouchRequest request) {
        log.debug("处理批量触达: requestId={}, userCount={}", 
                request.getRequestId(), request.getUserCount());
        
        Long startTime = System.currentTimeMillis();
        String batchNo = generateBatchNo();
        
        try {
            // 1. 创建批量结果
            BatchResult batchResult = BatchResult.create(request.getUserCount());
            
            // 2. 处理每个用户
            for (TouchUserInfo userInfo : request.getUserList()) {
                try {
                    // 创建单用户请求
                    TouchRequest singleRequest = createSingleRequest(request, userInfo);
                    
                    // 流控检查
                    if (shouldFlowControl(singleRequest)) {
                        batchResult.incrementFlowControlled();
                        continue;
                    }
                    
                    // 执行触达
                    TouchUserResult userResult = executeSingleTouch(singleRequest, batchNo);
                    
                    if (userResult.isSuccess()) {
                        batchResult.incrementSuccess();
                    } else {
                        batchResult.incrementFailed();
                        if (userResult.getErrorMessage() != null) {
                            batchResult.addFailedReason(userResult.getErrorMessage());
                        }
                    }
                    
                } catch (Exception e) {
                    log.error("批量触达中单用户处理失败: requestId={}, userId={}", 
                            request.getRequestId(), userInfo.getUserId(), e);
                    batchResult.incrementFailed();
                    batchResult.addFailedReason(e.getMessage());
                }
            }
            
            // 3. 完成批量处理
            batchResult.complete();
            
            // 4. 创建响应
            TouchStatus status = determineOverallStatus(batchResult);
            TouchResponse response;
            
            if (status == TouchStatus.SUCCESS) {
                response = TouchResponse.success(request.getRequestId(), batchNo, null, batchResult);
            } else if (status == TouchStatus.PARTIAL_SUCCESS) {
                response = TouchResponse.partialSuccess(request.getRequestId(), batchNo, null, batchResult);
            } else {
                response = TouchResponse.failed(request.getRequestId(), "BATCH_FAILED", "批量处理失败");
                response.setBatchResult(batchResult);
            }
            
            // 5. 设置统计信息
            Long endTime = System.currentTimeMillis();
            response.setStatistics(startTime, endTime);
            
            log.info("批量触达处理完成: requestId={}, totalCount={}, successCount={}, failedCount={}", 
                    request.getRequestId(), batchResult.getTotalCount(), 
                    batchResult.getSuccessCount(), batchResult.getFailedCount());
            
            return response;
            
        } catch (Exception e) {
            log.error("批量触达处理失败: requestId={}", request.getRequestId(), e);
            return TouchResponse.failed(request.getRequestId(), "BATCH_TOUCH_ERROR", e.getMessage());
        }
    }
    
    /**
     * 执行单个用户的触达
     */
    private TouchUserResult executeSingleTouch(TouchRequest request, String batchNo) {
        TouchUserInfo userInfo = request.getUserInfo();
        
        try {
            // 根据触达类型执行不同的逻辑
            if (request.getTouchType() == TouchType.REALTIME_NORMAL) {
                return executeT0NormalTouch(request, batchNo);
            } else if (request.getTouchType() == TouchType.REALTIME_ENGINE) {
                return executeT0EngineTouch(request, batchNo);
            } else if (request.getTouchType() == TouchType.OFFLINE_NORMAL) {
                return executeOfflineNormalTouch(request, batchNo);
            } else if (request.getTouchType() == TouchType.OFFLINE_ENGINE) {
                return executeOfflineEngineTouch(request, batchNo);
            } else {
                throw new IllegalArgumentException("不支持的触达类型: " + request.getTouchType());
            }
            
        } catch (Exception e) {
            log.error("执行用户触达失败: userId={}, touchType={}", 
                    userInfo.getUserId(), request.getTouchType(), e);
            return TouchUserResult.failed(userInfo.getUserId(), batchNo, "EXECUTE_ERROR", e.getMessage());
        }
    }
    
    /**
     * 执行T0普通触达
     */
    private TouchUserResult executeT0NormalTouch(TouchRequest request, String batchNo) {
        // TODO: 实现T0普通触达逻辑
        log.debug("执行T0普通触达: userId={}", request.getUserInfo().getUserId());
        
        // 模拟处理
        try {
            Thread.sleep(100); // 模拟处理时间
            return TouchUserResult.success(request.getUserInfo().getUserId(), batchNo);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return TouchUserResult.failed(request.getUserInfo().getUserId(), batchNo, "INTERRUPTED", "处理被中断");
        }
    }
    
    /**
     * 执行T0引擎触达
     */
    private TouchUserResult executeT0EngineTouch(TouchRequest request, String batchNo) {
        // TODO: 实现T0引擎触达逻辑
        log.debug("执行T0引擎触达: userId={}, engineGroupName={}", 
                request.getUserInfo().getUserId(), request.getEngineGroupName());
        
        // 模拟处理
        try {
            Thread.sleep(150); // 模拟处理时间
            return TouchUserResult.success(request.getUserInfo().getUserId(), batchNo);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return TouchUserResult.failed(request.getUserInfo().getUserId(), batchNo, "INTERRUPTED", "处理被中断");
        }
    }
    
    /**
     * 执行离线普通触达
     */
    private TouchUserResult executeOfflineNormalTouch(TouchRequest request, String batchNo) {
        // TODO: 实现离线普通触达逻辑
        log.debug("执行离线普通触达: userId={}", request.getUserInfo().getUserId());
        
        // 模拟处理
        try {
            Thread.sleep(50); // 模拟处理时间
            return TouchUserResult.success(request.getUserInfo().getUserId(), batchNo);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return TouchUserResult.failed(request.getUserInfo().getUserId(), batchNo, "INTERRUPTED", "处理被中断");
        }
    }
    
    /**
     * 执行离线引擎触达
     */
    private TouchUserResult executeOfflineEngineTouch(TouchRequest request, String batchNo) {
        // TODO: 实现离线引擎触达逻辑
        log.debug("执行离线引擎触达: userId={}, engineGroupName={}", 
                request.getUserInfo().getUserId(), request.getEngineGroupName());
        
        // 模拟处理
        try {
            Thread.sleep(80); // 模拟处理时间
            return TouchUserResult.success(request.getUserInfo().getUserId(), batchNo);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return TouchUserResult.failed(request.getUserInfo().getUserId(), batchNo, "INTERRUPTED", "处理被中断");
        }
    }
    
    /**
     * 判断是否需要流控
     */
    private boolean shouldFlowControl(TouchRequest request) {
        // TODO: 实现具体的流控逻辑
        FlowControlConfig flowConfig = request.getFlowControlConfig();
        if (flowConfig == null || !flowConfig.isAnyFlowControlEnabled()) {
            return false;
        }
        
        // 模拟流控检查
        return false; // 暂时不进行流控
    }
    
    /**
     * 从批量请求创建单用户请求
     */
    private TouchRequest createSingleRequest(TouchRequest batchRequest, TouchUserInfo userInfo) {
        TouchRequest singleRequest = new TouchRequest();
        
        // 复制基础信息
        singleRequest.setRequestId(batchRequest.getRequestId() + "_" + userInfo.getUserId());
        singleRequest.setTouchType(batchRequest.getTouchType());
        singleRequest.setTouchMode(TouchMode.SINGLE);
        singleRequest.setTimestamp(System.currentTimeMillis());
        singleRequest.setTraceId(batchRequest.getTraceId());
        
        // 复制策略信息
        singleRequest.setStrategyId(batchRequest.getStrategyId());
        singleRequest.setStrategyExecId(batchRequest.getStrategyExecId());
        singleRequest.setStrategyGroupId(batchRequest.getStrategyGroupId());
        singleRequest.setStrategyGroupName(batchRequest.getStrategyGroupName());
        singleRequest.setStrategyChannelId(batchRequest.getStrategyChannelId());
        singleRequest.setChannel(batchRequest.getChannel());
        singleRequest.setTemplateId(batchRequest.getTemplateId());
        
        // 设置单用户信息
        singleRequest.setUserInfo(userInfo);
        
        // 复制其他信息
        singleRequest.setBizEventType(batchRequest.getBizEventType());
        singleRequest.setBizEventData(batchRequest.getBizEventData());
        singleRequest.setTemplateParams(batchRequest.getTemplateParams());
        singleRequest.setExtData(batchRequest.getExtData());
        singleRequest.setEngineCode(batchRequest.getEngineCode());
        singleRequest.setEngineGroupName(batchRequest.getEngineGroupName());
        singleRequest.setEngineDetail(batchRequest.getEngineDetail());
        singleRequest.setTouchConfig(batchRequest.getTouchConfig());
        singleRequest.setFlowControlConfig(batchRequest.getFlowControlConfig());
        
        return singleRequest;
    }
    
    /**
     * 根据批量结果确定整体状态
     */
    private TouchStatus determineOverallStatus(BatchResult batchResult) {
        if (batchResult.isAllSuccess()) {
            return TouchStatus.SUCCESS;
        } else if (batchResult.isAllFailed()) {
            return TouchStatus.FAILED;
        } else {
            return TouchStatus.PARTIAL_SUCCESS;
        }
    }
    
    /**
     * 生成批次号
     */
    private String generateBatchNo() {
        return "BATCH_" + System.currentTimeMillis() + "_" + 
               java.util.UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
