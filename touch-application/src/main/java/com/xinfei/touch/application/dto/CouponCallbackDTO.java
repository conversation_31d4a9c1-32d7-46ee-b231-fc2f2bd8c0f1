package com.xinfei.touch.application.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 优惠券回执DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class CouponCallbackDTO {
    
    /**
     * 批次号
     */
    private String batchNo;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 策略ID
     */
    private Long strategyId;
    
    /**
     * 回执状态：successed-发送成功，failed-发送失败，used-使用成功
     */
    private String status;
    
    /**
     * 使用状态：0-未使用，1-已使用，-1-不适用
     */
    private Integer usedStatus;
    
    /**
     * 优惠券类型：4-生活权益，5-X天免息
     */
    private Integer couponType;
    
    /**
     * 业务类型
     */
    private String bizType;
    
    /**
     * 错误码
     */
    private String errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 回执时间
     */
    private LocalDateTime receiptTime;
    
    /**
     * 优惠券ID
     */
    private String couponId;
    
    /**
     * 优惠券金额
     */
    private Long couponAmount;
    
    /**
     * 扩展信息
     */
    private String extInfo;
}
