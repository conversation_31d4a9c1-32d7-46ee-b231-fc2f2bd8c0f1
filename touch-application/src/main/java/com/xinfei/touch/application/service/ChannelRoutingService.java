package com.xinfei.touch.application.service;

import com.xinfei.touch.domain.model.TouchChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 渠道路由服务
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChannelRoutingService {
    
    private final List<ChannelPlugin> channelPlugins;
    private final Map<TouchChannel, ChannelPlugin> channelPluginMap = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        // 初始化渠道插件映射
        for (ChannelPlugin plugin : channelPlugins) {
            channelPluginMap.put(plugin.getChannel(), plugin);
            log.info("注册渠道插件: channel={}, plugin={}", plugin.getChannel(), plugin.getClass().getSimpleName());
        }
    }
    
    /**
     * 路由到指定渠道
     * 
     * @param channel 触达渠道
     * @return 渠道插件
     */
    public ChannelPlugin route(TouchChannel channel) {
        ChannelPlugin plugin = channelPluginMap.get(channel);
        
        if (plugin == null) {
            log.warn("未找到渠道插件: channel={}", channel);
            return null;
        }
        
        if (!plugin.isAvailable()) {
            log.warn("渠道插件不可用: channel={}", channel);
            return null;
        }
        
        return plugin;
    }
    
    /**
     * 获取所有可用的渠道插件
     * 
     * @return 可用的渠道插件列表
     */
    public List<ChannelPlugin> getAvailablePlugins() {
        return channelPlugins.stream()
                .filter(ChannelPlugin::isAvailable)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查渠道是否可用
     * 
     * @param channel 触达渠道
     * @return 是否可用
     */
    public boolean isChannelAvailable(TouchChannel channel) {
        ChannelPlugin plugin = channelPluginMap.get(channel);
        return plugin != null && plugin.isAvailable();
    }
}
