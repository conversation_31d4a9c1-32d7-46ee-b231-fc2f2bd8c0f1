package com.xinfei.touch.application.service;

import com.xinfei.touch.application.dto.TouchResponse;
import com.xinfei.touch.application.command.TouchCommand;
import com.xinfei.touch.domain.model.TouchChannel;

/**
 * 渠道插件接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ChannelPlugin {
    
    /**
     * 获取渠道类型
     * 
     * @return 渠道类型
     */
    TouchChannel getChannel();
    
    /**
     * 发送触达
     * 
     * @param command 触达命令
     * @return 触达响应
     */
    TouchResponse send(TouchCommand command);
    
    /**
     * 渠道可用性检查
     * 
     * @return 是否可用
     */
    boolean isAvailable();
    
    /**
     * 获取渠道配置
     * 
     * @return 渠道配置
     */
    ChannelConfig getConfig();
    
    /**
     * 健康检查
     * 
     * @return 健康状态
     */
    default boolean healthCheck() {
        try {
            return isAvailable();
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取插件名称
     * 
     * @return 插件名称
     */
    default String getPluginName() {
        return getChannel().getName() + "Plugin";
    }
    
    /**
     * 获取插件版本
     * 
     * @return 插件版本
     */
    default String getPluginVersion() {
        return "1.0.0";
    }
}
