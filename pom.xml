<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xftech.cdp</groupId>
    <artifactId>xyf-cdp</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>cdp-api</module>
        <module>cdp-domain</module>
        <module>bootstrap</module>
    </modules>

    <!-- Spring Boot 启动父依赖 -->
    <parent>
        <groupId>com.xftech</groupId>
        <artifactId>com.xftech.parent</artifactId>
        <version>1.0.13</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <base.version>${project.version}</base.version>
        <lombok.version>1.18.24</lombok.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <aliyun.sdk.oss.version>3.18.1</aliyun.sdk.oss.version>
        <spring-boot.version>2.3.12.RELEASE</spring-boot.version>
        <xxl-job.version>2.2.0</xxl-job.version>
        <swagger.knife.version>3.0.3</swagger.knife.version>
        <mysql.version>8.0.25</mysql.version>
        <easyexcel.version>3.0.5</easyexcel.version>
        <mybatis-plus.version>3.4.2</mybatis-plus.version>
        <skywalking.version>8.8.0</skywalking.version>
        <fastjson.version>1.2.83</fastjson.version>
        <myabtis-generator>3.4.1</myabtis-generator>
        <freemaker.version>2.3.30</freemaker.version>
        <hutool.version>5.7.9</hutool.version>
        <alibaba-dingtalk-service-sdk.version>2.0.0</alibaba-dingtalk-service-sdk.version>
        <roaring-bitmap.version>0.9.39</roaring-bitmap.version>
        <cron-version> 9.2.0 </cron-version>
        <log4j-core>2.17.1</log4j-core>
        <spring-amqp.version>2.4.17</spring-amqp.version>
        <xfframework.starter.version>1.3.6.20240228</xfframework.starter.version>
        <chg.ctrl.version>1.0.4.20250410</chg.ctrl.version>
        <xfmq.starter.version>1.4.9.2025042201</xfmq.starter.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.leo.datainsight</groupId>
                <artifactId>client</artifactId>
                <version>1.0.7-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.leo.datainsightcore</groupId>
                <artifactId>client</artifactId>
                <version>1.0.2-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-context</artifactId>
                <version>${xfframework.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xinfei.chgctrlmng</groupId>
                <artifactId>sdk</artifactId>
                <version>${chg.ctrl.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <optional>true</optional>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xftech.cdp</groupId>
                <artifactId>cdp-api</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xftech.cdp</groupId>
                <artifactId>cdp-domain</artifactId>
                <version>${base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${swagger.knife.version}</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.sdk.oss.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-starter-apollo</artifactId>
                <version>${xfframework.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>


            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${myabtis-generator}</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemaker.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>${alibaba-dingtalk-service-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.roaringbitmap</groupId>
                <artifactId>RoaringBitmap</artifactId>
                <version>${roaring-bitmap.version}</version>
            </dependency>


            <dependency>
                <groupId>com.cronutils</groupId>
                <artifactId>cron-utils</artifactId>
                <version>${cron-version}</version>
            </dependency>

            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-starter-mq</artifactId>
                <version>${xfmq.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xinfei.xfframework</groupId>
                <artifactId>xfframework-starter-feign</artifactId>
                <version>${xfframework.starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xftech</groupId>
                <artifactId>com.xftech.zipkin.brave</artifactId>
                <version>1.0.19</version>
            </dependency>
            <dependency>
                <groupId>com.xftech</groupId>
                <artifactId>com.xftech.rabbitmq</artifactId>
                <version>1.0.50</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.xyf.common</groupId>-->
<!--                <artifactId>pulsar-stater</artifactId>-->
<!--                <version>1.0.1</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.dianping.cat</groupId>
                <artifactId>cat-client-proxy</artifactId>
                <version>3.1.7</version>
            </dependency>

            <dependency>
                <groupId>com.xftech</groupId>
                <artifactId>com.xftech.xxljob</artifactId>
                <version>1.0.14</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j-core}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-amqp</artifactId>
                <version>${spring-amqp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xyf.user</groupId>
                <artifactId>cis-query-facade</artifactId>
                <version>20250416.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.xyf.user</groupId>
                <artifactId>user-device-facade</artifactId>
                <version>20241118.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.xinfei.common</groupId>
                <artifactId>xinfei-common-lang</artifactId>
                <version>1.0.0.20240730</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


</project>