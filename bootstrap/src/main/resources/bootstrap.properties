spring.application.name=xyf-cdp
spring.profiles.active=local

spring.cloud.compatibility-verifier.enabled=false

#spring.mvc.servlet.path=/api
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.default-property-inclusion=non_null

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
#mybatis-plus.config-location=classpath*:**/mapper/*.xml

#mybatis-plus.mapper-locations = classpath*:**/mapper/*.xml
#mybatis-plus.type-aliases-package= com.xftech.cdp.infra.repository.cdp.*.po
#spring.jackson.time-zone=GMT+8

app.id=${spring.application.name}
app.name=\u9EBB\u96C0\u7CFB\u7EDF

# \u914D\u7F6E\u4E00\u4E2A\u8FDE\u63A5\u5728\u6C60\u4E2D\u6700\u5C0F\u751F\u5B58\u7684\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2
spring.datasource.minEvictableIdleTimeMillis=30000
spring.datasource.maxEvictableIdleTimeMillis=180000

spring.main.allow-bean-definition-overriding=true
logging.config=classpath:logback/logback-pro.xml

#feign.client.config.datafeaturecore.connectTimeout= 5000
#feign.client.config.datafeaturecore.readTimeout= 5000