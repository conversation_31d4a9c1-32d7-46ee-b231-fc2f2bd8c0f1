/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.dispatch.impl.AbstractDispatchService;
import com.xftech.cdp.domain.strategy.model.dto.IncreaseAmtParamDto;
import com.xftech.cdp.infra.client.increaseamt.IncreaseAmtClient;
import com.xftech.cdp.infra.client.increaseamt.model.dto.UserInfoDto;
import com.xftech.cdp.infra.client.increaseamt.model.req.Request;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Resp;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xyf.cis.query.facade.BatchQueryFacade;
import com.xyf.cis.query.facade.dto.standard.request.QueryMobilesByUserNosRequest;
import com.xyf.cis.query.facade.dto.standard.response.RegisterInfoDTO;
import com.xyf.user.facade.common.model.BaseResponse;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ BatchQueryFacadeTest, v 0.1 2024/3/13 16:51 yye.xu Exp $
 */

public class BatchQueryFacadeTest extends BaseTest {
    @Autowired
    private BatchQueryFacade batchQueryFacade;
    @Autowired
    CisService cisService;
    @Autowired
    IncreaseAmtClient increaseAmtClient;
    @Autowired
    AbstractDispatchService abstractDispatchService;

    @Test
    public void test_queryBatchUserByUserNo() {
        QueryMobilesByUserNosRequest var1 = new QueryMobilesByUserNosRequest();
        var1.setUserNos(Arrays.asList(1005144L, 1055648L, 1260008L));
        BaseResponse<List<RegisterInfoDTO>> response = batchQueryFacade.queryBatchUserByUserNo(var1);

        System.out.println(JsonUtil.toJson(response));
    }

    @Test
    public void test_increaseAmt() {
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setUserId(1639203089095980520L);
        crowdDetailDo.setApp("xyf01");
        crowdDetailDo.setInnerApp("xyf01");
        List<UserInfoDto> userInfoDtos = cisService.getUserInfoDtos(Arrays.asList(crowdDetailDo));
        UserInfoDto userInfoDto = userInfoDtos.get(0);
        IncreaseAmtParamDto increaseAmtParamDto = new IncreaseAmtParamDto();
        increaseAmtParamDto.setEndTime("2023-03-16 00:00:00");
        increaseAmtParamDto.setStartTime("2023-03-15 00:00:00");
        increaseAmtParamDto.setAmount(10000);
        Long detailId = 11L;
        String batchNum = "678900136901881857";
        String orderNo = String.format("%s_%s_%s", 202403, detailId, batchNum);
        RequestHeader header = abstractDispatchService.buildIncreaseAmtHeader(crowdDetailDo.getUserId(), orderNo, crowdDetailDo.getApp(), crowdDetailDo.getInnerApp(), null, null, null);
        // 自定义业务参数
        Request request = abstractDispatchService.buildIncreaseAmtBody(increaseAmtParamDto, "202403", batchNum, detailId, 0L, "real", crowdDetailDo.getApp(),
                crowdDetailDo.getInnerApp(), userInfoDto, orderNo);
        Resp resp = increaseAmtClient.increaseAmt(header, request);

        System.out.println(JsonUtil.toJson(resp));
    }
}