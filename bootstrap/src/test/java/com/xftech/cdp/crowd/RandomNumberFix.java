package com.xftech.cdp.crowd;


import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.impl.CrowdPackServiceImpl;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ RandomNumber, v 0.1 2025/4/9 16:31 mingwen.zang
 */
public class RandomNumberFix extends BaseTest {
    @Resource
    public CrowdPackRepository crowdPackRepository;

    @Resource
    CrowdPackServiceImpl crowdPackService;

    @Test
    public void test() {
        CrowdPackDo crowdPackDo = crowdPackRepository.selectById(717L);

        //生成sql
        crowdPackService.setSql(crowdPackDo);
    }
}
