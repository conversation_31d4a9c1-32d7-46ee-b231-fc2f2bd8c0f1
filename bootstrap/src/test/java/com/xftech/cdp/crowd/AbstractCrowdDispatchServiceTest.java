/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.crowd;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.factory.impl.UserLableOptService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.dispatch.InvokePage;
import com.xftech.cdp.domain.crowd.service.dispatch.impl.CrowdDispatchStartRockServiceImpl;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.Config;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @version $ AbstractCrowdDispatchServiceTest, v 0.1 2023/9/26 18:14 yye.xu Exp $
 */

public class AbstractCrowdDispatchServiceTest extends BaseTest {

    @Autowired
    CrowdDispatchStartRockServiceImpl crowdDispatchService;

    @Autowired
    private Config config;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private CrowdConfig crowdConfig;

    @Test
    public void test() {
        System.out.println("res==="+config.getAdsMobileAesKey());
        System.out.println("res2==="+config.getBatchCountUseCache());
    }

    @Test
    public void test2() {
        System.out.println("res==="+appConfigService.getAdbRealTimeVariableUrl());
        System.out.println("res2==="+ JsonUtil.toJson(appConfigService.getEventFlcConfig()));
    }

    @Test
    public void test3() {
        System.out.println("res==="+crowdConfig.getAlarmUrl());
        System.out.println("res2==="+ JsonUtil.toJson(crowdConfig.getCrowdRefreshTimeoutAtMobiles()));
    }


    @Test
    public void test1() {
        InvokePage invokePage = new InvokePage();
        invokePage.setAppUserId(0);
        invokePage.setPageSize(500);
        invokePage.setMaxAppUserId(100000022);
        CrowdContext crowdContext = CrowdContext.init(false, null, new UserLableOptService());
        //crowdDispatchService.invokePageById(invokePage, new StringBuilder("2222"), null, crowdContext);
        //crowdDispatchService.execute(323L);
    }
}