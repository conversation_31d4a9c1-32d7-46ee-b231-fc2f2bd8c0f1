/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.strategy;

import com.google.gson.Gson;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.StrategyApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.NameTypeReq;
import com.xftech.cdp.api.dto.req.PushTemplateReq;
import com.xftech.cdp.api.dto.req.UpdatePolicyPriorityReq;
import com.xftech.cdp.api.dto.req.external.TeleImportResultReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.param.service.ReplaceStrategy;
import com.xftech.cdp.domain.param.service.impl.TempParamReplacer;
import com.xftech.cdp.domain.strategy.model.enums.StrategyRulerEnum;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.TelePushService;
import com.xftech.cdp.infra.client.telemarketing.TelemarketingClient;
import com.xftech.cdp.infra.client.telemarketing.model.TelePolicyConfigListRequest;
import com.xftech.cdp.infra.client.telemarketing.model.TelePushArgs;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameSaveResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TelePolicyConfigListResp;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TelePushResp;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;



/**
 * <AUTHOR>
 * @version $ StrategTeleTest, v 0.1 2023/10/13 15:31 wancheng.qu Exp $
 */

public class StrategTeleTest extends BaseTest {


    @Autowired
    private StrategyService strategyService;

    @Autowired
    private TelemarketingClient telemarketingClient;

    @Autowired
    private TelePushService telePushService;

    @Resource
    private TempParamReplacer tempParamReplacer;
    @Autowired
    private UserCenterClient userCenterClient;

    @Autowired
    private StrategyApi strategyApi;

    @Test
    public void testqueryBatchUserByUserNo(){
        List<UserInfoResp> respDtos = userCenterClient.queryBatchUserByUserNo(Arrays.asList(1005144l,1055648l,1260008l));
        System.out.println(JsonUtil.toJson(respDtos));
    }

    @Test
    public void tests(){
        Map<String, Object> tempParam = new HashMap<>();
        UserInfoResp user = new UserInfoResp();
        user.setCustName("王发的");
        user.setMobile("18397963122");
        Map<String, Object> qqqqq = tempParamReplacer.replaceElements("qqqqq#user_four_phone，放的是#user_mask_name",user);
        Map<String, Object> b = tempParamReplacer.replaceElements("qqqqq#user_four_phone，放的是#",user);
        Map<String, Object> c = tempParamReplacer.replaceElements("qqqqq#user_four_phon，放的是#user_mask_nam",user);
        System.out.println(qqqqq);
        String template="fdsfdsafsdfd";
        Map<String, Supplier<ReplaceStrategy>> mp = tempParamReplacer.getReplacementMap();
        boolean templateContainsMpKey = mp.keySet().stream().anyMatch(template::contains);
        if (templateContainsMpKey) {
            Map<String, Object> up = tempParamReplacer.replaceElements(template, user);
            tempParam.putAll(up);
            template = mp.keySet().stream().reduce(template, (result, key) -> result.replace(key, ""));
        }
        System.out.println(111111);
    }

    @Test
    public void testbatch(){
        Map<Long, Map<String, Object>> tempParam = new HashMap<>();
        Map<Long, Map<String, Object>> tp = new HashMap<>();
        Map<String, Object> m = new HashMap<>();
        m.put("aaaa",111);
        Map<String, Object> n = new HashMap<>();
        n.put("bbb",222);
        tempParam.put(1l,m);
        tp.put(2l,n);
        tempParam= mergeMaps(tempParam,tp);
        System.out.println(tempParam);

    }
    public  Map<Long, Map<String, Object>> mergeMaps(Map<Long, Map<String, Object>> map1, Map<Long, Map<String, Object>> map2) {
        return Stream.of(map1, map2)
                .filter(Objects::nonNull)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existingValue, newValue) -> {
                            existingValue.putAll(newValue);
                            return existingValue;
                        },
                        HashMap::new
                ));
    }
    @Test
    public void getNameTypeList() {
        NameTypeReq nameTypeReq = new NameTypeReq();
        nameTypeReq.setTypeName("贷");
        PageResultResponse<NameTypeResp> nameTypeList = strategyService.getNameTypeList(nameTypeReq);
        System.out.println("resutl=========" + JsonUtil.toJson(nameTypeList));
    }

    @Test
    public void getNameTypeConfigList() {
        NameTypeConfigResp nameTypeConfigList = strategyService.getNameTypeConfigList();
        System.out.println("resutl=========" + JsonUtil.toJson(nameTypeConfigList));
    }
    @Test
    public void getQueryReportDaily(){
        StrategyReportDailyResp strategyReportDailyResp = strategyService.queryReportDailyList();
        System.out.println(strategyReportDailyResp);
    }


    @Test
    public void testSaveNameConfig() {
        String requestBody = "{\"nameTypeId\": 2, \"type\": \"tag\", \"tagName\": \"标99\", \"tagPromptTips\": \"标签提示话00术\", \"tagValidity\": 0, \"judgeMethod\": \"duplicate_remove\", \"validityProlonging\": 0, \"validityProlongingDay\": 3, \"caseValidity\": 7, \"caseEvent\": \"commit_loan,activation_failed,credit_completed,risk_black\", \"smsList\": [{ \"templateId\": \"templateId123\", \"autoSend\": 0 }], \"couponList\": [], \"isBlank\": 0, \"createdTime\": \"2023-09-04 20:36:02\", \"updatedTime\": \"2023-09-04 20:36:02\", \"isHigh\": 0, \"ua\": \"xyf-cdp\", \"traceId\": \"dfsd\", \"id\": 8, \"ts\": 0 }";
        TeleNameSaveResp teleNameSaveResp = telemarketingClient.saveNameConfig(requestBody);
        System.out.println("res====" + JsonUtil.toJson(teleNameSaveResp));
    }

    @Test
    public void testGetPolicyDetail() {
        String policyDetail = telePushService.getPolicyDetail(8);
        System.out.println("res=====" + policyDetail);
    }

    @Test
    public void testGetTelePushArgs() {
        List<CrowdDetailDo> userIdList = Lists.newArrayList();
        for(int i=0;i<10;i++){
            CrowdDetailDo c = new CrowdDetailDo();
            c.setUserId(111111111273922L+i);
            c.setMobile("183"+i+"7963134");
            userIdList.add(c);
        }
        DispatchDto reach = new DispatchDto();
        reach.setStrategyRulerEnum(StrategyRulerEnum.EVENT);
        reach.setNameTypeId("50");
        TelePushArgs xyf = telePushService.getTelePushArgs(4, userIdList, reach, "xyf");
        xyf.setBatchNumber("1");
        xyf.setTraceId(UUID.randomUUID().toString());
        System.out.println("arg======" + JsonUtil.toJson(xyf));
        TelePushResp teleSp = telemarketingClient.pushTeleData(xyf);
        System.out.println("res======" + JsonUtil.toJson(teleSp));
    }

    @Test
    public void testMessage(){
        String body="{\"flowNo\":\"batch11\",\"userType\":1,\"userList\":[{\"creditId\":1,\"status\":2,\"execTime\":\"2023-10-20 17:29:13\"}]}";
        TeleImportResultReq t = new Gson().fromJson(body,TeleImportResultReq.class);
        System.out.println(JsonUtil.toJson(t));

    }

    @Test
    public void testgetPolicyList() {
        TelePolicyConfigListRequest req = new TelePolicyConfigListRequest();
        req.setTraceId(UUID.randomUUID().toString());
        req.setTs(System.currentTimeMillis()/1000);
        req.setUa("xyf-cdp");
        TelePolicyConfigListResp res = telemarketingClient.getPolicyList(req);
//        System.out.println("res=====" + JsonUtil.toJson(res));
        for (TelePolicyConfigListResp.ResponseData.Item item : res.getResponse().getList()) {
            System.out.println(item);
            System.out.println();
        }
    }



    @Test
    public void testPushTemplateList(){
        PushTemplateReq pushTemplateReq = new PushTemplateReq();


        PageResultResponse<PushTemplateResp> pushTemplateList = strategyService.getPushTemplateList(pushTemplateReq);

        System.out.println(JsonUtil.toJson(pushTemplateList));
    }

    @Test
    public void testNewTeleStrategyDetail() {
        Long strategyId = 2518L;
        StrategyDetailResp resp = strategyService.getDetail(strategyId);
        System.out.println(resp);
    }

    @Test
    public void testUpdatePolicyPriority() {
        UpdatePolicyPriorityReq req = new UpdatePolicyPriorityReq();
        req.setId(242L);
        req.setPriority(3);

        boolean resp = strategyService.updatePolicyPriority(req);
        System.out.println(resp);
    }

    @Test
    public void testUpdatePolicyPriorityApi() {
        UpdatePolicyPriorityReq req = new UpdatePolicyPriorityReq();
        req.setId(242L);
        req.setPriority(3);

        Response<Boolean> resp = strategyApi.updatePolicyPriority(req);
        System.out.println(resp.getResult());

    }
}