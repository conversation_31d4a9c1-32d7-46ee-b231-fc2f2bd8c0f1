package com.xftech.cdp.strategy;

import com.xftech.cdp.BaseTest;

import com.xftech.cdp.api.ExternalApi;
import com.xftech.cdp.api.StrategyApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;
import com.xftech.cdp.api.dto.req.external.StrategyListReq;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.api.dto.resp.InstantStrategyDetailResp;
import com.xftech.cdp.api.dto.resp.PolicyListResp;
import com.xftech.cdp.api.dto.resp.external.StrategyListResp;
import com.xftech.cdp.domain.strategy.model.enums.BusinessTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.MarketCrowdTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyGroupTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.infra.constant.SysConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class StrategyApiTest extends BaseTest {
    @Autowired
    private StrategyService strategyService;

    @Autowired
    private ExternalApi externalApi;

    @Autowired
    private StrategyApi strategyApi;

    @Autowired
    private StrategyRepository strategyRepository;
//
//    @Autowired
//    StrategyMarketEventRepository strategyMarketEventRepository;
//
//    @Autowired
//    InstantStrategyServiceImpl instantStrategyService;

    @Test
    public void testGetStrategyList(){
        StrategyListReq req = new StrategyListReq();
        req.setCallingSource(CallingSourceEnum.Overloan);
        PageResultResponse<StrategyListResp> resp = strategyService.getStrategyList(req);
        List<StrategyListResp> records = resp.getRecords();
        for (int i = 0; i < records.size(); i++) {
            StrategyListResp strategyListResp = records.get(i);
            System.out.printf("%d:\n", i);
            System.out.println(JsonUtil.toJson(strategyListResp));
            System.out.println();
        }

        req.setCallingSource(CallingSourceEnum.Overloan);
        req.setBusinessType(BusinessTypeEnum.OLD_CUST.getCode());
        resp = strategyService.getStrategyList(req);
        records = resp.getRecords();
        for (int i = 0; i < records.size(); i++) {
            StrategyListResp strategyListResp = records.get(i);
            System.out.printf("%d:\n", i);
            System.out.println(JsonUtil.toJson(strategyListResp));
            System.out.println();
        }
    }


    @Test
    public void testDecideApi() {
        DecideReq decideReq = new DecideReq();
        decideReq.setCallingSource(CallingSourceEnum.Overloan);
        decideReq.setUserId(1002L);
        decideReq.setStrategyIdList(Arrays.asList(1L, 2L, 2469L, 2567L));

        ExternalBaseRequest<DecideReq> req = new ExternalBaseRequest<>();
        req.setArgs(decideReq);
        req.setUa(String.valueOf(SysConstants.SignerEnum.TEST.getUa()));

        Response<List<DecideResp>> resp = externalApi.decide(req);
        List<DecideResp> decideRespList = resp.getResult();
        for (int i = 0; i < decideRespList.size(); i++) {
            System.out.printf("%d: \n", i + 1);
            System.out.println(decideRespList.get(i));
        }
    }

    @Test
    public void testCreateOfflineStrategy() {
        String s = "{\"id\":\"\",\"forceExec\":\"0\",\"name\":\"测试会员卡标签\",\"detailDescription\":\"\",\"sendRuler\":\"1\",\"sendFrequency\":{\"type\":\"0\",\"value\":[]},\"abTest\":\"0\",\"abType\":\"0\",\"flowRuleType\":\"2\",\"limitDays\":\"1\",\"limitTimes\":\"2\",\"randomItem\":{},\"strategyGroupType\":\"7\",\"validityBegin\":\"2024-08-05 00:00:00\",\"validityEnd\":\"2024-08-08 23:59:59\",\"crowdPackIds\":\"316\",\"publishType\":\"0\",\"strategyGroups\":[{\"strategyMarketChannels\":[{\"marketChannel\":\"0\",\"sendTime\":\"17:55\",\"templateId\":\"\",\"template\":\"\"}],\"isExecutable\":1,\"name\":\"A组\",\"groupConfig\":{\"digits\":\"2\",\"positionStart\":\"0\",\"positionEnd\":\"99\",\"crowdLabelOption\":{\"segments\":[{\"min\":\"\",\"max\":\"\"}]}}}],\"marketCondition\":[{\"timeType\":3,\"labelName\":\"vc_core_is_vip\",\"operateType\":\"eq\",\"relationship\":1,\"conditionValue\":\"1\"}],\"excludeType\":[5,6,7,8],\"userConvert\":0,\"bizKey\":\"\",\"businessType\":\"new-cust\",\"businessTypeName\":\"新客\"}";
        StrategyCreateReq req = null;
        req = JsonUtil.parse(s, StrategyCreateReq.class);
        Response<Boolean> resp = strategyApi.create(req);
        System.out.println(resp.getResult());
    }

    @Test
    public void testCreateT0LoanOverload() {
        InstantStrategyCreateReq req = new InstantStrategyCreateReq();

        String paramText = "{\"id\":\"\",\"forceExec\":\"0\",\"name\":\"贷超xx\",\"abTest\":\"1\",\"abType\":\"0\",\"strategyGroupType\":\"1\",\"marketCrowdType\":\"1\",\"randomItem\":{},\"publishType\":\"-1\",\"detailDescription\":\"\",\"strategyGroups\":[{\"strategyMarketChannels\":[],\"isExecutable\":1,\"name\":\"A组\",\"groupConfig\":{\"digits\":\"2\",\"positionStart\":\"0\",\"positionEnd\":\"99\",\"crowdLabelOption\":{\"segments\":[{\"min\":\"11\",\"max\":\"22\"}]}}},{\"strategyMarketChannels\":[],\"isExecutable\":0,\"name\":\"B组\",\"groupConfig\":{\"digits\":\"2\",\"positionStart\":\"0\",\"positionEnd\":\"99\",\"crowdLabelOption\":{\"segments\":[{\"min\":\"88\",\"max\":\"99\"}]}}}],\"timeValue\":0,\"validityBegin\":\"2024-07-10 00:00:00\",\"validityEnd\":\"2024-07-31 23:59:59\",\"dispatchType\":\"NOTIFY\",\"dispatchConfig\":{\"dispatchTimeConfig\":{\"begin\":\"2024-07-14 00:00:00\",\"end\":\"2024-07-27 23:59:59\"}},\"eventSet\":[{\"eventName\":\"loanOverload\",\"delay\":{\"timeType\":1,\"timeUnit\":2,\"timeValue\":0}}],\"excludeType\":[],\"userConvert\":0,\"marketCondition\":[],\"marketType\":2,\"bizKey\":\"\",\"callingSource\":1,\"businessType\":\"loan-overload-cust\",\"businessTypeName\":\"贷超\"}";
        InstantStrategyCreateReq req1 = JsonUtil.parse(paramText, req.getClass());
        Response<Boolean> resp1 = strategyApi.createInstantStrategy(req1);
        System.out.println(resp1.toString());
    }

    @Test
    public void testUpdateT0Strategy() {
        InstantStrategyUpdateReq req;
        String s = "{\"id\":\"2564\",\"forceExec\":\"0\",\"type\":0,\"name\":\"贷超策略0006\",\"detailDescription\":\"\",\"userConvert\":0,\"marketCondition\":[{\"timeType\":3,\"timeValue\":0,\"labelName\":\"not_apply_withdraw\",\"conditionId\":1354,\"operateType\":\"eq\",\"relationship\":1,\"conditionValue\":\"0\"}],\"excludeType\":[5,1,6,7,8],\"dispatchConfig\":{\"dispatchTimeConfig\":{\"begin\":\"2024-07-10 00:00:00\",\"end\":\"2024-07-12 23:59:59\"}},\"dispatchType\":\"MKT\",\"marketType\":2,\"callingSource\":1,\"randomItem\":{},\"marketCrowdType\":\"3\",\"strategyGroupType\":\"2\",\"abTest\":\"0\",\"abType\":\"0\",\"publishType\":\"0\",\"strategyGroups\":[{\"groupId\":\"1638465430974519711\",\"strategyMarketChannels\":[],\"isExecutable\":1,\"name\":\"A组\",\"groupConfig\":{\"digits\":\"2\",\"positionStart\":0,\"positionEnd\":99,\"crowdLabelOption\":{\"segments\":[{\"min\":\"\",\"max\":\"\"}]}}}],\"timeValue\":0,\"validityBegin\":\"2024-07-10 00:00:00\",\"validityEnd\":\"2024-07-12 23:59:59\",\"eventSet\":[{\"eventName\":1,\"delay\":{\"timeType\":\"\",\"timeUnit\":\"\",\"timeValue\":0}}],\"bizKey\":\"\",\"businessType\":\"loan-overload-cust\",\"businessTypeName\":\"贷超\"}";
        req = JsonUtil.parse(s, InstantStrategyUpdateReq.class);
        Response<Boolean> resp = strategyApi.updateInstantStrategy(req);
        System.out.println(resp.getResult());
    }

    @Test
    public void testCreateApiHoldT0() {
        InstantStrategyCreateReq req = new InstantStrategyCreateReq();
        String param = "{\"id\":\"\",\"forceExec\":\"0\",\"name\":\"测试api卡单0005\",\"abTest\":\"0\",\"abType\":\"0\",\"strategyGroupType\":\"3\",\"marketCrowdType\":\"2\",\"randomItem\":{},\"publishType\":\"-1\",\"detailDescription\":\"\",\"strategyGroups\":[{\"strategyMarketChannels\":[],\"extInfo\":\"{\\\"hold_duration\\\":-1}\",\"isExecutable\":0,\"name\":\"A组\",\"groupConfig\":{\"digits\":\"2\",\"positionStart\":\"0\",\"positionEnd\":\"99\",\"crowdLabelOption\":{\"segments\":[{\"min\":\"\",\"max\":\"\"}]}}}],\"timeValue\":0,\"validityBegin\":\"2024-07-10 00:00:00\",\"validityEnd\":\"2024-07-10 23:59:59\",\"dispatchType\":\"MKT\",\"dispatchConfig\":{\"dispatchTimeConfig\":{\"begin\":\"2024-07-10 00:00:00\",\"end\":\"2024-07-10 23:59:59\"}},\"eventSet\":[{\"eventName\":\"apiHold\",\"delay\":{\"timeType\":1,\"timeUnit\":2,\"timeValue\":0},\"eventCondition\":[{\"subEventName\":\"app\",\"operateType\":\"contain\",\"eventValue\":\"xyf01\"},{\"subEventName\":\"apiHoldInnerApp\",\"operateType\":\"contain\",\"eventValue\":\"xyf01_ayr\"}]}],\"excludeType\":[4,5,6,7,8,9],\"userConvert\":0,\"marketCondition\":[],\"marketType\":2,\"bizKey\":\"\",\"crowdPackIds\":\"366\",\"callingSource\":2,\"businessType\":\"old-cust\",\"businessTypeName\":\"老客\"}";
        req = JsonUtil.parse(param, req.getClass());
        Response<Boolean> resp = strategyApi.createInstantStrategy(req);
        System.out.println(resp.getResult());
    }

    @Test
    public void testInstantStrategyDetail() {
        StrategyDetailReq req = new StrategyDetailReq();
        req.setStrategyId(2544L);
        Response<InstantStrategyDetailResp> detail = strategyApi.getInstantStrategyDetail(req);
        System.out.println(JsonUtil.toJson(detail));
    }

    @Test
    public void testPolicyList() {
        String s = "{\"current\":1,\"size\":30,\"policyId\":\"\",\"policyName\":\"\",\"startTime\":\"\",\"endTime\":\"\",\"type\":\"tag\",\"cdpBusinessLine\":\"new-cust\"}";
        PolicyListReq req = JsonUtil.parse(s, PolicyListReq.class);
        Response<PageResultResponse<PolicyListResp>> resp = strategyApi.getPolicyList(req);
        for (PolicyListResp record : resp.getResult().getRecords()) {
            System.out.println(JsonUtil.toJson(record));
        }
    }

    @Test
    public void testSelectByCallingSourceAndBusinessType() {
        List<StrategyDo> strategyDos = strategyRepository.selectByCallingSourceAndBusinessType(CallingSourceEnum.ApiHold, BusinessTypeEnum.TEST.getCode());
        for (StrategyDo strategyDo : strategyDos) {
            System.out.println(JsonUtil.toJson(strategyDo));
        }
    }

    @Test
    public void testAppBannerRecommend() {
        ExternalBaseRequest<AppBannerReq> req = new ExternalBaseRequest<>();
        AppBannerReq appBannerReq = new AppBannerReq();
        appBannerReq.setApp("app-banner");
        appBannerReq.setUserId(1146246011L);
        appBannerReq.setAppBannerIdList(Arrays.asList("84"));
        req.setArgs(appBannerReq);
        req.setUa("test");

        externalApi.recommendAppBannerList(req);
    }

}