package com.xftech.cdp.strategy;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.ExternalApi;
import com.xftech.cdp.api.StrategyApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;
import com.xftech.cdp.api.dto.req.external.StrategyListReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateListReq;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.api.dto.resp.SmsTemplateListResp;
import com.xftech.cdp.api.dto.resp.external.StrategyListResp;
import com.xftech.cdp.domain.strategy.model.enums.BusinessTypeEnum;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.impl.StrategyCommonService;
import com.xftech.cdp.infra.constant.SysConstants;
import com.xftech.cdp.infra.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class StrategySmsTest extends BaseTest {
    @Autowired
    private StrategyCommonService strategyCommonService;

    @Test
    public void testGetSmsTemplates() {
        SmsTemplateListReq req = new SmsTemplateListReq();
        req.setCurrent(1);
        req.setSize(10);
        req.setTemplateId("");
        req.setType("");
        PageResultResponse<SmsTemplateListResp> resp = strategyCommonService.smsTemplateList(req);
        for (SmsTemplateListResp record : resp.getRecords()) {
            System.out.println(record);
        }
    }

}