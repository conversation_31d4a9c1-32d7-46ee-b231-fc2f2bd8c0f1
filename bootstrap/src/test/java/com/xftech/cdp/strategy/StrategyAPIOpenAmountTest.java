package com.xftech.cdp.strategy;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateListReq;
import com.xftech.cdp.api.dto.resp.SmsTemplateListResp;
import com.xftech.cdp.domain.dispatch.BatchDispatchService;
import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.service.impl.StrategyCommonService;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xinfei.xfframework.common.starter.mq.MqTemplate;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class StrategyAPIOpenAmountTest extends BaseTest {
    @Autowired
    private BatchDispatchService batchDispatchService;

    @Test
    public void testSendOpenAmountMsg() {
        DispatchDto dispatchDto = new DispatchDto();
        dispatchDto.setStrategyChannel(StrategyMarketChannelEnum.API_OPEN_AMOUNT.getCode());
        dispatchDto.setStrategyChannelId(1638418976184824654L);
        dispatchDto.setStrategyId(2623L);
        dispatchDto.setStrategyGroupId(1638465430974519772L);
        dispatchDto.setStrategyExecLogId(1L);
        dispatchDto.setDetailTableNo("202407");


        List<CrowdDetailDo> crowdDetailDoList = new ArrayList<>();
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setUserId(1L);
        crowdDetailDo.setMobile("xxxxxxxx");
        crowdDetailDoList.add(crowdDetailDo);
        for (int i = 2; i < 12; i++) {
            crowdDetailDo = new CrowdDetailDo();
            crowdDetailDo.setUserId((long)(i));
            crowdDetailDo.setMobile("xxxxxxxx");
            crowdDetailDoList.add(crowdDetailDo);
        }

        batchDispatchService.sendAPIOpenAmount(dispatchDto, "xyf", "xyf01", crowdDetailDoList);
    }


}