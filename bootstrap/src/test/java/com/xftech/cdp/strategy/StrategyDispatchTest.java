package com.xftech.cdp.strategy;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.MonitorEngineListReq;
import com.xftech.cdp.api.dto.req.StrategyBatchDeleteReq;
import com.xftech.cdp.api.dto.req.StrategyListReq;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import com.xftech.cdp.api.dto.resp.StrategyListResp;
import com.xftech.cdp.application.StrategyHandler;
import com.xftech.cdp.domain.flowctrl.model.dto.UserDispatchIndexDto;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.rabbitmq.vo.HitResult;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/18 9:15
 */
public class StrategyDispatchTest extends BaseTest {

    @Autowired
    private FlowCtrlRepository flowCtrlRepository;
    @Autowired
    private StrategyHandler strategyHandler;
    @Autowired
    private UserDispatchDetailService userDispatchDetailService;
    @Autowired
    private StrategyEventDispatchService strategyEventDispatchService;
    @Autowired
    private TemplateParamService templateParamService;
    @Autowired
    private StrategyService strategyService;

    @Test
    public void strategyDispatch() {
        strategyHandler.execute(Convert.toLong("1638418976184824679"), null);
    }

    @Test
    public void testExecuteNewTele() {
        Long channelId = 1638418976184824646L;
        DispatchTaskDo dispatchTaskDo = null;
        strategyHandler.execute(channelId, dispatchTaskDo);
    }

    @Test
    public void prescreen() {
        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setMessageId("123456");
        bizEventVO.setBizEventType("Login");
        bizEventVO.setAppUserId(111111111119637L);
        bizEventVO.setMobile("***********");
        bizEventVO.setApp("xyf01");
//        strategyEventDispatchService.prescreen(bizEventVO);
    }

    @Test
    public void rescreen() {
        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setMessageId("57178:0:-1_00000525");
        bizEventVO.setBizEventType("Tracking_LoanApplyView");
        bizEventVO.setAppUserId(111111111119057L);
        bizEventVO.setMobile("***********");
        bizEventVO.setApp("xyf01");
//        bizEventVO.setCrowdPackId(195L);
        bizEventVO.setStrategyId(525L);
//        bizEventVO.setAbNum("169746546549879119");
        bizEventVO.setAppUserIdLast2(57);

//        bizEventVO.setTriggerDatetime(DateUtil.convert(new Date("2023-10-07 15:01:08")));
        bizEventVO.setTriggerDatetime(LocalDateTime.of(2023, 10, 15, 11, 16, 2));
        bizEventVO.setStrategyType(0);
        bizEventVO.setStrategyExecId("671370029916696576");
        bizEventVO.setOs("android");
        bizEventVO.setDecisionResult(false);

        HitResult hitResult = new HitResult();
//        hitResult.setExecuteTime(DateUtil.convert(new Date("2023-10-13T15:29:18.083")));
        hitResult.setExecuteTime(LocalDateTime.of(2023, 10, 19, 11, 5, 12));
        hitResult.setExpression("contain(app,'xyf01,fxk,cxh')");
        hitResult.setExpParam("{app=xyf01}");
        hitResult.setHit(true);
        List<HitResult> hitResultList = new ArrayList<>();
        hitResultList.add(hitResult);
        bizEventVO.setHitResultList(hitResultList);
        strategyEventDispatchService.rescreen(bizEventVO);
    }

    @Test
    public void dispatch() {
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(195L);
        crowdDetailDo.setUserId(111111111119637L);
        crowdDetailDo.setApp("xyf01");
        crowdDetailDo.setInnerApp("xyf01");
        crowdDetailDo.setMobile("***********");
        crowdDetailDo.setAbNum("169746546549879119");
        crowdDetailDo.setAppUserIdLast2(37);
//        strategyEventDispatchService.dispatch(1638418976176435550L, 1, crowdDetailDo);
    }

    @Test
    public void getSmsTempParam() {
        CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
        crowdDetailDo.setCrowdId(195L);
        crowdDetailDo.setUserId(111111111119637L);
        crowdDetailDo.setApp("xyf01");
        crowdDetailDo.setInnerApp("xyf01");
        crowdDetailDo.setMobile("***********");
        crowdDetailDo.setAbNum("169746546549879119");
        crowdDetailDo.setAppUserIdLast2(37);
        String templateId = "smstpl710089";

        Map<String, Object> smsTempParam = templateParamService.getSmsTempParam(crowdDetailDo, 280L, templateId, null);

        System.out.println(smsTempParam);
    }

    @Test
    public void userDispatchDetail() {
        List<Long> userIdList = Arrays.asList(
                21111111155201L,
                21111111155202L,
                21111111155203L,
                21111111155204L,
                21111111155205L
        );
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(15L);
        Triple<Long, Integer, FlowCtrlDo> triple = Triple.of(141L, 2, flowCtrlDo);
        List<UserDispatchIndexDto> userIndex2 = userDispatchDetailService.getUserIndex("202304", triple, userIdList, Arrays.asList(1));

        System.out.println(userIndex2);
    }

    @Test
    public void userDispatchDetail2() {
        List<Long> userIdList = Arrays.asList(
                21111111155201L,
                21111111155202L,
                21111111155203L,
                21111111155204L,
                21111111155205L
        );
        FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(15L);
        Triple<Long, Integer, FlowCtrlDo> triple = Triple.of(141L, 2, flowCtrlDo);
    }

    @Test
    public void deleteStrategy() {
        List<Long> strategyIds = new ArrayList<>();
        strategyIds.add(536L);
        StrategyBatchDeleteReq strategyBatchDeleteReq = new StrategyBatchDeleteReq();
        strategyBatchDeleteReq.setStrategyIds(strategyIds);
        strategyService.batchDelete(strategyBatchDeleteReq);
    }

    @Test
    public void getList() {
        StrategyListReq strategyListReq = new StrategyListReq();
        strategyListReq.setBusinessType("new-cust");
        PageResultResponse<StrategyListResp> list = strategyService.list(strategyListReq);
        System.out.println(list);
    }

    @Test
    public void testMonit() {
        MonitorEngineListReq monitorEngineListReq = new MonitorEngineListReq();
        monitorEngineListReq.setStrategyId(2466L);
        monitorEngineListReq.setStrategyGroupId("1638465430974520388");
        PageResultResponse<AbsMonitorListResp> absMonitorListRespPageResultResponse = strategyService.queryStrategyEngineMonitorList(monitorEngineListReq);
        System.out.println(absMonitorListRespPageResultResponse);
    }
}
