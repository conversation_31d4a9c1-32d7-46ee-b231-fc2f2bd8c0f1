package com.xftech.cdp.domain.cache;

import com.xftech.cdp.BaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CacheStrategyMarketEventServiceTest extends BaseTest {

    @Autowired
    private CacheStrategyMarketEventService cacheStrategyMarketEventService;


    @Test
    public void insert() {
    }

    @Test
    public void updateById() {
    }

    @Test
    public void getByEventName() {
        cacheStrategyMarketEventService.getByEventName("abc");
    }

    @Test
    public void refreshStrategyMarketEventCache() {

    }

    @Test
    public void testRefreshStrategyMarketEventCache() {
    }
}