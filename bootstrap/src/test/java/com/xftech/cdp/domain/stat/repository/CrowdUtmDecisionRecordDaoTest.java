package com.xftech.cdp.domain.stat.repository;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.stat.entity.CrowdUtmDecisionRecordEntity;
import com.xftech.cdp.domain.stat.entity.UtmResult;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class CrowdUtmDecisionRecordDaoTest extends BaseTest {

    @Autowired
    private CrowdUtmDecisionRecordDao crowdUtmDecisionRecordDao;

    @Test
    public void saveCrowdUtmDecisionRecord() {

        UtmResult result = new UtmResult();
        result.setUtmSource("xxxx");
        result.setUtmType("1");
        result.setHit(true);
        result.setNewMarketingType("2");
        List<UtmResult> utmResultList = new ArrayList<>();
        utmResultList.add(result);

        CrowdUtmDecisionRecordEntity entity = new CrowdUtmDecisionRecordEntity();
        entity.setCrowdId(1L);
        entity.setCrowdExecLogId(1L);
        entity.setAppUserId(1L);
        entity.setApp("xyf");
        entity.setRegisterTime(LocalDateTime.now());
        entity.setDecisionResult(1);
        entity.setMobileUtmSource("xxx");
        entity.setHistoryBorrowUtmSource("yyy");
        entity.setLastLoanSuccessUtmSource("xxx");
        entity.setTableNameNo(LocalDateTimeUtil.format(LocalDate.now(), "yyyyMM"));
        entity.setTraceId("xxx");
        entity.setUtmResultList(utmResultList);

        List<CrowdUtmDecisionRecordEntity> list = new ArrayList<>();
        list.add(entity);
        crowdUtmDecisionRecordDao.saveCrowdUtmDecisionRecord(list);

    }
}