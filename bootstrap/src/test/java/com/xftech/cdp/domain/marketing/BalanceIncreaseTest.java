package com.xftech.cdp.domain.marketing;


import com.alibaba.fastjson.JSON;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.strategy.repository.StrategyCrowdPackRepository;
import com.xftech.cdp.domain.strategy.repository.UserBalanceIncreaseRecordRepository;
import com.xftech.cdp.infra.repository.cdp.marketing.po.UserBalanceIncreaseRecordDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ BalanceIncreaseTest, v 0.1 2025/1/9 18:03 mingwen.zang
 */
public class BalanceIncreaseTest extends BaseTest {
    @Autowired
    private UserBalanceIncreaseRecordRepository userBalanceIncreaseRecordMapper;

    @Resource
    StrategyCrowdPackRepository strategyCrowdPackRepository;


    @Test
    public void testInsertRecord() {
        int i = userBalanceIncreaseRecordMapper.insertSelective(UserBalanceIncreaseRecordDo.builder()
                .amount(BigDecimal.valueOf(10.2))
                .reason("成功")
                .status(1)
                .appUserId(1939303106126936279L)
                .orderNumber("RE1939303106126936274")
                .build());
        assert i > 0;
    }
    @Test
    public void selectByOderAndUserId() {
        List<UserBalanceIncreaseRecordDo> i = userBalanceIncreaseRecordMapper.selectByOderAndUserId(UserBalanceIncreaseRecordDo.builder()
                .appUserId(1939303106126936279L)
                //.orderNumber("RE1939303106126936279")
                .build());
        System.out.println(JSON.toJSONString(i));
    }
}
