package com.xftech.cdp.domain.service;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.log.QueryOperateLogReq;
import com.xftech.cdp.api.dto.resp.log.OperateLogResp;
import com.xftech.cdp.domain.stat.service.impl.StatStrategyFlowDataServiceImpl;
import com.xftech.cdp.domain.stat.service.impl.StatStrategyGroupDataServiceImpl;
import com.xftech.cdp.domain.strategy.model.dto.EngineStrategyGroupIdInfoDto;
import com.xftech.cdp.domain.strategy.repository.DecisionRecordRepository;
import com.xftech.cdp.domain.strategy.repository.EventPushBatchRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.UserSendCounterService;
import com.xftech.cdp.domain.strategy.service.impl.OperateLogService;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.OperateLogDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import net.sf.cglib.core.Local;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Array;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class UserSendCounterServiceTest extends BaseTest {
    @Autowired
    private UserSendCounterService userSendCounterService;
    @Autowired
    private StatStrategyGroupDataServiceImpl statStrategyGroupDataService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private DecisionRecordRepository decisionRecordRepository;
    @Autowired
    private EventPushBatchRepository eventPushBatchRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private StatStrategyFlowDataServiceImpl statStrategyFlowDataService;
    @Autowired
    private OperateLogService operateLogService;

    @Test
    public void test_countSum() {
        long count = userSendCounterService
                .countSum(0, 0, 0, 0, 0);
        Assert.assertTrue(count == 0);
    }

    @Test
    public void test_counterIncrementSum() {
        userSendCounterService
                .counterIncrementSum(-99, null, -99, 0, 0, 1);
    }

    @Test
    public void test_addOperate() {
        OperateLogDo operateLogDo = new OperateLogDo();
        operateLogDo.setDescription("测试操作日志");
        operateLogDo.setType(OperateTypeEnum.ADD.getCode());
        operateLogDo.setModel(OperateModeEnum.CROWD.getCode());
        operateLogDo.setUrl("/testUrl");
        operateLogDo.setResponse("testResp");
        operateLogDo.setRequestParam("testReq");
        operateLogDo.setObjId(1L);
        operateLogDo.setUserIdentify("testUser");
        operateLogService.addOperateLog(operateLogDo);
    }

    @Test
    public void test_queryOperate() {
        QueryOperateLogReq queryOperateLogReq = new QueryOperateLogReq();
        queryOperateLogReq.setId(853L);
        queryOperateLogReq.setModel(2);
        PageResultResponse<OperateLogResp> operateLogRespPageResultResponse = operateLogService.queryPage(queryOperateLogReq);
        System.out.println(JsonUtil.toJson(operateLogRespPageResultResponse));
    }

    @Test
    public void testData(){

        EngineStrategyGroupIdInfoDto engineStrategyGroupIdInfoDto = new EngineStrategyGroupIdInfoDto();
        engineStrategyGroupIdInfoDto.setGroupId("002");
        List<EngineStrategyGroupIdInfoDto.ChannelInfo> channelInfoList = new ArrayList<>();
        EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo = new EngineStrategyGroupIdInfoDto.ChannelInfo();
        channelInfo.setTemplate("smstpl710185");
        channelInfo.setMarketChannel(1);
        channelInfoList.add(channelInfo);
        EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo1 = new EngineStrategyGroupIdInfoDto.ChannelInfo();
        channelInfo1.setTemplate("smstpl710185");
        channelInfo1.setMarketChannel(2);
        channelInfoList.add(channelInfo1);
//        EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo2 = new EngineStrategyGroupIdInfoDto.ChannelInfo();
//        channelInfo2.setTemplate("smstpl710185");
//        channelInfo2.setMarketChannel(3);
//        channelInfoList.add(channelInfo2);
//        EngineStrategyGroupIdInfoDto.ChannelInfo channelInfo3 = new EngineStrategyGroupIdInfoDto.ChannelInfo();
//        channelInfo3.setTemplate("smstpl710185");
//        channelInfo3.setMarketChannel(4);
//        channelInfoList.add(channelInfo3);
        engineStrategyGroupIdInfoDto.setChannelInfoList(channelInfoList);
        redisUtils.hPut("xyf-cdp:strategy_group_id_data:20231223_1638418976176435805", "002", JSON.toJSONString(engineStrategyGroupIdInfoDto), RedisUtils.DEFAULT_EXPIRE_DAYS);
        System.out.println("success");

    }


    @Test
    public void testStatGroupData(){
        statStrategyGroupDataService.statStrategyGroupDataProcess();
    }

    @Test
    public void testFlowData(){
        statStrategyFlowDataService.statStrategyGroupDataProcess();
    }

    @Test
    public void testStatGroupYesterdayData(){
        statStrategyGroupDataService.reportYesterdayData();
    }

    @Test
    public void testStatGroupDataEndStatus(){
        statStrategyGroupDataService.statStrategyGroupDateEndCurrentStatus();
    }

    @Test
    public void testDes(){
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
//        Integer i = decisionRecordRepository.countCurrentDecisionByGroupId(tableNameNo,495L,1638418976176435805L);
//        System.out.println(i);
    }

    @Test
    public void testPush(){
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
//        Integer i = eventPushBatchRepository.countByDetail(tableNameNo, 495L, 1638418976176435805L, "A", 2, "smstpl710185", null);
//        System.out.println(i);
//        Integer a = eventPushBatchRepository.countByDetail(tableNameNo, 495L, 1638418976176435805L, "A", 2, "smstpl710185", Arrays.asList(1,2));
//        System.out.println(a);
    }

    @Test
    public void testDispa(){
        String tableNameNo = LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMM");
        Integer i = userDispatchDetailRepository.countByDetail(tableNameNo, 495L, 1638418976176435805L, "A", 2, "smstpl710185", null,
                LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
        System.out.println(i);
        Integer a = userDispatchDetailRepository.countByDetail(tableNameNo, 495L, 1638418976176435805L, "A", 2, "smstpl710185", null,
                LocalDateTimeUtil.beginOfDay(LocalDateTime.now()), LocalDateTime.now());
        System.out.println(a);
    }

}
