package com.xftech.cdp.domain.strategy.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchGroupNum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class UserDispatchDetailRepositoryTest extends BaseTest {

    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;


    @Test
    public void countStrategyGroupDispatchUserNum() {
        List<Long> list = new ArrayList<>();
        list.add(1L);
        list.add(2L);
        Integer num = userDispatchDetailRepository.countStrategyGroupDispatchUserNum("202306", 1L, list, "2023-06-14", "2023-06-15");
        logger.info("num={}", num);
    }

    @Test
    public void countDispatchUserNum() {
    }

    @Test
    public void updateTest() {
        UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
        userDispatchDetailDo.setId(0L);
        userDispatchDetailDo.setUserId(1L);
        userDispatchDetailDo.setTableName("202401");

        UserDispatchDetailDo userDispatchDetailDo2 = new UserDispatchDetailDo();
        userDispatchDetailDo2.setId(0L);
        userDispatchDetailDo2.setUserId(1L);
        userDispatchDetailDo2.setTableName("202101");


        List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();
        userDispatchDetailDoList.add(userDispatchDetailDo);
        userDispatchDetailDoList.add(userDispatchDetailDo2);

        userDispatchDetailRepository.updateBybatchNumAndMobile(userDispatchDetailDoList);
    }
}