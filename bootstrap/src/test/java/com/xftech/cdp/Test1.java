package com.xftech.cdp;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.CrowdListReq;
import com.xftech.cdp.api.dto.req.LabelReq;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.flowctrl.FlowCtrlListReq;
import com.xftech.cdp.api.dto.resp.CrowdLabelsResp;
import com.xftech.cdp.domain.crowd.repository.AdsUserLabelDetailInfoDfRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdDetailRepository;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.AsyncService;
import com.xftech.cdp.domain.crowd.service.LabelService;
import com.xftech.cdp.domain.crowd.service.dispatch.impl.CrowdDispatchStartRockServiceImpl;
import com.xftech.cdp.domain.crowd.service.impl.CrowdPackServiceImpl;
import com.xftech.cdp.domain.risk.service.impl.RiskServiceImpl;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.impl.StrategyCommonService;
import com.xftech.cdp.domain.subtable.service.SplitTableService;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.AesDecodeReq;
import com.xftech.cdp.infra.client.ads.model.req.BaseAdsDataRequest;
import com.xftech.cdp.infra.client.ads.model.resp.AesDecodeResp;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.bi.BiClient;
import com.xftech.cdp.infra.client.bi.model.req.ModelMarketingReq;
import com.xftech.cdp.infra.client.bi.model.resp.BaseBiResponse;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.logging.log4j.core.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/2/8
 */

@SpringBootTest
@RunWith(SpringRunner.class)
public class Test1 {

    @Autowired
    AdsUserLabelDetailInfoDfRepository adsUserLabelDetailInfoDfRepository;
    @Resource
    private StrategyRepository strategyRepository;
    @Resource
    private CrowdDetailRepository crowdDetailMysqlRepository;
    @Resource
    private CrowdPackRepository crowdMysqlRepository;
    @Resource
    private CrowdPackServiceImpl crowdPackService;
    @Resource
    private AsyncService asyncService;
    @Resource
    private StrategyService strategyService;
    @Resource
    private LabelService labelService;
    @Autowired
    private CrowdDispatchStartRockServiceImpl crowdDispatchStartRockService;
    @Autowired
    private SplitTableService splitTableService;
    @Resource
    private StrategyCommonService strategyCommonService;

    @Autowired
    SqlSessionFactory sqlSessionFactory;

    @Autowired
    AdsClient adsClient;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    RiskServiceImpl riskService;

    // @Resource
    // private CacheRiskService cacheRiskService;

    @Test
    public void t1() {
        adsUserLabelDetailInfoDfRepository.queryWareHouseUserInfo("select * from ads_user_label_detail_info_df limit 1");
        //      System.out.println( crowdDetailMysqlRepository.selectById( 1 ) );
    }

    @Test
    public void t3() {
        CrowdPackDo crowdPackDo = new CrowdPackDo();
        crowdPackDo.setCrowdName("测试001");
        crowdPackDo.setStatus(0);
        crowdPackDo.setFilterMethod(0);
        crowdPackDo.setRefreshTime(LocalTime.parse("14:00:00"));
        crowdPackDo.setValidityBegin(LocalDateTime.parse("2023-02-14T12:00:00"));
        crowdPackDo.setValidityEnd(LocalDateTime.parse("2023-02-14T13:00:00"));
        crowdPackDo.setRefreshType(0);

        System.out.println(crowdPackDo);

//        System.out.println(crowdPackBo.getId());
//        System.out.println("insert=========> " + crowdMysqlRepository.insert( crowdPackBo ));
//        System.out.println(crowdPackBo.getId());
    }

    @Test
    public void t4() {
        CrowdListReq crowdListReq = new CrowdListReq();

        crowdListReq.setCrowdName("赛亚人");

        crowdPackService.queryList(crowdListReq);
    }

    @Test
    public void t5() {
        System.out.println(crowdPackService.getOne(30L));
    }



    @Test
    public void t7() {
        System.out.println("111111");
        System.out.println("22222");
    }

    @Test
    public void t8() {
        // 13 12 11 ? * 1,3,5 *
        // 13 12 11 1,3,5 * ? *
        StrategyCreateReq.SendFrequency sendFrequency = new StrategyCreateReq.SendFrequency();
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(3);
        list.add(5);
        sendFrequency.setType(2);
        sendFrequency.setValue(list);
        LocalTime time = LocalTime.parse("2022-01-02 11:12:13", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        strategyCommonService.convertToCron(sendFrequency, time);
    }

    @Test
    public void t9() {
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        System.out.println(date);
    }

    @Test
    public void t10() {
        LabelReq labelReq = new LabelReq();
        labelReq.setBusinessType("new-cust");
        List<CrowdLabelsResp> tree = labelService.getTree(labelReq);
        System.out.println(JsonUtil.toJson(tree));
    }

    @Test
    public void t11() {
        System.out.println(LocalDateTime.of(LocalDate.parse("2023-01-02"), LocalTime.parse("00:00:00")).format(TimeFormat.DATE_TIME_FORMATTER));
    }

    @Test
    public void t12() {
        crowdDispatchStartRockService.execute(1L);
    }

    @Test
    public void t13() {
        List<String> tableNameByLogId = splitTableService.getTableNameByLogId(86L);
        System.out.println(tableNameByLogId);
    }

    @Test
    public void t14() {
        FlowCtrlListReq flowCtrlListReq = new FlowCtrlListReq();
        List<Long> ids = new ArrayList<>();
        ids.add(1L);
        ids.add(2L);
        ids.add(8L);
        flowCtrlListReq.setStrategyIds(ids);

        String jsonStr = JSON.toJSONString(flowCtrlListReq);
        Map<String, Object> map = JSON.parseObject(jsonStr);
        System.out.println(map);

        BoundSql boundSql = sqlSessionFactory.getConfiguration()
                .getMappedStatement("flowCtrlMapper.selectAllRule").getBoundSql(flowCtrlListReq);

        List<ParameterMapping> params = boundSql.getParameterMappings();
        String result = boundSql.getSql();

        result = result.replaceAll("\\s", " ");
//        for (ParameterMapping pm : params) {
//            result = result.replaceFirst("\\?", getParameterValue(map.get(pm.getProperty())));
//        }
        System.out.println(result);
    }

    @Test
    public void t16() {
        riskService.RefreshRiskScore(LocalDate.parse("2023-05-10" ));
    }

    @Test
    public void tt() {
        System.out.println(riskService.distribute(1, 3, 5));
    }

    @Test
    public void t17() {
        ModelMarketingReq  modelMarketingReq = new ModelMarketingReq();
        ModelMarketingReq.Context context = new ModelMarketingReq.Context();
        AesDecodeReq aesDecodeReq = new AesDecodeReq("FaknxexDDPoosSAFeqj+TA==", "FaknxexDDPoosSAFeqj+TA==");
        ArrayList<AesDecodeReq> aesDecodeReqList = new ArrayList<>();
        aesDecodeReqList.add(aesDecodeReq);
        BaseAdsDataRequest<List<AesDecodeReq>> request = new BaseAdsDataRequest<>(aesDecodeReqList);
        BaseAdsResponse<ArrayList <AesDecodeResp>> aesDecodeResp = adsClient.aesDecode(request);
        context.setMobile(aesDecodeResp.getPayload().get(0).getMobile());
        context.setIdCardNumber(aesDecodeResp.getPayload().get(0).getIdCardNumber());
        modelMarketingReq.setContext(context);
        ArrayList<String> element_id_list = new ArrayList<>();
        element_id_list.add("personalloan_yunying_fd_fundcash_f90_lgb_v1_prob");
        modelMarketingReq.setElementIdList(element_id_list);
        System.out.println(aesDecodeResp.getPayload());
        System.out.println(aesDecodeResp.getPayload().get(0).getMobile());
        BaseBiResponse baseBiResponse = (new BiClient()).modelMarketing(modelMarketingReq);
    }

    private static String getParameterValue(Object obj) {
        String value = null;

        if(obj instanceof Integer || obj instanceof Long){
            value = obj.toString();
        }else{
            value = "'" + obj.toString() + "'";
        }
        return value;
    }
}
