///**
// * Xinfei.com Inc.
// * Copyright (c) 2004-2023 All Rights Reserved.
// */
//package com.xftech.cdp.infra.pulsar.listener;
//
//import com.xftech.cdp.BaseTest;
//import com.xftech.cdp.infra.pulsar.service.CdpPulsarService;
//import org.apache.pulsar.client.api.MessageId;
//import org.junit.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
///**
// * <AUTHOR>
// * @version $ CdpPulsarListenerTest, v 0.1 2023/10/8 11:29 lingang.han Exp $
// */
//
//public class CdpPulsarListenerTest extends BaseTest {
//
//    @Autowired
//    private CdpPulsarListener cdpPulsarListener;
//
//    @Autowired
//    private CdpPulsarService trackingLoanApplyView;
//    @Autowired
//    private CdpPulsarService trackingCardClick;
//
//    private static final String trackingLoanApplyViewMessage = "{\"route\":\"loanThree\",\"event_id\":\"1003806\",\"event_time\":\"1693378868778\",\"source_type\":\"client\",\"token\":\"eyj2zxjzaw9uijoxlcj1awqioiixmzgwodq4mduwnsisim1vymlszsi6ijezoda4ndgwnta1iiwiyxbwijoiehlmiiwidhlwijoislduiiwiywxnijoisfmyntyifq.eyj1awqioiixmzgwodq4mduwnsisimfwcci6inh5ziisimv4cci6mty5nta4nzqymiwibw9iawxlijoimtm4mdg0oda1mduilcjjcmvkaxrvc2vyswqioiixmda5mtu3mduyiiwiy3jlzgl0vxnlclvzzxjjzci6ijeynzq4odq5in0.dvpjohzwlem_nziym7yblgjmq_ajtyysw7yfzkcgd9o\",\"device_id\":\"202205096311156c1c\",\"app\":\"xyf01\",\"inner_app\":\"xyf01\",\"os\":\"android\",\"channel\":\"huawei\",\"version_code\":\"\",\"utm_source\":\"xyf01_app\",\"mobile\":\"13808480505\",\"user_id\":\"12748849\",\"http_client_ip\":\"*************\",\"created_time\":\"2023-08-30 15:01:08\",\"mobile_protyle\":\"m38330a9f8550497b4b3a0bc1ece31046\",\"credit_user_id\":\"12748849\",\"event_datetime\":\"2023-10-07 15:01:08\"}";
//    private static final String trackingCardClickMessage = "{\"route\":\"loanThree\",\"event_id\":\"1004089\",\"event_time\":\"1693378868778\",\"source_type\":\"client\",\"token\":\"eyj2zxjzaw9uijoxlcj1awqioiixmzgwodq4mduwnsisim1vymlszsi6ijezoda4ndgwnta1iiwiyxbwijoiehlmiiwidhlwijoislduiiwiywxnijoisfmyntyifq.eyj1awqioiixmzgwodq4mduwnsisimfwcci6inh5ziisimv4cci6mty5nta4nzqymiwibw9iawxlijoimtm4mdg0oda1mduilcjjcmvkaxrvc2vyswqioiixmda5mtu3mduyiiwiy3jlzgl0vxnlclvzzxjjzci6ijeynzq4odq5in0.dvpjohzwlem_nziym7yblgjmq_ajtyysw7yfzkcgd9o\",\"device_id\":\"202205096311156c1c\",\"app\":\"xyf01\",\"inner_app\":\"xyf01\",\"os\":\"android\",\"channel\":\"huawei\",\"version_code\":\"\",\"utm_source\":\"xyf01_app\",\"mobile\":\"13808480505\",\"user_id\":\"12748849\",\"http_client_ip\":\"*************\",\"created_time\":\"2023-08-30 15:01:08\",\"mobile_protyle\":\"m38330a9f8550497b4b3a0bc1ece31046\",\"credit_user_id\":\"12748849\",\"event_datetime\":\"2023-10-07 15:01:08\"}";
//
//    @Test
//    public void listenerTrackingLoanApplyView() {
//        cdpPulsarListener.listenerTrackingLoanApplyView(trackingLoanApplyViewMessage, MessageId.earliest);
//    }
//
//    @Test
//    public void listenerTrackingCardClick() {
//        cdpPulsarListener.listenerTrackingCardClick(trackingCardClickMessage, MessageId.earliest);
//    }
//
//}