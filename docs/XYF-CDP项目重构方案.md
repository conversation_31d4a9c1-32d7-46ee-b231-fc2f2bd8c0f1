# 麻雀平台重构专项方案

## 项目背景与现状分析

### 业务背景
麻雀平台（XYF-CDP）是智能营销平台，核心目标是**在合适的时间，通过合适的渠道，把合适的内容，给到合适的用户**。

### 当前架构痛点

#### 1. 架构层面问题
- **单体架构限制**：所有业务线集中在一个应用中，扩展性差
- **策略实现割裂**：4种营销策略（T0实时、离线、T0引擎、离线引擎）各自独立实现，缺乏统一抽象
- **平台化程度低**：未进行平台化思考，通用诉求改动影响面大
- **边界模糊**：目标用户、营销策略、用户触达三大核心模块边界不清晰

#### 2. 技术债务问题
- **代码质量**：存在大方法、重复代码、复杂条件判断
- **性能瓶颈**：数据库查询优化不足，缓存策略不统一
- **可维护性差**：核心业务逻辑注释不足，测试覆盖率低
- **扩展性弱**：新增业务线或策略类型成本高

#### 3. 业务支撑问题
- **响应速度慢**：新需求开发周期长
- **稳定性不足**：系统性能和稳定性表现欠佳
- **复用性差**：相似功能重复开发

## 重构目标与愿景

### 总体目标
构建**高内聚、低耦合、可扩展、可插拔**的智能营销平台架构，实现三大核心能力的平台化：

1. **目标用户识别**：合适的时机、合适的人
2. **营销策略决策**：合适的内容、合适的渠道
3. **用户触达执行**：合适的时间、传递动作

### 核心设计原则

#### 1. 领域驱动设计（DDD）
- **明确边界上下文**：目标用户、营销策略、用户触达三大领域
- **统一语言**：建立业务与技术的统一词汇表
- **领域模型**：构建富含业务逻辑的领域对象

#### 2. 微服务架构
- **服务拆分**：按业务能力拆分为独立的微服务
- **数据隔离**：每个服务拥有独立的数据存储
- **接口标准化**：统一的API设计规范

#### 3. 事件驱动架构
- **异步解耦**：通过事件实现服务间的松耦合
- **最终一致性**：保证分布式系统的数据一致性
- **可扩展性**：支持新业务场景的快速接入

#### 4. 插件化设计
- **策略可插拔**：支持不同策略算法的动态切换
- **渠道可扩展**：新增触达渠道无需修改核心逻辑
- **引擎可对接**：支持内部决策和外部引擎的统一接入

## 目标架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        麻雀智能营销平台                              │
├─────────────────────────────────────────────────────────────────┤
│  API Gateway & Web Portal (统一入口)                              │
├─────────────────────────────────────────────────────────────────┤
│                        核心业务服务                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   目标用户    │  │   营销策略    │  │   用户触达    │              │
│  │  (Target)   │  │ (Strategy)  │  │  (Touch)    │              │
│  │             │  │             │  │             │              │
│  │ • 客群圈选   │  │ • 策略决策   │  │ • 触达执行   │              │
│  │ • 实时事件   │  │ • AB实验    │  │ • 频控管理   │              │
│  │ • 事件属性   │  │ • 内容决策   │  │ • 渠道对接   │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│                        触发引擎层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │  离线触发引擎  │  │  实时触发引擎  │  │  策略执行引擎  │              │
│  │ (Trigger)   │  │ (Trigger)   │  │  (Engine)   │              │
│  │             │  │             │  │             │              │
│  │ • Job执行    │  │ • 事件过滤   │  │ • 特征过滤   │              │
│  │ • 客群圈选   │  │ • 实时匹配   │  │ • 决策路由   │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
├─────────────────────────────────────────────────────────────────┤
│                        基础设施层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   数据存储    │  │   消息中间件  │  │   配置中心    │              │
│  │             │  │             │  │             │              │
│  │ • MySQL     │  │ • RabbitMQ  │  │ • Apollo    │              │
│  │ • Redis     │  │ • Kafka     │  │ • Nacos     │              │
│  │ • ClickHouse│  │             │  │             │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

### 核心领域模型

#### 1. 目标用户领域 (Target Domain)
```java
/**
 * 目标用户聚合根
 */
@Entity
public class TargetUser {
    private TargetUserId id;
    private CrowdDefinition crowdDefinition;  // 客群定义
    private EventTrigger eventTrigger;        // 事件触发
    private List<UserAttribute> attributes;   // 用户属性

    // 领域行为
    public boolean matchesCrowd(User user);
    public boolean triggeredByEvent(Event event);
}

/**
 * 客群定义值对象
 */
@ValueObject
public class CrowdDefinition {
    private FilterMethod filterMethod;        // 过滤方式
    private List<LabelCondition> conditions;  // 标签条件
    private LogicalOperator operator;         // 逻辑操作符
}
```

#### 2. 营销策略领域 (Strategy Domain)
```java
/**
 * 营销策略聚合根
 */
@Entity
public class MarketingStrategy {
    private StrategyId id;
    private StrategyType type;                // 策略类型
    private TriggerCondition triggerCondition; // 触发条件
    private DecisionEngine decisionEngine;    // 决策引擎
    private List<ActionDefinition> actions;   // 动作定义

    // 领域行为
    public DecisionResult decide(DecisionContext context);
    public boolean canExecute(ExecutionContext context);
}

/**
 * 决策引擎接口
 */
public interface DecisionEngine {
    DecisionResult execute(DecisionContext context);
}

/**
 * 内部决策引擎实现
 */
@Component
public class InternalDecisionEngine implements DecisionEngine {
    @Override
    public DecisionResult execute(DecisionContext context) {
        // 内部决策逻辑
    }
}

/**
 * 外部引擎适配器
 */
@Component
public class ExternalEngineAdapter implements DecisionEngine {
    @Override
    public DecisionResult execute(DecisionContext context) {
        // 调用外部引擎
    }
}
```

#### 3. 用户触达领域 (Touch Domain)
```java
/**
 * 用户触达聚合根
 */
@Entity
public class UserTouch {
    private TouchId id;
    private UserId userId;
    private TouchChannel channel;             // 触达渠道
    private TouchContent content;             // 触达内容
    private TouchTiming timing;               // 触达时机
    private FrequencyControl frequencyControl; // 频控

    // 领域行为
    public boolean canTouch(FrequencyRule rule);
    public TouchResult execute();
}

/**
 * 触达渠道接口
 */
public interface TouchChannel {
    TouchResult send(TouchMessage message);
    boolean supports(ChannelType type);
}
```

## 三期重构实施计划

### 第一期：用户触达应用建设（Touch Service）

#### 1.1 目标
构建统一的用户触达服务，实现**触达渠道的可插拔**和**触达动作的标准化**。

#### 1.2 核心功能
- **频控管理**：统一的触达频次控制
- **时机决策**：立即执行 vs 延迟执行
- **渠道对接**：多渠道统一接入
- **内容补充**：动态内容生成和模板渲染
- **动作执行**：标准化的营销动作执行

#### 1.3 技术架构

```java
/**
 * 触达服务核心接口
 */
@Service
public interface TouchService {

    /**
     * 执行用户触达
     */
    TouchResult executeTouch(TouchRequest request);

    /**
     * 批量触达
     */
    List<TouchResult> batchTouch(List<TouchRequest> requests);

    /**
     * 延迟触达
     */
    void scheduleTouch(TouchRequest request, Duration delay);
}

/**
 * 触达请求
 */
@Data
public class TouchRequest {
    private UserId userId;                    // 用户ID
    private StrategyId strategyId;           // 策略ID
    private ChannelType channelType;         // 渠道类型
    private ContentTemplate contentTemplate; // 内容模板
    private Map<String, Object> variables;   // 变量参数
    private TouchTiming timing;              // 触达时机
    private FrequencyRule frequencyRule;    // 频控规则
}

/**
 * 渠道处理器接口
 */
public interface ChannelHandler {

    /**
     * 是否支持该渠道
     */
    boolean supports(ChannelType channelType);

    /**
     * 执行触达
     */
    TouchResult handle(TouchContext context);

    /**
     * 获取渠道优先级
     */
    int getPriority();
}

/**
 * 短信渠道处理器
 */
@Component
public class SmsChannelHandler implements ChannelHandler {

    @Override
    public boolean supports(ChannelType channelType) {
        return ChannelType.SMS.equals(channelType);
    }

    @Override
    public TouchResult handle(TouchContext context) {
        // 短信发送逻辑
        SmsTemplate template = buildSmsTemplate(context);
        SmsResult result = smsClient.send(template);
        return TouchResult.from(result);
    }

    @Override
    public int getPriority() {
        return 1;
    }
}

/**
 * 频控管理器
 */
@Component
public class FrequencyControlManager {

    /**
     * 检查是否可以触达
     */
    public boolean canTouch(UserId userId, ChannelType channel, FrequencyRule rule) {
        String key = buildFrequencyKey(userId, channel, rule);
        Long count = redisTemplate.opsForValue().get(key);
        return count == null || count < rule.getMaxCount();
    }

    /**
     * 记录触达次数
     */
    public void recordTouch(UserId userId, ChannelType channel, FrequencyRule rule) {
        String key = buildFrequencyKey(userId, channel, rule);
        redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, rule.getTimeWindow());
    }
}
```

#### 1.4 数据模型设计

```sql
-- 触达记录表
CREATE TABLE touch_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    strategy_id BIGINT NOT NULL COMMENT '策略ID',
    channel_type VARCHAR(50) NOT NULL COMMENT '渠道类型',
    content TEXT COMMENT '触达内容',
    status VARCHAR(20) NOT NULL COMMENT '状态',
    result_code VARCHAR(50) COMMENT '结果码',
    result_message TEXT COMMENT '结果消息',
    touch_time DATETIME NOT NULL COMMENT '触达时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_channel_time (user_id, channel_type, touch_time),
    INDEX idx_strategy_time (strategy_id, touch_time)
);

-- 频控规则表
CREATE TABLE frequency_rule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    channel_type VARCHAR(50) NOT NULL COMMENT '渠道类型',
    time_window_type VARCHAR(20) NOT NULL COMMENT '时间窗口类型',
    time_window_value INT NOT NULL COMMENT '时间窗口值',
    max_count INT NOT NULL COMMENT '最大次数',
    priority INT DEFAULT 0 COMMENT '优先级',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 第二期：策略触发应用建设（Trigger Service）

#### 2.1 目标
构建统一的策略触发服务，支持**实时事件触发**和**离线客群触发**两种模式。

#### 2.2 核心功能
- **实时事件处理**：事件过滤、实时匹配、快速响应
- **离线任务调度**：Job执行、客群圈选、批量处理
- **触发条件判断**：多维度条件组合判断
- **事件路由分发**：根据事件类型路由到不同处理器

#### 2.3 技术架构

```java
/**
 * 触发服务核心接口
 */
@Service
public interface TriggerService {

    /**
     * 实时事件触发
     */
    TriggerResult handleRealtimeEvent(RealtimeEvent event);

    /**
     * 离线任务触发
     */
    void executeOfflineJob(OfflineJobContext context);

    /**
     * 批量事件处理
     */
    List<TriggerResult> batchHandleEvents(List<RealtimeEvent> events);
}

/**
 * 实时事件处理器
 */
@Component
public class RealtimeEventProcessor {

    private final EventRouter eventRouter;
    private final StrategyMatcher strategyMatcher;

    public TriggerResult process(RealtimeEvent event) {
        // 1. 事件预处理和验证
        if (!validateEvent(event)) {
            return TriggerResult.invalid("事件验证失败");
        }

        // 2. 匹配相关策略
        List<MarketingStrategy> strategies = strategyMatcher.match(event);
        if (strategies.isEmpty()) {
            return TriggerResult.noMatch("未匹配到策略");
        }

        // 3. 执行策略触发
        List<StrategyExecution> executions = new ArrayList<>();
        for (MarketingStrategy strategy : strategies) {
            if (strategy.canTrigger(event)) {
                StrategyExecution execution = strategy.trigger(event);
                executions.add(execution);
            }
        }

        return TriggerResult.success(executions);
    }
}

/**
 * 离线任务处理器
 */
@Component
public class OfflineJobProcessor {

    private final CrowdSelector crowdSelector;
    private final JobScheduler jobScheduler;

    public void processOfflineJob(OfflineJobContext context) {
        try {
            // 1. 客群圈选
            CrowdResult crowdResult = crowdSelector.select(context.getCrowdDefinition());

            // 2. 策略匹配
            List<MarketingStrategy> strategies = findStrategiesByCrowd(crowdResult);

            // 3. 批量执行
            for (MarketingStrategy strategy : strategies) {
                batchExecuteStrategy(strategy, crowdResult.getUsers());
            }

        } catch (Exception e) {
            log.error("离线任务执行失败", e);
            handleJobFailure(context, e);
        }
    }
}

/**
 * 事件路由器
 */
@Component
public class EventRouter {

    private final Map<EventType, EventHandler> handlerMap;

    public EventHandler route(EventType eventType) {
        EventHandler handler = handlerMap.get(eventType);
        if (handler == null) {
            throw new UnsupportedOperationException("不支持的事件类型: " + eventType);
        }
        return handler;
    }
}

/**
 * 策略匹配器
 */
@Component
public class StrategyMatcher {

    private final StrategyRepository strategyRepository;
    private final RuleEngine ruleEngine;

    public List<MarketingStrategy> match(RealtimeEvent event) {
        // 1. 获取所有激活的策略
        List<MarketingStrategy> activeStrategies = strategyRepository.findActiveStrategies();

        // 2. 根据事件类型过滤
        List<MarketingStrategy> typeMatchedStrategies = activeStrategies.stream()
            .filter(strategy -> strategy.supportsEventType(event.getType()))
            .collect(Collectors.toList());

        // 3. 根据触发条件过滤
        return typeMatchedStrategies.stream()
            .filter(strategy -> ruleEngine.evaluate(strategy.getTriggerRule(), event))
            .collect(Collectors.toList());
    }
}
```

#### 2.4 事件处理流程

```java
/**
 * 事件处理流程编排
 */
@Component
public class EventProcessingOrchestrator {

    public void orchestrate(RealtimeEvent event) {
        // 1. 事件预处理
        EventContext context = preprocessEvent(event);

        // 2. 并行处理多个策略
        List<CompletableFuture<StrategyResult>> futures = context.getStrategies()
            .stream()
            .map(strategy -> CompletableFuture.supplyAsync(() ->
                executeStrategy(strategy, context), strategyExecutor))
            .collect(Collectors.toList());

        // 3. 等待所有策略执行完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenAccept(v -> {
                List<StrategyResult> results = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

                // 4. 结果汇总和后处理
                postProcessResults(results, context);
            });
    }
}
```

### 第三期：策略引擎应用建设（Strategy Engine Service）

#### 3.1 目标
构建统一的策略执行引擎，支持**内部决策**和**外部引擎对接**，实现策略决策的可插拔。

#### 3.2 核心功能
- **特征过滤**：用户特征匹配和过滤
- **AB实验**：策略分组和实验管理
- **决策路由**：内部决策 vs 外部引擎路由
- **内容决策**：营销内容和渠道选择
- **循环延迟**：策略的循环执行和延迟决策

#### 3.3 技术架构

```java
/**
 * 策略引擎核心接口
 */
@Service
public interface StrategyEngine {

    /**
     * 执行策略决策
     */
    DecisionResult executeDecision(DecisionRequest request);

    /**
     * 批量决策
     */
    List<DecisionResult> batchDecision(List<DecisionRequest> requests);

    /**
     * 异步决策
     */
    CompletableFuture<DecisionResult> asyncDecision(DecisionRequest request);
}

/**
 * 决策请求
 */
@Data
public class DecisionRequest {
    private UserId userId;                    // 用户ID
    private StrategyId strategyId;           // 策略ID
    private EventContext eventContext;       // 事件上下文
    private UserProfile userProfile;         // 用户画像
    private Map<String, Object> variables;   // 决策变量
    private DecisionMode mode;               // 决策模式（内部/外部）
}

/**
 * 策略执行引擎实现
 */
@Service
public class StrategyEngineImpl implements StrategyEngine {

    private final DecisionRouterFactory routerFactory;
    private final FeatureFilterChain filterChain;
    private final ABTestManager abTestManager;

    @Override
    public DecisionResult executeDecision(DecisionRequest request) {
        try {
            // 1. 特征过滤
            FilterResult filterResult = filterChain.filter(request);
            if (!filterResult.isPassed()) {
                return DecisionResult.filtered(filterResult.getReason());
            }

            // 2. AB实验分组
            ABTestResult abResult = abTestManager.assign(request);
            request.setAbTestGroup(abResult.getGroup());

            // 3. 决策路由
            DecisionRouter router = routerFactory.getRouter(request.getMode());
            DecisionResult result = router.route(request);

            // 4. 结果后处理
            return postProcessResult(result, request);

        } catch (Exception e) {
            log.error("策略决策执行失败", e);
            return DecisionResult.error(e.getMessage());
        }
    }
}

/**
 * 决策路由器工厂
 */
@Component
public class DecisionRouterFactory {

    private final Map<DecisionMode, DecisionRouter> routers;

    public DecisionRouter getRouter(DecisionMode mode) {
        DecisionRouter router = routers.get(mode);
        if (router == null) {
            throw new UnsupportedOperationException("不支持的决策模式: " + mode);
        }
        return router;
    }
}

/**
 * 内部决策路由器
 */
@Component
public class InternalDecisionRouter implements DecisionRouter {

    private final RuleEngine ruleEngine;
    private final ContentSelector contentSelector;
    private final ChannelSelector channelSelector;

    @Override
    public DecisionResult route(DecisionRequest request) {
        // 1. 规则引擎决策
        RuleResult ruleResult = ruleEngine.execute(request);
        if (!ruleResult.isSuccess()) {
            return DecisionResult.rejected(ruleResult.getReason());
        }

        // 2. 内容选择
        ContentResult contentResult = contentSelector.select(request);

        // 3. 渠道选择
        ChannelResult channelResult = channelSelector.select(request);

        // 4. 构建决策结果
        return DecisionResult.builder()
            .success(true)
            .content(contentResult.getContent())
            .channel(channelResult.getChannel())
            .variables(mergeVariables(ruleResult, contentResult, channelResult))
            .build();
    }
}

/**
 * 外部引擎路由器
 */
@Component
public class ExternalEngineRouter implements DecisionRouter {

    private final ExternalEngineClient engineClient;
    private final ResultTransformer resultTransformer;

    @Override
    public DecisionResult route(DecisionRequest request) {
        try {
            // 1. 构建外部引擎请求
            ExternalEngineRequest engineRequest = buildEngineRequest(request);

            // 2. 调用外部引擎
            ExternalEngineResponse engineResponse = engineClient.decide(engineRequest);

            // 3. 结果转换
            return resultTransformer.transform(engineResponse);

        } catch (Exception e) {
            log.error("外部引擎调用失败", e);
            // 降级到内部决策
            return fallbackToInternal(request);
        }
    }
}

/**
 * 特征过滤链
 */
@Component
public class FeatureFilterChain {

    private final List<FeatureFilter> filters;

    public FilterResult filter(DecisionRequest request) {
        for (FeatureFilter filter : filters) {
            FilterResult result = filter.doFilter(request);
            if (!result.isPassed()) {
                return result;
            }
        }
        return FilterResult.passed();
    }
}

/**
 * AB实验管理器
 */
@Component
public class ABTestManager {

    private final ABTestRepository abTestRepository;
    private final HashBasedAssigner hashAssigner;

    public ABTestResult assign(DecisionRequest request) {
        // 1. 获取策略的AB实验配置
        List<ABTestConfig> configs = abTestRepository.findByStrategyId(request.getStrategyId());

        // 2. 基于用户ID进行分组
        for (ABTestConfig config : configs) {
            if (config.isActive()) {
                ABTestGroup group = hashAssigner.assign(request.getUserId(), config);
                return ABTestResult.success(group);
            }
        }

        return ABTestResult.defaultGroup();
    }
}
```

#### 3.4 规则引擎设计

```java
/**
 * 规则引擎接口
 */
public interface RuleEngine {

    /**
     * 执行规则
     */
    RuleResult execute(DecisionRequest request);

    /**
     * 编译规则
     */
    CompiledRule compile(String ruleExpression);

    /**
     * 验证规则语法
     */
    ValidationResult validate(String ruleExpression);
}

/**
 * Aviator规则引擎实现
 */
@Component
public class AviatorRuleEngine implements RuleEngine {

    private final AviatorEvaluator evaluator;
    private final Cache<String, Expression> expressionCache;

    @Override
    public RuleResult execute(DecisionRequest request) {
        try {
            // 1. 获取策略规则
            MarketingStrategy strategy = getStrategy(request.getStrategyId());
            String ruleExpression = strategy.getRuleExpression();

            // 2. 编译规则（带缓存）
            Expression expression = expressionCache.get(ruleExpression,
                () -> evaluator.compile(ruleExpression));

            // 3. 构建执行上下文
            Map<String, Object> context = buildRuleContext(request);

            // 4. 执行规则
            Object result = expression.execute(context);

            return RuleResult.success((Boolean) result);

        } catch (Exception e) {
            log.error("规则执行失败", e);
            return RuleResult.error(e.getMessage());
        }
    }
}
```

## 数据迁移策略（渐进式重构）

### 核心原则：最小化数据迁移风险

考虑到现有系统的复杂性和业务连续性要求，我们采用**渐进式数据迁移**策略，而不是全盘重建表结构。

### 现有表结构分析

基于代码分析，当前主要表结构包括：
- `crowd_pack` - 人群包主表
- `crowd_exec_log` - 人群包执行日志
- `strategy` - 策略主表
- `strategy_crowd_pack` - 策略人群包关联表
- `strategy_exec_log` - 策略执行日志
- `flow_ctrl_rule` - 流控规则表

### 渐进式迁移方案

#### 第一期：Touch服务 - 扩展现有表结构

```sql
-- 1. 扩展现有strategy_exec_log表，增加触达相关字段
ALTER TABLE strategy_exec_log
ADD COLUMN touch_channel VARCHAR(50) COMMENT '触达渠道',
ADD COLUMN touch_content TEXT COMMENT '触达内容',
ADD COLUMN touch_result VARCHAR(20) COMMENT '触达结果',
ADD COLUMN touch_time DATETIME COMMENT '触达时间',
ADD COLUMN frequency_check_result TINYINT(1) COMMENT '频控检查结果',
ADD INDEX idx_touch_channel_time (touch_channel, touch_time);

-- 2. 新增频控记录表（新表，不影响现有业务）
CREATE TABLE frequency_control_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    channel_type VARCHAR(50) NOT NULL COMMENT '渠道类型',
    rule_id BIGINT COMMENT '规则ID',
    touch_count INT DEFAULT 1 COMMENT '触达次数',
    window_start DATETIME NOT NULL COMMENT '窗口开始时间',
    window_end DATETIME NOT NULL COMMENT '窗口结束时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_channel_window (user_id, channel_type, window_start),
    INDEX idx_window_end (window_end)
);

-- 3. 扩展flow_ctrl_rule表，增加新的频控配置
ALTER TABLE flow_ctrl_rule
ADD COLUMN channel_type VARCHAR(50) COMMENT '渠道类型',
ADD COLUMN time_window_type VARCHAR(20) COMMENT '时间窗口类型',
ADD COLUMN time_window_value INT COMMENT '时间窗口值',
ADD COLUMN global_rule TINYINT(1) DEFAULT 0 COMMENT '是否全局规则';
```

#### 第二期：Trigger服务 - 复用现有表结构

```sql
-- 1. 扩展crowd_pack表，增加触发相关配置
ALTER TABLE crowd_pack
ADD COLUMN trigger_type VARCHAR(50) DEFAULT 'OFFLINE' COMMENT '触发类型',
ADD COLUMN event_config JSON COMMENT '事件配置',
ADD COLUMN realtime_enabled TINYINT(1) DEFAULT 0 COMMENT '是否启用实时触发';

-- 2. 新增事件处理日志表（新表）
CREATE TABLE event_processing_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    event_id VARCHAR(64) NOT NULL COMMENT '事件ID',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    event_data JSON COMMENT '事件数据',
    matched_strategies JSON COMMENT '匹配的策略',
    processing_result VARCHAR(20) NOT NULL COMMENT '处理结果',
    processing_time_ms INT COMMENT '处理耗时',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_event_type_time (event_type, created_time),
    INDEX idx_user_time (user_id, created_time),
    INDEX idx_event_id (event_id)
);
```

#### 第三期：Strategy Engine服务 - 最小化表变更

```sql
-- 1. 扩展strategy表，增加决策引擎相关配置
ALTER TABLE strategy
ADD COLUMN decision_engine_type VARCHAR(50) DEFAULT 'INTERNAL' COMMENT '决策引擎类型',
ADD COLUMN external_engine_config JSON COMMENT '外部引擎配置',
ADD COLUMN ab_test_config JSON COMMENT 'AB实验配置',
ADD COLUMN rule_expression TEXT COMMENT '规则表达式';

-- 2. 新增决策执行详情表（新表）
CREATE TABLE decision_execution_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    strategy_exec_log_id BIGINT NOT NULL COMMENT '策略执行日志ID',
    decision_step VARCHAR(50) NOT NULL COMMENT '决策步骤',
    step_result VARCHAR(20) NOT NULL COMMENT '步骤结果',
    step_detail JSON COMMENT '步骤详情',
    execution_time_ms INT COMMENT '执行耗时',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (strategy_exec_log_id) REFERENCES strategy_exec_log(id),
    INDEX idx_exec_log_step (strategy_exec_log_id, decision_step)
);
```

### 数据适配层设计

为了在重构过程中保持业务连续性，我们设计数据适配层：

```java
/**
 * 数据适配层 - 屏蔽新老数据结构差异
 */
@Component
public class DataAdapterService {

    /**
     * 策略数据适配器
     */
    public MarketingStrategy adaptStrategy(StrategyDo legacyStrategy) {
        return MarketingStrategy.builder()
            .id(StrategyId.of(legacyStrategy.getId()))
            .name(legacyStrategy.getName())
            .type(StrategyType.valueOf(legacyStrategy.getType()))
            .decisionEngineType(parseDecisionEngineType(legacyStrategy))
            .ruleExpression(legacyStrategy.getRuleExpression())
            .build();
    }

    /**
     * 人群包数据适配器
     */
    public TargetUser adaptCrowdPack(CrowdPackDo legacyCrowd) {
        return TargetUser.builder()
            .id(TargetUserId.of(legacyCrowd.getId()))
            .name(legacyCrowd.getCrowdName())
            .filterMethod(CrowdFilterMethodEnum.getInstance(legacyCrowd.getFilterMethod()))
            .triggerType(parseTriggerType(legacyCrowd))
            .build();
    }

    /**
     * 执行日志适配器
     */
    public TouchRecord adaptExecLog(StrategyExecLogDo legacyLog) {
        return TouchRecord.builder()
            .id(TouchId.of(legacyLog.getId()))
            .userId(UserId.of(legacyLog.getUserId()))
            .strategyId(StrategyId.of(legacyLog.getStrategyId()))
            .channel(parseChannel(legacyLog.getTouchChannel()))
            .result(parseResult(legacyLog.getTouchResult()))
            .touchTime(legacyLog.getTouchTime())
            .build();
    }
}
```

### 迁移时间线

#### 第一期（4周）- Touch服务
- **Week 1-2**：扩展现有表结构，新增必要字段
- **Week 3-4**：开发数据适配层，确保新老数据兼容

#### 第二期（4周）- Trigger服务
- **Week 5-6**：扩展crowd_pack表，新增事件日志表
- **Week 7-8**：实现事件数据的双写机制

#### 第三期（4周）- Strategy Engine服务
- **Week 9-10**：扩展strategy表，新增决策详情表
- **Week 11-12**：完成数据迁移验证，清理临时字段

### 风险控制措施

#### 1. 双写策略
```java
@Service
public class DualWriteService {

    /**
     * 双写策略执行日志
     */
    @Transactional
    public void saveStrategyExecLog(StrategyExecLogDo legacyLog, TouchRecord newRecord) {
        // 1. 写入原有表结构
        strategyExecLogRepository.insert(legacyLog);

        // 2. 写入新的表结构（如果启用）
        if (isNewTableEnabled()) {
            touchRecordRepository.save(newRecord);
        }
    }
}
```

#### 2. 数据一致性校验
```java
@Component
public class DataConsistencyChecker {

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void checkDataConsistency() {
        // 校验新老数据的一致性
        List<InconsistentRecord> inconsistencies = findInconsistentRecords();
        if (!inconsistencies.isEmpty()) {
            alertService.sendAlert("数据一致性校验失败", inconsistencies);
        }
    }
}
```

#### 3. 回滚机制
```java
@Component
public class RollbackService {

    /**
     * 回滚到原有表结构
     */
    public void rollbackToLegacyTables() {
        // 1. 停止新表写入
        configService.disableNewTable();

        // 2. 数据回补到原表
        backfillLegacyTables();

        // 3. 切换读取源
        dataSourceSwitcher.switchToLegacy();
    }
}
```

### 优势分析

✅ **最小化风险**：不破坏现有表结构，业务连续性有保障
✅ **渐进式迁移**：分期实施，每期风险可控
✅ **可回滚**：任何阶段都可以快速回滚到原有方案
✅ **数据兼容**：通过适配层保证新老数据结构兼容
✅ **验证充分**：双写机制确保数据迁移的正确性

## 实施计划与里程碑

### 总体时间规划（12周）

#### 第一期：用户触达应用建设（4周）
**Week 1-2：基础架构搭建**
- 触达服务核心框架搭建
- 渠道处理器接口设计和实现
- 频控管理器开发
- 基础数据模型建立

**Week 3-4：功能完善和测试**
- 多渠道处理器实现（短信、Push、优惠券等）
- 内容模板引擎集成
- 延迟触达功能实现
- 单元测试和集成测试

**里程碑验收标准：**
- [ ] 支持5种以上触达渠道
- [ ] 频控功能正常运行
- [ ] 延迟触达准确性达到99%
- [ ] 接口响应时间<100ms
- [ ] 单元测试覆盖率>80%

#### 第二期：策略触发应用建设（4周）
**Week 5-6：触发引擎开发**
- 实时事件处理器实现
- 离线任务调度器开发
- 事件路由器和策略匹配器
- 并行处理框架搭建

**Week 7-8：性能优化和测试**
- 事件处理性能优化
- 批量处理能力提升
- 异常处理和降级机制
- 压力测试和性能调优

**里程碑验收标准：**
- [ ] 实时事件处理延迟<50ms
- [ ] 支持10万+/秒事件处理能力
- [ ] 离线任务执行成功率>99.9%
- [ ] 策略匹配准确率100%
- [ ] 系统可用性>99.95%

#### 第三期：策略引擎应用建设（4周）
**Week 9-10：决策引擎开发**
- 内部决策引擎实现
- 外部引擎适配器开发
- AB实验管理器
- 规则引擎集成

**Week 11-12：集成测试和上线**
- 三个服务集成联调
- 端到端测试
- 性能压测
- 生产环境部署

**里程碑验收标准：**
- [ ] 决策引擎响应时间<200ms
- [ ] AB实验分流准确率>99.99%
- [ ] 外部引擎调用成功率>99.5%
- [ ] 规则引擎支持复杂表达式
- [ ] 整体系统稳定性达标

## 技术选型与架构决策

### 微服务框架
- **Spring Cloud Alibaba**：服务注册发现、配置管理、熔断降级
- **Dubbo 3.0**：高性能RPC通信
- **Gateway**：统一API网关

### 数据存储
- **MySQL 8.0**：主要业务数据存储
- **Redis Cluster**：缓存和分布式锁
- **ClickHouse**：实时数据分析和报表
- **MongoDB**：非结构化数据存储

### 消息中间件
- **RabbitMQ**：可靠消息传递
- **Apache Kafka**：高吞吐量事件流
- **RocketMQ**：事务消息支持

### 监控与运维
- **SkyWalking**：分布式链路追踪
- **Prometheus + Grafana**：监控告警
- **ELK Stack**：日志收集分析
- **Sentinel**：流量控制和熔断

## 风险控制与应对策略

### 技术风险
1. **数据迁移风险（重点关注）**
   - 风险：表结构变更导致的数据不一致
   - 应对：采用渐进式扩展，而非重建表结构
   - 保障：双写机制 + 数据一致性校验 + 快速回滚
   - 验证：每个阶段都有完整的数据验证流程

2. **服务拆分风险**
   - 风险：分布式事务和数据一致性问题
   - 应对：通过数据适配层屏蔽复杂性，保持现有事务边界
   - 降级：保留单体应用作为备选方案

3. **性能风险**
   - 风险：新增字段和表可能影响查询性能
   - 应对：合理设计索引，分阶段优化查询
   - 监控：建立完善的性能监控体系

### 业务风险
1. **功能回归风险**
   - 风险：重构过程中功能缺失
   - 应对：完善的自动化测试覆盖
   - 验证：业务方参与UAT测试

2. **上线风险**
   - 风险：新系统稳定性问题
   - 应对：灰度发布，逐步放量
   - 监控：实时监控关键业务指标

### 团队风险
1. **技能风险**
   - 风险：团队对新技术栈不熟悉
   - 应对：提前技术培训和实践
   - 支持：引入外部技术专家

2. **进度风险**
   - 风险：开发进度延期
   - 应对：合理的里程碑设置和进度跟踪
   - 调整：必要时调整功能范围

## 质量保证体系

### 代码质量
- **SonarQube**：静态代码分析
- **CheckStyle**：代码规范检查
- **SpotBugs**：潜在Bug检测
- **JaCoCo**：测试覆盖率统计

### 测试策略
- **单元测试**：覆盖率目标80%+
- **集成测试**：关键业务流程覆盖
- **性能测试**：压力测试和稳定性测试
- **安全测试**：漏洞扫描和渗透测试

### 部署策略
- **蓝绿部署**：零停机部署
- **金丝雀发布**：风险可控的渐进式发布
- **自动回滚**：异常情况下的快速回滚

## 预期收益分析

### 技术收益
1. **架构升级**
   - 从单体架构升级为微服务架构
   - 系统可扩展性提升300%+
   - 服务独立部署，故障隔离能力增强

2. **性能提升**
   - 接口响应时间优化50%+
   - 系统并发处理能力提升200%+
   - 资源利用率提升30%+

3. **开发效率**
   - 新功能开发周期缩短40%+
   - 代码复用率提升60%+
   - Bug修复时间减少50%+

### 业务收益
1. **营销效果**
   - 策略执行准确率提升至99.9%+
   - 用户触达成功率提升20%+
   - 营销ROI提升15%+

2. **运营效率**
   - 策略配置时间减少70%+
   - 新业务线接入时间缩短80%+
   - 运营人员工作效率提升50%+

3. **用户体验**
   - 营销内容个性化程度提升
   - 用户触达时机更加精准
   - 用户投诉率下降30%+

### 成本收益
1. **开发成本**
   - 重复开发工作减少60%+
   - 维护成本降低40%+
   - 人力成本优化25%+

2. **运维成本**
   - 系统故障率降低70%+
   - 运维工作量减少50%+
   - 基础设施成本优化20%+

## 总结与建议

### 重构价值
本次麻雀平台重构专项是一次全面的技术架构升级，将从根本上解决当前系统面临的扩展性、性能和维护性问题。通过构建**目标用户、营销策略、用户触达**三大核心领域的平台化能力，实现智能营销的标准化和规模化。

### 关键成功因素
1. **领导层支持**：确保充足的资源投入和决策支持
2. **团队协作**：跨部门协作，业务与技术深度融合
3. **渐进实施**：分期实施，降低风险，确保业务连续性
4. **质量保证**：建立完善的测试和监控体系
5. **知识传承**：做好技术文档和团队培训

### 实施建议
1. **优先级排序**：建议按照Touch → Trigger → Strategy Engine的顺序实施
2. **数据迁移优先**：每期都优先完成数据结构扩展和验证，再进行业务逻辑开发
3. **渐进式切换**：通过配置开关控制新老逻辑的切换，确保平滑过渡
4. **充分验证**：每个阶段都要进行充分的数据一致性验证和回归测试
5. **经验总结**：及时总结重构经验，为后续类似项目提供参考

### 数据迁移的核心优势

相比于全盘重建表结构，我们的渐进式扩展方案具有以下优势：

✅ **零停机迁移**：通过ALTER TABLE扩展字段，无需停机
✅ **数据安全**：原有数据完全保留，新字段允许NULL值
✅ **快速回滚**：任何时候都可以忽略新字段，回到原有逻辑
✅ **分期验证**：每期都可以独立验证数据正确性
✅ **业务连续**：整个过程中业务功能不受影响

通过本次重构，麻雀平台将成为一个真正意义上的智能营销平台，具备强大的扩展能力和业务支撑能力，为公司的数字化营销战略提供坚实的技术基础。同时，我们采用的渐进式数据迁移策略将最大程度降低重构风险，确保业务的平稳过渡。
