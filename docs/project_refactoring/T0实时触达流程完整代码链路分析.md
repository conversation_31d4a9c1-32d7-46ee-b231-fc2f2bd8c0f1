# T0实时触达流程完整代码链路分析

## 概述

本文档详细梳理T0实时触达流程从消息入口到最终触达结果的完整代码链路，包括每个环节的具体实现类、方法调用、Apollo配置等技术细节。

## 1. 消息入口层 - RocketMQ消息消费

### 1.1 消息消费入口

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/infra/rocketmq/EventMessageBaseProcessor.java`

```java
@Component
public class EventMessageBaseProcessor {
    @Autowired
    private MqConsumeService mqConsumeService;
    @Autowired
    private EventMessageProcessor eventMessageProcessor;

    // 统一消息处理入口
    public void doMessageProcess(RocketMQMessageListener messageListener, MessageExt messageExt, String message) {
        log.info("Event message consumer, topic={} messageId={} body={}", 
                messageListener.topic(), messageExt.getMsgId(), message);
        
        // 1. 消息幂等控制
        if(!isNeedProcess(messageListener, messageExt, message)) {
            return;
        }
        
        // 2. 消息解析和字段映射
        BizEventMessageVO bizEventMessage = eventMessageProcessor.doMessageProcessor(
                messageListener.topic(), messageListener.consumerGroup(), 
                getConsumerTag(messageListener), message);
        
        // 3. 调用业务处理
        mqConsumeService.bizEventProcess(messageExt.getMsgId(), bizEventMessage);
    }
}
```

### 1.2 具体消息消费者示例

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/infra/rocketmq/risk/NewRiskSingleAdjBusinessMsgConsumer.java`

```java
@RocketMQMessageListener(
    topic = "tp_risk_single_adj_business", 
    consumerGroup = "cg_risk_single_adj_business_xyf_cdp"
)
public class NewRiskSingleAdjBusinessMsgConsumer extends MqConsumerListener<String> {
    @Autowired
    private EventMessageBaseProcessor eventMessageBaseProcessor;

    @Override
    public void doMessage(String topic, String message, MessageExt messageExt) {
        if (WhitelistSwitchUtil.boolSwitchByApollo("NewRiskSingleAdjBusinessMsgConsumerEnable")) {
            RocketMQMessageListener messageListener = this.getClass().getAnnotation(RocketMQMessageListener.class);
            eventMessageBaseProcessor.doMessageProcess(messageListener, messageExt, message);
        }
    }
}
```

### 1.3 消息解析处理

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/event/EventMessageProcessor.java`

```java
@Service
public class EventMessageProcessor {
    
    // 消息处理主方法
    public BizEventMessageVO doMessageProcessor(String topic, String consumer, String tag, String messageBody) {
        // 1. 消息校验
        String validateResult = messageProcessCheck(topic, consumer, messageBody);
        if(StringUtils.isNotEmpty(validateResult)) {
            log.warn("message validate failed, topic={},consumer={},message={}", topic, consumer, messageBody);
            return null;
        }

        // 2. 获取字段映射配置
        MqEventFieldMappingConfig mappingConfig = mqEventFieldConfigService.getMqEventFieldMappingConfig(topic, consumer, tag);
        if(Objects.isNull(mappingConfig)) {
            log.warn("can't find mq event field config, topic={},consumer={},tag={}", topic, consumer, tag);
            return null;
        }

        // 3. 消息解析和数据处理
        BizEventMessageVO bizEventMessage = messageProcess(mappingConfig, messageBody);
        return bizEventMessage;
    }
}
```

## 2. 业务处理层 - 事件流控和预筛

### 2.1 业务事件处理入口

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/mq/impl/MqConsumeServiceImpl.java`

```java
@Service
public class MqConsumeServiceImpl implements MqConsumeService {

    @Override
    public void bizEventProcess(String messageId, BizEventMessageVO bizEventMessageVO) {
        Transaction transaction = Tracer.newTransaction("MqConsumeService", "bizEventProcess");
        try {
            // 1. 事件流控检查
            if (isReject(bizEventMessageVO)) {
                Tracer.logEvent("RejectT0Event", bizEventMessageVO.getBizEventType());
                log.info("事件命中流控丢弃, event:{}, userId:{}", 
                        bizEventMessageVO.getBizEventType(), bizEventMessageVO.getCreditUserId());
                return;
            }
            
            // 2. 调用策略预筛
            strategyEventDispatchService.prescreen(messageId, bizEventMessageVO);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            logger.warn("策略预筛方法异常", e);
            transaction.setStatus(Transaction.SUCCESS);
        } finally {
            transaction.complete();
        }
    }
}
```

### 2.2 事件流控逻辑

**Apollo配置**: 
```properties
# 用户事件限流配置
eventFlcConfig = {
    "IterableDataBase": 600,
    "Start": 600,
    "ApiCreditEnablenNoApplySuccess": 300,
    "Login": 600,
    "RepaySuccess": 600
}
```

**代码实现**:
```java
// 事件流控检查
private boolean isReject(BizEventMessageVO bizEventMessageVO) {
    try {
        Long userId = Optional.ofNullable(bizEventMessageVO.getCreditUserId())
                .orElse(bizEventMessageVO.getUser_id());
        
        if (StringUtils.isNotEmpty(bizEventMessageVO.getBizEventType()) && userId != null && userId > 0) {
            EventFlcConfig eventFlcConfig = appConfigService.getEventFlcConfig();
            if (eventFlcConfig != null) {
                Integer limitSeconds = eventFlcConfig.getLimitSeconds(bizEventMessageVO.getBizEventType());
                if (limitSeconds != null && limitSeconds > 0) {
                    String limitKey = String.format("eventflc:%s:%s", bizEventMessageVO.getBizEventType(), userId);
                    boolean ret = redisUtils.lock(limitKey, "1", limitSeconds);
                    if (!ret) {
                        return true; // 被流控拦截
                    }
                }
            }
        }
    } catch (Exception ex) {
        log.error("isReject", ex);
    }
    return false;
}
```

## 3. 策略预筛阶段

### 3.1 预筛入口

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/StrategyEventDispatchServiceImpl.java`

```java
@Override
public void prescreen(String messageId, BizEventMessageVO bizEventMessageVO) {
    long startTime = Instant.now().toEpochMilli();
    Tracer.logEvent("T0Events", bizEventMessageVO.getBizEventType());
    log.info("实时策略-预筛开始,事件:{},用户ID:{},消息ID:{}", 
            bizEventMessageVO.getBizEventType(), bizEventMessageVO.getCreditUserId(), messageId);

    // 1. 检查是否有对应策略配置
    List<StrategyMarketEventDo> marketEventList = cacheStrategyMarketEventService
            .getByEventName(bizEventMessageVO.getBizEventType());
    if (CollectionUtils.isEmpty(marketEventList)) {
        log.warn("不存在[{}]事件所对应的策略", bizEventMessageVO.getBizEventType());
        return;
    }

    // 2. 遍历每个策略进行预筛
    for (StrategyMarketEventDo marketEventDo : marketEventList) {
        try {
            BizEventVO event = convertToBizEventVO(bizEventMessageVO, marketEventDo, messageId);
            StrategyEventCheckContext eventContext = new StrategyEventCheckContext(event);
            
            // 策略预筛
            this.strategyPrescreenHandler(eventContext);
            // 事件预筛
            this.eventPrescreenHandler(eventContext);
            // 人群预筛
            this.crowdPrescreenHandler(eventContext, bizEventMessageVO);
            // 推入引擎次数限制
            strategyEngineService.checkEngineRateLimitThrowException(event.getAppUserId(), event.getStrategyId());
            // 延迟处理
            this.delayHandler(eventContext);
            
        } catch (Exception e) {
            log.warn("未通过预筛, 用户ID:{}, 手机号:{}", event.getAppUserId(), event.getMobile(), e);
        }
    }
}
```

### 3.2 策略预筛逻辑

```java
// 策略预筛 - 检查策略有效期
private void strategyPrescreenHandler(StrategyEventCheckContext eventContext) {
    LocalDateTime currTime = LocalDateTime.now();
    StrategyDo strategyDo = eventContext.getStrategyDo();
    
    if (currTime.isBefore(strategyDo.getValidityBegin()) || currTime.isAfter(strategyDo.getValidityEnd())) {
        Map<String, Object> param = new HashMap<>(6);
        param.put("validityBegin", strategyDo.getValidityBegin());
        param.put("validityEnd", strategyDo.getValidityEnd());
        param.put("currTime", currTime);
        eventContext.getBizEventVO().addHitResult("time.compare(currTime,between,validityBegin,validityEnd)", param, false);
        eventContext.getBizEventVO().addDecisionFailResult(DecisionResultEnum.STRATEGY_EXPIRE);
        throw new StrategyException("策略已过期");
    }
}
```

### 3.3 延迟处理逻辑

```java
// 延迟处理
private void delayHandler(StrategyEventCheckContext eventContext) {
    BizEventVO bizEventVO = eventContext.getBizEventVO();
    StrategyMarketEventDo marketEventDo = eventContext.getStrategyMarketEventDo();
    
    if (marketEventDo.getTimeType() == 2) { // 延迟类型
        Integer delaySeconds = calculateDelaySeconds(marketEventDo);
        log.info("事件延迟处理, 延迟时间:{}秒, 事件:{}, 用户ID:{}", 
                delaySeconds, bizEventVO.getBizEventType(), bizEventVO.getAppUserId());
        
        // 发送延迟消息
        bizEventMqService.sendDelayMessage(bizEventVO, delaySeconds);
        return;
    }
    
    // 立即处理 - 发送复筛消息
    bizEventMqService.sendDelayMessage(bizEventVO, 0);
}
```

## 4. 策略复筛阶段

### 4.1 复筛入口

```java
@Override
public void rescreen(BizEventVO event) {
    long startTime = Instant.now().toEpochMilli();
    log.info("实时策略-复筛开始,事件:{},用户ID:{},消息ID:{},引擎名称:{}", 
            event.getBizEventType(), event.getAppUserId(), event.getMessageId(), event.getEngineCode());
    
    StrategyLabelCheckContext eventContext = new StrategyLabelCheckContext(event);
    try {
        // 1. 检查策略状态
        StrategyDo strategyDo = strategyRepository.selectById(event.getStrategyId());
        if (StrategyStatusEnum.getPausedCodes().contains(strategyDo.getStatus())) {
            log.info("策略状态:{},不执行复筛流程,strategyId:{}", 
                    StrategyStatusEnum.getInstance(strategyDo.getStatus()).getDescription(), event.getStrategyId());
            return;
        }
        
        // 2. 判断策略类型
        if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT_ENGINE 
                && !Objects.equals(event.getIfIntoEngine(), false)) {
            // 引擎策略复筛
            rescreenWithEngine(event);
            return;
        }
        
        // 3. 普通策略复筛
        this.queryLabelHandler(eventContext);  // 实时标签查询
        this.rescreeningHandler(eventContext); // 策略复筛
        
        // 4. 触达处理
        if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT) {
            this.dispatchHandler(eventContext);
        }
        
    } catch (Exception be) {
        log.warn("未通过复筛,用户:{}", event.getAppUserId(), be);
        rescreenFailProcess(eventContext);
    }
}
```

### 4.2 引擎策略复筛

```java
// 引擎策略复筛逻辑
private void rescreenWithEngine(BizEventVO event) {
    // 1. 构建引擎请求参数
    ModelPredictionReq modelPredictionReq = buildModelPredictionReq(event);
    addStrategyIdParam(modelPredictionReq, event.getStrategyId());

    // 2. 调用AI引擎
    event.setEngineCallerCount(event.getEngineCallerCount() + 1);
    JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
    event.addEngineDetail("enginePredictionResp", resp);

    // 3. 解析引擎决策结果
    Object decisionVal = resp.getJSONObject("data").getJSONObject("output_data").get(event.getEngineCode());
    PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);

    if (predictDecisionDto != null && predictDecisionDto.isSucced()) {
        if (predictDecisionDto.isDelay()) {
            // 延迟处理
            log.info("引擎决策结果:延迟,引擎名称:{},用户ID:{},策略ID:{},延迟时间:{}s",
                    event.getEngineCode(), event.getAppUserId(), event.getStrategyId(),
                    predictDecisionDto.getDelaySeconds());
            delayDispatch(event, predictDecisionDto.getDelaySeconds());
            return;
        }

        // 4. 立即营销 - 遍历引擎返回的动作
        List<PredictDecisionDto.DecisionData.Action> actions = predictDecisionDto.getActions();
        for (PredictDecisionDto.DecisionData.Action action : actions) {
            for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : action.getOrderedDispatch()) {
                try {
                    StrategyMarketChannelEnum strategyMarketChannelEnum =
                            StrategyMarketChannelEnum.getInstance(dispatch.getChannel());

                    // 构建触达参数
                    BizEventVO eventCopy = JsonUtil.parse(JsonUtil.toJson(event), BizEventVO.class);
                    DispatchDto dispatchDto = convertDispatchDto(eventCopy, 0L, strategyMarketChannelEnum,
                                                               StrategyRulerEnum.EVENT, null);
                    CrowdDetailDo crowdDetailDo = new CrowdDetailDo();
                    crowdDetailDo.setUserId(eventCopy.getAppUserId());
                    crowdDetailDo.setMobile(eventCopy.getMobile());
                    crowdDetailDo.setApp(eventCopy.getApp());
                    crowdDetailDo.setInnerApp(eventCopy.getInnerApp());

                    // 调用营销发送
                    int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum,
                                              action.getGroup_id(), dispatch.getDetail_info(), eventCopy);

                    if (sendRet == 0) {
                        log.warn("策略引擎营销触达失败,marketChannel:{},detailInfo:{}",
                                strategyMarketChannelEnum.getDescription(), JsonUtil.toJson(dispatch.getDetail_info()));
                        break;
                    }
                } catch (Exception exception) {
                    log.error("引擎派发营销出现失败:引擎名称:{},用户ID:{},策略ID:{}",
                             event.getEngineCode(), event.getAppUserId(), event.getStrategyId(), exception);
                    break;
                }
            }
        }
    }
}
```

### 4.3 实时标签查询

**Apollo配置**:
```properties
# 标签查询批次大小
strategy.label.query.BatchSize = 200

# 特征平台变量灰度配置
adb.realTime.variable.strategy.contain = (#label == 'user_cur_available_balance' or #label == 'current_utm_soucre_channel' or #label == 'not_login')
```

```java
// 实时标签查询
private void queryLabelHandler(StrategyLabelCheckContext eventContext) {
    BizEventVO bizEventVO = eventContext.getBizEventVO();

    // 1. 获取策略标签配置
    List<StrategyLabelPrimaryDo> labelPrimaryList = cacheStrategyLabelPrimaryService
            .selectListByStrategyId(bizEventVO.getStrategyId());

    if (CollectionUtils.isEmpty(labelPrimaryList)) {
        return;
    }

    // 2. 构建标签查询请求
    List<String> labelCodes = labelPrimaryList.stream()
            .map(StrategyLabelPrimaryDo::getLabelCode)
            .collect(Collectors.toList());

    // 3. 调用标签查询服务
    Map<String, Object> labelValues = abstractAdsStrategyLabelService
            .queryRealTimeLabel(bizEventVO.getAppUserId(), bizEventVO.getApp(),
                              bizEventVO.getInnerApp(), labelCodes, bizEventVO.getStrategyId());

    // 4. 保存标签查询结果
    eventContext.setLabelValues(labelValues);
    bizEventVO.addLabelResult(labelValues);
}
```

## 5. 触达执行阶段

### 5.1 分组渠道匹配

```java
// 分组/渠道匹配
private void dispatchHandler(StrategyLabelCheckContext eventContext) {
    BizEventVO bizEventVO = eventContext.getBizEventVO();
    CrowdDetailDo crowdDetail = convertToCrowdDetail(bizEventVO);
    boolean notFlow = StringUtils.equals("NOTIFY", bizEventVO.getDispatchType());

    // 1. 获取该策略下所有分组
    List<StrategyGroupDo> strategyGroupDoList = cacheStrategyGroupService
            .selectListByStrategyId(bizEventVO.getStrategyId());

    // 2. 用户分组匹配
    strategyGroupDoList = userGroupMatch(bizEventVO, crowdDetail, strategyGroupDoList);

    // 3. 循环分组渠道调用下发
    strategyGroupDoList.forEach(strategyGroupDo -> {
        List<StrategyMarketChannelDo> marketChannelList = cacheStrategyMarketChannelService
                .selectByStrategyGroupId(strategyGroupDo.getId());

        for (StrategyMarketChannelDo channelDo : marketChannelList) {
            StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum
                    .getInstance(channelDo.getMarketChannel());

            // 4. 频控检查
            if (marketChannelEnum != StrategyMarketChannelEnum.NONE && !notFlow) {
                List<CrowdDetailDo> crowdDetailList = flowCtrl(bizEventVO.getMessageId(),
                        bizEventVO.getTriggerDatetime(), channelDo, crowdDetail,
                        Arrays.asList(-1, 1), bizEventVO.getBizEventType());

                if (CollectionUtils.isEmpty(crowdDetailList)) {
                    log.warn("分组/渠道匹配,用户已被流控拦截,终止下发.用户ID:{}", bizEventVO.getAppUserId());
                    continue;
                }
            }

            // 5. 设置触达参数并发送到触达队列
            bizEventVO.setMarketChannelId(channelDo.getId());
            bizEventVO.setStrategyGroupId(strategyGroupDo.getId());
            bizEventVO.setMarketChannel(channelDo.getMarketChannel());

            log.info("策略分组-满足所有条件,调用触达队列...");
            mqProducerService.channelDelivery(bizEventVO);
        }
    });
}
```

### 5.2 触达队列配置

**Apollo配置**:
```properties
# 触达队列配置
biz.event.dispatch.exchange = exchange_biz_event_dispatch_topic
biz.event.dispatch.exchangeType = topic

# 各渠道触达队列
biz.event.sms.dispatch.routingKey = key_biz_event_sms_dispatch_xyf_cdp
biz.event.sms.dispatch.queue.name = queue_biz_event_sms_dispatch_xyf_cdp

biz.event.tele.dispatch.routingKey = key_biz_event_tele_dispatch_xyf_cdp
biz.event.tele.dispatch.queue.name = queue_biz_event_tele_dispatch_xyf_cdp

biz.event.coupon.dispatch.routingKey = key_biz_event_coupon_dispatch_xyf_cdp
biz.event.coupon.dispatch.queue.name = queue_biz_event_coupon_dispatch_xyf_cdp

biz.event.push.dispatch.routingKey = key_biz_event_push_dispatch_xyf_cdp
biz.event.push.dispatch.queue.name = queue_biz_event_push_dispatch_xyf_cdp
```

### 5.3 最终触达执行

```java
@Override
public void dispatch(BizEventVO event) {
    // 1. 触达时间有效性检查
    StrategyDo strategyDo = cacheStrategyService.selectById(event.getStrategyId());
    if (strategyDo != null && strategyDo.getDispatchConfig() != null) {
        DispatchConfig dispatchConfig = JsonUtil.parse(strategyDo.getDispatchConfig(), DispatchConfig.class);
        if (dispatchConfig != null && !dispatchConfig.isInTime(LocalDateTime.now())) {
            log.info("不在有效的触达时间内, 丢弃不进行触达, event={}", JsonUtil.toJson(event));
            return;
        }
    }

    long startTime = Instant.now().toEpochMilli();
    log.info("实时策略-触达开始 事件:{} 用户ID:{} 消息ID:{}",
            event.getBizEventType(), event.getAppUserId(), event.getMessageId());

    CrowdDetailDo crowdDetail = convertToCrowdDetail(event);
    CrowdDetailDo newCrowd = abstractAdsStrategyLabelService.convertCrowdOne(crowdDetail, event.getUserConvert());
    StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(event.getMarketChannel());

    // 2. 再次频控检查
    boolean flcRet = dispatchFlcService.dispatchFlc(event, newCrowd, this);
    if (flcRet) {
        return;
    }

    // 3. 查询策略配置
    Triple<StrategyGroupDo, StrategyMarketChannelDo, StrategyExecLogDo> triple =
            this.getStrategyConfig(event.getStrategyGroupId(), marketChannelEnum);

    // 4. 封装下发请求参数
    DispatchDto dispatchDto = this.convertToDispatchDto(event, triple);
    dispatchDto.setDispatchType(strategyDo.getDispatchType());

    // 5. 执行下发
    try {
        ImmutableTriple<Integer, EventPushBatchDo, Boolean> dispatchResult =
                this.execSend(dispatchDto, newCrowd, marketChannelEnum, triple.getMiddle(), event);

        // 6. 处理下发结果
        if (dispatchResult.getLeft() > 0) {
            log.info("实时策略-触达成功 用户ID:{} 渠道:{} 发送数量:{}",
                    event.getAppUserId(), marketChannelEnum.getDescription(), dispatchResult.getLeft());
        }
    } catch (StrategyException e) {
        log.warn("下发异常,用户ID:{},策略:{},渠道:{},异常信息:{}",
                newCrowd.getUserId(), dispatchDto.getStrategyId(),
                dispatchDto.getStrategyChannel(), e.getMessage(), e);
    }
}
```

### 5.4 渠道具体执行

```java
// 执行发送 - 根据渠道类型分发
private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(
        DispatchDto dispatchDto, CrowdDetailDo crowdDetail,
        StrategyMarketChannelEnum channelEnum, StrategyMarketChannelDo channelDo, BizEventVO bizEvent) {

    ImmutableTriple<Integer, EventPushBatchDo, Boolean> dispatchResult = ImmutableTriple.of(0, null, false);

    if (Objects.equals(Boolean.TRUE, this.convertUserInfo(crowdDetail, channelDo))) {
        switch (channelEnum) {
            case SMS:
                // 短信触达
                dispatchResult = eventDispatchService.sendSmsEvent(dispatchDto,
                        crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent, channelDo.getExtInfo());
                break;
            case VOICE:
                // 电销触达
                dispatchResult = eventDispatchService.sendTeleEvent(dispatchDto,
                        crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
                break;
            case COUPON:
                // 优惠券触达
                dispatchResult = eventDispatchService.sendCouponEvent(dispatchDto,
                        crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent);
                break;
            case PUSH:
                // Push触达
                dispatchResult = eventDispatchService.sendPushEvent(dispatchDto,
                        crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent, channelDo.getExtInfo());
                break;
            case NONE:
                // 不营销
                dispatchResult = ImmutableTriple.of(1, null, false);
                break;
        }
    }
    return dispatchResult;
}
```

## 6. 频控检查机制

### 6.1 频控配置

**Apollo配置**:
```properties
# 复筛常规流控开关
singleDispatchFlc.1 = false  # 短信频控开关
singleDispatchFlc.2 = false  # 电销频控开关
singleDispatchFlc.3 = false  # 优惠券频控开关
singleDispatchFlc.4 = false  # Push频控开关

# 频控线程池配置
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20
```

### 6.2 频控检查实现

**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/dispatch/DispatchFlcService.java`

```java
public boolean dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail,
                          AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
    // 1. 检查频控开关
    StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(bizEventVO.getMarketChannel());
    boolean switchFlag = appConfigService.getSingleDispatchFlcSwitch(marketChannelEnum);
    if (!Objects.equals(true, switchFlag)) {
        return false; // 频控开关未开启
    }

    // 2. 获取频控规则
    StrategyMarketChannelDo channelDo = cacheStrategyMarketChannelService.selectById(bizEventVO.getMarketChannelId());
    List<Integer> sucStatus = Arrays.asList(-1, 1);

    // 3. 执行频控检查
    List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(
            bizEventVO.getMessageId(),
            Optional.ofNullable(bizEventVO.getTriggerDatetime()).orElse(LocalDateTime.now()),
            channelDo, crowdDetail, sucStatus, bizEventVO.getBizEventType());

    // 4. 返回频控结果
    if (CollectionUtils.isEmpty(crowdDetailList)) {
        log.warn("触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}",
                bizEventVO.getAppUserId(), bizEventVO.getMarketChannel(), bizEventVO.getStrategyId());
        return true; // 被频控拦截
    }
    return false; // 通过频控检查
}
```

## 7. 渠道下发实现

### 7.1 短信渠道下发

**Apollo配置**:
```properties
# 短信服务配置
cdp.sms.host = http://sms.xinfei.io
cdp.sms.personal.host = http://sms.xinfei.io
```

**代码实现**:
```java
// 短信事件触达
public ImmutableTriple<Integer, EventPushBatchDo, Boolean> sendSmsEvent(
        DispatchDto dispatchDto, String app, String innerApp, CrowdDetailDo crowdDetail,
        BizEventVO bizEvent, String extInfo) {

    // 1. 构建短信请求
    SmsSingleSendRequester smsRequest = new SmsSingleSendRequester();
    smsRequest.setMobile(crowdDetail.getMobile());
    smsRequest.setTemplateId(dispatchDto.getTemplateId());
    smsRequest.setApp(app);
    smsRequest.setBatchNum(dispatchDto.getStrategyExecId());

    // 2. 设置模板参数
    Map<String, Object> templateParams = buildSmsTemplateParams(bizEvent, extInfo);
    smsRequest.setTemplateParam(templateParams);

    // 3. 调用短信服务
    SmsSingleSendResp smsResp = smsClient.sendSingleSms(smsRequest);

    // 4. 保存发送记录
    EventPushBatchDo eventPushBatch = new EventPushBatchDo();
    eventPushBatch.setBatchNum(smsResp.getBatchNum());
    eventPushBatch.setSendCode(smsResp.getCode());
    eventPushBatch.setSendMsg(smsResp.getMsg());
    eventPushBatch.setStatus(sendStatus(StrategyMarketChannelEnum.SMS.getCode(), smsResp.getCode()));

    // 5. 保存用户触达明细
    UserDispatchDetailDo dispatchDetail = buildDispatchDetail(dispatchDto, crowdDetail, bizEvent);
    userDispatchDetailService.saveEventDispatchDetail(dispatchDto, eventPushBatch, dispatchDetail,
                                                     smsResp.getBatchNum(), crowdDetail,
                                                     Triple.of(smsResp.getCode(), smsResp.getMsg(), smsResp.getBatchNum()));

    return ImmutableTriple.of(1, eventPushBatch, false);
}
```

### 7.2 电销渠道下发

**Apollo配置**:
```properties
# 电销服务配置
cdp.tele.host = http://inner-api-telemarketing-backend.xinfei.io/cdp
cdp.tele.newHost = http://telemkt.xinfei.io
```

### 7.3 优惠券渠道下发

**Apollo配置**:
```properties
# 优惠券服务配置
cdp.coupon.host = http://inner-coupon-api.xinyongfei.io
cdp.coupon.newHost = http://userassetcore.xinfei.io
cdp.coupon.useNewRoutes = true
```

### 7.4 Push渠道下发

**Apollo配置**:
```properties
# Push服务配置
xf.push-service.url = http://sms.xinfei.io
strategy.dispatch.channel.push.pagesize = 1000
```

## 8. 回执处理机制

### 8.1 短信回执处理

**Apollo配置**:
```properties
# 短信回执队列配置
sms.report.exchange = exchange_report_callback_topic
sms.report.exchangeType = topic
sms.report.routingKey = sms_center_callback_app_xyf-cdp
sms.report.queue.name = sms_supplier_report_callback

# 短信回执推送超时时间：单位：小时
sms.report.over.time = 60
```

**代码实现**:
```java
// 短信回执消费者
@RabbitListener(queues = "${sms.report.queue.name}")
public void smsReportCallback(String message) {
    try {
        SmsReportCallbackReq smsReportReq = JsonUtil.parse(message, SmsReportCallbackReq.class);
        log.info("短信回执消息: {}", JsonUtil.toJson(smsReportReq));

        // 更新触达状态
        mqConsumeService.smsReportProcess(smsReportReq);
    } catch (Exception e) {
        log.error("短信回执处理异常", e);
    }
}
```

### 8.2 优惠券回执处理

**Apollo配置**:
```properties
# 优惠券回执队列配置
coupon.callback.exchange = exchange_batch_coupon_callback_send_topic
coupon.callback.exchangeType = direct
coupon.callback.routingKey = coupon_center_callback_app_xyf_cdp
coupon.callback.queue.name = coupon_center_cash_coupon_cdp_process
```

### 8.3 电销回执处理

**RocketMQ配置**: 电销回执通过RocketMQ Topic: `tp_telemkt_import_result` 接收

## 9. 数据存储和统计

### 9.1 核心数据表

```sql
-- 用户触达明细表(分表)
user_dispatch_detail_YYYYMM: 记录每次用户触达的详细信息
-- 事件推送批次表(分表)
event_push_batch_YYYYMM: 记录批次推送信息
-- 策略执行日志表
strategy_exec_log: 记录策略执行统计信息
-- 频控拦截日志表
flow_ctrl_interception_log: 记录频控拦截详情
```

### 9.2 分表策略

**Apollo配置**:
```properties
# 分表大小配置
crowd.detail.table.size = 8000000
```

## 10. 监控和链路追踪

### 10.1 链路追踪配置

**Apollo配置**:
```properties
# 链路追踪配置
xyf.trace.report.enable = true
xyf.brave.zipkin.endpoint = https://gz-trace.cn-beijing.log.aliyuncs.com/zipkin/api/v2/spans
xyf.trace.sls.otel.project = prod-gz-java-cdp
xyf.trace.sls.otel.instance.id = prod-gz-java-cdp
```

### 10.2 关键监控点

```java
// 关键监控埋点
Tracer.logEvent("T0Events", bizEventMessageVO.getBizEventType());           // 事件接收
Tracer.logEvent("RejectT0Event", bizEventMessageVO.getBizEventType());      // 事件流控
Tracer.logEvent("dispatchReject", String.valueOf(event.getStrategyId()));   // 触达时间拒绝
Tracer.logMetricForCount(String.format("send_failed_%s_%s", event.getStrategyId(), channelEnum.getCode())); // 发送失败
```

## 11. 总结

T0实时触达流程的完整链路包括：

1. **消息入口**: RocketMQ消息消费 → 消息解析 → 字段映射
2. **事件流控**: 用户事件频率限制 → Redis分布式锁控制
3. **策略预筛**: 策略有效期检查 → 事件匹配 → 人群预筛 → 延迟处理
4. **策略复筛**: 实时标签查询 → 策略规则验证 → 引擎决策(可选)
5. **触达执行**: 分组匹配 → 频控检查 → 渠道路由 → 具体下发
6. **回执处理**: 各渠道回执消息消费 → 状态更新 → 统计计算

整个流程通过Apollo配置中心进行统一配置管理，通过链路追踪进行全链路监控，确保了系统的可观测性和可维护性。
```
```
