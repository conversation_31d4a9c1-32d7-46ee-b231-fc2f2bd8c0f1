# 四种触达方式详细分析报告

## 概述

基于对XYF-CDP系统的深入代码分析，本报告详细分析了四种核心触达方式的业务逻辑、技术架构、数据流转和差异对比，为"四合一 + 支持实时/延时触达 + 频控 + 回执 + 报表"的触达功能独立部署提供技术支撑。

## 1. 四种触达方式概览

### 1.1 触达方式分类

| 触达方式 | 代码实现类 | 策略类型 | 执行模式 | 主要特点 |
|---------|-----------|---------|---------|---------|
| T0-引擎触达 | StrategyEventDispatchServiceImpl.marketingSend | EVENT_ENGINE | 实时事件+AI引擎 | 智能决策、实时响应 |
| T0-普通触达 | StrategyEventDispatchServiceImpl.execSend | EVENT | 实时事件 | 快速响应、规则驱动 |
| 离线-引擎触达 | StrategyEventDispatchServiceImpl.marketingSend | OFFLINE_ENGINE | 批量+AI引擎 | 智能批处理、资源优化 |
| 离线-普通触达 | AbstractStrategyDispatchService.dispatchHandler | OFFLINE | 批量定时 | 大规模处理、成本优化 |

### 1.2 核心差异对比

#### 1.2.1 触发机制差异
- **T0实时**: 基于RocketMQ事件消息实时触发
- **离线批量**: 基于XXL-JOB定时调度触发

#### 1.2.2 决策机制差异
- **引擎版本**: 调用AI模型平台进行智能决策
- **普通版本**: 基于预设规则进行决策

#### 1.2.3 处理规模差异
- **T0实时**: 单用户实时处理，毫秒级响应
- **离线批量**: 批量用户处理，分页查询+批量下发

## 2. 分层架构分析

### 2.1 整体架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        应用层 (Application)                   │
├─────────────────────────────────────────────────────────────┤
│  触达控制器  │  策略管理  │  人群管理  │  频控管理  │  报表管理  │
├─────────────────────────────────────────────────────────────┤
│                        领域层 (Domain)                       │
├─────────────────────────────────────────────────────────────┤
│  策略服务   │  触达服务  │  频控服务  │  回执服务  │  标签服务   │
├─────────────────────────────────────────────────────────────┤
│                      基础设施层 (Infrastructure)               │
├─────────────────────────────────────────────────────────────┤
│  数据库    │  消息队列  │  外部接口  │  缓存     │  配置中心   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心服务分层

#### 2.2.1 策略执行服务层
- **StrategyEventDispatchServiceImpl**: T0实时策略执行
- **AbstractStrategyDispatchService**: 离线策略执行基类
- **DistributeOfflineEngineDispatchServiceImpl**: 离线引擎策略执行

#### 2.2.2 触达执行服务层
- **EventDispatchService**: 事件触达服务接口
- **EventDispatchServiceImpl**: 事件触达服务实现
- **AbstractDispatchService**: 触达服务基类

#### 2.2.3 频控服务层
- **DispatchFlcService**: 触达频控服务
- **FlowCtrlCoreService**: 频控核心逻辑
- **FlowCtrlServiceImpl**: 频控规则管理

### 2.3 领域层(Domain)各服务实体详解

#### 2.3.1 策略服务(Strategy Service)实体

**核心聚合根**:
```java
// 策略聚合根
StrategyDo {
    Long id;                    // 策略ID
    String name;                // 策略名称
    Integer type;               // 策略类型(0-事件, 1-事件引擎)
    Integer sendRuler;          // 执行规则(0-单次, 1-例行, 2-事件, 3-循环)
    Integer status;             // 策略状态
    String bizKey;              // 场景值
    String engineCode;          // 引擎编号(引擎策略专用)
    String dispatchConfig;      // 触达配置
    LocalDateTime validityBegin; // 策略开始时间
    LocalDateTime validityEnd;   // 策略结束时间
    Long flowCtrlId;            // 流控规则ID
    Integer userConvert;        // 用户转换标识
}
```

**策略分组实体**:
```java
// 策略分组实体
StrategyGroupDo {
    Long id;                    // 分组ID
    Long strategyId;            // 策略ID
    String name;                // 分组名称
    String groupConfig;         // 分组配置(JSON格式)
    Integer isExecutable;       // 是否可执行
    String extInfo;             // 扩展信息

    // 业务方法
    BiPredicate<String, Integer> match(StrategyGroupTypeEnum type); // 分组匹配规则
}
```

**策略渠道实体**:
```java
// 策略渠道实体
StrategyMarketChannelDo {
    Long id;                    // 渠道ID
    Long strategyId;            // 策略ID
    Long strategyGroupId;       // 分组ID
    Integer marketChannel;      // 渠道类型(1-短信, 2-电销, 3-优惠券, 5-Push等)
    String templateId;          // 模板ID
    String app;                 // 应用标识
    String dispatchApp;         // 下发应用
    LocalTime sendTime;         // 发送时间
    String cron;                // 定时表达式
    Integer xxlJobId;           // XXL-JOB任务ID
    String extInfo;             // 扩展信息
}
```

**策略执行日志实体**:
```java
// 策略执行日志实体
StrategyExecLogDo {
    Long id;                    // 日志ID
    Long strategyId;            // 策略ID
    Long strategyGroupId;       // 分组ID
    String strategyGroupName;   // 分组名称
    Long strategyMarketChannelId; // 渠道ID
    Integer strategyMarketChannel; // 渠道类型
    String templateId;          // 模板ID
    Integer execStatus;         // 执行状态
    String failReason;          // 失败原因
    Integer groupCount;         // 圈选人数
    Integer execCount;          // 执行人数
    Integer sendCount;          // 发送人数
    Integer succCount;          // 成功人数
    LocalDateTime execTime;     // 执行时间
    LocalDateTime finishExecTime; // 完成时间
}
```

#### 2.3.2 人群服务(Crowd Service)实体

**人群包聚合根**:
```java
// 人群包聚合根
CrowdPackDo {
    Long id;                    // 人群包ID
    String crowdName;           // 人群包名称
    Integer filterMethod;       // 筛选类型
    Integer refreshType;        // 刷新类型
    Integer status;             // 状态
    Integer groupType;          // 分组类型
    LocalDateTime validityBegin; // 有效开始时间
    LocalDateTime validityEnd;   // 有效结束时间
    LocalTime refreshTime;      // 刷新时间
    String cron;                // 定时表达式
    Integer xxlJobId;           // XXL-JOB任务ID
    String h5Option;            // H5选项
    Integer crowdPersonNum;     // 人群数量
    String businessType;        // 业务线
    String includeSql;          // 圈选SQL
    String excludeSql;          // 排除SQL
    String crowdSql;            // 人群SQL

    // 业务方法
    void execFailed();          // 执行失败
    void execSuccess(Integer personNum); // 执行成功
    void execExecuting();       // 执行中
}
```

**人群明细实体**:
```java
// 人群明细实体
CrowdDetailDo {
    Long id;                    // 明细ID
    Long crowdId;               // 人群包ID
    Long userId;                // 用户ID
    String app;                 // 应用标识
    String innerApp;            // 内部应用
    Long crowdExecLogId;        // 执行日志ID
    String abNum;               // AB分组号
    Integer appUserIdLast2;     // 用户ID后两位
    LocalDateTime registerTime; // 注册时间
    String mobile;              // 手机号
    String tableName;           // 表名

    // 流程画布扩展字段
    Long preStrategyId;         // 前置策略ID
    String flowBatchNo;         // 流程批次号
    Long nextStrategyId;        // 后置策略ID
    String flowNo;              // 流程号
    Long strategyId;            // 策略ID
    String ossRunVersion;       // OSS运行版本
}
```

#### 2.3.3 触达服务(Dispatch Service)实体

**用户触达明细实体**:
```java
// 用户触达明细实体(分表)
UserDispatchDetailDo {
    Long id;                    // 记录ID
    String tableName;           // 表名
    Long userId;                // 用户ID
    String mobile;              // 手机号
    String batchNum;            // 批次号
    Long crowdPackId;           // 人群包ID
    Long strategyId;            // 策略ID
    Long strategyChannelId;     // 策略渠道ID
    Integer marketChannel;      // 营销渠道
    String strategyExecId;      // 策略执行ID
    Long execLogId;             // 执行日志ID
    Integer status;             // 状态(-1-失败, 0-处理中, 1-成功)
    Integer usedStatus;         // 使用状态
    LocalDateTime dispatchTime; // 触达时间
    String messageId;           // 消息ID
    LocalDateTime triggerDatetime; // 触发时间
    String groupName;           // 分组名称
    Long strategyGroupId;       // 策略分组ID
    String strategyGroupName;   // 策略分组名称
    String bizEventType;        // 业务事件类型
    String templateId;          // 模板ID
    String extDetail;           // 扩展详情
    String dispatchType;        // 触达类型(MKT-营销, SYS-系统)
    String bizType;             // 业务类型
}
```

**事件推送批次实体**:
```java
// 事件推送批次实体(分表)
EventPushBatchDo {
    Long id;                    // 批次ID
    String tableName;           // 表名
    Long strategyId;            // 策略ID
    Long marketChannelId;       // 渠道ID
    Long execLogId;             // 执行日志ID
    Integer marketChannel;      // 触达渠道
    Long userId;                // 用户ID
    String mobile;              // 手机号
    String app;                 // 应用标识
    String templateId;          // 模板ID
    String batchNum;            // 批次号
    String innerBatchNum;       // 内部批次号
    String sendCode;            // 发送状态码
    String sendMsg;             // 发送消息
    Integer status;             // 下发状态(1-成功, 2-失败)
    Integer queryStatus;        // 结果查询状态
    String detailTableNo;       // 明细表序号
    String groupName;           // 分组名称
    Long strategyGroupId;       // 策略分组ID
    String strategyGroupName;   // 策略分组名称
    String bizEventType;        // 业务事件类型
}
```

**触达请求DTO**:
```java
// 触达请求DTO
DispatchDto {
    Long strategyId;            // 策略ID
    Long strategyGroupId;       // 策略分组ID
    String strategyGroupName;   // 策略分组名称
    Long strategyChannelId;     // 策略渠道ID
    String strategyChannel;     // 策略渠道
    String templateId;          // 模板ID
    String detailTableNo;       // 明细表序号
    String strategyExecId;      // 策略执行ID
    StrategyRulerEnum strategyRulerEnum; // 策略类型
    String failReason;          // 失败原因
    String bizEventType;        // 事件名
    StrategyMarketChannelDo strategyMarketChannelDo; // 触达渠道
    List<FlowCtrlDo> flowCtrlList; // 流控规则
    String nameTypeId;          // 类型ID
    String activityId;          // 优惠券活动ID
    String dispatchType;        // 触达类型
    String signatureKey;        // 短信签名
    Map<String, Object> eventParamMap; // 事件参数
    String bizType;             // 业务线类型
}
```

#### 2.3.4 频控服务(FlowCtrl Service)实体

**频控规则聚合根**:
```java
// 频控规则聚合根
FlowCtrlDo {
    Long id;                    // 规则ID
    Integer type;               // 规则类型(1-策略, 2-渠道, 3-多策略, 4-业务线)
    String name;                // 规则名称
    String description;         // 规则描述
    Integer status;             // 规则状态(0-初始化, 1-生效中, 2-已关闭)
    String effectiveStrategy;   // 生效策略(0-全部，多个逗号分隔)
    String effectiveChannel;    // 生效渠道(0-全部，多个逗号分隔)
    Integer dayCount;           // 日触达次数
    Integer weekCount;          // 周触达次数
    Integer monthCount;         // 月触达次数
    Integer priority;           // 优先级
    Integer strategyType;       // 策略类型(1-离线, 2-事件)
    Integer limitDays;          // 触达限制天数
    Integer limitTimes;         // 触达限制次数
    String bizType;             // 业务类型

    // 业务方法
    String getEffectiveContent(Map<Long, StrategyDo> strategyDoMap); // 获取生效内容
}
```

**频控检查DTO**:
```java
// 频控检查DTO
FlowCtrlDto {
    String tableNo;             // 表序号
    StrategyMarketChannelDo marketChannelDo; // 渠道信息
    List<CrowdDetailDo> list;   // 用户明细集合
    List<FlowCtrlDo> flowCtrlRuleList; // 频控规则列表
    String messageId;           // 消息ID
    LocalDateTime triggerDatetime; // 上报时间
    StrategyRulerEnum strategyRulerEnum; // 策略规则类型
    String bizEventType;        // 业务事件类型
}
```

**频控拦截日志实体**:
```java
// 频控拦截日志实体
FlowCtrlInterceptionLogDo {
    Long id;                    // 日志ID
    Long flowCtrlId;            // 频控规则ID
    Long userId;                // 用户ID
    Long strategyId;            // 策略ID
    Integer marketChannel;      // 渠道类型
    LocalDateTime interceptionTime; // 拦截时间
    String tableNo;             // 表序号
}
```

#### 2.3.5 回执服务(Receipt Service)实体

**回执处理实体**:
```java
// 短信回执请求
SmsReportCallbackReq {
    String batchNum;            // 批次号
    String status;              // 回执状态
    String mobile;              // 手机号
    String messageId;           // 消息ID
    LocalDateTime reportTime;   // 回执时间
    String errorCode;           // 错误码
    String errorMsg;            // 错误信息
}

// 优惠券回执请求
CouponCallbackReq {
    String batchNum;            // 批次号
    Long userId;                // 用户ID
    String status;              // 发放状态
    Integer usedStatus;         // 使用状态
    String couponId;            // 优惠券ID
    LocalDateTime callbackTime; // 回执时间
}

// 电销回执请求
TeleImportResultReq {
    String batchNum;            // 批次号
    List<TeleCallResult> callResults; // 通话结果列表

    static class TeleCallResult {
        Long userId;            // 用户ID
        String callStatus;      // 通话状态
        Integer callDuration;   // 通话时长
        LocalDateTime callTime; // 通话时间
        String recordUrl;       // 录音地址
    }
}

// Push回执请求
PushReportReq {
    String batchNum;            // 批次号
    Long userId;                // 用户ID
    String status;              // Push状态
    String pushId;              // Push ID
    LocalDateTime reportTime;   // 回执时间
}
```

#### 2.3.6 标签服务(Label Service)实体

**标签元数据实体**:
```java
// 标签元数据实体
MetaLabelDto {
    Long id;                    // 标签ID
    String labelCode;           // 标签编码
    String labelName;           // 标签名称
    String labelType;           // 标签类型
    String dataType;            // 数据类型
    String description;         // 标签描述
    String category;            // 标签分类
    Integer status;             // 标签状态
    LocalDateTime createTime;   // 创建时间
    LocalDateTime updateTime;   // 更新时间
}

// 人群标签主表实体
CrowdLabelPrimaryDo {
    Long id;                    // 主键ID
    Long crowdId;               // 人群包ID
    String labelCode;           // 标签编码
    String labelName;           // 标签名称
    String labelCondition;      // 标签条件
    String labelValue;          // 标签值
    Integer labelType;          // 标签类型
    Integer status;             // 状态
}
```

#### 2.3.7 上下文对象(Context Objects)

**策略上下文**:
```java
// 策略执行上下文
StrategyContext {
    StrategyDo strategyDo;      // 策略信息
    StrategyGroupDo strategyGroupDo; // 分组配置
    StrategyMarketChannelDo strategyMarketChannelDo; // 触达渠道
    StrategyExecLogDo strategyExecLogDo; // 执行日志
    List<FlowCtrlDo> flowCtrlList; // 流控规则
    List<Long> crowdIds;        // 人群包ID集合
    Map<Long, CrowdContext> crowdContent; // 人群包属性
    ImmutableTriple<Integer, Integer, Integer> countTriple; // 统计信息
    String detailTableNo;       // 明细表序号
    boolean completeDispatch;   // 是否完成下发
}
```

**人群上下文**:
```java
// 人群执行上下文
CrowdContext {
    CrowdPackDo crowdPackDo;    // 人群包信息
    CrowdExecLogDo crowdExecLogDo; // 执行日志
    List<CrowdLabelPrimaryDo> labelList; // 标签列表
    String tableName;           // 表名
    Integer totalCount;         // 总数量
    LocalDateTime execTime;     // 执行时间
}
```

#### 2.3.8 值对象(Value Objects)

**枚举值对象**:
```java
// 策略状态枚举
StrategyStatusEnum {
    DRAFT(-1, "待发布"),
    INIT(0, "已发布"),
    EXECUTING(1, "执行中"),
    SUCCESS(2, "执行成功"),
    FAIL(3, "执行失败"),
    PAUSING(4, "暂停中"),
    ENDED(5, "已结束"),
    DEPRECATED(6, "已删除");
}

// 策略规则枚举
StrategyRulerEnum {
    ONCE(0, "单次"),
    CYCLE(1, "例行"),
    EVENT(2, "事件"),
    CYCLE_DAY(3, "每日循环周期");
}

// 频控类型枚举
FlowCtrlTypeEnum {
    CHANNEL(2, 1, "渠道"),
    STRATEGY(1, 2, "策略"),
    MULTI_STRATEGY(3, 3, "多策略共享"),
    BIZ_LINE(4, 4, "业务线");
}

// 渠道类型枚举
StrategyMarketChannelEnum {
    SMS(1, "短信"),
    VOICE(2, "电销"),
    COUPON(3, "优惠券"),
    VOICE_NEW(4, "新电销"),
    PUSH(5, "Push"),
    APP_RESOURCE(100, "APP资源位");
}
```
```

## 3. 外部接口分析

### 3.1 上游事件接口

#### 3.1.1 业务事件消息队列
```yaml
# RocketMQ配置 (Apollo配置)
业务事件队列:
  高优先级: queue_biz_event_high_level_xyf_cdp
  中优先级: queue_biz_event_middle_level_xyf_cdp
  延迟队列: queue_biz_event_delay_hl_xyf_cdp
```

#### 3.1.2 主要事件类型
- **用户行为事件**: 登录、申请、还款等
- **业务状态事件**: 放款成功、还款成功等
- **风控事件**: 撞库、禁申等
- **系统事件**: 提额、激活等

### 3.2 下游触达接口

#### 3.2.1 短信服务接口
```java
// 短信服务配置
cdp.sms.host = http://sms.xinfei.io
cdp.sms.personal.host = http://sms.xinfei.io
```

#### 3.2.2 电销服务接口
```java
// 电销服务配置
cdp.tele.host = http://inner-api-telemarketing-backend.xinfei.io/cdp
cdp.tele.newHost = http://telemkt.xinfei.io
```

#### 3.2.3 优惠券服务接口
```java
// 优惠券服务配置
cdp.coupon.host = http://inner-coupon-api.xinyongfei.io
cdp.coupon.newHost = http://userassetcore.xinfei.io
```

#### 3.2.4 Push服务接口
```java
// Push服务配置
xf.push-service.url = http://sms.xinfei.io
```

## 4. 请求流程分析

### 4.1 T0实时触达流程

```mermaid
sequenceDiagram
    participant Event as 业务事件
    participant MQ as RocketMQ
    participant Prescreen as 预筛服务
    participant Engine as AI引擎
    participant Rescreen as 复筛服务
    participant FlowCtrl as 频控服务
    participant Dispatch as 触达服务
    participant Channel as 渠道服务

    Event->>MQ: 发送业务事件
    MQ->>Prescreen: 消费事件消息
    Prescreen->>Prescreen: 事件预筛
    Prescreen->>Prescreen: 人群预筛
    
    alt 引擎策略
        Prescreen->>Engine: 调用AI引擎
        Engine->>Rescreen: 引擎决策结果
    else 普通策略
        Prescreen->>Rescreen: 直接复筛
    end
    
    Rescreen->>Rescreen: 实时标签查询
    Rescreen->>Rescreen: 策略复筛
    Rescreen->>FlowCtrl: 频控检查
    FlowCtrl->>Dispatch: 触达执行
    Dispatch->>Channel: 渠道下发
    Channel->>Dispatch: 回执结果
```

### 4.2 离线批量触达流程

```mermaid
sequenceDiagram
    participant Job as XXL-JOB
    participant Strategy as 策略服务
    participant Crowd as 人群服务
    participant Label as 标签服务
    participant FlowCtrl as 频控服务
    participant Dispatch as 触达服务
    participant Channel as 渠道服务

    Job->>Strategy: 定时触发策略
    Strategy->>Crowd: 查询人群数据
    Crowd->>Strategy: 分页返回用户
    Strategy->>Label: 批量标签查询
    Label->>Strategy: 返回标签结果
    Strategy->>FlowCtrl: 批量频控检查
    FlowCtrl->>Strategy: 返回通过用户
    Strategy->>Dispatch: 批量触达执行
    Dispatch->>Channel: 批量渠道下发
    Channel->>Dispatch: 批量回执结果
```

## 5. 数据流转分析

### 5.1 核心数据实体

#### 5.1.1 策略相关表
```sql
-- 策略主表
strategy: 策略基本信息、执行规则、状态管理
-- 策略分组表  
strategy_group: AB测试分组配置
-- 策略渠道表
strategy_market_channel: 触达渠道配置、模板配置
-- 策略执行日志表
strategy_exec_log: 策略执行状态、统计信息
```

#### 5.1.2 人群相关表
```sql
-- 人群包主表
crowd_pack: 人群包基本信息、状态
-- 人群明细表(分表)
crowd_detail_YYYYMM: 用户明细数据
-- 人群执行日志表
crowd_exec_log: 人群包执行记录
```

#### 5.1.3 触达相关表
```sql
-- 用户触达明细表(分表)
user_dispatch_detail_YYYYMM: 用户触达记录、状态
-- 事件推送批次表(分表)  
event_push_batch_YYYYMM: 批次推送记录
-- 触达流控拦截日志表
flow_ctrl_interception_log: 频控拦截记录
```

### 5.2 数据流转路径

#### 5.2.1 T0实时数据流
```
业务事件 → RocketMQ → 事件处理器 → 策略预筛 → AI引擎(可选) → 
策略复筛 → 频控检查 → 触达执行 → 渠道下发 → 回执处理 → 状态更新
```

#### 5.2.2 离线批量数据流
```
定时任务 → 策略查询 → 人群查询 → 标签查询 → 分组过滤 → 
频控检查 → 批量触达 → 渠道下发 → 回执处理 → 统计更新
```

## 6. 表结构详细分析

### 6.1 策略核心表结构

#### 6.1.1 strategy表
```sql
-- 策略主表字段分析
id: 策略ID
name: 策略名称  
type: 策略类型(0-事件, 1-事件引擎)
send_ruler: 执行规则(0-单次, 1-例行, 2-事件, 3-循环)
status: 策略状态(0-已发布, 1-执行中, 2-成功, 3-失败, 4-暂停, 5-结束)
engine_code: 引擎代码(引擎策略专用)
dispatch_config: 触达配置(时间窗口等)
flow_ctrl_id: 流控规则ID
```

#### 6.1.2 strategy_group表
```sql
-- 策略分组表字段分析
id: 分组ID
strategy_id: 策略ID
name: 分组名称
group_config: 分组配置(JSON格式，包含AB测试规则)
is_executable: 是否可执行
```

#### 6.1.3 strategy_market_channel表
```sql
-- 策略渠道表字段分析
id: 渠道ID
strategy_id: 策略ID
strategy_group_id: 分组ID
market_channel: 渠道类型(1-短信, 2-电销, 3-优惠券, 4-Push等)
template_id: 模板ID
app: 应用标识
dispatch_app: 下发应用
send_time: 发送时间
cron: 定时表达式
xxl_job_id: XXL-JOB任务ID
ext_info: 扩展信息
```

### 6.2 触达记录表结构

#### 6.2.1 user_dispatch_detail表(分表)
```sql
-- 用户触达明细表字段分析
id: 记录ID
strategy_id: 策略ID
strategy_channel_id: 策略渠道ID
crowd_pack_id: 人群包ID
exec_log_id: 执行日志ID
market_channel: 渠道类型
strategy_exec_id: 策略执行ID
batch_num: 批次号
user_id: 用户ID
mobile: 手机号
status: 触达状态(-1-失败, 1-成功, 0-处理中)
dispatch_time: 触达时间
used_status: 使用状态
message_id: 消息ID
trigger_datetime: 触发时间
group_name: 分组名称
template_id: 模板ID
strategy_group_id: 策略分组ID
strategy_group_name: 策略分组名称
ext_detail: 扩展详情
dispatch_type: 触达类型(MKT-营销, SYS-系统)
biz_type: 业务类型
```

#### 6.2.2 event_push_batch表(分表)
```sql
-- 事件推送批次表字段分析
id: 批次ID
strategy_id: 策略ID
market_channel_id: 渠道ID
exec_log_id: 执行日志ID
market_channel: 渠道类型
user_id: 用户ID
app: 应用标识
template_id: 模板ID
batch_num: 批次号
inner_batch_num: 内部批次号
send_code: 发送状态码
send_msg: 发送消息
status: 批次状态
query_status: 查询状态
detail_table_no: 明细表序号
group_name: 分组名称
strategy_group_id: 策略分组ID
strategy_group_name: 策略分组名称
```

### 6.3 频控相关表结构

#### 6.3.1 flow_ctrl_rule表
```sql
-- 流控规则表字段分析
id: 规则ID
name: 规则名称
type: 流控类型(1-策略, 2-渠道, 3-多策略共享, 4-业务线)
effective_content_type: 生效内容类型
effective_content: 生效内容
market_channel: 渠道类型
limit_count: 限制次数
limit_days: 限制天数
priority: 优先级
status: 规则状态
```

#### 6.3.2 flow_ctrl_interception_log表
```sql
-- 流控拦截日志表字段分析
id: 日志ID
flow_ctrl_id: 流控规则ID
user_id: 用户ID
strategy_id: 策略ID
market_channel: 渠道类型
interception_time: 拦截时间
table_no: 表序号
```

## 7. 四种触达方式逻辑差异详解

### 7.1 T0-引擎触达 (StrategyEventDispatchServiceImpl.marketingSend)

#### 7.1.1 核心特征
- **触发方式**: 实时事件驱动
- **决策机制**: AI引擎智能决策
- **处理模式**: 单用户实时处理
- **响应时间**: 毫秒级

#### 7.1.2 执行流程
```java
// 核心代码逻辑
public void rescreenWithEngine(BizEventVO event) {
    // 1. 调用AI引擎进行决策
    JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
    PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);

    // 2. 根据引擎决策结果执行
    if (predictDecisionDto.isSucced()) {
        if (predictDecisionDto.isDelay()) {
            // 延迟处理
            delayDispatch(event, predictDecisionDto.getDelaySeconds());
        } else {
            // 立即营销
            for (PredictDecisionDto.DecisionData.Action action : predictDecisionDto.getDecisionData().getActions()) {
                for (PredictDecisionDto.DecisionData.Action.Dispatch dispatch : action.getOrderedDispatch()) {
                    // 调用marketingSend进行触达
                    int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum,
                                              action.getGroup_id(), dispatch.getDetail_info(), eventCopy);
                }
            }
        }
    }
}
```

#### 7.1.3 业务特点
- **智能决策**: 基于机器学习模型进行用户画像分析
- **动态参数**: 引擎可返回动态的触达内容和参数
- **多渠道编排**: 支持多渠道组合触达策略
- **延迟支持**: 支持引擎决策的延迟触达

### 7.2 T0-普通触达 (StrategyEventDispatchServiceImpl.execSend)

#### 7.2.1 核心特征
- **触发方式**: 实时事件驱动
- **决策机制**: 预设规则决策
- **处理模式**: 单用户实时处理
- **响应时间**: 毫秒级

#### 7.2.2 执行流程
```java
// 核心代码逻辑
private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(
    DispatchDto dispatchDto, CrowdDetailDo crowdDetail,
    StrategyMarketChannelEnum channelEnum, StrategyMarketChannelDo channelDo, BizEventVO bizEvent) {

    // 1. 用户信息转换
    if (Objects.equals(Boolean.TRUE, this.convertUserInfo(crowdDetail, channelDo))) {
        switch (channelEnum) {
            case SMS:
                // 短信触达
                dispatchResult = eventDispatchService.sendSmsEvent(dispatchDto, crowdDetail.getApp(),
                                crowdDetail.getInnerApp(), crowdDetail, bizEvent, channelDo.getExtInfo());
                break;
            case VOICE:
                // 电销触达
                dispatchResult = eventDispatchService.sendTeleEvent(dispatchDto, crowdDetail.getApp(),
                                crowdDetail.getInnerApp(), crowdDetail);
                break;
            case COUPON:
                // 优惠券触达
                dispatchResult = eventDispatchService.sendCouponEvent(dispatchDto, crowdDetail.getApp(),
                                crowdDetail.getInnerApp(), crowdDetail, bizEvent);
                break;
            case PUSH:
                // Push触达
                dispatchResult = eventDispatchService.sendPushEvent(dispatchDto, crowdDetail.getApp(),
                                crowdDetail.getInnerApp(), crowdDetail, bizEvent, channelDo.getExtInfo());
                break;
        }
    }
    return dispatchResult;
}
```

#### 7.2.3 业务特点
- **规则驱动**: 基于预设的业务规则进行决策
- **快速响应**: 无需调用外部AI服务，响应更快
- **配置灵活**: 通过策略配置控制触达逻辑
- **渠道丰富**: 支持短信、电销、优惠券、Push等多种渠道

### 7.3 离线-引擎触达 (StrategyEventDispatchServiceImpl.marketingSend)

#### 7.3.1 核心特征
- **触发方式**: 定时调度触发
- **决策机制**: AI引擎批量决策
- **处理模式**: 批量用户处理
- **响应时间**: 分钟级

#### 7.3.2 执行流程
```java
// 核心代码逻辑 - 离线引擎策略
public class StrategyDispatchForOfflineEngineServiceImpl extends AbstractStrategyDispatchService {

    @Override
    protected void coreLogicExecute(StrategyContext strategyContext) {
        // 1. 获取分组匹配规则
        Map<Long, BiPredicate<String, Integer>> matchFunctions = new HashMap<>();
        List<StrategyGroupDo> strategyGroupDoList = getStrategyGroupRepository()
                .selectListByStrategyId(strategyDo.getId());

        // 2. 分页查询+按分组规则过滤+下发
        batchDispatch(strategyContext, Triple.of(strategyDo.getBizKey(), matchFunctions, strategyGroupDoList));
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
        StrategyContext strategyContext, String app, String innerApp,
        List<CrowdDetailDo> batch, List<T> templateParam) {

        // 调用引擎进行批量决策
        for (CrowdDetailDo crowdDetail : batch) {
            // 调用AI引擎
            JSONObject engineResp = modelPlatformService.prediction(buildModelRequest(crowdDetail));
            // 根据引擎结果进行触达
            marketingSend(dispatchDto, crowdDetail, channelEnum, groupId, detailInfo, null);
        }
    }
}
```

#### 7.3.3 业务特点
- **批量智能**: 对大批量用户进行AI引擎决策
- **资源优化**: 在业务低峰期执行，避免影响实时业务
- **成本控制**: 批量调用引擎，降低单次调用成本
- **数据一致性**: 基于快照数据保证批量处理一致性

### 7.4 离线-普通触达 (AbstractStrategyDispatchService.dispatchHandler)

#### 7.4.1 核心特征
- **触发方式**: 定时调度触发
- **决策机制**: 预设规则决策
- **处理模式**: 批量用户处理
- **响应时间**: 分钟级

#### 7.4.2 执行流程
```java
// 核心代码逻辑 - 离线普通策略
public abstract class AbstractStrategyDispatchService {

    protected void dispatchHandler(StrategyContext context, AtomicInteger groupCount,
                                 AtomicInteger totalCount, AtomicInteger sendCount,
                                 String app, List<CrowdDetailDo> list, List<Object> params) {
        try {
            // 1. 执行下发逻辑
            Pair<Integer, Integer> respPair = this.executeDispatch(context, app, app, list, params);
        } catch (StrategyException e) {
            throw new StrategyException(e.getCode(), e.getMessage());
        }
    }

    private <T> ImmutablePair<Integer, Integer> executeDispatch(StrategyContext strategyContext,
                                                               String k1, String k2,
                                                               List<CrowdDetailDo> v2, List<T> templateParam) {
        // 调用具体的渠道实现类
        ImmutablePair<Integer, CrowdPushBatchDo> pair = this.dispatchHandler(strategyContext, k1, k2, v2, templateParam);
        return ImmutablePair.of(pair.getLeft(), pair.getLeft());
    }

    // 抽象方法，由具体渠道实现类实现
    protected abstract <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(
        StrategyContext strategyContext, String app, String innerApp,
        List<CrowdDetailDo> batch, List<T> templateParam);
}
```

#### 7.4.3 具体渠道实现
```java
// 短信渠道实现
public class StrategyDispatchForSmsServiceImpl extends AbstractStrategyDispatchService {
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(...) {
        return sendSms(strategyContext, app, innerApp, batch, Convert.convert(..., params));
    }
}

// 电销渠道实现
public class StrategyDispatchForTeleServiceImpl extends AbstractStrategyDispatchService {
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(...) {
        return sendTele(strategyContext, app, innerApp, batch);
    }
}

// 优惠券渠道实现
public class StrategyDispatchForCouponServiceImpl extends AbstractStrategyDispatchService {
    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(...) {
        return sendCoupon(strategyContext, app, innerApp, batch, Convert.convert(..., params));
    }
}
```

#### 7.4.4 业务特点
- **大规模处理**: 支持百万级用户批量处理
- **渠道专业化**: 每种渠道有专门的实现类
- **模板参数化**: 支持个性化模板参数
- **分页优化**: 通过分页查询避免内存溢出

## 8. 频控机制详细分析

### 8.1 频控架构设计

#### 8.1.1 频控服务层次
```
┌─────────────────────────────────────────┐
│            频控应用层                    │
│  DispatchFlcService (触达频控服务)        │
├─────────────────────────────────────────┤
│            频控核心层                    │
│  FlowCtrlCoreService (频控核心逻辑)       │
├─────────────────────────────────────────┤
│            频控规则层                    │
│  FlowCtrlService (频控规则管理)           │
├─────────────────────────────────────────┤
│            频控数据层                    │
│  FlowCtrlRepository (频控数据访问)        │
└─────────────────────────────────────────┘
```

#### 8.1.2 频控类型分类
```java
// 频控类型枚举
public enum FlowCtrlTypeEnum {
    CHANNEL(2, 1, "渠道"),        // 渠道级频控
    STRATEGY(1, 2, "策略"),       // 策略级频控
    MULTI_STRATEGY(3, 3, "多策略共享"), // 多策略共享频控
    BIZ_LINE(4, 4, "业务线");     // 业务线级频控
}
```

### 8.2 频控规则配置

#### 8.2.1 Apollo频控配置
```properties
# 频控开关配置
singleDispatchFlc.1 = false  # 短信频控开关
singleDispatchFlc.2 = false  # 电销频控开关
singleDispatchFlc.3 = false  # 优惠券频控开关
singleDispatchFlc.4 = false  # Push频控开关

# 频控策略配置
strategy.dispatch.channel.sms.pagesize = 1000      # 短信批次大小
strategy.dispatch.channel.tele.pagesize = 1500     # 电销批次大小
strategy.dispatch.channel.coupon.pagesize = 5000   # 优惠券批次大小
strategy.dispatch.channel.push.pagesize = 1000     # Push批次大小
```

#### 8.2.2 频控规则示例
```sql
-- 频控规则表配置示例
INSERT INTO flow_ctrl_rule (
    name, type, effective_content_type, effective_content,
    market_channel, limit_count, limit_days, priority, status
) VALUES (
    '短信日频控', 2, 1, 'SMS', 1, 3, 1, 1, 1
), (
    '电销周频控', 2, 1, 'VOICE', 2, 5, 7, 2, 1
), (
    '策略级频控', 1, 2, '1001,1002,1003', 0, 10, 30, 3, 1
);
```

### 8.3 频控检查机制

#### 8.3.1 实时频控流程
```java
// T0实时频控检查
public boolean dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail,
                          AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
    // 1. 检查频控开关
    StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(bizEventVO.getMarketChannel());
    boolean switchFlag = appConfigService.getSingleDispatchFlcSwitch(marketChannelEnum);
    if (!Objects.equals(true, switchFlag)) {
        return false; // 频控开关未开启
    }

    // 2. 获取频控规则
    StrategyMarketChannelDo channelDo = cacheStrategyMarketChannelService.selectById(bizEventVO.getMarketChannelId());
    List<Integer> sucStatus = Arrays.asList(-1, 1);

    // 3. 执行频控检查
    List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(
        bizEventVO.getMessageId(),
        Optional.ofNullable(bizEventVO.getTriggerDatetime()).orElse(LocalDateTime.now()),
        channelDo, crowdDetail, sucStatus, bizEventVO.getBizEventType()
    );

    // 4. 返回频控结果
    if (CollectionUtils.isEmpty(crowdDetailList)) {
        log.warn("触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}",
                bizEventVO.getAppUserId(), bizEventVO.getMarketChannel(), bizEventVO.getStrategyId());
        return true; // 被频控拦截
    }
    return false; // 通过频控检查
}
```

#### 8.3.2 批量频控流程
```java
// 离线批量频控检查
public List<CrowdDetailDo> flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
    // 1. 参数校验
    if (CollectionUtils.isEmpty(flowCtrlDto.getList()) ||
        CollectionUtils.isEmpty(flowCtrlDto.getFlowCtrlRuleList())) {
        return flowCtrlDto.getList();
    }

    // 2. 执行频控逻辑
    List<Long> passUserIdList = new ArrayList<>();
    Long strategyId = flowCtrlDto.getMarketChannelDo().getStrategyId();

    if(WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId, "newFlowCtrlSwitch")) {
        // 新频控逻辑
        passUserIdList = this.newExecute(flowCtrlDto, statusList);
    } else {
        // 旧频控逻辑
        passUserIdList = this.execute(flowCtrlDto, statusList);
    }

    // 3. 返回通过频控的用户
    Map<Long, CrowdDetailDo> detailMap = flowCtrlDto.getList().stream()
            .collect(Collectors.toMap(CrowdDetailDo::getUserId, item -> item));
    List<CrowdDetailDo> result = passUserIdList.stream()
            .map(detailMap::get).collect(Collectors.toList());

    return result;
}
```

### 8.4 频控拦截逻辑

#### 8.4.1 频控判断条件
```java
// 频控拦截判断
private boolean interception(FlowCtrlDo flowCtrlDo, UserDispatchIndexDto index) {
    // 1. 获取用户触达次数
    Integer userCount = index.getCount();

    // 2. 获取频控限制次数
    Integer limitCount = flowCtrlDo.getLimitCount();

    // 3. 判断是否超过限制
    if (userCount >= limitCount) {
        log.info("用户触达次数超过限制, userId:{}, 当前次数:{}, 限制次数:{}, 频控规则ID:{}",
                index.getUserId(), userCount, limitCount, flowCtrlDo.getId());
        return true; // 需要拦截
    }

    return false; // 不需要拦截
}
```

#### 8.4.2 频控拦截日志
```java
// 保存频控拦截日志
private void saveInterceptionLogNew(FlowCtrlDto flowCtrlDto,
                                   List<ImmutablePair<Long, Long>> flowCtrlRefuseList) {
    if (CollectionUtils.isEmpty(flowCtrlRefuseList)) {
        return;
    }

    List<FlowCtrlInterceptionLogDo> logList = new ArrayList<>();
    for (ImmutablePair<Long, Long> pair : flowCtrlRefuseList) {
        FlowCtrlInterceptionLogDo logDo = new FlowCtrlInterceptionLogDo();
        logDo.setFlowCtrlId(pair.getLeft());
        logDo.setUserId(pair.getRight());
        logDo.setStrategyId(flowCtrlDto.getMarketChannelDo().getStrategyId());
        logDo.setMarketChannel(flowCtrlDto.getMarketChannelDo().getMarketChannel());
        logDo.setInterceptionTime(LocalDateTime.now());
        logDo.setTableNo(flowCtrlDto.getTableNo());
        logList.add(logDo);
    }

    // 批量保存拦截日志
    flowCtrlInterceptionLogService.saveBatch(logList);
}
```

## 9. 回执处理机制分析

### 9.1 回执架构设计

#### 9.1.1 回执处理流程
```
外部系统回执 → RabbitMQ → 回执消费者 → 状态更新 → 统计计算 → 报表生成
```

#### 9.1.2 回执消息队列配置
```properties
# 短信回执队列配置 (Apollo配置)
sms.report.exchange = exchange_report_callback_topic
sms.report.exchangeType = topic
sms.report.routingKey = sms_center_callback_app_xyf-cdp
sms.report.queue.name = sms_supplier_report_callback

# 优惠券回执队列配置
coupon.callback.exchange = exchange_batch_coupon_callback_send_topic
coupon.callback.exchangeType = direct
coupon.callback.routingKey = coupon_center_callback_app_xyf_cdp
coupon.callback.queue.name = coupon_center_cash_coupon_cdp_process

# 电销回执队列配置 (通过RocketMQ)
电销回执Topic: tp_telemkt_import_result
```

### 9.2 不同渠道回执处理

#### 9.2.1 短信回执处理
```java
// 短信回执消费者
@RabbitListener(queues = "${sms.report.queue.name}")
public void smsReportCallback(String message) {
    try {
        SmsReportCallbackReq smsReportReq = JsonUtil.parse(message, SmsReportCallbackReq.class);
        log.info("短信回执消息: {}", JsonUtil.toJson(smsReportReq));

        // 更新触达状态
        mqConsumeService.smsReportProcess(smsReportReq);
    } catch (Exception e) {
        log.error("短信回执处理异常", e);
    }
}

// 短信回执状态更新
public void smsReportProcess(SmsReportCallbackReq smsReportReq) {
    // 1. 查询触达记录
    List<UserDispatchDetailDo> dispatchDetailList = userDispatchDetailService
            .getByBatchNum(smsReportReq.getBatchNum());

    // 2. 更新触达状态
    for (UserDispatchDetailDo detail : dispatchDetailList) {
        // 根据回执状态更新记录
        if ("SUCCESS".equals(smsReportReq.getStatus())) {
            detail.setStatus(1); // 成功
        } else {
            detail.setStatus(-1); // 失败
        }
        detail.setUpdatedTime(LocalDateTime.now());
    }

    // 3. 批量更新数据库
    userDispatchDetailService.updateBatchById(dispatchDetailList);
}
```

#### 9.2.2 优惠券回执处理
```java
// 优惠券回执消费者
@RabbitListener(queues = "${coupon.callback.queue.name}")
public void couponCallback(String message) {
    try {
        CouponCallbackReq couponReq = JsonUtil.parse(message, CouponCallbackReq.class);
        log.info("优惠券回执消息: {}", JsonUtil.toJson(couponReq));

        // 更新触达状态
        mqConsumeService.couponReportProcess(couponReq);
    } catch (Exception e) {
        log.error("优惠券回执处理异常", e);
    }
}

// 优惠券回执状态更新
public void couponReportProcess(CouponCallbackReq couponReq) {
    // 1. 根据批次号查询触达记录
    List<UserDispatchDetailDo> dispatchDetailList = userDispatchDetailService
            .getByBatchNumAndChannel(couponReq.getBatchNum(), StrategyMarketChannelEnum.COUPON.getCode());

    // 2. 更新优惠券发放状态
    for (UserDispatchDetailDo detail : dispatchDetailList) {
        if (couponReq.getUserId().equals(detail.getUserId())) {
            if ("SUCCESS".equals(couponReq.getStatus())) {
                detail.setStatus(1); // 发放成功
                detail.setUsedStatus(couponReq.getUsedStatus()); // 使用状态
            } else {
                detail.setStatus(-1); // 发放失败
            }
            detail.setUpdatedTime(LocalDateTime.now());
            break;
        }
    }

    // 3. 更新数据库
    userDispatchDetailService.updateBatchById(dispatchDetailList);
}
```

#### 9.2.3 电销回执处理
```java
// 电销回执RocketMQ消费者
@Component
public class TeleCallRocketMqConsumer extends AbstractRocketMqConsumer {

    @Override
    protected void doMessage(String topic, String tag, MessageExt messageExt) {
        try {
            String body = new String(messageExt.getBody(), "UTF-8");
            TeleImportResultReq teleResult = new Gson().fromJson(body, TeleImportResultReq.class);

            log.info("电销回执消息: topic={}, messageId={}, body={}",
                    topic, messageExt.getMsgId(), body);

            // 处理电销回执
            userDispatchDetailService.teleCall(teleResult, StrategyMarketChannelEnum.VOICE_NEW);
        } catch (Exception e) {
            log.error("电销回执处理异常: messageId={}, body={}",
                     messageExt.getMsgId(), new String(messageExt.getBody()), e);
        }
    }
}

// 电销回执状态更新
public void teleCall(TeleImportResultReq teleResult, StrategyMarketChannelEnum channelEnum) {
    // 1. 根据批次号查询电销触达记录
    List<UserDispatchDetailDo> dispatchDetailList = userDispatchDetailService
            .getByBatchNumAndChannel(teleResult.getBatchNum(), channelEnum.getCode());

    // 2. 更新电销结果
    for (TeleImportResultReq.TeleCallResult callResult : teleResult.getCallResults()) {
        for (UserDispatchDetailDo detail : dispatchDetailList) {
            if (callResult.getUserId().equals(detail.getUserId())) {
                // 更新通话状态
                detail.setStatus(getCallStatus(callResult.getCallStatus()));
                detail.setExtDetail(JsonUtil.toJson(callResult)); // 保存详细通话信息
                detail.setUpdatedTime(LocalDateTime.now());
                break;
            }
        }
    }

    // 3. 批量更新数据库
    userDispatchDetailService.updateBatchById(dispatchDetailList);
}

// 电销状态映射
private Integer getCallStatus(String callStatus) {
    switch (callStatus) {
        case "CONNECTED": return 1;    // 接通成功
        case "NO_ANSWER": return 0;    // 未接听
        case "BUSY": return 0;         // 忙线
        case "FAILED": return -1;      // 呼叫失败
        default: return 0;             // 其他状态
    }
}
```

#### 9.2.4 Push回执处理
```java
// Push回执处理
public void pushReportProcess(PushReportReq pushReportReq) {
    log.info("push发送结果通知: {}", JsonUtil.toJson(pushReportReq));

    // 1. 参数校验
    if (Objects.isNull(pushReportReq)) {
        log.warn("push回执内容异常：push回执内容为空");
        return;
    }

    if (PushCallbackStatusEnum.getEnum(pushReportReq.getStatus()) == null) {
        log.warn("该用户触达明细最终状态不正确，批次号：{}，最终状态：{}",
                pushReportReq.getBatchNum(), pushReportReq.getStatus());
        return;
    }

    // 2. 查询Push触达记录
    List<UserDispatchDetailDo> dispatchDetailList = userDispatchDetailService
            .getByBatchNumAndChannel(pushReportReq.getBatchNum(), StrategyMarketChannelEnum.PUSH.getCode());

    // 3. 更新Push状态
    for (UserDispatchDetailDo detail : dispatchDetailList) {
        if (pushReportReq.getUserId().equals(detail.getUserId())) {
            // 根据Push回执状态更新
            PushCallbackStatusEnum statusEnum = PushCallbackStatusEnum.getEnum(pushReportReq.getStatus());
            detail.setStatus(statusEnum.getDbStatus());
            detail.setUpdatedTime(LocalDateTime.now());
            break;
        }
    }

    // 4. 批量更新数据库
    userDispatchDetailService.updateBatchById(dispatchDetailList);
}
```

### 9.3 回执超时处理

#### 9.3.1 回执超时配置
```properties
# Apollo配置 - 回执超时时间
sms.report.over.time = 60  # 短信回执推送超时时间：单位：小时
```

#### 9.3.2 回执超时检查
```java
// 回执超时检查定时任务
@XxlJob("smsReportTimeoutCheck")
public void smsReportTimeoutCheck() {
    try {
        // 1. 获取超时时间配置
        int overTimeHours = appConfigService.getSmsReportOverTime();
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusHours(overTimeHours);

        // 2. 查询超时未回执的记录
        List<UserDispatchDetailDo> timeoutList = userDispatchDetailService
                .getTimeoutDispatchDetails(timeoutThreshold, StrategyMarketChannelEnum.SMS.getCode());

        // 3. 标记超时记录
        for (UserDispatchDetailDo detail : timeoutList) {
            detail.setStatus(-1); // 标记为失败
            detail.setUpdatedTime(LocalDateTime.now());
        }

        // 4. 批量更新
        if (!CollectionUtils.isEmpty(timeoutList)) {
            userDispatchDetailService.updateBatchById(timeoutList);
            log.info("短信回执超时检查完成，处理超时记录数：{}", timeoutList.size());
        }
    } catch (Exception e) {
        log.error("短信回执超时检查异常", e);
    }
}
```

## 10. 报表统计分析

### 10.1 报表数据来源

#### 10.1.1 核心统计表
```sql
-- 策略执行统计
strategy_exec_log: 策略级别统计数据
-- 用户触达统计
user_dispatch_detail_YYYYMM: 用户级别触达数据
-- 批次执行统计
event_push_batch_YYYYMM: 批次级别执行数据
-- 频控拦截统计
flow_ctrl_interception_log: 频控拦截统计数据
```

#### 10.1.2 统计维度分析
```java
// 统计维度枚举
public enum StatisticsDimensionEnum {
    STRATEGY("策略维度"),           // 按策略统计
    CHANNEL("渠道维度"),            // 按渠道统计
    TIME("时间维度"),              // 按时间统计
    USER("用户维度"),              // 按用户统计
    BUSINESS("业务维度");          // 按业务线统计
}
```

### 10.2 实时统计指标

#### 10.2.1 策略执行指标
```java
// 策略执行统计
public class StrategyExecStatistics {
    private Long strategyId;           // 策略ID
    private String strategyName;       // 策略名称
    private Integer groupCount;        // 圈选人数
    private Integer execCount;         // 执行人数
    private Integer sendCount;         // 发送人数
    private Integer succCount;         // 成功人数
    private Integer failCount;         // 失败人数
    private BigDecimal successRate;    // 成功率
    private LocalDateTime execTime;    // 执行时间
}

// 统计查询实现
public StrategyExecStatistics getStrategyStatistics(Long strategyId, LocalDate startDate, LocalDate endDate) {
    // 1. 查询策略执行日志
    List<StrategyExecLogDo> execLogs = strategyExecLogService
            .getByStrategyIdAndDateRange(strategyId, startDate, endDate);

    // 2. 聚合统计数据
    StrategyExecStatistics statistics = new StrategyExecStatistics();
    statistics.setStrategyId(strategyId);
    statistics.setGroupCount(execLogs.stream().mapToInt(StrategyExecLogDo::getGroupCount).sum());
    statistics.setExecCount(execLogs.stream().mapToInt(StrategyExecLogDo::getExecCount).sum());
    statistics.setSendCount(execLogs.stream().mapToInt(StrategyExecLogDo::getSendCount).sum());
    statistics.setSuccCount(execLogs.stream().mapToInt(StrategyExecLogDo::getSuccCount).sum());

    // 3. 计算成功率
    if (statistics.getSendCount() > 0) {
        BigDecimal successRate = BigDecimal.valueOf(statistics.getSuccCount())
                .divide(BigDecimal.valueOf(statistics.getSendCount()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        statistics.setSuccessRate(successRate);
    }

    return statistics;
}
```

#### 10.2.2 渠道触达指标
```java
// 渠道触达统计
public class ChannelDispatchStatistics {
    private Integer channelType;       // 渠道类型
    private String channelName;        // 渠道名称
    private Integer totalCount;        // 总触达数
    private Integer successCount;      // 成功数
    private Integer failCount;         // 失败数
    private Integer pendingCount;      // 处理中数
    private BigDecimal successRate;    // 成功率
    private BigDecimal avgCost;        // 平均成本
}

// 渠道统计查询
public List<ChannelDispatchStatistics> getChannelStatistics(LocalDate startDate, LocalDate endDate) {
    // 1. 按渠道分组统计
    String sql = """
        SELECT
            market_channel,
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN status = -1 THEN 1 ELSE 0 END) as fail_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_count
        FROM user_dispatch_detail_{tableNo}
        WHERE dispatch_time BETWEEN ? AND ?
        GROUP BY market_channel
        """;

    // 2. 执行查询并转换结果
    List<ChannelDispatchStatistics> statisticsList = new ArrayList<>();
    // ... 查询逻辑实现

    return statisticsList;
}
```

### 10.3 频控统计分析

#### 10.3.1 频控拦截统计
```java
// 频控拦截统计
public class FlowCtrlStatistics {
    private Long flowCtrlId;           // 频控规则ID
    private String flowCtrlName;       // 频控规则名称
    private Integer interceptionCount; // 拦截次数
    private Integer totalCount;        // 总触达次数
    private BigDecimal interceptionRate; // 拦截率
    private LocalDate statisticsDate;  // 统计日期
}

// 频控统计查询
public List<FlowCtrlStatistics> getFlowCtrlStatistics(LocalDate startDate, LocalDate endDate) {
    // 1. 查询频控拦截日志
    List<FlowCtrlInterceptionLogDo> interceptionLogs = flowCtrlInterceptionLogService
            .getByDateRange(startDate, endDate);

    // 2. 按频控规则分组统计
    Map<Long, Integer> interceptionCountMap = interceptionLogs.stream()
            .collect(Collectors.groupingBy(
                FlowCtrlInterceptionLogDo::getFlowCtrlId,
                Collectors.summingInt(log -> 1)
            ));

    // 3. 查询总触达次数
    Map<Long, Integer> totalCountMap = getTotalDispatchCount(startDate, endDate);

    // 4. 计算拦截率
    List<FlowCtrlStatistics> statisticsList = new ArrayList<>();
    for (Map.Entry<Long, Integer> entry : interceptionCountMap.entrySet()) {
        FlowCtrlStatistics statistics = new FlowCtrlStatistics();
        statistics.setFlowCtrlId(entry.getKey());
        statistics.setInterceptionCount(entry.getValue());

        Integer totalCount = totalCountMap.getOrDefault(entry.getKey(), 0);
        statistics.setTotalCount(totalCount);

        if (totalCount > 0) {
            BigDecimal interceptionRate = BigDecimal.valueOf(entry.getValue())
                    .divide(BigDecimal.valueOf(totalCount), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            statistics.setInterceptionRate(interceptionRate);
        }

        statisticsList.add(statistics);
    }

    return statisticsList;
}
```

## 11. 技术架构重构建议

### 11.1 微服务拆分方案

#### 11.1.1 服务边界划分
```
┌─────────────────────────────────────────────────────────────┐
│                    触达服务 (Touch Service)                   │
│  - 统一触达接口                                              │
│  - 渠道路由管理                                              │
│  - 触达状态管理                                              │
├─────────────────────────────────────────────────────────────┤
│                    策略服务 (Strategy Service)                │
│  - 策略配置管理                                              │
│  - 规则引擎执行                                              │
│  - AI引擎集成                                               │
├─────────────────────────────────────────────────────────────┤
│                    频控服务 (FlowCtrl Service)                │
│  - 频控规则管理                                              │
│  - 实时频控检查                                              │
│  - 频控统计分析                                              │
├─────────────────────────────────────────────────────────────┤
│                    回执服务 (Receipt Service)                 │
│  - 回执消息处理                                              │
│  - 状态更新管理                                              │
│  - 超时处理机制                                              │
├─────────────────────────────────────────────────────────────┤
│                    报表服务 (Report Service)                  │
│  - 实时统计计算                                              │
│  - 报表数据生成                                              │
│  - 数据可视化                                               │
└─────────────────────────────────────────────────────────────┘
```

#### 11.1.2 数据库拆分策略
```sql
-- 触达服务数据库
touch_db:
  - touch_record          # 触达记录主表
  - touch_channel_config  # 渠道配置表
  - touch_template        # 模板管理表

-- 策略服务数据库
strategy_db:
  - strategy              # 策略主表
  - strategy_group        # 策略分组表
  - strategy_rule         # 策略规则表

-- 频控服务数据库
flowctrl_db:
  - flow_ctrl_rule        # 频控规则表
  - flow_ctrl_log         # 频控日志表
  - flow_ctrl_statistics  # 频控统计表

-- 回执服务数据库
receipt_db:
  - receipt_record        # 回执记录表
  - receipt_config        # 回执配置表
  - receipt_retry         # 回执重试表

-- 报表服务数据库
report_db:
  - report_statistics     # 统计数据表
  - report_dashboard      # 仪表盘配置表
  - report_export         # 导出记录表
```

### 11.2 接口标准化设计

#### 11.2.1 统一触达接口
```java
// 统一触达请求接口
@RestController
@RequestMapping("/api/touch")
public class TouchController {

    @PostMapping("/send")
    public TouchResponse send(@RequestBody TouchRequest request) {
        // 1. 参数校验
        validateRequest(request);

        // 2. 频控检查
        if (!flowCtrlService.checkFlowCtrl(request)) {
            return TouchResponse.flowCtrlReject();
        }

        // 3. 渠道路由
        TouchChannel channel = channelRouter.route(request.getChannelType());

        // 4. 执行触达
        TouchResult result = channel.send(request);

        // 5. 记录日志
        touchRecordService.saveRecord(request, result);

        return TouchResponse.success(result);
    }
}

// 触达请求模型
public class TouchRequest {
    private String requestId;          // 请求ID
    private Long userId;               // 用户ID
    private String mobile;             // 手机号
    private String channelType;        // 渠道类型
    private String templateId;         // 模板ID
    private Map<String, Object> params; // 模板参数
    private String bizType;            // 业务类型
    private Integer priority;          // 优先级
    private LocalDateTime scheduleTime; // 调度时间
}

// 触达响应模型
public class TouchResponse {
    private String code;               // 响应码
    private String message;            // 响应消息
    private TouchResult data;          // 响应数据
    private Long timestamp;            // 时间戳
}
```

#### 11.2.2 渠道适配器模式
```java
// 渠道适配器接口
public interface TouchChannel {
    String getChannelType();
    TouchResult send(TouchRequest request);
    boolean isAvailable();
}

// 短信渠道适配器
@Component
public class SmsChannelAdapter implements TouchChannel {

    @Override
    public String getChannelType() {
        return "SMS";
    }

    @Override
    public TouchResult send(TouchRequest request) {
        // 1. 构建短信请求
        SmsSingleSendRequester smsRequest = buildSmsRequest(request);

        // 2. 调用短信服务
        SmsSingleSendResp smsResp = smsClient.sendSingleSms(smsRequest);

        // 3. 转换响应结果
        return convertToTouchResult(smsResp);
    }

    @Override
    public boolean isAvailable() {
        return smsClient.healthCheck();
    }
}

// 电销渠道适配器
@Component
public class TeleChannelAdapter implements TouchChannel {

    @Override
    public String getChannelType() {
        return "TELE";
    }

    @Override
    public TouchResult send(TouchRequest request) {
        // 电销触达逻辑实现
        TeleSaveBatchRequest teleRequest = buildTeleRequest(request);
        TeleSaveBatchResp teleResp = teleClient.saveBatch(teleRequest);
        return convertToTouchResult(teleResp);
    }

    @Override
    public boolean isAvailable() {
        return teleClient.healthCheck();
    }
}
```

### 11.3 配置中心统一管理

#### 11.3.1 配置分层设计
```yaml
# 应用级配置
application:
  name: touch-service
  version: 1.0.0
  environment: production

# 渠道配置
channels:
  sms:
    host: http://sms.xinfei.io
    timeout: 5000
    retry: 3
    batch_size: 1000
  tele:
    host: http://telemkt.xinfei.io
    timeout: 10000
    retry: 2
    batch_size: 1500
  coupon:
    host: http://userassetcore.xinfei.io
    timeout: 8000
    retry: 3
    batch_size: 5000

# 频控配置
flow_ctrl:
  enable: true
  rules:
    - channel: SMS
      limit_count: 3
      limit_days: 1
    - channel: TELE
      limit_count: 5
      limit_days: 7

# 回执配置
receipt:
  timeout_hours: 60
  retry_times: 3
  batch_size: 1000
```

## 12. 总结与建议

### 12.1 现状总结

#### 12.1.1 优势分析
1. **功能完整**: 四种触达方式覆盖了实时/离线、智能/规则的全部场景
2. **架构清晰**: 分层架构设计合理，职责边界明确
3. **扩展性强**: 支持多种渠道和业务场景扩展
4. **监控完善**: 具备完整的频控、回执、统计功能

#### 12.1.2 问题识别
1. **耦合度高**: 四种触达方式代码耦合，难以独立部署
2. **配置分散**: 配置信息分散在多个地方，管理复杂
3. **接口不统一**: 不同渠道接口标准不一致
4. **监控分散**: 监控指标分散，缺乏统一视图

### 12.2 重构建议

#### 12.2.1 短期优化 (1-2个月)
1. **接口标准化**: 统一触达接口规范
2. **配置集中化**: 将配置迁移到Apollo配置中心
3. **监控统一化**: 建立统一的监控指标体系
4. **文档完善化**: 完善技术文档和操作手册

#### 12.2.2 中期重构 (3-6个月)
1. **服务拆分**: 按照微服务架构拆分独立服务
2. **数据库分离**: 按服务边界拆分数据库
3. **消息队列优化**: 优化消息队列架构和配置
4. **性能优化**: 优化批量处理和并发性能

#### 12.2.3 长期规划 (6-12个月)
1. **云原生改造**: 支持容器化和Kubernetes部署
2. **智能化升级**: 增强AI引擎集成和智能决策能力
3. **多云部署**: 支持多云环境部署和灾备
4. **国际化支持**: 支持多语言和多地区部署

### 12.3 实施路径

#### 12.3.1 渐进式重构策略
1. **保持业务连续性**: 采用渐进式重构，避免业务中断
2. **灰度发布**: 新功能采用灰度发布策略
3. **数据迁移**: 制定详细的数据迁移计划
4. **回滚机制**: 建立完善的回滚机制

#### 12.3.2 风险控制措施
1. **充分测试**: 建立完善的测试体系
2. **监控告警**: 建立实时监控和告警机制
3. **应急预案**: 制定详细的应急处理预案
4. **团队培训**: 加强团队技术培训和知识传递

通过以上分析和建议，可以为"四合一 + 支持实时/延时触达 + 频控 + 回执 + 报表"的触达功能独立部署提供全面的技术支撑和实施指导。
```
