# XYF Touch Service - 统一触达服务

## 项目简介

XYF Touch Service 是麻雀重构专项一期的核心项目，旨在将T0实时触达和离线触达的流程抽象收口到一个新的独立应用，实现触达、频控、回执处理的标准化和统一化。

## 项目特性

- **统一触达入口**：T0实时触达和离线触达统一收口处理
- **标准化流程**：触达、频控、回执处理标准化
- **渠道插件化**：各渠道实现插件化，易于扩展
- **配置化管理**：触达规则、频控策略配置化管理
- **高可用性**：支持实时和批量两种触达模式

## 技术架构

### 分层架构
```
xyf-touch-service/
├── touch-api/                    # API层 - 对外接口
├── touch-application/           # 应用层 - 业务编排
├── touch-domain/               # 领域层 - 核心业务逻辑
├── touch-infrastructure/       # 基础设施层 - 技术实现
└── touch-starter/              # 启动层 - 应用启动
```

### 技术栈
- **框架**: Spring Boot 2.7.18, Spring Cloud 2021.0.8
- **数据库**: MySQL 8.0, MyBatis Plus 3.5.3
- **缓存**: Redis, Redisson 3.21.3
- **消息队列**: RocketMQ 2.2.3
- **配置中心**: Apollo 2.0.1
- **监控**: Micrometer, Prometheus
- **容器化**: Docker, Kubernetes

## 快速开始

### 环境要求
- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- RocketMQ 4.9+

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd xyf-touch-service
```

2. **初始化数据库**
```bash
mysql -u root -p < sql/init.sql
```

3. **修改配置**
编辑 `touch-starter/src/main/resources/application.yml`，配置数据库、Redis等连接信息。

4. **编译项目**
```bash
mvn clean compile
```

5. **启动应用**
```bash
mvn spring-boot:run -pl touch-starter
```

6. **验证启动**
访问 http://localhost:8080/touch-service/actuator/health

### Docker部署

1. **构建镜像**
```bash
mvn clean package -DskipTests
docker build -f docker/Dockerfile -t touch-service:1.0.0 .
```

2. **运行容器**
```bash
docker run -d \
  --name touch-service \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  touch-service:1.0.0
```

### Kubernetes部署

```bash
kubectl apply -f k8s/deployment.yaml
```

## API文档

### 触达接口

#### 单个触达
```http
POST /api/touch/send
Content-Type: application/json

{
  "requestId": "req_123456789",
  "touchType": "REALTIME_NORMAL",
  "channel": "SMS",
  "strategyId": 1001,
  "userId": 123456,
  "bizEventType": "Login",
  "templateParams": {
    "mobile": "13800138000",
    "content": "您好，欢迎登录！"
  }
}
```

#### 批量触达
```http
POST /api/touch/batch-send
Content-Type: application/json

[
  {
    "requestId": "req_123456789",
    "touchType": "OFFLINE_NORMAL",
    "channel": "SMS",
    "strategyId": 1001,
    "userId": 123456
  }
]
```

#### 查询状态
```http
GET /api/touch/status/{requestId}
```

#### 查询记录
```http
GET /api/touch/records?userId=123456&channel=SMS&pageNum=1&pageSize=20
```

## 渠道支持

当前支持的触达渠道：

| 渠道 | 编码 | 描述 | 状态 |
|------|------|------|------|
| SMS | 1 | 短信 | ✅ |
| VOICE | 2 | 电销 | ✅ |
| PUSH | 3 | Push推送 | ✅ |
| COUPON | 4 | 优惠券 | ✅ |
| CREDIT_LIMIT | 5 | 提额 | 🚧 |
| LIFE_BENEFIT | 6 | 生活权益 | 🚧 |
| INTEREST_FREE | 7 | X天免息 | 🚧 |
| AI_CALL | 8 | AI外呼 | 🚧 |

## 频控规则

### 频控类型
- **事件级流控**: 对特定业务事件进行流控
- **触达级流控**: 对触达请求进行流控
- **分布式流控**: 分布式环境下的流控
- **批量流控**: 对批量触达进行流控

### 频控范围
- **用户级**: 针对单个用户的频控
- **策略级**: 针对特定策略的频控
- **渠道级**: 针对特定渠道的频控
- **全局级**: 全局范围的频控

## 监控指标

### 核心指标
- `touch_success_total`: 触达成功总数
- `touch_failure_total`: 触达失败总数
- `touch_flow_control_total`: 频控拦截总数
- `touch_latency_seconds`: 触达延迟分布
- `touch_channel_health`: 渠道健康状态

### 告警规则
- 触达成功率低于95%
- 频控拦截率超过30%
- 渠道不可用
- 触达延迟超过10秒

## 开发指南

### 新增渠道插件

1. 实现 `ChannelPlugin` 接口
2. 添加 `@Component` 注解
3. 实现渠道特定的发送逻辑
4. 配置渠道参数

示例：
```java
@Component
public class NewChannelPlugin implements ChannelPlugin {
    
    @Override
    public TouchChannel getChannel() {
        return TouchChannel.NEW_CHANNEL;
    }
    
    @Override
    public TouchResponse send(TouchCommand command) {
        // 实现发送逻辑
    }
    
    // 其他方法实现...
}
```

### 配置管理

配置优先级：Apollo > application.yml > 默认值

主要配置项：
- `touch.channel.*`: 渠道配置
- `touch.flow-control.*`: 频控配置
- `touch.executor.*`: 线程池配置

## 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn verify
```

### 性能测试
使用JMeter或其他工具进行压力测试，建议测试场景：
- 单个触达QPS测试
- 批量触达吞吐量测试
- 频控功能测试
- 故障恢复测试

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目负责人: CDP开发团队
- 邮箱: <EMAIL>
- 文档: [项目Wiki](wiki-url)

## 更新日志

### v1.0.0 (2025-06-20)
- 初始版本发布
- 支持短信、电销、Push、优惠券四种渠道
- 实现事件级和触达级频控
- 提供REST API和内部服务接口
- 支持Docker和Kubernetes部署
